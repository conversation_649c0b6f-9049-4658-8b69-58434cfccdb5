//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package creator is used for quickly generating pages and content with a simple interface.
// It is built on top of the model package to provide access to the most common
// operations such as creating text and image reports and manipulating existing pages.
package creator ;import (_c "bytes";_e "encoding/xml";_ee "errors";_g "fmt";_ce "github.com/gorilla/i18n/linebreak";_fed "github.com/unidoc/unichart/render";_a "github.com/unidoc/unipdf/v4/common";_eg "github.com/unidoc/unipdf/v4/contentstream";_gc "github.com/unidoc/unipdf/v4/contentstream/draw";
_dd "github.com/unidoc/unipdf/v4/core";_ba "github.com/unidoc/unipdf/v4/internal/graphic2d";_fcf "github.com/unidoc/unipdf/v4/internal/integrations/unichart";_bced "github.com/unidoc/unipdf/v4/internal/license";_cg "github.com/unidoc/unipdf/v4/internal/precision";
_bce "github.com/unidoc/unipdf/v4/internal/transform";_db "github.com/unidoc/unipdf/v4/model";_d "golang.org/x/net/html/charset";_ea "image";_cc "io";_ggd "log";_fa "math";_eb "os";_fb "path/filepath";_bc "regexp";_gg "sort";_fbf "strconv";_fc "strings";
_b "text/template";_ec "unicode";_fe "unicode/utf8";);func (_deb *GraphicSVGElement )processDefs (){_deb ._ccfaaf =make (map[string ]*LinearShading );_deb ._gcdge =make (map[string ]*RadialShading );for _ ,_aeab :=range _deb .Children {if _aeab .Name =="\u0064\u0065\u0066\u0073"{for _ ,_fbcc :=range _aeab .Children {if _fbcc .Name =="\u006c\u0069\u006e\u0065\u0061\u0072\u0047\u0072\u0061d\u0069\u0065\u006e\u0074"{_caae :=_fbcc .Attributes ["\u0069\u0064"];
_aaaf :=_fbcc .parseColorPoints ();_dagf :=_abcff (_aaaf );_afea :=_fbcc .getGradientAngle ();_dagf .SetAngle (-_afea );_dagf .SetExtends (true ,true );_dagf .SetBoundingBox (0,0,_deb .Width ,_deb .Height );_deb ._ccfaaf [_caae ]=_dagf ;}else if _fbcc .Name =="\u0072\u0061\u0064\u0069\u0061\u006c\u0047\u0072\u0061d\u0069\u0065\u006e\u0074"{_gbaee :=_fbcc .Attributes ["\u0069\u0064"];
_dadc :=_fbcc .parseColorPoints ();_gfbb :=_fabg (_deb ._dbcda ,_deb .ViewBox .H ,0,_fa .Min (_deb .Width ,_deb .Height )/2,_dadc );_gfbb .SetExtends (true ,true );_gfbb .SetBoundingBox (0,0,_deb .Width ,_deb .Height );_deb ._gcdge [_gbaee ]=_gfbb ;};};
};};};type fontMetrics struct{_eggd float64 ;_dcba float64 ;_ecbbg float64 ;_agcc float64 ;};

// SetExtends specifies whether ot extend the shading beyond the starting and ending points.
//
// Text extends is set to `[]bool{false, false}` by default.
func (_cbeb *LinearShading )SetExtends (start bool ,end bool ){_cbeb ._afdb .SetExtends (start ,end )};

// SetBorderRadius sets the radius of the background corners.
func (_dg *Background )SetBorderRadius (topLeft ,topRight ,bottomLeft ,bottomRight float64 ){_dg .BorderRadiusTopLeft =topLeft ;_dg .BorderRadiusTopRight =topRight ;_dg .BorderRadiusBottomLeft =bottomLeft ;_dg .BorderRadiusBottomRight =bottomRight ;};func (_ffafg *templateProcessor )parseStyledParagraph (_fcaag *templateNode )(interface{},error ){_aeabe :=_ffafg .creator .NewStyledParagraph ();
for _ ,_efccf :=range _fcaag ._ccge .Attr {_aaffd :=_efccf .Value ;switch _cabbd :=_efccf .Name .Local ;_cabbd {case "\u0074\u0065\u0078\u0074\u002d\u0061\u006c\u0069\u0067\u006e":_aeabe .SetTextAlignment (_ffafg .parseTextAlignmentAttr (_cabbd ,_aaffd ));
case "\u0076\u0065\u0072\u0074ic\u0061\u006c\u002d\u0074\u0065\u0078\u0074\u002d\u0061\u006c\u0069\u0067\u006e":_aeabe .SetTextVerticalAlignment (_ffafg .parseTextVerticalAlignmentAttr (_cabbd ,_aaffd ));case "l\u0069\u006e\u0065\u002d\u0068\u0065\u0069\u0067\u0068\u0074":_aeabe .SetLineHeight (_ffafg .parseFloatAttr (_cabbd ,_aaffd ));
case "\u006d\u0061\u0072\u0067\u0069\u006e":_baga :=_ffafg .parseMarginAttr (_cabbd ,_aaffd );_aeabe .SetMargins (_baga .Left ,_baga .Right ,_baga .Top ,_baga .Bottom );case "e\u006e\u0061\u0062\u006c\u0065\u002d\u0077\u0072\u0061\u0070":_aeabe .SetEnableWrap (_ffafg .parseBoolAttr (_cabbd ,_aaffd ));
case "\u0065\u006ea\u0062\u006c\u0065-\u0077\u006f\u0072\u0064\u002d\u0077\u0072\u0061\u0070":_aeabe .EnableWordWrap (_ffafg .parseBoolAttr (_cabbd ,_aaffd ));case "\u0074\u0065\u0078\u0074\u002d\u006f\u0076\u0065\u0072\u0066\u006c\u006f\u0077":_aeabe .SetTextOverflow (_ffafg .parseTextOverflowAttr (_cabbd ,_aaffd ));
case "\u0078":_aeabe .SetPos (_ffafg .parseFloatAttr (_cabbd ,_aaffd ),_aeabe ._gbdad );case "\u0079":_aeabe .SetPos (_aeabe ._ecgb ,_ffafg .parseFloatAttr (_cabbd ,_aaffd ));case "\u0061\u006e\u0067l\u0065":_aeabe .SetAngle (_ffafg .parseFloatAttr (_cabbd ,_aaffd ));
default:_ffafg .nodeLogDebug (_fcaag ,"\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0073\u0074\u0079l\u0065\u0064\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0060\u0025\u0073`.\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_cabbd );
};};return _aeabe ,nil ;};func _gbda (_dgcc ,_edbe ,_abgc ,_ggcd float64 )*Ellipse {return &Ellipse {_bace :_dgcc ,_gdabb :_edbe ,_fdgf :_abgc ,_gaabd :_ggcd ,_cddg :PositionAbsolute ,_cebc :1.0,_bcgd :ColorBlack ,_daa :1.0,_deee :1.0};};

// GraphicSVGElement represents SVG instances.
type GraphicSVGElement struct{

// ViewBox represents viewBox value of element.
ViewBox struct{X ,Y ,W ,H float64 ;};

// Name of element.
Name string ;

// Attributes of element.
Attributes map[string ]string ;

// Children elements.
Children []*GraphicSVGElement ;

// Content contains text value of element.
Content string ;

// Style element style attribute.
Style *GraphicSVGStyle ;

// Width of element.
Width float64 ;

// Height of element.
Height float64 ;_aece float64 ;_ccfaaf map[string ]*LinearShading ;_gcdge map[string ]*RadialShading ;_dbcda float64 ;_feeb float64 ;};func _eacd (_ddf ,_bbb *_db .PdfPageResources )error {_ddfg ,_ :=_ddf .GetColorspaces ();if _ddfg !=nil &&len (_ddfg .Colorspaces )> 0{for _eeg ,_aab :=range _ddfg .Colorspaces {_bgf :=*_dd .MakeName (_eeg );
if _bbb .HasColorspaceByName (_bgf ){continue ;};_cfd :=_bbb .SetColorspaceByName (_bgf ,_aab );if _cfd !=nil {return _cfd ;};};};return nil ;};

// SetAnchor set gradient position anchor.
// Default to center.
func (_fcfe *RadialShading )SetAnchor (anchor AnchorPoint ){_fcfe ._edfg =anchor };

// Angle returns the block rotation angle in degrees.
func (_aa *Block )Angle ()float64 {return _aa ._da };

// SetMarkedContentID sets the marked content ID.
func (_ecef *PolyBezierCurve )SetMarkedContentID (mcid int64 ){if _ecef ._ceefc ==nil {_ecef ._ceefc =_db .NewStructureTagInfo ();};_ecef ._ceefc .Mcid =mcid ;};

// Decode decodes the child elements of element.
func (_beagd *GraphicSVGElement )Decode (decoder *_e .Decoder )error {for {_bdbd ,_acbbf :=decoder .Token ();if _bdbd ==nil &&_acbbf ==_cc .EOF {break ;};if _acbbf !=nil {return _acbbf ;};switch _gbae :=_bdbd .(type ){case _e .StartElement :_cfab :=_eafda (_gbae );
_fdga :=_cfab .Decode (decoder );if _fdga !=nil {return _fdga ;};_beagd .Children =append (_beagd .Children ,_cfab );case _e .CharData :_agbf :=_fc .TrimSpace (string (_gbae ));if _agbf !=""{_beagd .Content =string (_gbae );};case _e .EndElement :if _gbae .Name .Local ==_beagd .Name {return nil ;
};};};return nil ;};

// HeaderFunctionArgs holds the input arguments to a header drawing function.
// It is designed as a struct, so additional parameters can be added in the future with backwards
// compatibility.
type HeaderFunctionArgs struct{PageNum int ;TotalPages int ;};

// SetMarkedContentID sets marked content ID.
func (_eafb *FilledCurve )SetMarkedContentID (mcid int64 ){if _eafb ._eae ==nil {_eafb ._eae =_db .NewStructureTagInfo ();};_eafb ._eae .Mcid =mcid ;};

// SetMargins sets the Chapter margins: left, right, top, bottom.
// Typically not needed as the creator's page margins are used.
func (_aacf *Chapter )SetMargins (left ,right ,top ,bottom float64 ){_aacf ._cdec .Left =left ;_aacf ._cdec .Right =right ;_aacf ._cdec .Top =top ;_aacf ._cdec .Bottom =bottom ;};

// MoveRight moves the drawing context right by relative displacement dx (negative goes left).
func (_cbec *Creator )MoveRight (dx float64 ){_cbec ._fcga .X +=dx };

// BuyerAddress returns the buyer address used in the invoice template.
func (_cfff *Invoice )BuyerAddress ()*InvoiceAddress {return _cfff ._egde };

// SetLanguageIdentifier sets the language identifier for the paragraph.
func (_eefd *Paragraph )SetLanguageIdentifier (id string ){if _eefd ._dfec ==nil {_eefd ._dfec =_db .NewStructureTagInfo ();_eefd ._dfec .StructureType =_db .StructureTypeParagraph ;};_eefd ._fedb =id ;};func _efcbd (_ggega string ,_ebef ,_cbafa TextStyle )*TOC {_dgbcc :=_cbafa ;
_dgbcc .FontSize =14;_addce :=_gcefe (_dgbcc );_addce .SetEnableWrap (true );_addce .SetTextAlignment (TextAlignmentLeft );_addce .SetMargins (0,0,0,5);_begea :=_addce .Append (_ggega );_begea .Style =_dgbcc ;return &TOC {_bcbeb :_addce ,_bebaa :[]*TOCLine {},_fabcf :_ebef ,_cdaad :_ebef ,_fbgcd :_ebef ,_dfbae :_ebef ,_fedgf :"\u002e",_bfffc :10,_dfcfb :Margins {0,0,2,2},_bdcca :PositionRelative ,_dgbc :_ebef ,_dace :true };
};

// Height returns the height of the division, assuming all components are
// stacked on top of each other.
func (_cdgf *Division )Height ()float64 {var _dac float64 ;for _ ,_bgfe :=range _cdgf ._cadaa {switch _bfgc :=_bgfe .(type ){case marginDrawable :_ ,_ ,_fafg ,_baff :=_bfgc .GetMargins ();_dac +=_bfgc .Height ()+_fafg +_baff ;default:_dac +=_bfgc .Height ();
};};return _dac ;};

// SetFillOpacity sets the fill opacity.
func (_faec *Polygon )SetFillOpacity (opacity float64 ){_faec ._eddfc =opacity };

// Add adds a new Drawable to the chapter.
// Currently supported Drawables:
// - *Paragraph
// - *StyledParagraph
// - *Image
// - *Chart
// - *Table
// - *Division
// - *List
// - *Rectangle
// - *Ellipse
// - *Line
// - *Block,
// - *PageBreak
// - *Chapter
func (_cecd *Chapter )Add (d Drawable )error {if Drawable (_cecd )==d {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0043\u0061\u006e\u006e\u006f\u0074 \u0061\u0064\u0064\u0020\u0069\u0074\u0073\u0065\u006c\u0066");return _ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");
};switch _eea :=d .(type ){case *Paragraph ,*StyledParagraph ,*Image ,*Chart ,*Table ,*Division ,*List ,*Rectangle ,*Ellipse ,*Line ,*Block ,*PageBreak ,*Chapter :_cecd ._bade =append (_cecd ._bade ,d );case containerDrawable :_ded ,_fga :=_eea .ContainerComponent (_cecd );
if _fga !=nil {return _fga ;};_cecd ._bade =append (_cecd ._bade ,_ded );default:_a .Log .Debug ("\u0055n\u0073u\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u003a\u0020\u0025\u0054",d );return _ee .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");
};return nil ;};

// GenerateKDict generates a K dictionary for the text chunk.
func (_facc *TextChunk )GenerateKDict ()(*_db .KDict ,error ){if _facc ._gefgc ==nil {return nil ,_g .Errorf ("t\u0061\u0062\u006c\u0065\u0020\u0073t\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0074\u0061\u0067\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _facc ._gefgc .GenerateKDict (),nil ;};func (_bcgdd *List )split (_agbff DrawContext )(_ecbb ,_faea *List ){var (_gggaa float64 ;_gdabf ,_egfde []*listItem ;);_dgef :=_agbff .Width -_bcgdd ._ddggd .Horizontal ()-_bcgdd ._gagc -_bcgdd .markerWidth ();
_gaage :=_bcgdd .markerWidth ();for _ffda ,_gadb :=range _bcgdd ._bbbe {_bdeg :=_gadb .ctxHeight (_dgef );_gggaa +=_bdeg ;if _gggaa <=_agbff .Height {_gdabf =append (_gdabf ,_gadb );}else {switch _adcg :=_gadb ._agafc .(type ){case *List :_agee :=_agbff ;
_agee .Height =_fa .Floor (_bdeg -(_gggaa -_agbff .Height ));_bfgeb ,_bfecc :=_adcg .split (_agee );if _bfgeb !=nil {_gdfb :=_gfbde ();_gdfb ._daedg =_gadb ._daedg ;_gdfb ._agafc =_bfgeb ;_gdabf =append (_gdabf ,_gdfb );};if _bfecc !=nil {_ceaab :=_adcg ._debc .Style .FontSize ;
_badea ,_accbg :=_adcg ._debc .Style .Font .GetRuneMetrics (' ');if _accbg {_ceaab =_adcg ._debc .Style .FontSize *_badea .Wx *_adcg ._debc .Style .horizontalScale ()/1000.0;};_cdfge :=_fc .Repeat ("\u0020",int (_gaage /_ceaab ));_bafac :=_gfbde ();_bafac ._daedg =*NewTextChunk (_cdfge ,_adcg ._debc .Style );
_bafac ._agafc =_bfecc ;_egfde =append (_egfde ,_bafac );_egfde =append (_egfde ,_bcgdd ._bbbe [_ffda +1:]...);};default:_egfde =_bcgdd ._bbbe [_ffda :];};if len (_egfde )> 0{break ;};};};if len (_gdabf )> 0{_ecbb =_dfafd (_bcgdd ._afeff );*_ecbb =*_bcgdd ;
_ecbb ._bbbe =_gdabf ;};if len (_egfde )> 0{_faea =_dfafd (_bcgdd ._afeff );*_faea =*_bcgdd ;_faea ._bbbe =_egfde ;};return _ecbb ,_faea ;};var PPMM =float64 (72*1.0/25.4);

// SetBorderOpacity sets the border opacity.
func (_gcec *Polygon )SetBorderOpacity (opacity float64 ){_gcec ._bbdb =opacity };

// AddressHeadingStyle returns the style properties used to render the
// heading of the invoice address sections.
func (_acdg *Invoice )AddressHeadingStyle ()TextStyle {return _acdg ._fcfb };

// SetText sets the text content of the Paragraph.
func (_fdbfe *Paragraph )SetText (text string ){_fdbfe ._cegc =text };

// SetPos sets the Table's positioning to absolute mode and specifies the upper-left corner
// coordinates as (x,y).
// Note that this is only sensible to use when the table does not wrap over multiple pages.
// TODO: Should be able to set width too (not just based on context/relative positioning mode).
func (_adbacc *Table )SetPos (x ,y float64 ){_adbacc ._bafbg =PositionAbsolute ;_adbacc ._cafd =x ;_adbacc ._efcce =y ;};type templateNode struct{_aeceb interface{};_ccge _e .StartElement ;_gafge *templateNode ;_eagge int ;_adafb int ;_abbc int64 ;};

// SetLineHeight sets the line height (1.0 default).
func (_ffbb *StyledParagraph )SetLineHeight (lineheight float64 ){_ffbb ._abbd =lineheight };

// SetAltText sets the alternative text for the text chunk.
func (_baagc *TextChunk )SetAltText (text string ){_baagc ._fcgd =&text };

// GeneratePageBlocks generate the Page blocks.  Multiple blocks are generated if the contents wrap
// over multiple pages.
func (_fbed *Chapter )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_ddb :=ctx ;if _fbed ._egfc .IsRelative (){ctx .X +=_fbed ._cdec .Left ;ctx .Y +=_fbed ._cdec .Top ;ctx .Width -=_fbed ._cdec .Left +_fbed ._cdec .Right ;ctx .Height -=_fbed ._cdec .Top ;
};_adbe ,_gbb ,_edgc :=_fbed ._dgca .GeneratePageBlocks (ctx );if _edgc !=nil {return _adbe ,ctx ,_edgc ;};ctx =_gbb ;_dgb :=ctx .X ;_fbae :=ctx .Y -_fbed ._dgca .Height ();_cag :=int64 (ctx .Page );_aeaf :=_fbed .headingNumber ();_bdcd :=_fbed .headingText ();
if _fbed ._cadag {_cdf :=_fbed ._fbg .Add (_aeaf ,_fbed ._bfag ,_fbf .FormatInt (_cag ,10),_fbed ._bafa );if _fbed ._fbg ._dace {_cdf .SetLink (_cag ,_dgb ,_fbae );};};if _fbed ._eggc ==nil {_fbed ._eggc =_db .NewOutlineItem (_bdcd ,_db .NewOutlineDest (_cag -1,_dgb ,_fbae ));
if _fbed ._age !=nil {_fbed ._age ._eggc .Add (_fbed ._eggc );}else {_fbed ._eabb .Add (_fbed ._eggc );};}else {_aeaa :=&_fbed ._eggc .Dest ;_aeaa .Page =_cag -1;_aeaa .X =_dgb ;_aeaa .Y =_fbae ;};for _ ,_ebeg :=range _fbed ._bade {_gcdee ,_defc ,_bfbf :=_ebeg .GeneratePageBlocks (ctx );
if _bfbf !=nil {return _adbe ,ctx ,_bfbf ;};if len (_gcdee )< 1{continue ;};_adbe [len (_adbe )-1].mergeBlocks (_gcdee [0]);_adbe =append (_adbe ,_gcdee [1:]...);ctx =_defc ;};if _fbed ._egfc .IsRelative (){ctx .X =_ddb .X ;};if _fbed ._egfc .IsAbsolute (){return _adbe ,_ddb ,nil ;
};return _adbe ,ctx ,nil ;};

// GetCoords returns the center coordinates of ellipse (`xc`, `yc`).
func (_feed *Ellipse )GetCoords ()(float64 ,float64 ){return _feed ._bace ,_feed ._gdabb };func _dbdedf (_aecb *GraphicSVGElement ,_ggaa *_eg .ContentCreator ){_bfbce ,_acde :=_aecb .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];if _acde {_acaa :=_fc .Fields (_bfbce );
for _ ,_gegeb :=range _acaa {_edadc :=_fc .FieldsFunc (_gegeb ,_gdfbe );if len (_edadc )< 3{_a .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bfbce );
return ;};_fagdb ,_ebacf :=_cacac (_edadc [1],64);if _ebacf !=nil {_a .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bfbce );
return ;};_aegdf ,_ebacf :=_cacac (_edadc [2],64);if _ebacf !=nil {_a .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bfbce );
return ;};if _edadc [0]=="\u0074r\u0061\u006e\u0073\u006c\u0061\u0074e"{_ggaa .Translate (_fagdb ,_aegdf );}else if _edadc [0]=="\u0073\u0063\u0061l\u0065"{_ggaa .Scale (_fagdb ,_aegdf );}else {_a .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bfbce );
return ;};};};};

// SetStyleLeft sets border style for left side.
func (_cede *border )SetStyleLeft (style CellBorderStyle ){_cede ._cdd =style };

// SetColorBottom sets border color for bottom.
func (_gbab *border )SetColorBottom (col Color ){_gbab ._dcg =col };

// SetBorderColor sets border color of the rectangle.
func (_fgfca *Rectangle )SetBorderColor (col Color ){_fgfca ._gdea =col };func _gcefe (_bebf TextStyle )*StyledParagraph {return &StyledParagraph {_dgaca :[]*TextChunk {},_acfg :_bebf ,_ecff :_aedgg (_bebf .Font ),_abbd :1.0,_deace :TextAlignmentLeft ,_gfce :true ,_afdg :true ,_fbfd :false ,_bbacbd :0,_eeadace :1,_faeag :1,_dfedd :PositionRelative ,_eafc :""};
};func (_efabd *Table )wrapRow (_gbcb int ,_fbba DrawContext ,_feba float64 )(bool ,error ){if !_efabd ._dffga {return false ,nil ;};var (_fefac =_efabd ._ccgf [_gbcb ];_dgbf =-1;_abga []*TableCell ;_adgca float64 ;_agfe bool ;_eagea =make ([]float64 ,0,len (_efabd ._bdggd ));
);_dagff :=func (_ffcbc *TableCell ,_eaaf VectorDrawable ,_gfbdb bool )*TableCell {_cddff :=*_ffcbc ;_cddff ._fbaee =_eaaf ;if _gfbdb {_cddff ._efage ++;};return &_cddff ;};_ddfeg :=func (_eeebg int ,_bgeg VectorDrawable ){var _faedb float64 =-1;if _bgeg ==nil {if _cfbcf :=_eagea [_eeebg -_gbcb ];
_cfbcf > _fbba .Height {_bgeg =_efabd ._ccgf [_eeebg ]._fbaee ;_efabd ._ccgf [_eeebg ]._fbaee =nil ;_eagea [_eeebg -_gbcb ]=0;_faedb =_cfbcf ;};};_gacad :=_dagff (_efabd ._ccgf [_eeebg ],_bgeg ,true );_abga =append (_abga ,_gacad );if _faedb < 0{_faedb =_gacad .height (_fbba .Width );
};if _faedb > _adgca {_adgca =_faedb ;};};for _cgbde :=_gbcb ;_cgbde < len (_efabd ._ccgf );_cgbde ++{_fbdgc :=_efabd ._ccgf [_cgbde ];if _fefac ._efage !=_fbdgc ._efage {_dgbf =_cgbde ;break ;};_fbba .Width =_fbdgc .width (_efabd ._bdggd ,_feba );_bbab :=_fbdgc .height (_fbba .Width );
var _fbgb VectorDrawable ;switch _fedgd :=_fbdgc ._fbaee .(type ){case *StyledParagraph :if _bbab > _fbba .Height {_gaada :=_fbba ;_gaada .Height =_fa .Floor (_fbba .Height -_fedgd ._feeea .Top -_fedgd ._feeea .Bottom -0.5*_fedgd .getTextHeight ());_aebgd ,_gefb ,_eafbf :=_fedgd .split (_gaada );
if _eafbf !=nil {return false ,_eafbf ;};if _aebgd !=nil &&_gefb !=nil {_fedgd =_aebgd ;_fbdgc =_dagff (_fbdgc ,_aebgd ,false );_efabd ._ccgf [_cgbde ]=_fbdgc ;_fbgb =_gefb ;_agfe =true ;};_bbab =_fbdgc .height (_fbba .Width );};case *Division :if _bbab > _fbba .Height {_ebfaf :=_fbba ;
_ebfaf .Height =_fa .Floor (_fbba .Height -_fedgd ._fccaf .Top -_fedgd ._fccaf .Bottom );_bfgag ,_agbd :=_fedgd .split (_ebfaf );if _bfgag !=nil &&_agbd !=nil {_fedgd =_bfgag ;_fbdgc =_dagff (_fbdgc ,_bfgag ,false );_efabd ._ccgf [_cgbde ]=_fbdgc ;_fbgb =_agbd ;
_agfe =true ;if _bfgag ._gfef !=nil {_bfgag ._gfef .BorderRadiusBottomLeft =0;_bfgag ._gfef .BorderRadiusBottomRight =0;};if _agbd ._gfef !=nil {_agbd ._gfef .BorderRadiusTopLeft =0;_agbd ._gfef .BorderRadiusTopRight =0;};_bbab =_fbdgc .height (_fbba .Width );
};};case *List :if _bbab > _fbba .Height {_adgbb :=_fbba ;_adgbb .Height =_fa .Floor (_fbba .Height -_fedgd ._ddggd .Vertical ());_gcdd ,_ggbe :=_fedgd .split (_adgbb );if _gcdd !=nil {_fedgd =_gcdd ;_fbdgc =_dagff (_fbdgc ,_gcdd ,false );_efabd ._ccgf [_cgbde ]=_fbdgc ;
};if _ggbe !=nil {_fbgb =_ggbe ;_agfe =true ;};_bbab =_fbdgc .height (_fbba .Width );};};_eagea =append (_eagea ,_bbab );if _agfe {if _abga ==nil {_abga =make ([]*TableCell ,0,len (_efabd ._bdggd ));for _eceeg :=_gbcb ;_eceeg < _cgbde ;_eceeg ++{_ddfeg (_eceeg ,nil );
};};_ddfeg (_cgbde ,_fbgb );};};var _fabf float64 ;for _ ,_fddc :=range _eagea {if _fddc > _fabf {_fabf =_fddc ;};};if _agfe &&_fabf < _fbba .Height {if _dgbf < 0{_dgbf =len (_efabd ._ccgf );};_aegega :=_efabd ._ccgf [_dgbf -1]._efage +_efabd ._ccgf [_dgbf -1]._cgedc -1;
for _caef :=_dgbf ;_caef < len (_efabd ._ccgf );_caef ++{_efabd ._ccgf [_caef ]._efage ++;};_efabd ._ccgf =append (_efabd ._ccgf [:_dgbf ],append (_abga ,_efabd ._ccgf [_dgbf :]...)...);_efabd ._gdbgf =append (_efabd ._gdbgf [:_aegega ],append ([]float64 {_adgca },_efabd ._gdbgf [_aegega :]...)...);
_efabd ._gdbgf [_fefac ._efage +_fefac ._cgedc -2]=_fabf ;};return _agfe ,nil ;};

// WriteToFile writes the Creator output to file specified by path.
func (_gbe *Creator )WriteToFile (outputPath string )error {_aaeg ,_cdaa :=_eb .Create (outputPath );if _cdaa !=nil {return _cdaa ;};defer _aaeg .Close ();return _gbe .Write (_aaeg );};

// Write output of creator to io.Writer interface.
func (_ddeab *Creator )Write (ws _cc .Writer )error {if _dbab :=_ddeab .Finalize ();_dbab !=nil {return _dbab ;};_bbf :="";if _dggg ,_ddeb :=ws .(*_eb .File );_ddeb {_bbf =_dggg .Name ();};_efad :=_db .NewPdfWriter ();_efad .SetOptimizer (_ddeab ._dfaf );
_efad .SetFileName (_bbf );if _ddeab ._edbf !=nil {_ebga :=_efad .SetForms (_ddeab ._edbf );if _ebga !=nil {_a .Log .Debug ("F\u0061\u0069\u006c\u0075\u0072\u0065\u003a\u0020\u0025\u0076",_ebga );return _ebga ;};};if _ddeab ._gdc !=nil {_efad .AddOutlineTree (_ddeab ._gdc );
}else if _ddeab ._gcbc !=nil &&_ddeab .AddOutlines {_efad .AddOutlineTree (&_ddeab ._gcbc .ToPdfOutline ().PdfOutlineTreeNode );};if _ddeab ._gfed !=nil {if _cacb :=_efad .SetPageLabels (_ddeab ._gfed );_cacb !=nil {_a .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020C\u006f\u0075\u006c\u0064 no\u0074 s\u0065\u0074\u0020\u0070\u0061\u0067\u0065 l\u0061\u0062\u0065\u006c\u0073\u003a\u0020%\u0076",_cacb );
return _cacb ;};};if _ddeab ._feca !=nil {for _ ,_fbgg :=range _ddeab ._feca {_gede :=_fbgg .SubsetRegistered ();if _gede !=nil {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043\u006f\u0075\u006c\u0064\u0020\u006e\u006ft\u0020s\u0075\u0062\u0073\u0065\u0074\u0020\u0066\u006f\u006e\u0074\u003a\u0020\u0025\u0076",_gede );
return _gede ;};};};if _ddeab ._cecda !=nil {_dede :=_ddeab ._cecda (&_efad );if _dede !=nil {_a .Log .Debug ("F\u0061\u0069\u006c\u0075\u0072\u0065\u003a\u0020\u0025\u0076",_dede );return _dede ;};};for _adca ,_gbd :=range _ddeab ._bcbe {_bdgc :=_efad .AddPage (_gbd );
if _bdgc !=nil {_a .Log .Error ("\u0046\u0061\u0069\u006ced\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u0050\u0061\u0067\u0065\u003a\u0020%\u0076",_bdgc );return _bdgc ;};if _ddeab ._aceb !=nil {_dbbee :=_ddeab ._aceb .K ;_ccdf ,_dfcc :=_efad .GetPageIndirectObject (_adca );
if _dfcc !=nil {_a .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0043\u006fu\u006c\u0064\u0020n\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0070\u0061ge\u0020\u0069\u006ed\u0069\u0072e\u0063\u0074\u0020\u006f\u0062\u006ae\u0063\u0074 \u0025\u0076",_dfcc );
};var _dcfca func (_afac *_db .KDict );_dcfca =func (_cfaa *_db .KDict ){if _cfaa ==nil {return ;};if _cfaa .GetPageNumber ()-1==int64 (_adca ){_cfaa .SetPage (_ccdf );};for _ ,_bcedd :=range _cfaa .GetChildren (){if _gaa :=_bcedd .GetKDict ();_gaa !=nil {_dcfca (_gaa );
};};};for _ ,_bdcdg :=range _dbbee {_dcfca (_bdcdg );};};};if _ddeab ._aceb !=nil {if _fgea :=_efad .SetCatalogStructTreeRoot (_ddeab ._aceb .ToPdfObject ());_fgea !=nil {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043\u006f\u0075\u006c\u0064\u0020n\u006f\u0074\u0020\u0073\u0065\u0074 \u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006ft\u003a\u0020\u0025\u0076",_fgea );
return _fgea ;};};if _ddeab ._gbf !=nil {if _bbdg :=_efad .SetCatalogViewerPreferences (_ddeab ._gbf .ToPdfObject ());_bbdg !=nil {_a .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0043\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074\u0020\u0056\u0069\u0065\u0077\u0065\u0072\u0050\u0072\u0065\u0066\u0065\u0072e\u006e\u0063\u0065\u0073\u003a\u0020\u0025\u0076",_bbdg );
return _bbdg ;};};if _ddeab ._bge !=""{if _dcbf :=_efad .SetCatalogLanguage (_dd .MakeString (_ddeab ._bge ));_dcbf !=nil {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0043\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0073\u0065t\u0020\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065\u003a\u0020\u0025\u0076",_dcbf );
return _dcbf ;};};_cdgd :=_efad .Write (ws );if _cdgd !=nil {return _cdgd ;};return nil ;};

// Height returns Image's document height.
func (_fegb *Image )Height ()float64 {return _fegb ._fcbd };func _fddd (_fcae ,_acab ,_bad ,_dbee float64 )*border {_fdgc :=&border {};_fdgc ._fbac =_fcae ;_fdgc ._gba =_acab ;_fdgc ._geb =_bad ;_fdgc ._agd =_dbee ;_fdgc ._geaa =ColorBlack ;_fdgc ._dcg =ColorBlack ;
_fdgc ._decd =ColorBlack ;_fdgc ._aff =ColorBlack ;_fdgc ._cfb =0;_fdgc ._eaag =0;_fdgc ._edfb =0;_fdgc ._cbfa =0;_fdgc ._afec =1.0;_fdgc .LineStyle =_gc .LineStyleSolid ;return _fdgc ;};

// SetLineLevelOffset sets the amount of space an indentation level occupies
// for all new lines of the table of contents.
func (_afgc *TOC )SetLineLevelOffset (levelOffset float64 ){_afgc ._bfffc =levelOffset };

// InvoiceCellProps holds all style properties for an invoice cell.
type InvoiceCellProps struct{TextStyle TextStyle ;Alignment CellHorizontalAlignment ;BackgroundColor Color ;BorderColor Color ;BorderWidth float64 ;BorderSides []CellBorderSide ;};

// SetPos sets the grid positioning to absolute mode and specifies the upper-left corner
// coordinates as (x,y).
// Note that this is only sensible to use when the grid does not wrap over multiple pages.
// TODO: Should be able to set width too (not just based on context/relative positioning mode).
func (_egb *Grid )SetPos (x ,y float64 ){_egb ._cgaf =PositionAbsolute ;_egb ._edfdg =x ;_egb ._dbbgd =y ;};

// GenerateKDict generates a K dictionary for the list.
func (_fegba *List )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };func _efbe (_fbabfc *templateProcessor ,_abgf *templateNode )(interface{},error ){return _fbabfc .parsePageBreak (_abgf );};func (_dfcaa *templateProcessor )parseColorAttr (_dfdc ,_cbcgf string )Color {_a .Log .Debug ("\u0050\u0061rs\u0069\u006e\u0067 \u0063\u006f\u006c\u006fr a\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020(`\u0025\u0073\u0060\u002c\u0020\u0025\u0073)\u002e",_dfdc ,_cbcgf );
_cbcgf =_fc .TrimSpace (_cbcgf );if _fc .HasPrefix (_cbcgf ,"\u006c\u0069n\u0065\u0061\u0072-\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0028")&&_fc .HasSuffix (_cbcgf ,"\u0029")&&len (_cbcgf )> 17{return _dfcaa .parseLinearGradientAttr (_dfcaa .creator ,_cbcgf );
};if _fc .HasPrefix (_cbcgf ,"\u0072\u0061d\u0069\u0061\u006c-\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0028")&&_fc .HasSuffix (_cbcgf ,"\u0029")&&len (_cbcgf )> 17{return _dfcaa .parseRadialGradientAttr (_dfcaa .creator ,_cbcgf );};if _cgffe :=_dfcaa .parseColor (_cbcgf );
_cgffe !=nil {return _cgffe ;};return ColorBlack ;};func _dfafd (_ggfa TextStyle )*List {return &List {_debc :TextChunk {Text :"\u2022\u0020",Style :_ggfa },_gagc :0,_dgbd :true ,_gdcf :PositionRelative ,_afeff :_ggfa };};func (_cfbca *GraphicSVGElement )getGradientAngle ()float64 {_gcga ,_efcbe :=_cfbca .Attributes ["\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0054\u0072\u0061\u006es\u0066\u006f\u0072\u006d"];
if _efcbe {_ecgca :=_fc .Fields (_gcga );for _ ,_dade :=range _ecgca {_bfea :=_fc .FieldsFunc (_dade ,_gdfbe );if len (_bfea )< 2{continue ;};if _bfea [0]=="\u0072\u006f\u0074\u0061\u0074\u0065"{_dfefe ,_ecdfd :=_fcde (_bfea [1]);if _ecdfd !=nil {_a .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_gcga );
return 0.0;};return _dfefe ;};};};return 0.0;};

// SetStructureType sets the structure type for the curve polygon.
func (_bcbg *CurvePolygon )SetStructureType (structureType _db .StructureType ){if _bcbg ._ceada ==nil {_bcbg ._ceada =_db .NewStructureTagInfo ();};_bcbg ._ceada .StructureType =structureType ;};

// AddColorStop add color stop information for rendering gradient.
func (_cbdbca *shading )AddColorStop (color Color ,point float64 ){_cbdbca ._bacgg =append (_cbdbca ._bacgg ,_acfa (color ,point ));};

// SetStructTreeRoot sets the structure tree root to be appended in the document that will be created.
func (_bcca *Creator )SetStructTreeRoot (structTreeRoot *_db .StructTreeRoot ){_bcca ._aceb =structTreeRoot ;};

// SetColor sets the line color.
func (_ffge *Curve )SetColor (col Color ){_ffge ._gaab =col };func _aced (_ffced *templateProcessor ,_agdgg *templateNode )(interface{},error ){return _ffced .parseTableCell (_agdgg );};

// SetFillColor sets the fill color for the path.
func (_cdbg *FilledCurve )SetFillColor (color Color ){_cdbg ._fcef =color };

// Marker returns the marker used for the list items.
// The marker instance can be used the change the text and the style
// of newly added list items.
func (_bdce *List )Marker ()*TextChunk {return &_bdce ._debc };

// NewGraphicSVGFromString creates a graphic SVG from a SVG string.
func NewGraphicSVGFromString (svgStr string )(*GraphicSVG ,error ){return _aaad (svgStr )};func (_egcb *pageTransformations )transformPage (_fbcf *_db .PdfPage )error {if _dbca :=_egcb .applyFlip (_fbcf );_dbca !=nil {return _dbca ;};return nil ;};

// NewRadialGradientColor creates a radial gradient color that could act as a color in other componenents.
// Note: The innerRadius must be smaller than outerRadius for the circle to render properly.
func (_acbb *Creator )NewRadialGradientColor (x float64 ,y float64 ,innerRadius float64 ,outerRadius float64 ,colorPoints []*ColorPoint )*RadialShading {return _fabg (x ,y ,innerRadius ,outerRadius ,colorPoints );};

// NewImageFromData creates an Image from image data.
func (_gbbfe *Creator )NewImageFromData (data []byte )(*Image ,error ){return _cecde (data )};

// ToRGB implements interface Color.
// Note: It's not directly used since shading color works differently than regular color.
func (_bcefa *LinearShading )ToRGB ()(float64 ,float64 ,float64 ){return 0,0,0};

// SetDate sets the date of the invoice.
func (_ebbf *Invoice )SetDate (date string )(*InvoiceCell ,*InvoiceCell ){_ebbf ._fgeaf [1].Value =date ;return _ebbf ._fgeaf [0],_ebbf ._fgeaf [1];};func (_gfeaa *GraphicSVGElement )drawPolygon (_eecef *_eg .ContentCreator ,_acebe *_db .PdfPageResources ){_eecef .Add_q ();
_gfeaa .Style .toContentStream (_eecef ,_acebe ,_gfeaa );_begee ,_bdgf :=_bdgcc (_gfeaa .Attributes ["\u0070\u006f\u0069\u006e\u0074\u0073"]);if _bdgf !=nil {_a .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0025\u0076",_bdgf );
return ;};if len (_begee )%2> 0{_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0069n\u0076\u0061l\u0069\u0064\u0020\u0070\u006f\u0069\u006e\u0074s\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u006ce\u006e\u0067\u0074\u0068");return ;
};for _bbde :=0;_bbde < len (_begee );{if _bbde ==0{_eecef .Add_m (_begee [_bbde ]*_gfeaa ._aece ,_begee [_bbde +1]*_gfeaa ._aece );}else {_eecef .Add_l (_begee [_bbde ]*_gfeaa ._aece ,_begee [_bbde +1]*_gfeaa ._aece );};_bbde +=2;};_eecef .Add_l (_begee [0]*_gfeaa ._aece ,_begee [1]*_gfeaa ._aece );
_gfeaa .Style .fillStroke (_eecef );_eecef .Add_h ();_eecef .Add_Q ();};

// SetBackgroundColor sets the cell's background color.
func (_dgce *GridCell )SetBackgroundColor (col Color ){_dgce ._ddgeb =col };

// GeneratePageBlocks draws the rectangle on a new block representing the page. Implements the Drawable interface.
func (_fbfad *Rectangle )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var (_fbfbf []*Block ;_ddddc =NewBlock (ctx .PageWidth ,ctx .PageHeight );_dffb =ctx ;_gaaeb =_fbfad ._dfbcd /2;);_gfggf :=_fbfad ._fbbdf .IsRelative ();if _gfggf {_fbfad .applyFitMode (ctx .Width );
ctx .X +=_fbfad ._abeec .Left +_gaaeb ;ctx .Y +=_fbfad ._abeec .Top +_gaaeb ;ctx .Width -=_fbfad ._abeec .Left +_fbfad ._abeec .Right ;ctx .Height -=_fbfad ._abeec .Top +_fbfad ._abeec .Bottom ;if _fbfad ._cfedb > ctx .Height {_fbfbf =append (_fbfbf ,_ddddc );
_ddddc =NewBlock (ctx .PageWidth ,ctx .PageHeight );ctx .Page ++;_aeece :=ctx ;_aeece .Y =ctx .Margins .Top +_fbfad ._abeec .Top +_gaaeb ;_aeece .X =ctx .Margins .Left +_fbfad ._abeec .Left +_gaaeb ;_aeece .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom -_fbfad ._abeec .Top -_fbfad ._abeec .Bottom ;
_aeece .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_fbfad ._abeec .Left -_fbfad ._abeec .Right ;ctx =_aeece ;};}else {ctx .X =_fbfad ._eeadac ;ctx .Y =_fbfad ._abegd ;};_agfc :=_gc .Rectangle {X :ctx .X ,Y :ctx .PageHeight -ctx .Y -_fbfad ._cfedb ,Width :_fbfad ._cdcgg ,Height :_fbfad ._cfedb ,BorderRadiusTopLeft :_fbfad ._edbfa ,BorderRadiusTopRight :_fbfad ._efgaa ,BorderRadiusBottomLeft :_fbfad ._bbef ,BorderRadiusBottomRight :_fbfad ._fcdc ,Opacity :1.0};
if _fbfad ._ggac !=nil {_agfc .FillEnabled =true ;_faba :=_cdfe (_fbfad ._ggac );_deadd :=_gfae (_ddddc ,_faba ,_fbfad ._ggac ,func ()Rectangle {return Rectangle {_eeadac :_agfc .X ,_abegd :_agfc .Y ,_cdcgg :_agfc .Width ,_cfedb :_agfc .Height };});if _deadd !=nil {return nil ,ctx ,_deadd ;
};_agfc .FillColor =_faba ;};if _fbfad ._gdea !=nil &&_fbfad ._dfbcd > 0{_agfc .BorderEnabled =true ;_agfc .BorderColor =_cdfe (_fbfad ._gdea );_agfc .BorderWidth =_fbfad ._dfbcd ;};_bebd ,_cbcb :=_ddddc .setOpacity (_fbfad ._dfecg ,_fbfad ._adaf );if _cbcb !=nil {return nil ,ctx ,_cbcb ;
};_fcfgd ,_ ,_cbcb :=_agfc .MarkedDraw (_bebd ,_fbfad ._bgbc );if _cbcb !=nil {return nil ,ctx ,_cbcb ;};if _cbcb =_ddddc .addContentsByString (string (_fcfgd ));_cbcb !=nil {return nil ,ctx ,_cbcb ;};if _gfggf {ctx .X =_dffb .X ;ctx .Width =_dffb .Width ;
_aebb :=_fbfad ._cfedb +_gaaeb ;ctx .Y +=_aebb +_fbfad ._abeec .Bottom ;ctx .Height -=_aebb ;}else {ctx =_dffb ;};_fbfbf =append (_fbfbf ,_ddddc );return _fbfbf ,ctx ,nil ;};func (_gcdf *templateProcessor )parseTextChunk (_edeba *templateNode ,_eeea *TextChunk )(interface{},error ){if _edeba ._gafge ==nil {_gcdf .nodeLogError (_edeba ,"\u0054\u0065\u0078\u0074\u0020\u0063\u0068\u0075\u006e\u006b\u0020\u0070\u0061\u0072\u0065n\u0074 \u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c\u002e");
return nil ,_bbcfc ;};var (_beafc =_gcdf .creator .NewTextStyle ();_bbbfb bool ;);for _ ,_begbd :=range _edeba ._ccge .Attr {if _begbd .Name .Local =="\u006c\u0069\u006e\u006b"{_gcgdf ,_fafdfc :=_edeba ._gafge ._aeceb .(*StyledParagraph );if !_fafdfc {_gcdf .nodeLogError (_edeba ,"\u004c\u0069\u006e\u006b \u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065 \u006f\u006e\u006c\u0079\u0020\u0061\u0070\u0070\u006c\u0069\u0063\u0061\u0062\u006c\u0065\u0020\u0074\u006f \u0070\u0061\u0072\u0061\u0067r\u0061\u0070\u0068\u0027\u0073\u0020\u0074\u0065\u0078\u0074\u0020\u0063\u0068\u0075\u006e\u006b\u002e");
_bbbfb =true ;}else {_beafc =_gcgdf ._ecff ;};break ;};};if _eeea ==nil {_eeea =NewTextChunk ("",_beafc );};for _ ,_cgacgg :=range _edeba ._ccge .Attr {_cfbaa :=_cgacgg .Value ;switch _abfg :=_cgacgg .Name .Local ;_abfg {case "\u0063\u006f\u006co\u0072":_eeea .Style .Color =_gcdf .parseColorAttr (_abfg ,_cfbaa );
case "\u006f\u0075\u0074\u006c\u0069\u006e\u0065\u002d\u0063\u006f\u006c\u006f\u0072":_eeea .Style .OutlineColor =_gcdf .parseColorAttr (_abfg ,_cfbaa );case "\u0066\u006f\u006e\u0074":_eeea .Style .Font =_gcdf .parseFontAttr (_abfg ,_cfbaa );case "\u0066o\u006e\u0074\u002d\u0073\u0069\u007ae":_eeea .Style .FontSize =_gcdf .parseFloatAttr (_abfg ,_cfbaa );
case "\u006f\u0075\u0074l\u0069\u006e\u0065\u002d\u0073\u0069\u007a\u0065":_eeea .Style .OutlineSize =_gcdf .parseFloatAttr (_abfg ,_cfbaa );case "\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u002d\u0073\u0070a\u0063\u0069\u006e\u0067":_eeea .Style .CharSpacing =_gcdf .parseFloatAttr (_abfg ,_cfbaa );
case "\u0068o\u0072i\u007a\u006f\u006e\u0074\u0061l\u002d\u0073c\u0061\u006c\u0069\u006e\u0067":_eeea .Style .HorizontalScaling =_gcdf .parseFloatAttr (_abfg ,_cfbaa );case "\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067-\u006d\u006f\u0064\u0065":_eeea .Style .RenderingMode =_gcdf .parseTextRenderingModeAttr (_abfg ,_cfbaa );
case "\u0075n\u0064\u0065\u0072\u006c\u0069\u006ee":_eeea .Style .Underline =_gcdf .parseBoolAttr (_abfg ,_cfbaa );case "\u0075n\u0064e\u0072\u006c\u0069\u006e\u0065\u002d\u0063\u006f\u006c\u006f\u0072":_eeea .Style .UnderlineStyle .Color =_gcdf .parseColorAttr (_abfg ,_cfbaa );
case "\u0075\u006ed\u0065\u0072\u006ci\u006e\u0065\u002d\u006f\u0066\u0066\u0073\u0065\u0074":_eeea .Style .UnderlineStyle .Offset =_gcdf .parseFloatAttr (_abfg ,_cfbaa );case "\u0075\u006e\u0064\u0065rl\u0069\u006e\u0065\u002d\u0074\u0068\u0069\u0063\u006b\u006e\u0065\u0073\u0073":_eeea .Style .UnderlineStyle .Thickness =_gcdf .parseFloatAttr (_abfg ,_cfbaa );
case "\u006c\u0069\u006e\u006b":if !_bbbfb {_eeea .AddAnnotation (_gcdf .parseLinkAttr (_abfg ,_cfbaa ));};case "\u0074e\u0078\u0074\u002d\u0072\u0069\u0073e":_eeea .Style .TextRise =_gcdf .parseFloatAttr (_abfg ,_cfbaa );default:_gcdf .nodeLogDebug (_edeba ,"\u0055\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u0078\u0074\u0020\u0063\u0068\u0075\u006e\u006b\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006bi\u0070p\u0069\u006e\u0067\u002e",_abfg );
};};return _eeea ,nil ;};func (_egcg grayColor )ToRGB ()(float64 ,float64 ,float64 ){return _egcg ._dfa ,_egcg ._dfa ,_egcg ._dfa };

// EnableFontSubsetting enables font subsetting for `font` when the creator output is written to file.
// Embeds only the subset of the runes/glyphs that are actually used to display the file.
// Subsetting can reduce the size of fonts significantly.
func (_feecb *Creator )EnableFontSubsetting (font *_db .PdfFont ){_feecb ._feca =append (_feecb ._feca ,font );};

// Scale sets the scale ratio with `X` factor and `Y` factor for the graphic svg.
func (_bafea *GraphicSVG )Scale (xFactor ,yFactor float64 ){_bafea ._bdca .Width =xFactor *_bafea ._bdca .Width ;_bafea ._bdca .Height =yFactor *_bafea ._bdca .Height ;_bafea ._bdca .SetScaling (xFactor ,yFactor );};

// AddColorStop add color stop info for rendering gradient color.
func (_aaaac *LinearShading )AddColorStop (color Color ,point float64 ){_aaaac ._afdb .AddColorStop (color ,point );};

// IsRelative checks if the positioning is relative.
func (_gcab Positioning )IsRelative ()bool {return _gcab ==PositionRelative };

// GetMargins returns the Chapter's margin: left, right, top, bottom.
func (_adc *Chapter )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _adc ._cdec .Left ,_adc ._cdec .Right ,_adc ._cdec .Top ,_adc ._cdec .Bottom ;};

// SetStructureType sets the structure type for the table cell.
func (_egcdg *TableCell )SetStructureType (structureType _db .StructureType ){if _egcdg ._cdffb ==nil {_egcdg ._cdffb =_db .NewStructureTagInfo ();};_egcdg ._cdffb .StructureType =structureType ;};

// SetMargins sets the Paragraph's margins.
func (_bacd *StyledParagraph )SetMargins (left ,right ,top ,bottom float64 ){_bacd ._feeea .Left =left ;_bacd ._feeea .Right =right ;_bacd ._feeea .Top =top ;_bacd ._feeea .Bottom =bottom ;};

// ScaleToWidth scales the Block to a specified width, maintaining the same aspect ratio.
func (_ebaa *Block )ScaleToWidth (w float64 ){_dfd :=w /_ebaa ._fedd ;_ebaa .Scale (_dfd ,_dfd )};func (_bg *Block )translate (_af ,_gee float64 ){_fgf :=_eg .NewContentCreator ().Translate (_af ,-_gee ).Operations ();*_bg ._fd =append (*_fgf ,*_bg ._fd ...);
_bg ._fd .WrapIfNeeded ();};

// Chapter is used to arrange multiple drawables (paragraphs, images, etc) into a single section.
// The concept is the same as a book or a report chapter.
type Chapter struct{_eab int ;_bfag string ;_dgca *StyledParagraph ;_bade []Drawable ;_eed int ;_ebfb bool ;_cadag bool ;_egfc Positioning ;_cbgc ,_baaf float64 ;_cdec Margins ;_age *Chapter ;_fbg *TOC ;_eabb *_db .Outline ;_eggc *_db .OutlineItem ;_bafa uint ;
};

// SetFontSize sets the font size for the paragraph.
func (_faae *StyledParagraph )SetFontSize (fontSize float64 ){_faae ._acfg .FontSize =fontSize ;for _ ,_aeac :=range _faae ._dgaca {_aeac .Style .FontSize =fontSize ;};};func (_cddgf *Invoice )generateNoteBlocks (_cage DrawContext )([]*Block ,DrawContext ,error ){_becf :=_dfge ();
_bgddd :=append ([][2]string {_cddgf ._egfgg ,_cddgf ._ddgeg },_cddgf ._edgb ...);for _ ,_bfda :=range _bgddd {if _bfda [1]!=""{_bdda :=_cddgf .drawSection (_bfda [0],_bfda [1]);for _ ,_dega :=range _bdda {_becf .Add (_dega );};_acbef :=_gcefe (_cddgf ._bcag );
_acbef .SetMargins (0,0,10,0);_becf .Add (_acbef );};};return _becf .GeneratePageBlocks (_cage );};func (_ebfad *StyledParagraph )getMaxLineWidth ()float64 {if _ebfad ._cbba ==nil ||(_ebfad ._cbba !=nil &&len (_ebfad ._cbba )==0){_ebfad .wrapText ();};
var _ffcg float64 ;for _ ,_bddd :=range _ebfad ._cbba {_abea :=_ebfad .getTextLineWidth (_bddd );if _abea > _ffcg {_ffcg =_abea ;};};return _ffcg ;};func _caaad (_gbbag ...interface{})[]interface{}{return _gbbag };func (_faaf *InvoiceAddress )fmtLine (_ffce ,_edcb string ,_feaga bool )string {if _feaga {_edcb ="";
};return _g .Sprintf ("\u0025\u0073\u0025s\u000a",_edcb ,_ffce );};

// SetStructureType sets the structure type for the graphic svg component.
func (_edcfa *GraphicSVG )SetStructureType (structureType _db .StructureType ){if _edcfa ._fdcf ==nil {_edcfa ._fdcf =_db .NewStructureTagInfo ();};_edcfa ._fdcf .StructureType =structureType ;};

// SetMargins sets the margins of the rectangle.
// NOTE: rectangle margins are only applied if relative positioning is used.
func (_dbdd *Rectangle )SetMargins (left ,right ,top ,bottom float64 ){_dbdd ._abeec .Left =left ;_dbdd ._abeec .Right =right ;_dbdd ._abeec .Top =top ;_dbdd ._abeec .Bottom =bottom ;};

// SetLineWidth sets the line width.
func (_fbff *Polyline )SetLineWidth (lineWidth float64 ){_fbff ._dabba .LineWidth =lineWidth };

// SetMakeredContentID sets the marked content identifier for the ellipse.
func (_dbbed *Ellipse )SetMarkedContentID (mcid int64 ){if _dbbed ._cgbg ==nil {_dbbed ._cgbg =_db .NewStructureTagInfo ();};_dbbed ._cgbg .Mcid =mcid ;};

// AddColorStop add color stop info for rendering gradient color.
func (_agdb *RadialShading )AddColorStop (color Color ,point float64 ){_agdb ._aada .AddColorStop (color ,point );};

// Height returns the height of the Paragraph. The height is calculated based on the input text and how it is wrapped
// within the container. Does not include Margins.
func (_aedg *StyledParagraph )Height ()float64 {_aedg .wrapText ();var _bbdfb float64 ;for _ ,_beggg :=range _aedg ._cbba {var _cebaf float64 ;for _ ,_afedf :=range _beggg {_cegd :=_aedg ._abbd *_afedf .Style .FontSize ;if _cegd > _cebaf {_cebaf =_cegd ;
};};_bbdfb +=_cebaf ;};return _bbdfb ;};

// GetMargins returns the margins of the ellipse: left, right, top, bottom.
func (_edde *Ellipse )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _edde ._dafc .Left ,_edde ._dafc .Right ,_edde ._dafc .Top ,_edde ._dafc .Bottom ;};

// InfoLines returns all the rows in the invoice information table as
// description-value cell pairs.
func (_aaaa *Invoice )InfoLines ()[][2]*InvoiceCell {_cfcbb :=[][2]*InvoiceCell {_aaaa ._eddg ,_aaaa ._fgeaf ,_aaaa ._fbbc };return append (_cfcbb ,_aaaa ._febf ...);};const (CellHorizontalAlignmentLeft CellHorizontalAlignment =iota ;CellHorizontalAlignmentCenter ;
CellHorizontalAlignmentRight ;);

// SetMaxLines sets the maximum number of lines to be drawn.
func (_feebc *StyledParagraph )SetMaxLines (maxLines int ){_feebc ._ffeg =maxLines };func _bfg (_aaebe [][]_gc .CubicBezierCurve )*CurvePolygon {return &CurvePolygon {_accc :&_gc .CurvePolygon {Rings :_aaebe },_feece :1.0,_gafd :1.0};};

// SetContent sets the cell's content.  The content is a VectorDrawable, i.e.
// a Drawable with a known height and width.
// Currently supported VectorDrawables:
// - *Paragraph
// - *StyledParagraph
// - *Image
// - *Chart
// - *Table
// - *Division
// - *List
// - *Rectangle
// - *Ellipse
// - *Line
func (_aegce *TableCell )SetContent (vd VectorDrawable )error {switch _dadcb :=vd .(type ){case *Paragraph :if _dadcb ._gcfc {_dadcb ._gcdc =true ;};_aegce ._fbaee =vd ;case *StyledParagraph :if _dadcb ._afdg {_dadcb ._gfce =true ;};_aegce ._fbaee =vd ;
case *Image ,*Chart ,*Table ,*Division ,*List ,*Rectangle ,*Ellipse ,*Line :_aegce ._fbaee =vd ;default:_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0063e\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0079p\u0065\u0020\u0025\u0054",vd );
return _dd .ErrTypeError ;};return nil ;};

// NewBlock creates a new Block with specified width and height.
func NewBlock (width float64 ,height float64 )*Block {_bcee :=&Block {};_bcee ._fd =&_eg .ContentStreamOperations {};_bcee ._faa =_db .NewPdfPageResources ();_bcee ._fedd =width ;_bcee ._dbe =height ;return _bcee ;};

// SetPos set position of the element on PDF page
func (_gagg *GraphicSVGElement )SetPos (x ,y float64 ){_gagg ._dbcda =x ;_gagg ._feeb =y };

// MoveTo moves the drawing context to absolute coordinates (x, y).
func (_cegf *Creator )MoveTo (x ,y float64 ){_cegf ._fcga .X =x ;_cegf ._fcga .Y =y };

// NewGrid creates a new Grid with a specified number of columns.
func (_eaae *Creator )NewGrid (cols int )*Grid {return _eafbb (cols )};const (HorizontalAlignmentLeft HorizontalAlignment =iota ;HorizontalAlignmentCenter ;HorizontalAlignmentRight ;);func (_bafc *templateProcessor )parseListItem (_dcbde *templateNode )(interface{},error ){if _dcbde ._gafge ==nil {_bafc .nodeLogError (_dcbde ,"\u004c\u0069\u0073t\u0020\u0069\u0074\u0065m\u0020\u0070\u0061\u0072\u0065\u006e\u0074 \u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c\u002e");
return nil ,_bbcfc ;};_fbag ,_bbgf :=_dcbde ._gafge ._aeceb .(*List );if !_bbgf {_bafc .nodeLogError (_dcbde ,"\u004c\u0069s\u0074\u0020\u0069\u0074\u0065\u006d\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u004cis\u0074\u002e");
return nil ,_bbcfc ;};_fcec :=_gfbde ();_fcec ._daedg =_fbag ._debc ;return _fcec ,nil ;};const (DefaultHorizontalScaling =100;);

// SetStructureType sets the structure type for the table.
func (_cccgg *Table )SetStructureType (structureType _db .StructureType ){if _cccgg ._fefdd ==nil {_cccgg ._fefdd =_db .NewStructureTagInfo ();};_cccgg ._fefdd .StructureType =structureType ;};

// GenerateKDict generates a K dictionary for the ellipse.
func (_bafb *Ellipse )GenerateKDict ()(*_db .KDict ,error ){if _bafb ._cgbg ==nil {return nil ,_g .Errorf ("\u0065\u006cli\u0070\u0073\u0065 \u006d\u0061\u0072\u006bed \u0063on\u0074\u0065\u006e\u0074\u0020\u0069\u0064 i\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _bafb ._cgbg .GenerateKDict (),nil ;};

// NewColorPoint creates a new color and point object for use in the gradient rendering process.
func NewColorPoint (color Color ,point float64 )*ColorPoint {return _acfa (color ,point )};

// SetStyleRight sets border style for right side.
func (_fdbe *border )SetStyleRight (style CellBorderStyle ){_fdbe ._egc =style };

// SetLanguage sets the language identifier that will be stored inside document catalog.
func (_dbbcc *Creator )SetLanguage (language string ){_dbbcc ._bge =language };

// SetFillOpacity sets the fill opacity of the ellipse.
func (_fccg *Ellipse )SetFillOpacity (opacity float64 ){_fccg ._cebc =opacity };

// SetSellerAddress sets the seller address of the invoice.
func (_faaa *Invoice )SetSellerAddress (address *InvoiceAddress ){_faaa ._eeddc =address };func _fafdd (_befg *_e .Decoder )(*GraphicSVGElement ,error ){for {_eeeb ,_fdfa :=_befg .Token ();if _eeeb ==nil &&_fdfa ==_cc .EOF {break ;};if _fdfa !=nil {return nil ,_fdfa ;
};switch _eace :=_eeeb .(type ){case _e .StartElement :return _eafda (_eace ),nil ;};};return &GraphicSVGElement {},nil ;};func (_geaab *Invoice )drawSection (_cebe ,_edgbd string )[]*StyledParagraph {var _baaff []*StyledParagraph ;if _cebe !=""{_ddfee :=_gcefe (_geaab ._bgcc );
_ddfee .SetMargins (0,0,0,5);_ddfee .Append (_cebe );_baaff =append (_baaff ,_ddfee );};if _edgbd !=""{_bgaeb :=_gcefe (_geaab ._afafg );_bgaeb .Append (_edgbd );_baaff =append (_baaff ,_bgaeb );};return _baaff ;};

// SetHeight sets the custom height for the row.
func (_egag *GridRow )SetHeight (h float64 ){_egag ._deege =h };

// GetHeading returns the chapter heading paragraph. Used to give access to address style: font, sizing etc.
func (_dbde *Chapter )GetHeading ()*StyledParagraph {return _dbde ._dgca };

// TOC returns the table of contents component of the creator.
func (_cbc *Creator )TOC ()*TOC {return _cbc ._dbaf };

// DrawHeader sets a function to draw a header on created output pages.
func (_bcdc *Creator )DrawHeader (drawHeaderFunc func (_dgfc *Block ,_agde HeaderFunctionArgs )){_bcdc ._cgeg =drawHeaderFunc ;};

// SetAddressStyle sets the style properties used to render the content of
// the invoice address sections.
func (_dadb *Invoice )SetAddressStyle (style TextStyle ){_dadb ._eefg =style };func (_efdec *StyledParagraph )appendChunk (_eafgc *TextChunk )*TextChunk {_efdec ._dgaca =append (_efdec ._dgaca ,_eafgc );_efdec .wrapText ();return _eafgc ;};

// SetPageMargins sets the page margins: left, right, top, bottom.
// The default page margins are 10% of document width.
func (_aeaae *Creator )SetPageMargins (left ,right ,top ,bottom float64 ){_aeaae ._gecf .Left =left ;_aeaae ._gecf .Right =right ;_aeaae ._gecf .Top =top ;_aeaae ._gecf .Bottom =bottom ;};

// InvoiceCell represents any cell belonging to a table from the invoice
// template. The main tables are the invoice information table, the line
// items table and totals table. Contains the text value of the cell and
// the style properties of the cell.
type InvoiceCell struct{InvoiceCellProps ;Value string ;};type listItem struct{_agafc VectorDrawable ;_daedg TextChunk ;};func (_eddae *Paragraph )getTextWidth ()float64 {_fbdc :=0.0;for _ ,_fedc :=range _eddae ._cegc {if _fedc =='\u000A'{continue ;};_bead ,_cebf :=_eddae ._aad .GetRuneMetrics (_fedc );
if !_cebf {_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0052u\u006e\u0065\u0020\u0063\u0068a\u0072\u0020\u006d\u0065\u0074\u0072\u0069\u0063\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0021\u0020\u0028\u0072\u0075\u006e\u0065\u0020\u0030\u0078\u0025\u0030\u0034\u0078\u003d\u0025\u0063\u0029",_fedc ,_fedc );
return -1;};_fbdc +=_eddae ._febee *_bead .Wx ;};return _fbdc ;};func (_bafgb *Path )compare (_efddf *Path )bool {if len (_bafgb .Subpaths )!=len (_efddf .Subpaths ){return false ;};for _gbgd ,_agec :=range _bafgb .Subpaths {if !_agec .compare (_efddf .Subpaths [_gbgd ]){return false ;
};};return true ;};

// This method is not supported by PageBreak component and exists solely to satisfy the Drawable interface.
func (_cdfgg *PageBreak )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };func (_fged *Command )compare (_cgfgb *Command )bool {if _fged .Symbol !=_cgfgb .Symbol {return false ;};for _abgg ,_bcab :=range _fged .Params {if _bcab !=_cgfgb .Params [_abgg ]{return false ;
};};return true ;};

// SetLogo sets the logo of the invoice.
func (_efdc *Invoice )SetLogo (logo *Image ){_efdc ._aeae =logo };func (_bcedg *templateProcessor )renderNode (_fgdce *templateNode )error {_agcee :=_fgdce ._aeceb ;if _agcee ==nil {return nil ;};_edbg :=_fgdce ._ccge .Name .Local ;_aaafc ,_efggc :=_cgacc [_edbg ];
if !_efggc {_bcedg .nodeLogDebug (_fgdce ,"I\u006e\u0076\u0061\u006c\u0069\u0064 \u0074\u0061\u0067\u0020\u003c\u0025\u0073\u003e\u002e \u0053\u006b\u0069p\u0070i\u006e\u0067\u002e",_edbg );return nil ;};var _becdb interface{};if _fgdce ._gafge !=nil &&_fgdce ._gafge ._aeceb !=nil {_cabeb :=_fgdce ._gafge ._ccge .Name .Local ;
if _ ,_efggc =_aaafc ._acadd [_cabeb ];!_efggc {_bcedg .nodeLogDebug (_fgdce ,"\u0054\u0061\u0067\u0020\u003c\u0025\u0073\u003e \u0069\u0073\u0020no\u0074\u0020\u0061\u0020\u0076\u0061l\u0069\u0064\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u003c\u0025\u0073\u003e\u0020\u0074a\u0067\u002e",_cabeb ,_edbg );
return _bbcfc ;};_becdb =_fgdce ._gafge ._aeceb ;}else {_deaf :="\u0063r\u0065\u0061\u0074\u006f\u0072";switch _bcedg ._cgdcf .(type ){case *Block :_deaf ="\u0062\u006c\u006fc\u006b";};if _ ,_efggc =_aaafc ._acadd [_deaf ];!_efggc {_bcedg .nodeLogDebug (_fgdce ,"\u0054\u0061\u0067\u0020\u003c\u0025\u0073\u003e \u0069\u0073\u0020no\u0074\u0020\u0061\u0020\u0076\u0061l\u0069\u0064\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u003c\u0025\u0073\u003e\u0020\u0074a\u0067\u002e",_deaf ,_edbg );
return _bbcfc ;};_becdb =_bcedg ._cgdcf ;};switch _ccfdc :=_becdb .(type ){case componentRenderer :_dfad ,_bgdde :=_agcee .(Drawable );if !_bgdde {_bcedg .nodeLogError (_fgdce ,"\u0054\u0061\u0067\u0020\u003c\u0025\u0073\u003e\u0020\u0028\u0025\u0054\u0029\u0020\u0069s\u0020n\u006f\u0074\u0020\u0061\u0020\u0064\u0072\u0061\u0077\u0061\u0062\u006c\u0065\u002e",_edbg ,_agcee );
return _gcaaa ;};_daba :=_ccfdc .Draw (_dfad );if _daba !=nil {return _bcedg .nodeError (_fgdce ,"\u0043\u0061\u006en\u006f\u0074\u0020\u0064r\u0061\u0077\u0073\u0020\u0074\u0061\u0067 \u003c\u0025\u0073\u003e\u0020\u0028\u0025\u0054\u0029\u003a\u0020\u0025\u0073\u002e",_edbg ,_agcee ,_daba );
};case *Division :switch _fdgad :=_agcee .(type ){case *Background :_ccfdc .SetBackground (_fdgad );case VectorDrawable :_edcfab :=_ccfdc .Add (_fdgad );if _edcfab !=nil {return _bcedg .nodeError (_fgdce ,"\u0043a\u006e\u006eo\u0074\u0020\u0061d\u0064\u0020\u0074\u0061\u0067\u0020\u003c%\u0073\u003e\u0020\u0028\u0025\u0054)\u0020\u0069\u006e\u0074\u006f\u0020\u0061\u0020\u0044\u0069\u0076i\u0073\u0069\u006f\u006e\u003a\u0020\u0025\u0073\u002e",_edbg ,_agcee ,_edcfab );
};};case *TableCell :_afgef ,_eaacg :=_agcee .(VectorDrawable );if !_eaacg {_bcedg .nodeLogError (_fgdce ,"\u0054\u0061\u0067\u0020\u003c\u0025\u0073\u003e\u0020\u0028\u0025\u0054\u0029 \u0069\u0073\u0020\u006e\u006f\u0074 \u0061\u0020\u0076\u0065\u0063\u0074\u006f\u0072\u0020\u0064\u0072\u0061\u0077a\u0062\u006c\u0065\u002e",_edbg ,_agcee );
return _gcaaa ;};_aaef :=_ccfdc .SetContent (_afgef );if _aaef !=nil {return _bcedg .nodeError (_fgdce ,"C\u0061\u006e\u006e\u006f\u0074\u0020\u0061\u0064\u0064 \u0074\u0061\u0067\u0020\u003c\u0025\u0073> \u0028\u0025\u0054\u0029 \u0069\u006e\u0074\u006f\u0020\u0061\u0020\u0074\u0061bl\u0065\u0020c\u0065\u006c\u006c\u003a\u0020\u0025\u0073\u002e",_edbg ,_agcee ,_aaef );
};case *StyledParagraph :_cfgbd ,_bdacf :=_agcee .(*TextChunk );if !_bdacf {_bcedg .nodeLogError (_fgdce ,"\u0054\u0061\u0067 <\u0025\u0073\u003e\u0020\u0028\u0025\u0054\u0029\u0020i\u0073 \u006eo\u0074 \u0061\u0020\u0074\u0065\u0078\u0074\u0020\u0063\u0068\u0075\u006e\u006b\u002e",_edbg ,_agcee );
return _gcaaa ;};_ccfdc .appendChunk (_cfgbd );case *Chapter :switch _ebdbe :=_agcee .(type ){case *Chapter :return nil ;case *StyledParagraph :if _fgdce ._ccge .Name .Local =="\u0063h\u0061p\u0074\u0065\u0072\u002d\u0068\u0065\u0061\u0064\u0069\u006e\u0067"{return nil ;
};_bfeg :=_ccfdc .Add (_ebdbe );if _bfeg !=nil {return _bcedg .nodeError (_fgdce ,"\u0043a\u006e\u006eo\u0074\u0020\u0061\u0064d\u0020\u0074\u0061g\u0020\u003c\u0025\u0073\u003e\u0020\u0028\u0025\u0054) \u0069\u006e\u0074o\u0020\u0061 \u0043\u0068\u0061\u0070\u0074\u0065r\u003a\u0020%\u0073\u002e",_edbg ,_agcee ,_bfeg );
};case Drawable :_ddcdc :=_ccfdc .Add (_ebdbe );if _ddcdc !=nil {return _bcedg .nodeError (_fgdce ,"\u0043a\u006e\u006eo\u0074\u0020\u0061\u0064d\u0020\u0074\u0061g\u0020\u003c\u0025\u0073\u003e\u0020\u0028\u0025\u0054) \u0069\u006e\u0074o\u0020\u0061 \u0043\u0068\u0061\u0070\u0074\u0065r\u003a\u0020%\u0073\u002e",_edbg ,_agcee ,_ddcdc );
};};case *List :switch _eedc :=_agcee .(type ){case *TextChunk :case *listItem :_ccfdc ._bbbe =append (_ccfdc ._bbbe ,_eedc );default:_bcedg .nodeLogError (_fgdce ,"\u0054\u0061\u0067\u0020\u003c\u0025\u0073>\u0020\u0028\u0025T\u0029\u0020\u0069\u0073 \u006e\u006f\u0074\u0020\u0061\u0020\u006c\u0069\u0073\u0074\u0020\u0069\u0074\u0065\u006d\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_edbg ,_agcee );
};case *listItem :switch _ffegg :=_agcee .(type ){case *TextChunk :case *StyledParagraph :_ccfdc ._agafc =_ffegg ;case *List :if _ffegg ._dgbd {_ffegg ._gagc =15;};_ccfdc ._agafc =_ffegg ;case *Image :_ccfdc ._agafc =_ffegg ;case *Division :_ccfdc ._agafc =_ffegg ;
case *Table :_ccfdc ._agafc =_ffegg ;default:_bcedg .nodeLogError (_fgdce ,"\u0054\u0061\u0067\u0020\u003c%\u0073\u003e\u0020\u0028\u0025\u0054\u0029\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u006c\u0069\u0073\u0074\u002e",_edbg ,_agcee );
return _gcaaa ;};};return nil ;};

// GetMargins returns the margins of the rectangle: left, right, top, bottom.
func (_bacbf *Rectangle )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _bacbf ._abeec .Left ,_bacbf ._abeec .Right ,_bacbf ._abeec .Top ,_bacbf ._abeec .Bottom ;};

// GeneratePageBlocks draws the polyline on a new block representing the page.
// Implements the Drawable interface.
func (_defdc *Polyline )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_cdcec :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_dcaag ,_aaca :=_cdcec .setOpacity (_defdc ._aade ,_defdc ._aade );if _aaca !=nil {return nil ,ctx ,_aaca ;
};_fgdf :=_defdc ._dabba .Points ;for _efac :=range _fgdf {_cacef :=&_fgdf [_efac ];_cacef .Y =ctx .PageHeight -_cacef .Y ;};_bddc ,_ ,_aaca :=_defdc ._dabba .MarkedDraw (_dcaag ,_defdc ._bafaf );if _aaca !=nil {return nil ,ctx ,_aaca ;};if _aaca =_cdcec .addContentsByString (string (_bddc ));
_aaca !=nil {return nil ,ctx ,_aaca ;};return []*Block {_cdcec },ctx ,nil ;};

// NewInvoice returns an instance of an empty invoice.
func (_ffed *Creator )NewInvoice ()*Invoice {_cafc :=_ffed .NewTextStyle ();_cafc .Font =_ffed ._edeg ;return _ffgg (_ffed .NewTextStyle (),_cafc );};var (_gdf =[]string {"\u0063\u006d","\u006d\u006d","\u0070\u0078","\u0070\u0074"};_eca =map[string ]float64 {"\u0063\u006d":_fee ,"\u006d\u006d":_agdc ,"\u0070\u0078":_gce ,"\u0070\u0074":1};
);

// SetPos sets the position of the graphic svg to the specified coordinates.
// This method sets the graphic svg to use absolute positioning.
func (_bbcf *GraphicSVG )SetPos (x ,y float64 ){_bbcf ._cgd =PositionAbsolute ;_bbcf ._edbc =x ;_bbcf ._cbdfc =y ;};func (_ebfcd *Line )computeCoords (_eacbf DrawContext )(_edcdd ,_agfgc ,_fcgaf ,_ebebd float64 ){_edcdd =_eacbf .X ;_fcgaf =_edcdd +_ebfcd ._cacg -_ebfcd ._ggdbc ;
_bfaf :=_ebfcd ._gbgbb ;if _ebfcd ._ggdbc ==_ebfcd ._cacg {_bfaf /=2;};if _ebfcd ._geega < _ebfcd ._degba {_agfgc =_eacbf .PageHeight -_eacbf .Y -_bfaf ;_ebebd =_agfgc -_ebfcd ._degba +_ebfcd ._geega ;}else {_ebebd =_eacbf .PageHeight -_eacbf .Y -_bfaf ;
_agfgc =_ebebd -_ebfcd ._geega +_ebfcd ._degba ;};switch _ebfcd ._gagab {case FitModeFillWidth :_fcgaf =_edcdd +_eacbf .Width ;};return _edcdd ,_agfgc ,_fcgaf ,_ebebd ;};func (_gbg *Division )ctxHeight (_faeg float64 )float64 {_faeg -=_gbg ._fccaf .Left +_gbg ._fccaf .Right +_gbg ._ddcg .Left +_gbg ._ddcg .Right ;
var _gccc float64 ;for _ ,_efec :=range _gbg ._cadaa {_gccc +=_abcfe (_efec ,_faeg );};return _gccc ;};

// Scale block by specified factors in the x and y directions.
func (_gge *Block )Scale (sx ,sy float64 ){_eba :=_eg .NewContentCreator ().Scale (sx ,sy ).Operations ();*_gge ._fd =append (*_eba ,*_gge ._fd ...);_gge ._fd .WrapIfNeeded ();_gge ._fedd *=sx ;_gge ._dbe *=sy ;};func (_deefc *templateProcessor )parseImage (_dbgf *templateNode )(interface{},error ){var _ddacfg string ;
for _ ,_bdde :=range _dbgf ._ccge .Attr {_fbgcg :=_bdde .Value ;switch _fdcgf :=_bdde .Name .Local ;_fdcgf {case "\u0073\u0072\u0063":_ddacfg =_fbgcg ;};};_bbcg ,_acbfd :=_deefc .loadImageFromSrc (_ddacfg );if _acbfd !=nil {return nil ,_acbfd ;};var _adbca _dd .StreamEncoder ;
_efdfaf :=_fc .ToLower (_fb .Ext (_ddacfg ));if _efdfaf =="\u006a\u0070\u0067"||_efdfaf =="\u006a\u0070\u0065\u0067"{_adbca =_dd .NewDCTEncoder ();}else {_adbca =_dd .NewFlateEncoder ();};_bbcg .SetEncoder (_adbca );for _ ,_gecfaf :=range _dbgf ._ccge .Attr {_ebda :=_gecfaf .Value ;
switch _dfdfd :=_gecfaf .Name .Local ;_dfdfd {case "\u0061\u006c\u0069g\u006e":_bbcg .SetHorizontalAlignment (_deefc .parseHorizontalAlignmentAttr (_dfdfd ,_ebda ));case "\u006fp\u0061\u0063\u0069\u0074\u0079":_bbcg .SetOpacity (_deefc .parseFloatAttr (_dfdfd ,_ebda ));
case "\u006d\u0061\u0072\u0067\u0069\u006e":_cggc :=_deefc .parseMarginAttr (_dfdfd ,_ebda );_bbcg .SetMargins (_cggc .Left ,_cggc .Right ,_cggc .Top ,_cggc .Bottom );case "\u0066\u0069\u0074\u002d\u006d\u006f\u0064\u0065":_bbcg .SetFitMode (_deefc .parseFitModeAttr (_dfdfd ,_ebda ));
case "\u0078":_bbcg .SetPos (_deefc .parseFloatAttr (_dfdfd ,_ebda ),_bbcg ._dff );case "\u0079":_bbcg .SetPos (_bbcg ._fabc ,_deefc .parseFloatAttr (_dfdfd ,_ebda ));case "\u0077\u0069\u0064t\u0068":_bbcg .SetWidth (_deefc .parseFloatAttr (_dfdfd ,_ebda ));
case "\u0068\u0065\u0069\u0067\u0068\u0074":_bbcg .SetHeight (_deefc .parseFloatAttr (_dfdfd ,_ebda ));case "\u0061\u006e\u0067l\u0065":_bbcg .SetAngle (_deefc .parseFloatAttr (_dfdfd ,_ebda ));case "\u0065n\u0063\u006f\u0064\u0065\u0072":_adbca =_deefc .parseImageEncoder (_dfdfd ,_ebda );
if _adbca !=nil {_bbcg .SetEncoder (_adbca );};case "\u0073\u0072\u0063":break ;default:_deefc .nodeLogDebug (_dbgf ,"\u0055n\u0073\u0075p\u0070\u006f\u0072\u0074e\u0064\u0020\u0069m\u0061\u0067\u0065\u0020\u0061\u0074\u0074\u0072\u0069bu\u0074\u0065\u003a \u0060\u0025s\u0060\u002e\u0020\u0053\u006b\u0069p\u0070\u0069n\u0067\u002e",_dfdfd );
};};return _bbcg ,nil ;};

// Margins represents page margins or margins around an element.
type Margins struct{Left float64 ;Right float64 ;Top float64 ;Bottom float64 ;};

// SetHorizontalAlignment sets the horizontal alignment of the image.
func (_gedf *Image )SetHorizontalAlignment (alignment HorizontalAlignment ){_gedf ._aagg =alignment };

// Positioning represents the positioning type for drawing creator components (relative/absolute).
type Positioning int ;

// SetAnnotation sets an annotation on a TextChunk,
// this will replace any existing annotation that has been set.
// Supplying a nil value as parameter would clear all the annotations.
func (_efafab *TextChunk )SetAnnotation (annotation *_db .PdfAnnotation ){if annotation ==nil {_efafab .ClearAnnotations ();}else {_efafab ._aecg =[]*_db .PdfAnnotation {annotation };};};

// SetStructureType sets the structure type for the chart.
func (_cdc *Chart )SetStructureType (structureType _db .StructureType ){if _cdc ._aaf ==nil {_cdc ._aaf =_db .NewStructureTagInfo ();};_cdc ._aaf .StructureType =structureType ;};func _gcba (_efaaa *Creator ,_fead _cc .Reader ,_dgddd interface{},_bdcdc *TemplateOptions ,_agdea componentRenderer )error {if _efaaa ==nil {_a .Log .Error ("\u0043\u0072\u0065a\u0074\u006f\u0072\u0020i\u006e\u0073\u0074\u0061\u006e\u0063\u0065 \u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c\u002e");
return _adbad ;};_eedg :="";if _dbeea ,_aagge :=_fead .(*_eb .File );_aagge {_eedg =_dbeea .Name ();};_cbbfd :=_c .NewBuffer (nil );if _ ,_cffa :=_cc .Copy (_cbbfd ,_fead );_cffa !=nil {return _cffa ;};_gabg :=_b .FuncMap {"\u0064\u0069\u0063\u0074":_efcbf ,"\u0061\u0064\u0064":_ecfb ,"\u0061\u0072\u0072a\u0079":_caaad ,"\u0065\u0078\u0074\u0065\u006e\u0064\u0044\u0069\u0063\u0074":_ccdc ,"\u006da\u006b\u0065\u0053\u0065\u0071":_aadc };
if _bdcdc !=nil &&_bdcdc .HelperFuncMap !=nil {for _bbcb ,_cgaad :=range _bdcdc .HelperFuncMap {if _ ,_edebb :=_gabg [_bbcb ];_edebb {_a .Log .Debug ("\u0043\u0061\u006e\u006e\u006f\u0074 \u006f\u0076\u0065r\u0072\u0069\u0064e\u0020\u0062\u0075\u0069\u006c\u0074\u002d\u0069\u006e\u0020`\u0025\u0073\u0060\u0020\u0068el\u0070\u0065\u0072\u0020\u0066\u0075\u006e\u0063\u0074\u0069\u006f\u006e\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_bbcb );
continue ;};_gabg [_bbcb ]=_cgaad ;};};_edbaf ,_gbead :=_b .New ("").Funcs (_gabg ).Parse (_cbbfd .String ());if _gbead !=nil {return _gbead ;};if _bdcdc !=nil &&_bdcdc .SubtemplateMap !=nil {for _ffcd ,_egged :=range _bdcdc .SubtemplateMap {if _ffcd ==""{_a .Log .Debug ("\u0053\u0075\u0062\u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u0063\u0061\u006en\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006d\u0070\u0074\u0079\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067.\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065 \u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e");
continue ;};if _egged ==nil {_a .Log .Debug ("S\u0075\u0062t\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0063\u0061\u006e\u006eo\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069n\u0067\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079 \u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e");
continue ;};_dfdg :=_c .NewBuffer (nil );if _ ,_edfee :=_cc .Copy (_dfdg ,_egged );_edfee !=nil {return _edfee ;};if _ ,_cbfca :=_edbaf .New (_ffcd ).Parse (_dfdg .String ());_cbfca !=nil {return _cbfca ;};};};_cbbfd .Reset ();if _fecdb :=_edbaf .Execute (_cbbfd ,_dgddd );
_fecdb !=nil {return _fecdb ;};return _baefa (_efaaa ,_eedg ,_cbbfd .Bytes (),_bdcdc ,_agdea ).run ();};func (_fdfag *templateProcessor )parseHorizontalAlignmentAttr (_bdbga ,_aaag string )HorizontalAlignment {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069n\u0067\u0020\u0068\u006f\u0072\u0069\u007a\u006f\u006e\u0074\u0061\u006c\u0020a\u006c\u0069\u0067\u006e\u006d\u0065\u006e\u0074\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029.",_bdbga ,_aaag );
_fgcd :=map[string ]HorizontalAlignment {"\u006c\u0065\u0066\u0074":HorizontalAlignmentLeft ,"\u0063\u0065\u006e\u0074\u0065\u0072":HorizontalAlignmentCenter ,"\u0072\u0069\u0067h\u0074":HorizontalAlignmentRight }[_aaag ];return _fgcd ;};

// GenerateKDict generates a K dictionary for the table.
func (_aabb *Table )GenerateKDict ()(*_db .KDict ,error ){if _aabb ._fefdd ==nil {return nil ,_g .Errorf ("t\u0061\u0062\u006c\u0065\u0020\u0073t\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0074\u0061\u0067\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _aabb ._fefdd .GenerateKDict (),nil ;};

// SetStyleBottom sets border style for bottom side.
func (_eade *border )SetStyleBottom (style CellBorderStyle ){_eade ._cgbc =style };func (_eadd *Invoice )generateHeaderBlocks (_afca DrawContext )([]*Block ,DrawContext ,error ){_befe :=_gcefe (_eadd ._gecfa );_befe .SetEnableWrap (true );_befe .Append (_eadd ._gfegb );
_faged :=_cgdd (2);if _eadd ._aeae !=nil {_decfa :=_faged .NewCell ();_decfa .SetHorizontalAlignment (CellHorizontalAlignmentLeft );_decfa .SetVerticalAlignment (CellVerticalAlignmentMiddle );_decfa .SetIndent (0);_decfa .SetContent (_eadd ._aeae );_eadd ._aeae .ScaleToHeight (_befe .Height ()+20);
}else {_faged .SkipCells (1);};_gbgf :=_faged .NewCell ();_gbgf .SetHorizontalAlignment (CellHorizontalAlignmentRight );_gbgf .SetVerticalAlignment (CellVerticalAlignmentMiddle );_gbgf .SetContent (_befe );return _faged .GeneratePageBlocks (_afca );};

// SetVerticalAlignment set the cell's vertical alignment of content.
// Can be one of:
// - CellHorizontalAlignmentTop
// - CellHorizontalAlignmentMiddle
// - CellHorizontalAlignmentBottom
func (_fadagf *TableCell )SetVerticalAlignment (valign CellVerticalAlignment ){_fadagf ._adfcfg =valign ;};

// BorderOpacity returns the border opacity of the rectangle (0-1).
func (_beeda *Rectangle )BorderOpacity ()float64 {return _beeda ._adaf };

// SetBorderOpacity sets the border opacity of the rectangle.
func (_gcdb *Rectangle )SetBorderOpacity (opacity float64 ){_gcdb ._adaf =opacity };

// CurRow returns the currently active cell's row number.
func (_adgb *Table )CurRow ()int {_bcaga :=(_adgb ._gfcb -1)/_adgb ._fcegg +1;return _bcaga };

// SetLineOpacity sets the line opacity.
func (_gbec *Polyline )SetLineOpacity (opacity float64 ){_gbec ._aade =opacity };func _feaag (_adbgb string ,_bdge TextStyle )*Paragraph {_cgag :=&Paragraph {_cegc :_adbgb ,_aad :_bdge .Font ,_febee :_bdge .FontSize ,_gafdf :1.0,_gcdc :true ,_gcfc :true ,_aegda :TextAlignmentLeft ,_cagf :0,_cgbge :1,_cgdc :1,_aecc :PositionRelative ,_fedb :""};
_cgag .SetColor (_bdge .Color );return _cgag ;};

// Draw processes the specified Drawable widget and generates blocks that can
// be rendered to the output document. The generated blocks can span over one
// or more pages. Additional pages are added if the contents go over the current
// page. Each generated block is assigned to the creator page it will be
// rendered to. In order to render the generated blocks to the creator pages,
// call Finalize, Write or WriteToFile.
func (_ccb *Creator )Draw (d Drawable )error {if _ccb .getActivePage ()==nil {_ccb .NewPage ();};_gff ,_abed ,_facb :=d .GeneratePageBlocks (_ccb ._fcga );if _facb !=nil {return _facb ;};if len (_abed ._aeag )> 0{_ccb .Errors =append (_ccb .Errors ,_abed ._aeag ...);
};for _bdba ,_edfd :=range _gff {if _bdba > 0{_ccb .NewPage ();};_aege :=_ccb .getActivePage ();if _bcga ,_acabc :=_ccb ._gade [_aege ];_acabc {if _ceaa :=_bcga .mergeBlocks (_edfd );_ceaa !=nil {return _ceaa ;};if _dfca :=_eacd (_edfd ._faa ,_bcga ._faa );
_dfca !=nil {return _dfca ;};}else {_ccb ._gade [_aege ]=_edfd ;};};_ccb ._fcga .X =_abed .X ;_ccb ._fcga .Y =_abed .Y ;_ccb ._fcga .Height =_cg .RoundDefault (_abed .PageHeight -_abed .Y -_abed .Margins .Bottom );return nil ;};

// SetStyleTop sets border style for top side.
func (_agc *border )SetStyleTop (style CellBorderStyle ){_agc ._fdd =style };

// SetStyle sets the style of the line (solid or dashed).
func (_faabg *Line )SetStyle (style _gc .LineStyle ){_faabg ._efabg =style };func _gege (_cgad ,_eegca ,_eceb ,_cegbb ,_cfcb ,_fbde float64 )*Curve {_ecbd :=&Curve {};_ecbd ._cfcbc =_cgad ;_ecbd ._fafc =_eegca ;_ecbd ._gebb =_eceb ;_ecbd ._feaa =_cegbb ;
_ecbd ._cbdb =_cfcb ;_ecbd ._ddad =_fbde ;_ecbd ._gaab =ColorBlack ;_ecbd ._aged =1.0;return _ecbd ;};func _eafda (_gbbde _e .StartElement )*GraphicSVGElement {_eeed :=&GraphicSVGElement {};_begc :=make (map[string ]string );for _ ,_bebb :=range _gbbde .Attr {_begc [_bebb .Name .Local ]=_bebb .Value ;
};_eeed .Name =_gbbde .Name .Local ;_eeed .Attributes =_begc ;_eeed ._aece =1;if _eeed .Name =="\u0073\u0076\u0067"{_dbbad ,_fcfc :=_bdgcc (_begc ["\u0076i\u0065\u0077\u0042\u006f\u0078"]);if _fcfc !=nil {_a .Log .Debug ("\u0055\u006ea\u0062\u006c\u0065\u0020t\u006f\u0020p\u0061\u0072\u0073\u0065\u0020\u0076\u0069\u0065w\u0042\u006f\u0078\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074e\u003a\u0020\u0025\u0076",_fcfc );
return nil ;};if len (_dbbad )>=4{_eeed .ViewBox .X =_dbbad [0];_eeed .ViewBox .Y =_dbbad [1];_eeed .ViewBox .W =_dbbad [2];_eeed .ViewBox .H =_dbbad [3];};_eeed .Width =_eeed .ViewBox .W ;_eeed .Height =_eeed .ViewBox .H ;if _aafag ,_ebae :=_begc ["\u0077\u0069\u0064t\u0068"];
_ebae {if _fc .HasSuffix (_aafag ,"\u0025"){_bgfc ,_cefd :=_fbf .ParseFloat (_fc .TrimSuffix (_aafag ,"\u0025"),64);if _cefd !=nil {_a .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0077\u0069\u0064\u0074\u0068\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_cefd );
return nil ;};_eeed .Width =_bgfc *_eeed .ViewBox .W ;}else {_afaf ,_dfce :=_cacac (_aafag ,64);if _dfce !=nil {_a .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0077\u0069\u0064\u0074\u0068\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_dfce );
return nil ;};_eeed .Width =_afaf ;if len (_dbbad )< 4{_eeed .ViewBox .W =_afaf ;};};};if _cdaf ,_ebggd :=_begc ["\u0068\u0065\u0069\u0067\u0068\u0074"];_ebggd {if _fc .HasSuffix (_cdaf ,"\u0025"){_gdaf ,_dccee :=_fbf .ParseFloat (_fc .TrimSuffix (_cdaf ,"\u0025"),64);
if _dccee !=nil {_a .Log .Debug ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0070\u0061rse\u0020he\u0069\u0067\u0068\u0074\u0020\u0061\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_dccee );return nil ;};_eeed .Height =_gdaf *_eeed .ViewBox .H ;
}else {_fece ,_cggd :=_cacac (_cdaf ,64);if _cggd !=nil {_a .Log .Debug ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0070\u0061rse\u0020he\u0069\u0067\u0068\u0074\u0020\u0061\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_cggd );return nil ;
};_eeed .Height =_fece ;if len (_dbbad )< 4{_eeed .ViewBox .H =_fece ;};};};if _eeed .Width > 0&&_eeed .Height > 0{_eeed ._aece =_eeed .Width /_eeed .ViewBox .W ;};};return _eeed ;};

// PolyBezierCurve represents a composite curve that is the result of joining
// multiple cubic Bezier curves.
// Implements the Drawable interface and can be drawn on PDF using the Creator.
type PolyBezierCurve struct{_gefa *_gc .PolyBezierCurve ;_dfdf float64 ;_efcf float64 ;_eagb Color ;_ceefc *_db .StructureTagInfo ;};func (_eefgf *templateProcessor )processGradientColorPair (_egcf []string )(_ebged []Color ,_agdbg []float64 ){for _ ,_agdbe :=range _egcf {var (_gbdae =_fc .Fields (_agdbe );
_gcged =len (_gbdae ););if _gcged ==0{continue ;};_bdbfg :="";if _gcged > 1{_bdbfg =_fc .TrimSpace (_gbdae [1]);};_ebeee :=-1.0;if _fc .HasSuffix (_bdbfg ,"\u0025"){_adfgda ,_cgdf :=_fbf .ParseFloat (_bdbfg [:len (_bdbfg )-1],64);if _cgdf !=nil {_a .Log .Debug ("\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0070\u0061\u0072s\u0069\u006e\u0067\u0020\u0070\u006f\u0069n\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cgdf );
};_ebeee =_adfgda /100.0;};_gfgbc :=_eefgf .parseColor (_fc .TrimSpace (_gbdae [0]));if _gfgbc !=nil {_ebged =append (_ebged ,_gfgbc );_agdbg =append (_agdbg ,_ebeee );};};if len (_ebged )!=len (_agdbg ){_a .Log .Debug ("\u0049\u006e\u0076\u0061\u006ci\u0064\u0020\u006c\u0069\u006e\u0065\u0061\u0072\u0020\u0067\u0072\u0061\u0064i\u0065\u006e\u0074\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0064\u0065\u0066\u0069\u006e\u0069\u0074\u0069\u006f\u006e\u0021");
return nil ,nil ;};_cfdf :=-1;_fbccb :=0.0;for _egfb ,_bbfffg :=range _agdbg {if _bbfffg ==-1.0{if _egfb ==0{_bbfffg =0.0;_agdbg [_egfb ]=0.0;continue ;};_cfdf ++;if _egfb < len (_agdbg )-1{continue ;}else {_bbfffg =1.0;_agdbg [_egfb ]=1.0;};};_fgcbb :=_cfdf +1;
for _dbgd :=_egfb -_cfdf ;_dbgd < _egfb ;_dbgd ++{_agdbg [_dbgd ]=_fbccb +(float64 (_dbgd )*(_bbfffg -_fbccb )/float64 (_fgcbb ));};_fbccb =_bbfffg ;_cfdf =-1;};return _ebged ,_agdbg ;};func _efga (_eeada [][]_gc .Point )*Polygon {return &Polygon {_addde :&_gc .Polygon {Points :_eeada },_eddfc :1.0,_bbdb :1.0};
};func (_edbcg *commands )isCommand (_dccdf string )bool {for _ ,_cdcf :=range _edbcg ._egbaf {if _fc .ToLower (_dccdf )==_cdcf {return true ;};};return false ;};

// SetMargins sets the margins of the ellipse.
// NOTE: ellipse margins are only applied if relative positioning is used.
func (_ffad *Ellipse )SetMargins (left ,right ,top ,bottom float64 ){_ffad ._dafc .Left =left ;_ffad ._dafc .Right =right ;_ffad ._dafc .Top =top ;_ffad ._dafc .Bottom =bottom ;};

// Scale scales the rectangle dimensions by the specified factors.
func (_eecf *Rectangle )Scale (xFactor ,yFactor float64 ){_eecf ._cdcgg =xFactor *_eecf ._cdcgg ;_eecf ._cfedb =yFactor *_eecf ._cfedb ;};

// SetMargins sets the margins of the chart component.
func (_fbfa *Chart )SetMargins (left ,right ,top ,bottom float64 ){_fbfa ._ace .Left =left ;_fbfa ._ace .Right =right ;_fbfa ._ace .Top =top ;_fbfa ._ace .Bottom =bottom ;};func (_ebaff *Grid )updateRowHeights (_cdcd float64 ){for _ ,_cab :=range _ebaff ._bgcb {_cab .updateRowHeight (_cdcd );
};};func (_aedff *TableCell )cloneProps (_fgaga VectorDrawable )*TableCell {_cacc :=*_aedff ;_cacc ._fbaee =_fgaga ;return &_cacc ;};func (_gdaffb *templateProcessor )parseLine (_dffef *templateNode )(interface{},error ){_cbbfdf :=_gdaffb .creator .NewLine (0,0,0,0);
for _ ,_cacf :=range _dffef ._ccge .Attr {_aeabda :=_cacf .Value ;switch _dgfaa :=_cacf .Name .Local ;_dgfaa {case "\u0078\u0031":_cbbfdf ._ggdbc =_gdaffb .parseFloatAttr (_dgfaa ,_aeabda );case "\u0079\u0031":_cbbfdf ._geega =_gdaffb .parseFloatAttr (_dgfaa ,_aeabda );
case "\u0078\u0032":_cbbfdf ._cacg =_gdaffb .parseFloatAttr (_dgfaa ,_aeabda );case "\u0079\u0032":_cbbfdf ._degba =_gdaffb .parseFloatAttr (_dgfaa ,_aeabda );case "\u0074h\u0069\u0063\u006b\u006e\u0065\u0073s":_cbbfdf .SetLineWidth (_gdaffb .parseFloatAttr (_dgfaa ,_aeabda ));
case "\u0063\u006f\u006co\u0072":_cbbfdf .SetColor (_gdaffb .parseColorAttr (_dgfaa ,_aeabda ));case "\u0073\u0074\u0079l\u0065":_cbbfdf .SetStyle (_gdaffb .parseLineStyleAttr (_dgfaa ,_aeabda ));case "\u0064\u0061\u0073\u0068\u002d\u0061\u0072\u0072\u0061\u0079":_cbbfdf .SetDashPattern (_gdaffb .parseInt64Array (_dgfaa ,_aeabda ),_cbbfdf ._fafgc );
case "\u0064\u0061\u0073\u0068\u002d\u0070\u0068\u0061\u0073\u0065":_cbbfdf .SetDashPattern (_cbbfdf ._feda ,_gdaffb .parseInt64Attr (_dgfaa ,_aeabda ));case "\u006fp\u0061\u0063\u0069\u0074\u0079":_cbbfdf .SetOpacity (_gdaffb .parseFloatAttr (_dgfaa ,_aeabda ));
case "\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e":_cbbfdf .SetPositioning (_gdaffb .parsePositioningAttr (_dgfaa ,_aeabda ));case "\u0066\u0069\u0074\u002d\u006d\u006f\u0064\u0065":_cbbfdf .SetFitMode (_gdaffb .parseFitModeAttr (_dgfaa ,_aeabda ));
case "\u006d\u0061\u0072\u0067\u0069\u006e":_fggab :=_gdaffb .parseMarginAttr (_dgfaa ,_aeabda );_cbbfdf .SetMargins (_fggab .Left ,_fggab .Right ,_fggab .Top ,_fggab .Bottom );default:_gdaffb .nodeLogDebug (_dffef ,"\u0055\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u006c\u0069\u006e\u0065 \u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_dgfaa );
};};return _cbbfdf ,nil ;};func (_dbef *Table )getLastCellFromCol (_dagg int )(int ,*TableCell ){for _bbeac :=len (_dbef ._ccgf )-1;_bbeac >=0;_bbeac --{if _dbef ._ccgf [_bbeac ]._afdac <=_dagg &&_dagg <=_dbef ._ccgf [_bbeac ]._afdac +_dbef ._ccgf [_bbeac ]._edece {return _bbeac ,_dbef ._ccgf [_bbeac ];
};};return 0,nil ;};func (_dafb *Image )applyFitMode (_eegd float64 ){_eegd -=_dafb ._efde .Left +_dafb ._efde .Right ;switch _dafb ._dddb {case FitModeFillWidth :_dafb .ScaleToWidth (_eegd );};};func (_dgbgf *TextStyle )horizontalScale ()float64 {return _dgbgf .HorizontalScaling /100};


// Height returns the height of the chart.
func (_ffdb *Chart )Height ()float64 {return float64 (_ffdb ._cegb .Height ())};

// SetIndent sets the cell's left indent.
func (_bggc *GridCell )SetIndent (indent float64 ){_bggc ._ddca =indent };

// Width returns the width of the chart. In relative positioning mode,
// all the available context width is used at render time.
func (_aacb *Chart )Width ()float64 {return float64 (_aacb ._cegb .Width ())};

// EnableRowWrap controls whether rows are wrapped across pages.
// NOTE: Currently, row wrapping is supported for rows using StyledParagraphs.
func (_ebade *Table )EnableRowWrap (enable bool ){_ebade ._dffga =enable };func _cggab (_becda *templateProcessor ,_fgece *templateNode )(interface{},error ){return _becda .parseList (_fgece );};

// Number returns the invoice number description and value cells.
// The returned values can be used to customize the styles of the cells.
func (_agea *Invoice )Number ()(*InvoiceCell ,*InvoiceCell ){return _agea ._eddg [0],_agea ._eddg [1]};

// MultiColCell makes a new cell with the specified column span and inserts it
// into the table at the current position.
func (_effcc *Table )MultiColCell (colspan int )*TableCell {return _effcc .MultiCell (1,colspan )};

// Height returns the total height of all rows.
func (_gffca *Table )Height ()float64 {_cagb :=float64 (0.0);for _ ,_fadfa :=range _gffca ._gdbgf {_cagb +=_fadfa ;};return _cagb ;};

// MultiRowCell makes a new cell with the specified row span and inserts it
// into the table at the current position.
func (_dgcbg *Table )MultiRowCell (rowspan int )*TableCell {return _dgcbg .MultiCell (rowspan ,1)};func (_geee *templateProcessor )parseBackground (_ddbgd *templateNode )(interface{},error ){_abbeb :=&Background {};for _ ,_gedae :=range _ddbgd ._ccge .Attr {_gafdg :=_gedae .Value ;
switch _fdceb :=_gedae .Name .Local ;_fdceb {case "\u0066\u0069\u006c\u006c\u002d\u0063\u006f\u006c\u006f\u0072":_abbeb .FillColor =_geee .parseColorAttr (_fdceb ,_gafdg );case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0063\u006f\u006c\u006f\u0072":_abbeb .BorderColor =_geee .parseColorAttr (_fdceb ,_gafdg );
case "b\u006f\u0072\u0064\u0065\u0072\u002d\u0073\u0069\u007a\u0065":_abbeb .BorderSize =_geee .parseFloatAttr (_fdceb ,_gafdg );case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0072\u0061\u0064\u0069\u0075\u0073":_efdab ,_ddbad ,_gddcc ,_eeee :=_geee .parseBorderRadiusAttr (_fdceb ,_gafdg );
_abbeb .SetBorderRadius (_efdab ,_ddbad ,_eeee ,_gddcc );case "\u0062\u006f\u0072\u0064er\u002d\u0074\u006f\u0070\u002d\u006c\u0065\u0066\u0074\u002d\u0072\u0061\u0064\u0069u\u0073":_abbeb .BorderRadiusTopLeft =_geee .parseFloatAttr (_fdceb ,_gafdg );case "\u0062\u006f\u0072de\u0072\u002d\u0074\u006f\u0070\u002d\u0072\u0069\u0067\u0068\u0074\u002d\u0072\u0061\u0064\u0069\u0075\u0073":_abbeb .BorderRadiusTopRight =_geee .parseFloatAttr (_fdceb ,_gafdg );
case "\u0062o\u0072\u0064\u0065\u0072-\u0062\u006f\u0074\u0074\u006fm\u002dl\u0065f\u0074\u002d\u0072\u0061\u0064\u0069\u0075s":_abbeb .BorderRadiusBottomLeft =_geee .parseFloatAttr (_fdceb ,_gafdg );case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0062\u006f\u0074\u0074o\u006d\u002d\u0072\u0069\u0067\u0068\u0074\u002d\u0072\u0061d\u0069\u0075\u0073":_abbeb .BorderRadiusBottomRight =_geee .parseFloatAttr (_fdceb ,_gafdg );
default:_geee .nodeLogDebug (_ddbgd ,"\u0055\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0062\u0061\u0063\u006b\u0067\u0072\u006f\u0075\u006e\u0064\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006bi\u0070p\u0069\u006e\u0067\u002e",_fdceb );
};};return _abbeb ,nil ;};func (_dcbgc *Subpath )compare (_afefd *Subpath )bool {if len (_dcbgc .Commands )!=len (_afefd .Commands ){return false ;};for _fcea ,_afcab :=range _dcbgc .Commands {if !_afcab .compare (_afefd .Commands [_fcea ]){return false ;
};};return true ;};

// SetTOC sets the table of content component of the creator.
// This method should be used when building a custom table of contents.
func (_gfgd *Creator )SetTOC (toc *TOC ){if toc ==nil {return ;};_gfgd ._dbaf =toc ;};

// SetTextVerticalAlignment sets the vertical alignment of the text within the
// bounds of the styled paragraph.
//
// Note: Currently Styled Paragraph doesn't support TextVerticalAlignmentBottom
// as that option only used for aligning text chunks.
//
// In order to change the vertical alignment of individual text chunks, use TextChunk.VerticalAlignment.
func (_deccb *StyledParagraph )SetTextVerticalAlignment (align TextVerticalAlignment ){_deccb ._bded =align ;};

// Insert adds a new text chunk at the specified position in the paragraph.
func (_acfgf *StyledParagraph )Insert (index uint ,text string )*TextChunk {_cegcg :=uint (len (_acfgf ._dgaca ));if index > _cegcg {index =_cegcg ;};_cffd :=NewTextChunk (text ,_acfgf ._acfg );_acfgf ._dgaca =append (_acfgf ._dgaca [:index ],append ([]*TextChunk {_cffd },_acfgf ._dgaca [index :]...)...);
_acfgf .wrapText ();return _cffd ;};func _bdgcc (_gged string )([]float64 ,error ){_gfcf :=-1;var _fdab []float64 ;_edcdda :=' ';for _ffbbec ,_abbggd :=range _gged {if !_ec .IsNumber (_abbggd )&&_abbggd !='.'&&!(_abbggd =='-'&&_edcdda =='e')&&_abbggd !='e'{if _gfcf !=-1{_beeg ,_fddbg :=_eedac (_gged [_gfcf :_ffbbec ]);
if _fddbg !=nil {return _fdab ,_fddbg ;};_fdab =append (_fdab ,_beeg ...);};if _abbggd =='-'{_gfcf =_ffbbec ;}else {_gfcf =-1;};}else if _gfcf ==-1{_gfcf =_ffbbec ;};_edcdda =_abbggd ;};if _gfcf !=-1&&_gfcf !=len (_gged ){_bcfe ,_baafe :=_eedac (_gged [_gfcf :]);
if _baafe !=nil {return _fdab ,_baafe ;};_fdab =append (_fdab ,_bcfe ...);};return _fdab ,nil ;};

// SetInline sets the inline mode of the division.
func (_bede *Division )SetInline (inline bool ){_bede ._ffeb =inline };

// ToRGB implements interface Color.
// Note: It's not directly used since shading color works differently than regular color.
func (_ecae *RadialShading )ToRGB ()(float64 ,float64 ,float64 ){return 0,0,0};func _bgdaf (_bddcaf *_e .Decoder )(int ,int ){return _bddcaf .InputPos ()};

// InsertColumn inserts a column in the line items table at the specified index.
func (_bbbdc *Invoice )InsertColumn (index uint ,description string )*InvoiceCell {_ecbgb :=uint (len (_bbbdc ._egfdb ));if index > _ecbgb {index =_ecbgb ;};_gfdcf :=_bbbdc .NewColumn (description );_bbbdc ._egfdb =append (_bbbdc ._egfdb [:index ],append ([]*InvoiceCell {_gfdcf },_bbbdc ._egfdb [index :]...)...);
return _gfdcf ;};

// Add adds a VectorDrawable to the Division container.
// Currently supported VectorDrawables:
// - *Paragraph
// - *StyledParagraph
// - *Image
// - *Chart
// - *Rectangle
// - *Ellipse
// - *Line
// - *Table
// - *Division
// - *List
func (_fdfe *Division )Add (d VectorDrawable )error {switch _cbdf :=d .(type ){case *Paragraph ,*StyledParagraph ,*Image ,*Chart ,*Rectangle ,*Ellipse ,*Line ,*Table ,*Division ,*List :case containerDrawable :_fecc ,_fbeb :=_cbdf .ContainerComponent (_fdfe );
if _fbeb !=nil {return _fbeb ;};_begf ,_ccbg :=_fecc .(VectorDrawable );if !_ccbg {return _g .Errorf ("\u0072\u0065\u0073\u0075\u006ct\u0020\u006f\u0066\u0020\u0043\u006f\u006et\u0061\u0069\u006e\u0065\u0072\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0020\u002d\u0020\u0025\u0054\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074\u0020\u0069\u006d\u0070\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u0056\u0065c\u0074\u006f\u0072\u0044\u0072\u0061\u0077\u0061\u0062\u006c\u0065\u0020i\u006e\u0074\u0065\u0072\u0066\u0061c\u0065",_fecc );
};d =_begf ;default:return _ee .New ("\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0079\u0070e\u0020i\u006e\u0020\u0044\u0069\u0076\u0069\u0073i\u006f\u006e");};_fdfe ._cadaa =append (_fdfe ._cadaa ,d );return nil ;};

// SetStructureType sets the structure type for the line component.
func (_cdfg *Line )SetStructureType (structureType _db .StructureType ){if _cdfg ._eegb ==nil {_cdfg ._eegb =_db .NewStructureTagInfo ();};_cdfg ._eegb .StructureType =structureType ;};

// This method is not supported by Division component and exists solely to satisfy the Drawable interface.
func (_bedee *Division )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };

// GenerateKDict generates a KDict for the Polygon structure info.
func (_fegc *Polygon )GenerateKDict ()(*_db .KDict ,error ){if _fegc ._daega ==nil {return nil ,_g .Errorf ("\u0070\u006f\u006c\u0079\u0067\u006fn\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075\u0072\u0065\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");
};return _fegc ._daega .GenerateKDict (),nil ;};

// SetFont sets the Paragraph's font.
func (_fdfb *Paragraph )SetFont (font *_db .PdfFont ){_fdfb ._aad =font };const (_gce =0.72;_fee =28.3464;_agdc =_fee /10;_aebg =0.551784;_ddfb =96;_fad =16.0;);

// SetBorderOpacity sets the border opacity.
func (_gefcb *PolyBezierCurve )SetBorderOpacity (opacity float64 ){_gefcb ._efcf =opacity };

// SetAngle sets the rotation angle in degrees.
func (_bab *Block )SetAngle (angleDeg float64 ){_bab ._da =angleDeg };

// NewCellProps returns the default properties of an invoice cell.
func (_bdcc *Invoice )NewCellProps ()InvoiceCellProps {_gdg :=ColorRGBFrom8bit (255,255,255);return InvoiceCellProps {TextStyle :_bdcc ._bcag ,Alignment :CellHorizontalAlignmentLeft ,BackgroundColor :_gdg ,BorderColor :_gdg ,BorderWidth :1,BorderSides :[]CellBorderSide {CellBorderSideAll }};
};

// SetCoords sets the upper left corner coordinates of the rectangle.
func (_gegf *Rectangle )SetCoords (x ,y float64 ){_gegf ._eeadac =x ;_gegf ._abegd =y };func _gfdb (_ceb string )string {_eebc :=_bagc .FindAllString (_ceb ,-1);if len (_eebc )==0{_ceb =_ceb +"\u0030";}else {_cde ,_aabe :=_fbf .Atoi (_eebc [len (_eebc )-1]);
if _aabe !=nil {_a .Log .Debug ("\u0045r\u0072\u006f\u0072 \u0063\u006f\u006ev\u0065rt\u0069\u006e\u0067\u0020\u0064\u0069\u0067i\u0074\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073\u0020\u006e\u0061\u006de,\u0020f\u0061\u006c\u006c\u0062\u0061\u0063k\u0020\u0074\u006f\u0020\u0062a\u0073\u0069\u0063\u0020\u006d\u0065\u0074\u0068\u006f\u0064\u003a \u0025\u0076",_aabe );
_ceb =_ceb +"\u0030";}else {_cde ++;_aae :=_fc .LastIndex (_ceb ,_eebc [len (_eebc )-1]);if _aae ==-1{_ceb =_g .Sprintf ("\u0025\u0073\u0025\u0064",_ceb [:len (_ceb )-1],_cde );}else {_ceb =_ceb [:_aae ]+_fbf .Itoa (_cde );};};};return _ceb ;};

// Rows returns the total number of rows the table has.
func (_gefag *Table )Rows ()int {return _gefag ._fgdc };

// SetBorderWidth sets the border width.
func (_bdaga *PolyBezierCurve )SetBorderWidth (borderWidth float64 ){_bdaga ._gefa .BorderWidth =borderWidth ;};func (_gedfc *templateProcessor )parseBorderRadiusAttr (_gfged ,_dagbc string )(_gccda ,_dggfg ,_gbecd ,_dfgc float64 ){_a .Log .Debug ("\u0050a\u0072\u0073i\u006e\u0067\u0020\u0062o\u0072\u0064\u0065r\u0020\u0072\u0061\u0064\u0069\u0075\u0073\u0020\u0061tt\u0072\u0069\u0062u\u0074\u0065:\u0020\u0028\u0060\u0025\u0073\u0060,\u0020\u0025s\u0029\u002e",_gfged ,_dagbc );
switch _cagfc :=_fc .Fields (_dagbc );len (_cagfc ){case 1:_gccda ,_ =_fbf .ParseFloat (_cagfc [0],64);_dggfg =_gccda ;_gbecd =_gccda ;_dfgc =_gccda ;case 2:_gccda ,_ =_fbf .ParseFloat (_cagfc [0],64);_gbecd =_gccda ;_dggfg ,_ =_fbf .ParseFloat (_cagfc [1],64);
_dfgc =_dggfg ;case 3:_gccda ,_ =_fbf .ParseFloat (_cagfc [0],64);_dggfg ,_ =_fbf .ParseFloat (_cagfc [1],64);_dfgc =_dggfg ;_gbecd ,_ =_fbf .ParseFloat (_cagfc [2],64);case 4:_gccda ,_ =_fbf .ParseFloat (_cagfc [0],64);_dggfg ,_ =_fbf .ParseFloat (_cagfc [1],64);
_gbecd ,_ =_fbf .ParseFloat (_cagfc [2],64);_dfgc ,_ =_fbf .ParseFloat (_cagfc [3],64);};return _gccda ,_dggfg ,_gbecd ,_dfgc ;};func _babg (_fdgag *templateProcessor ,_ceddb *templateNode )(interface{},error ){return _fdgag .parseEllipse (_ceddb );};func _cbaf (_dbeb *templateProcessor ,_gegfe *templateNode )(interface{},error ){return _dbeb .parseStyledParagraph (_gegfe );
};

// SetBorderColor sets the border color.
func (_geebd *CurvePolygon )SetBorderColor (color Color ){_geebd ._accc .BorderColor =_cdfe (color )};

// SetStructureType sets the structure type for the ellipse.
func (_fgdb *Ellipse )SetStructureType (structureType _db .StructureType ){if _fgdb ._cgbg ==nil {_fgdb ._cgbg =_db .NewStructureTagInfo ();};_fgdb ._cgbg .StructureType =structureType ;};func _cecbae (_edbde *_db .PdfAnnotationHighlight )*_db .PdfAnnotationHighlight {if _edbde ==nil {return nil ;
};_cagg :=_db .NewPdfAnnotationHighlight ();_cagg .PdfAnnotation =_edbde .PdfAnnotation ;_cagg .C =_edbde .C ;_cagg .CA =_edbde .CA ;_cagg .Rect =_edbde .Rect ;_cagg .QuadPoints =_edbde .QuadPoints ;return _cagg ;};

// DrawTemplate renders the template provided through the specified reader,
// using the specified `data` and `options`.
// Creator templates are first executed as text/template *Template instances,
// so the specified `data` is inserted within the template.
// The second phase of processing is actually parsing the template, translating
// it into creator components and rendering them using the provided options.
// Both the `data` and `options` parameters can be nil.
func (_cege *Creator )DrawTemplate (r _cc .Reader ,data interface{},options *TemplateOptions )error {return _gcba (_cege ,r ,data ,options ,_cege );};

// SetColumnWidths sets the fractional column widths.
// Each width should be in the range 0-1 and is a fraction of the table width.
// The number of width inputs must match number of columns, otherwise an error is returned.
func (_cgafd *Table )SetColumnWidths (widths ...float64 )error {if len (widths )!=_cgafd ._fcegg {_a .Log .Debug ("M\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0069\u006e\u0067\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066\u0020\u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0061nd\u0020\u0063\u006fl\u0075m\u006e\u0073");
return _ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");};_cgafd ._bdggd =widths ;return nil ;};func _adeg (_eagccd ,_bccfg ,_cdffda float64 )(_afaga ,_ecccg ,_caebfa ,_aeecg float64 ){if _cdffda ==0{return 0,0,_eagccd ,_bccfg ;
};_cdege :=_gc .Path {Points :[]_gc .Point {_gc .NewPoint (0,0).Rotate (_cdffda ),_gc .NewPoint (_eagccd ,0).Rotate (_cdffda ),_gc .NewPoint (0,_bccfg ).Rotate (_cdffda ),_gc .NewPoint (_eagccd ,_bccfg ).Rotate (_cdffda )}}.GetBoundingBox ();return _cdege .X ,_cdege .Y ,_cdege .Width ,_cdege .Height ;
};

// NewBlockFromPage creates a Block from a PDF Page.  Useful for loading template pages as blocks
// from a PDF document and additional content with the creator.
func NewBlockFromPage (page *_db .PdfPage )(*Block ,error ){_dc :=&Block {};_gca ,_cf :=page .GetAllContentStreams ();if _cf !=nil {return nil ,_cf ;};_gd :=_eg .NewContentStreamParser (_gca );_fcc ,_cf :=_gd .Parse ();if _cf !=nil {return nil ,_cf ;};
_fcc .WrapIfNeeded ();_dc ._fd =_fcc ;if page .Resources !=nil {_dc ._faa =page .Resources ;}else {_dc ._faa =_db .NewPdfPageResources ();};_ff ,_cf :=page .GetMediaBox ();if _cf !=nil {return nil ,_cf ;};if _ff .Llx !=0||_ff .Lly !=0{_dc .translate (-_ff .Llx ,_ff .Lly );
};_dc ._fedd =_ff .Urx -_ff .Llx ;_dc ._dbe =_ff .Ury -_ff .Lly ;if page .Rotate !=nil {_dc ._da =-float64 (*page .Rotate );};return _dc ,nil ;};

// Title returns the title of the invoice.
func (_bgcg *Invoice )Title ()string {return _bgcg ._gfegb };func (_acfd cmykColor )ToRGB ()(float64 ,float64 ,float64 ){_gac :=_acfd ._edb ;return 1-(_acfd ._dgfd *(1-_gac )+_gac ),1-(_acfd ._dcb *(1-_gac )+_gac ),1-(_acfd ._eead *(1-_gac )+_gac );};

// ClearAnnotations clears any existing annotations.
func (_effg *TextChunk )ClearAnnotations (){_effg ._aecg =[]*_db .PdfAnnotation {}};

// FillOpacity returns the fill opacity of the rectangle (0-1).
func (_gdbae *Rectangle )FillOpacity ()float64 {return _gdbae ._dfecg };func (_bgca *Invoice )setCellBorder (_ageg *TableCell ,_faefe *InvoiceCell ){for _ ,_aegf :=range _faefe .BorderSides {_ageg .SetBorder (_aegf ,CellBorderStyleSingle ,_faefe .BorderWidth );
};_ageg .SetBorderColor (_faefe .BorderColor );};

// This method is not supported by Chapter component and exists solely to satisfy the Drawable interface.
func (_acgg *Chapter )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };func (_bccfa *GraphicSVGElement )drawLine (_gfeaf *_eg .ContentCreator ,_dfgab *_db .PdfPageResources ){_gfeaf .Add_q ();_bccfa .Style .toContentStream (_gfeaf ,_dfgab ,_bccfa );
_bbbdcc ,_cega :=_cacac (_bccfa .Attributes ["\u0078\u0031"],64);if _cega !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cega .Error ());
};_fadgb ,_cega :=_cacac (_bccfa .Attributes ["\u0079\u0031"],64);if _cega !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cega .Error ());
};_ggae ,_cega :=_cacac (_bccfa .Attributes ["\u0078\u0032"],64);if _cega !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cega .Error ());
};_befb ,_cega :=_cacac (_bccfa .Attributes ["\u0079\u0032"],64);if _cega !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cega .Error ());
};_gfeaf .Add_m (_bbbdcc *_bccfa ._aece ,_fadgb *_bccfa ._aece );_gfeaf .Add_l (_ggae *_bccfa ._aece ,_befb *_bccfa ._aece );_bccfa .Style .fillStroke (_gfeaf );_gfeaf .Add_h ();_gfeaf .Add_Q ();};func _cdfd (_fede []_gc .Point )*Polyline {return &Polyline {_dabba :&_gc .Polyline {Points :_fede ,LineColor :_db .NewPdfColorDeviceRGB (0,0,0),LineWidth :1.0},_aade :1.0};
};

// Logo returns the logo of the invoice.
func (_gcacb *Invoice )Logo ()*Image {return _gcacb ._aeae };

// Chart represents a chart drawable.
// It is used to render unichart chart components using a creator instance.
type Chart struct{_cegb _fed .ChartRenderable ;_gcac Positioning ;_dfcb float64 ;_cda float64 ;_ace Margins ;_aaf *_db .StructureTagInfo ;};

// ParseFromSVGString creates a GraphicSVG instance from string SVG.
func ParseFromSVGString (svgStr string )(*GraphicSVGElement ,error ){return ParseFromSVGStream (_fc .NewReader (svgStr ));};type marginDrawable interface{VectorDrawable ;GetMargins ()(float64 ,float64 ,float64 ,float64 );};

// SkipCells skips over a specified number of cells in the table.
func (_bgadf *Table )SkipCells (num int ){if num < 0{_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065:\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0073\u006b\u0069\u0070\u0020b\u0061\u0063\u006b\u0020\u0074\u006f\u0020\u0070\u0072\u0065\u0076\u0069\u006f\u0075\u0073\u0020\u0063\u0065\u006c\u006c\u0073");
return ;};for _eeba :=0;_eeba < num ;_eeba ++{_bgadf .NewCell ();};};

// SetAntiAlias enables anti alias config.
//
// Anti alias is disabled by default.
func (_deda *LinearShading )SetAntiAlias (enable bool ){_deda ._afdb .SetAntiAlias (enable )};func (_gaeg *Division )drawBackground (_dcac []*Block ,_eaada ,_gfb DrawContext ,_dadff bool )([]*Block ,error ){_gfgg :=len (_dcac );if _gfgg ==0||_gaeg ._gfef ==nil {return _dcac ,nil ;
};_gcce :=make ([]*Block ,0,len (_dcac ));for _fbgc ,_ecbg :=range _dcac {var (_aba =_gaeg ._gfef .BorderRadiusTopLeft ;_fdc =_gaeg ._gfef .BorderRadiusTopRight ;_cgfc =_gaeg ._gfef .BorderRadiusBottomLeft ;_bagd =_gaeg ._gfef .BorderRadiusBottomRight ;
);_cade :=_eaada ;_cade .Page +=_fbgc ;if _fbgc ==0{if _dadff {_gcce =append (_gcce ,_ecbg );continue ;};if _gfgg ==1{_cade .Height =_gfb .Y -_eaada .Y ;};}else {_cade .X =_cade .Margins .Left +_gaeg ._fccaf .Left ;_cade .Y =_cade .Margins .Top ;_cade .Width =_cade .PageWidth -_cade .Margins .Left -_cade .Margins .Right -_gaeg ._fccaf .Left -_gaeg ._fccaf .Right ;
if _fbgc ==_gfgg -1{_cade .Height =_gfb .Y -_cade .Margins .Top -_gaeg ._fccaf .Top ;}else {_cade .Height =_cade .PageHeight -_cade .Margins .Top -_cade .Margins .Bottom ;};if !_dadff {_aba =0;_fdc =0;};};if _gfgg > 1&&_fbgc !=_gfgg -1{_cgfc =0;_bagd =0;
};_fbfe :=_cgaag (_cade .X ,_cade .Y ,_cade .Width ,_cade .Height );_fbfe .SetFillColor (_gaeg ._gfef .FillColor );_fbfe .SetBorderColor (_gaeg ._gfef .BorderColor );_fbfe .SetBorderWidth (_gaeg ._gfef .BorderSize );_fbfe .SetBorderRadius (_aba ,_fdc ,_cgfc ,_bagd );
_eadc ,_ ,_gbbff :=_fbfe .GeneratePageBlocks (_cade );if _gbbff !=nil {return nil ,_gbbff ;};if len (_eadc )==0{continue ;};_aedf :=_eadc [0];if _gbbff =_aedf .mergeBlocks (_ecbg );_gbbff !=nil {return nil ,_gbbff ;};_gcce =append (_gcce ,_aedf );};return _gcce ,nil ;
};func _bacba (_cfdgf map[string ]string ,_aaff float64 )(*GraphicSVGStyle ,error ){_edce :=_gdbg ();_gadac ,_fdff :=_cfdgf ["\u0066\u0069\u006c\u006c"];if _fdff {_edce .FillColor =_gadac ;if _gadac =="\u006e\u006f\u006e\u0065"{_edce .FillColor ="";};};
_dggb ,_dfdfg :=_cfdgf ["\u0066\u0069\u006cl\u002d\u006f\u0070\u0061\u0063\u0069\u0074\u0079"];if _dfdfg {_ffbf ,_adfaf :=_abbgb (_dggb );if _adfaf !=nil {return nil ,_adfaf ;};_edce .FillOpacity =_ffbf ;};_eagf ,_cabd :=_cfdgf ["\u0073\u0074\u0072\u006f\u006b\u0065"];
if _cabd {_edce .StrokeColor =_eagf ;if _eagf =="\u006e\u006f\u006e\u0065"{_edce .StrokeColor ="";};};_efcda ,_cdccf :=_cfdgf ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];if _cdccf {_cdcba ,_ddgf :=_cacac (_efcda ,64);if _ddgf !=nil {return nil ,_ddgf ;
};_edce .StrokeWidth =_cdcba *_aaff ;};return _edce ,nil ;};func (_efbda *templateProcessor )parseRectangle (_deagb *templateNode )(interface{},error ){_ffdfd :=_efbda .creator .NewRectangle (0,0,0,0);for _ ,_bcfbg :=range _deagb ._ccge .Attr {_eeddb :=_bcfbg .Value ;
switch _degfc :=_bcfbg .Name .Local ;_degfc {case "\u0078":_ffdfd ._eeadac =_efbda .parseFloatAttr (_degfc ,_eeddb );case "\u0079":_ffdfd ._abegd =_efbda .parseFloatAttr (_degfc ,_eeddb );case "\u0077\u0069\u0064t\u0068":_ffdfd .SetWidth (_efbda .parseFloatAttr (_degfc ,_eeddb ));
case "\u0068\u0065\u0069\u0067\u0068\u0074":_ffdfd .SetHeight (_efbda .parseFloatAttr (_degfc ,_eeddb ));case "\u0066\u0069\u006c\u006c\u002d\u0063\u006f\u006c\u006f\u0072":_ffdfd .SetFillColor (_efbda .parseColorAttr (_degfc ,_eeddb ));case "\u0066\u0069\u006cl\u002d\u006f\u0070\u0061\u0063\u0069\u0074\u0079":_ffdfd .SetFillOpacity (_efbda .parseFloatAttr (_degfc ,_eeddb ));
case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0063\u006f\u006c\u006f\u0072":_ffdfd .SetBorderColor (_efbda .parseColorAttr (_degfc ,_eeddb ));case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u006f\u0070a\u0063\u0069\u0074\u0079":_ffdfd .SetBorderOpacity (_efbda .parseFloatAttr (_degfc ,_eeddb ));
case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0077\u0069\u0064\u0074\u0068":_ffdfd .SetBorderWidth (_efbda .parseFloatAttr (_degfc ,_eeddb ));case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0072\u0061\u0064\u0069\u0075\u0073":_egbe ,_adfce ,_fgef ,_aegbg :=_efbda .parseBorderRadiusAttr (_degfc ,_eeddb );
_ffdfd .SetBorderRadius (_egbe ,_adfce ,_aegbg ,_fgef );case "\u0062\u006f\u0072\u0064er\u002d\u0074\u006f\u0070\u002d\u006c\u0065\u0066\u0074\u002d\u0072\u0061\u0064\u0069u\u0073":_ffdfd ._edbfa =_efbda .parseFloatAttr (_degfc ,_eeddb );case "\u0062\u006f\u0072de\u0072\u002d\u0074\u006f\u0070\u002d\u0072\u0069\u0067\u0068\u0074\u002d\u0072\u0061\u0064\u0069\u0075\u0073":_ffdfd ._efgaa =_efbda .parseFloatAttr (_degfc ,_eeddb );
case "\u0062o\u0072\u0064\u0065\u0072-\u0062\u006f\u0074\u0074\u006fm\u002dl\u0065f\u0074\u002d\u0072\u0061\u0064\u0069\u0075s":_ffdfd ._bbef =_efbda .parseFloatAttr (_degfc ,_eeddb );case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0062\u006f\u0074\u0074o\u006d\u002d\u0072\u0069\u0067\u0068\u0074\u002d\u0072\u0061d\u0069\u0075\u0073":_ffdfd ._fcdc =_efbda .parseFloatAttr (_degfc ,_eeddb );
case "\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e":_ffdfd .SetPositioning (_efbda .parsePositioningAttr (_degfc ,_eeddb ));case "\u0066\u0069\u0074\u002d\u006d\u006f\u0064\u0065":_ffdfd .SetFitMode (_efbda .parseFitModeAttr (_degfc ,_eeddb ));case "\u006d\u0061\u0072\u0067\u0069\u006e":_fgbb :=_efbda .parseMarginAttr (_degfc ,_eeddb );
_ffdfd .SetMargins (_fgbb .Left ,_fgbb .Right ,_fgbb .Top ,_fgbb .Bottom );default:_efbda .nodeLogDebug (_deagb ,"\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072t\u0065\u0064\u0020re\u0063\u0074\u0061\u006e\u0067\u006ce\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073`\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069n\u0067\u002e",_degfc );
};};return _ffdfd ,nil ;};func _dcgdc (_gfda *Block ,_dbad *Image ,_acaf DrawContext )(DrawContext ,error ){_efag :=_acaf ;_ebdf :=1;_egdcb :=_dd .PdfObjectName (_g .Sprintf ("\u0049\u006d\u0067%\u0064",_ebdf ));for _gfda ._faa .HasXObjectByName (_egdcb ){_ebdf ++;
_egdcb =_dd .PdfObjectName (_g .Sprintf ("\u0049\u006d\u0067%\u0064",_ebdf ));};_baaa :=_gfda ._faa .SetXObjectImageByNameLazy (_egdcb ,_dbad ._agge ,_dbad ._gbed );if _baaa !=nil {return _acaf ,_baaa ;};_eddb :=0;_dadd :=_dd .PdfObjectName (_g .Sprintf ("\u0047\u0053\u0025\u0064",_eddb ));
for _gfda ._faa .HasExtGState (_dadd ){_eddb ++;_dadd =_dd .PdfObjectName (_g .Sprintf ("\u0047\u0053\u0025\u0064",_eddb ));};_ccga :=_dd .MakeDict ();_ccga .Set ("\u0042\u004d",_dd .MakeName ("\u004e\u006f\u0072\u006d\u0061\u006c"));if _dbad ._efee < 1.0{_ccga .Set ("\u0043\u0041",_dd .MakeFloat (_dbad ._efee ));
_ccga .Set ("\u0063\u0061",_dd .MakeFloat (_dbad ._efee ));};_baaa =_gfda ._faa .AddExtGState (_dadd ,_dd .MakeIndirectObject (_ccga ));if _baaa !=nil {return _acaf ,_baaa ;};_ffcb :=_dbad .Width ();_dffe :=_dbad .Height ();_ ,_agagg :=_dbad .rotatedSize ();
_abca :=_acaf .X ;_dcdf :=_acaf .PageHeight -_acaf .Y -_dffe ;if _dbad ._dfbc .IsRelative (){_dcdf -=(_agagg -_dffe )/2;switch _dbad ._aagg {case HorizontalAlignmentCenter :_abca +=(_acaf .Width -_ffcb )/2;case HorizontalAlignmentRight :_abca =_acaf .PageWidth -_acaf .Margins .Right -_dbad ._efde .Right -_ffcb ;
};};_fdcd :=_dbad ._edebf ;_bbgc :=_eg .NewContentCreator ();if _dbad ._fecd !=nil {_bbgc .Add_BDC (*_dd .MakeName (string (_dbad ._fecd .StructureType )),map[string ]_dd .PdfObject {"\u004d\u0043\u0049\u0044":_dd .MakeInteger (_dbad ._fecd .Mcid )});};
_bbgc .Add_gs (_dadd );_bbgc .Translate (_abca ,_dcdf );if _fdcd !=0{_bbgc .Translate (_ffcb /2,_dffe /2);_bbgc .RotateDeg (_fdcd );_bbgc .Translate (-_ffcb /2,-_dffe /2);};_bbgc .Scale (_ffcb ,_dffe ).Add_Do (_egdcb );if _dbad ._fecd !=nil {_bbgc .Add_EMC ();
};_bfga :=_bbgc .Operations ();_bfga .WrapIfNeeded ();_gfda .addWrappedContents (_bfga );if _dbad ._dfbc .IsRelative (){_acaf .Y +=_agagg ;_acaf .Height -=_agagg ;return _acaf ,nil ;};return _efag ,nil ;};type Grid struct{_cecb int ;_daad []float64 ;_gfeb float64 ;
_cgaf Positioning ;_edfdg ,_dbbgd float64 ;_cbdfcg Margins ;_bgcb []*GridRow ;_gbaa *_db .StructureTagInfo ;};

// SetText replaces all the text of the paragraph with the specified one.
func (_bdaa *StyledParagraph )SetText (text string )*TextChunk {_bdaa .Reset ();return _bdaa .Append (text );};

// SetIndent sets the cell's left indent.
func (_geeac *TableCell )SetIndent (indent float64 ){_geeac ._eggag =indent };

// Add appends a new item to the list.
// The supported components are: *Paragraph, *StyledParagraph, *Division, *Image, *Table, and *List.
// Returns the marker used for the newly added item. The returned marker
// object can be used to change the text and style of the marker for the
// current item.
func (_acea *List )Add (item VectorDrawable )(*TextChunk ,error ){_bbfb :=&listItem {_agafc :item ,_daedg :_acea ._debc };switch _gabe :=item .(type ){case *Paragraph :case *StyledParagraph :case *List :if _gabe ._dgbd {_gabe ._gagc =15;};case *Division :case *Image :case *Table :default:return nil ,_ee .New ("\u0074\u0068i\u0073\u0020\u0074\u0079\u0070\u0065\u0020\u006f\u0066\u0020\u0064\u0072\u0061\u0077\u0061\u0062\u006c\u0065\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0069\u006e\u0020\u006c\u0069\u0073\u0074");
};_acea ._bbbe =append (_acea ._bbbe ,_bbfb );return &_bbfb ._daedg ,nil ;};func (_fcefb *templateProcessor )parseFloatArray (_eeffc ,_ggggg string )[]float64 {_a .Log .Debug ("\u0050\u0061\u0072s\u0069\u006e\u0067\u0020\u0066\u006c\u006f\u0061\u0074\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060%\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_eeffc ,_ggggg );
_dbbac :=_fc .Fields (_ggggg );_adcbe :=make ([]float64 ,0,len (_dbbac ));for _ ,_cagbe :=range _dbbac {_bggbd ,_ :=_fbf .ParseFloat (_cagbe ,64);_adcbe =append (_adcbe ,_bggbd );};return _adcbe ;};func (_aadfa *templateProcessor )parseEllipse (_bfacag *templateNode )(interface{},error ){_eddce :=_aadfa .creator .NewEllipse (0,0,0,0);
for _ ,_abde :=range _bfacag ._ccge .Attr {_bgege :=_abde .Value ;switch _begbde :=_abde .Name .Local ;_begbde {case "\u0063\u0078":_eddce ._bace =_aadfa .parseFloatAttr (_begbde ,_bgege );case "\u0063\u0079":_eddce ._gdabb =_aadfa .parseFloatAttr (_begbde ,_bgege );
case "\u0077\u0069\u0064t\u0068":_eddce .SetWidth (_aadfa .parseFloatAttr (_begbde ,_bgege ));case "\u0068\u0065\u0069\u0067\u0068\u0074":_eddce .SetHeight (_aadfa .parseFloatAttr (_begbde ,_bgege ));case "\u0066\u0069\u006c\u006c\u002d\u0063\u006f\u006c\u006f\u0072":_eddce .SetFillColor (_aadfa .parseColorAttr (_begbde ,_bgege ));
case "\u0066\u0069\u006cl\u002d\u006f\u0070\u0061\u0063\u0069\u0074\u0079":_eddce .SetFillOpacity (_aadfa .parseFloatAttr (_begbde ,_bgege ));case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0063\u006f\u006c\u006f\u0072":_eddce .SetBorderColor (_aadfa .parseColorAttr (_begbde ,_bgege ));
case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u006f\u0070a\u0063\u0069\u0074\u0079":_eddce .SetBorderOpacity (_aadfa .parseFloatAttr (_begbde ,_bgege ));case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0077\u0069\u0064\u0074\u0068":_eddce .SetBorderWidth (_aadfa .parseFloatAttr (_begbde ,_bgege ));
case "\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e":_eddce .SetPositioning (_aadfa .parsePositioningAttr (_begbde ,_bgege ));case "\u0066\u0069\u0074\u002d\u006d\u006f\u0064\u0065":_eddce .SetFitMode (_aadfa .parseFitModeAttr (_begbde ,_bgege ));case "\u006d\u0061\u0072\u0067\u0069\u006e":_ccgac :=_aadfa .parseMarginAttr (_begbde ,_bgege );
_eddce .SetMargins (_ccgac .Left ,_ccgac .Right ,_ccgac .Top ,_ccgac .Bottom );default:_aadfa .nodeLogDebug (_bfacag ,"\u0055\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u006c\u0069\u0070\u0073\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_begbde );
};};return _eddce ,nil ;};

// New creates a new instance of the PDF Creator.
func New ()*Creator {const _cef ="c\u0072\u0065\u0061\u0074\u006f\u0072\u002e\u004e\u0065\u0077";_gde :=&Creator {};_gde ._bcbe =[]*_db .PdfPage {};_gde ._gade =map[*_db .PdfPage ]*Block {};_gde ._fbgd =map[*_db .PdfPage ]*pageTransformations {};_gde .SetPageSize (PageSizeLetter );
_fdae :=0.1*_gde ._cfgg ;_gde ._gecf .Left =_fdae ;_gde ._gecf .Right =_fdae ;_gde ._gecf .Top =_fdae ;_gde ._gecf .Bottom =_fdae ;var _cca error ;_gde ._bae ,_cca =_db .NewStandard14Font (_db .HelveticaName );if _cca !=nil {_gde ._bae =_db .DefaultFont ();
};_gde ._edeg ,_cca =_db .NewStandard14Font (_db .HelveticaBoldName );if _cca !=nil {_gde ._bae =_db .DefaultFont ();};_gde ._dbaf =_gde .NewTOC ("\u0054\u0061\u0062\u006c\u0065\u0020\u006f\u0066\u0020\u0043\u006f\u006et\u0065\u006e\u0074\u0073");_gde .AddOutlines =true ;
_gde ._gcbc =_db .NewOutline ();_bced .TrackUse (_cef );return _gde ;};func (_gcd *Block )duplicate ()*Block {_eda :=&Block {};*_eda =*_gcd ;_eece :=_eg .ContentStreamOperations {};_eece =append (_eece ,*_gcd ._fd ...);_eda ._fd =&_eece ;return _eda ;};


// SetTitle sets the title of the invoice.
func (_agaf *Invoice )SetTitle (title string ){_agaf ._gfegb =title };

// Polyline represents a slice of points that are connected as straight lines.
// Implements the Drawable interface and can be rendered using the Creator.
type Polyline struct{_dabba *_gc .Polyline ;_aade float64 ;_bafaf *_db .StructureTagInfo ;};

// SetColor sets the line color. Use ColorRGBFromHex, ColorRGBFrom8bit or
// ColorRGBFromArithmetic to create the color object.
func (_cdcb *Line )SetColor (color Color ){_cdcb ._feedb =color };

// GeneratePageBlocks generates the page blocks for the Division component.
// Multiple blocks are generated if the contents wrap over multiple pages.
func (_fdaeg *Division )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var (_fdfd []*Block ;_beaf bool ;_gfc error ;_fgab =_fdaeg ._ebd .IsRelative ();_gfgb =_fdaeg ._fccaf .Top ;);if _fgab &&!_fdaeg ._gbfd &&!_fdaeg ._ffeb {_dcgd :=_fdaeg .ctxHeight (ctx .Width );
if _dcgd > ctx .Height -_fdaeg ._fccaf .Top &&_dcgd <=ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom {if _fdfd ,ctx ,_gfc =_aegb ().GeneratePageBlocks (ctx );_gfc !=nil {return nil ,ctx ,_gfc ;};_beaf =true ;_gfgb =0;};};_bafg :=ctx ;_gdfc :=ctx ;
if _fgab {ctx .X +=_fdaeg ._fccaf .Left ;ctx .Y +=_gfgb ;ctx .Width -=_fdaeg ._fccaf .Left +_fdaeg ._fccaf .Right ;ctx .Height -=_gfgb ;_gdfc =ctx ;ctx .X +=_fdaeg ._ddcg .Left ;ctx .Y +=_fdaeg ._ddcg .Top ;ctx .Width -=_fdaeg ._ddcg .Left +_fdaeg ._ddcg .Right ;
ctx .Height -=_fdaeg ._ddcg .Top ;ctx .Margins .Top +=_fdaeg ._ddcg .Top ;ctx .Margins .Bottom +=_fdaeg ._ddcg .Bottom ;ctx .Margins .Left +=_fdaeg ._fccaf .Left +_fdaeg ._ddcg .Left ;ctx .Margins .Right +=_fdaeg ._fccaf .Right +_fdaeg ._ddcg .Right ;};
ctx .Inline =_fdaeg ._ffeb ;_ebaac :=ctx ;_edaa :=ctx ;var _ggc float64 ;for _ ,_ccbc :=range _fdaeg ._cadaa {if ctx .Inline {if (ctx .X -_ebaac .X )+_ccbc .Width ()<=ctx .Width {ctx .Y =_edaa .Y ;ctx .Height =_edaa .Height ;}else {ctx .X =_ebaac .X ;ctx .Width =_ebaac .Width ;
_edaa .Y +=_ggc ;_edaa .Height -=_ggc ;_ggc =0;};};_bfecd ,_cggg ,_efc :=_ccbc .GeneratePageBlocks (ctx );if _efc !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074\u0069\u006eg\u0020p\u0061\u0067\u0065\u0020\u0062\u006c\u006f\u0063\u006b\u0073\u003a\u0020\u0025\u0076",_efc );
return nil ,ctx ,_efc ;};if len (_bfecd )< 1{continue ;};if len (_fdfd )> 0{_fdfd [len (_fdfd )-1].mergeBlocks (_bfecd [0]);_fdfd =append (_fdfd ,_bfecd [1:]...);}else {if _fgee :=_bfecd [0]._fd ;_fgee ==nil ||len (*_fgee )==0{_beaf =true ;};_fdfd =append (_fdfd ,_bfecd [0:]...);
};_ebec :=0.0;switch _bafe :=_ccbc .(type ){case *Paragraph :_ebec =(0.5*_bafe ._febee *_bafe ._gafdf );case *StyledParagraph :_ebec =(0.5*_bafe .getTextHeight ());};_cggg .Y +=_ebec ;_cggg .Height -=_ebec ;if ctx .Inline {if ctx .Page !=_cggg .Page {_ebaac .Y =ctx .Margins .Top ;
_ebaac .Height =ctx .PageHeight -ctx .Margins .Top ;_edaa .Y =_ebaac .Y ;_edaa .Height =_ebaac .Height ;_ggc =_cggg .Height -_ebaac .Height ;}else {if _cgc :=ctx .Height -_cggg .Height ;_cgc > _ggc {_ggc =_cgc ;};};}else {_cggg .X =ctx .X ;};ctx =_cggg ;
};if len (_fdaeg ._cadaa )==0{_ebgab :=NewBlock (ctx .Width ,0);_fdfd =append (_fdfd ,_ebgab );};ctx .Inline =_bafg .Inline ;ctx .Margins =_bafg .Margins ;if _fgab {ctx .X =_bafg .X ;ctx .Width =_bafg .Width ;ctx .Y +=_fdaeg ._ddcg .Bottom ;ctx .Height -=_fdaeg ._ddcg .Bottom ;
};if _fdaeg ._gfef !=nil {_fdfd ,_gfc =_fdaeg .drawBackground (_fdfd ,_gdfc ,ctx ,_beaf );if _gfc !=nil {return nil ,ctx ,_gfc ;};};if _fdaeg ._ebd .IsAbsolute (){return _fdfd ,_bafg ,nil ;};ctx .Y +=_fdaeg ._fccaf .Bottom ;ctx .Height -=_fdaeg ._fccaf .Bottom ;
return _fdfd ,ctx ,nil ;};func (_bdbfd *TOCLine )getLineLink ()*_db .PdfAnnotation {if _bdbfd ._ageffa <=0{return nil ;};return _bagfe (_bdbfd ._ageffa -1,_bdbfd ._fbcga ,_bdbfd ._ebafe ,0,"");};

// Command is a representation of an SVG path command and its parameters.
type Command struct{Symbol string ;Params []float64 ;};func (_aeeb *GraphicSVGStyle )toContentStream (_cbfgc *_eg .ContentCreator ,_cdecf *_db .PdfPageResources ,_efff *GraphicSVGElement ){if _aeeb ==nil {return ;};if _aeeb .FillColor !=""{var _bfef ,_fefgc ,_agef float64 ;
if _ddacf ,_fafd :=_ba .ColorMap [_aeeb .FillColor ];_fafd {_bbfe ,_cafa ,_aeafc ,_ :=_ddacf .RGBA ();_bfef ,_fefgc ,_agef =float64 (_bbfe ),float64 (_cafa ),float64 (_aeafc );_cbfgc .Add_rg (_bfef ,_fefgc ,_agef );}else if _fc .HasPrefix (_aeeb .FillColor ,"\u0072\u0067\u0062\u0028"){_bfef ,_fefgc ,_agef =_badge (_aeeb .FillColor );
_cbfgc .Add_rg (_bfef ,_fefgc ,_agef );}else if _fc .HasPrefix (_aeeb .FillColor ,"\u0075\u0072\u006c\u0028"){_eede :=_fc .TrimPrefix (_aeeb .FillColor ,"\u0075\u0072\u006c\u0028\u0027\u0023");_eede =_fc .TrimPrefix (_eede ,"\u0075\u0072\u006c(\u0023");
_eede =_fc .TrimSuffix (_eede ,"\u0027\u0029");_eede =_fc .TrimSuffix (_eede ,"\u0029");if _efff ._ccfaaf [_eede ]!=nil {_cdecf .SetPatternByName (*_dd .MakeName (_eede ),_efff ._ccfaaf [_eede ].ToPdfShadingPattern ().ToPdfObject ());_cbfgc .Add_cs (*_dd .MakeName ("\u0050a\u0074\u0074\u0065\u0072\u006e"));
_cbfgc .Add_scn_pattern (*_dd .MakeName (_eede ));}else if _efff ._gcdge [_eede ]!=nil {_cdecf .SetPatternByName (*_dd .MakeName (_eede ),_efff ._gcdge [_eede ].ToPdfShadingPattern ().ToPdfObject ());_cbfgc .Add_cs (*_dd .MakeName ("\u0050a\u0074\u0074\u0065\u0072\u006e"));
_cbfgc .Add_scn_pattern (*_dd .MakeName (_eede ));};}else {_bfef ,_fefgc ,_agef =ColorRGBFromHex (_aeeb .FillColor ).ToRGB ();_cbfgc .Add_rg (_bfef ,_fefgc ,_agef );};};if _aeeb .FillOpacity < 1.0{_gcadg :=0;_dcaf :=_dd .PdfObjectName (_g .Sprintf ("\u0047\u0053\u0025\u0064",_gcadg ));
for {_ ,_fgfff :=_cdecf .GetExtGState (_dcaf );if !_fgfff {break ;};_gcadg ++;_dcaf =_dd .PdfObjectName (_g .Sprintf ("\u0047\u0053\u0025\u0064",_gcadg ));};_ffeac :=_dd .MakeDict ();_ffeac .Set ("\u0063\u0061",_dd .MakeFloat (_aeeb .FillOpacity ));_ecdfdf :=_cdecf .AddExtGState (_dcaf ,_dd .MakeIndirectObject (_ffeac ));
if _ecdfdf !=nil {_a .Log .Debug (_ecdfdf .Error ());return ;};_cbfgc .Add_gs (_dcaf );};if _aeeb .StrokeColor !=""{var _geba ,_gfbd ,_cgga float64 ;if _gdcc ,_fdbc :=_ba .ColorMap [_aeeb .StrokeColor ];_fdbc {_aceef ,_efce ,_bfge ,_ :=_gdcc .RGBA ();_geba ,_gfbd ,_cgga =float64 (_aceef )/255.0,float64 (_efce )/255.0,float64 (_bfge )/255.0;
}else if _fc .HasPrefix (_aeeb .FillColor ,"\u0072\u0067\u0062\u0028"){_geba ,_gfbd ,_cgga =_badge (_aeeb .FillColor );}else {_geba ,_gfbd ,_cgga =ColorRGBFromHex (_aeeb .StrokeColor ).ToRGB ();};_cbfgc .Add_RG (_geba ,_gfbd ,_cgga );};if _aeeb .StrokeWidth > 0{_cbfgc .Add_w (_aeeb .StrokeWidth );
};};func (_bggd *Invoice )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };

// SetEnableWrap sets the line wrapping enabled flag.
func (_gfgga *Paragraph )SetEnableWrap (enableWrap bool ){_gfgga ._gcdc =enableWrap ;_gfgga ._gcfc =false ;};func (_gecd *GraphicSVGStyle )fillStroke (_cdcc *_eg .ContentCreator ){if _gecd .FillColor !=""&&_gecd .StrokeColor !=""{_cdcc .Add_B ();}else if _gecd .FillColor !=""{_cdcc .Add_f ();
}else if _gecd .StrokeColor !=""{_cdcc .Add_S ();};};

// SetBorderColor sets the border color.
func (_cgacg *Polygon )SetBorderColor (color Color ){_cgacg ._addde .BorderColor =_cdfe (color )};

// GenerateKDict generates a K dictionary for the line component.
func (_beec *Line )GenerateKDict ()(*_db .KDict ,error ){if _beec ._eegb ==nil {return nil ,_g .Errorf ("\u006c\u0069\u006e\u0065\u0020\u0073\u0074\u0072\u0075\u0063t\u0075\u0072\u0065\u0020\u0069\u006e\u0066o\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _beec ._eegb .GenerateKDict (),nil ;};

// Positioning returns the type of positioning the ellipse is set to use.
func (_aef *Ellipse )Positioning ()Positioning {return _aef ._cddg };func _cgaag (_gddba ,_dccd ,_bcceg ,_eeddg float64 )*Rectangle {return &Rectangle {_eeadac :_gddba ,_abegd :_dccd ,_cdcgg :_bcceg ,_cfedb :_eeddg ,_fbbdf :PositionAbsolute ,_dfecg :1.0,_gdea :ColorBlack ,_dfbcd :1.0,_adaf :1.0};
};func _bfefce (_gcdce *templateProcessor ,_geca *templateNode )(interface{},error ){return _gcdce .parseRectangle (_geca );};func (_fedfc *Command )isAbsolute ()bool {return _fedfc .Symbol ==_fc .ToUpper (_fedfc .Symbol )};

// SkipOver skips over a specified number of rows and cols.
func (_dfgeb *Table )SkipOver (rows ,cols int ){_ageff :=rows *_dfgeb ._fcegg +cols -1;if _ageff < 0{_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065:\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0073\u006b\u0069\u0070\u0020b\u0061\u0063\u006b\u0020\u0074\u006f\u0020\u0070\u0072\u0065\u0076\u0069\u006f\u0075\u0073\u0020\u0063\u0065\u006c\u006c\u0073");
return ;};for _cecegg :=0;_cecegg < _ageff ;_cecegg ++{_dfgeb .NewCell ();};};

// This method is not supported by Block component and exists solely to satisfy the Drawable interface.
func (_bf *Block )SetStructureType (structureType _db .StructureType ){};

// Drawable is a widget that can be used to draw with the Creator.
type Drawable interface{

// GeneratePageBlocks draw onto blocks representing Page contents. As the content can wrap over many pages, multiple
// templates are returned, one per Page.  The function also takes a draw context containing information
// where to draw (if relative positioning) and the available height to draw on accounting for Margins etc.
GeneratePageBlocks (_ecea DrawContext )([]*Block ,DrawContext ,error );

// SetMarkedContentID sets the marked content id for the drawable.
SetMarkedContentID (_gecfd int64 );

// SetStructureType sets the structure type for the drawable.
SetStructureType (_dbec _db .StructureType );

// GenerateKDict generates a K dictionary for the drawable.
GenerateKDict ()(*_db .KDict ,error );};

// SetNoteHeadingStyle sets the style properties used to render the heading
// of the invoice note sections.
func (_eccc *Invoice )SetNoteHeadingStyle (style TextStyle ){_eccc ._bgcc =style };func (_ddeeb *StyledParagraph )createAccessibleLinkChunk (_gdaff string ,_aeaac *_db .PdfAnnotation ,_cdaeb LinkTagOptions )(*TextChunk ,*_db .KDict ,error ){var _ecdfb *_db .KDict ;
_bcbd :=NewTextChunk (_gdaff ,_ddeeb ._ecff );_bcbd .AddAnnotation (_aeaac );if _cdaeb .MCID > 0{_bcbd .SetMarkedContentID (_cdaeb .MCID );_bcbd .SetStructureType (_db .StructureTypeLink );if _cdaeb .AltText !=""&&_cdaeb .AltText !=_gdaff {_bcbd .SetAltText (_cdaeb .AltText );
};_bcbd .AssociateAnnotationWithStructure (_aeaac );_ebcfd ,_geebb :=_bcbd .GenerateKDict ();if _geebb !=nil {return nil ,nil ,_g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020t\u006f\u0020\u0067e\u006e\u0065\u0072\u0061t\u0065\u0020\u004b\u0044\u0069\u0063\u0074\u0020\u0066\u006f\u0072\u0020\u006c\u0069\u006e\u006b\u0020\u0063\u0068\u0075\u006e\u006b\u003a\u0020\u0025\u0076",_geebb );
};if _cdaeb .AltText !=""&&_cdaeb .AltText !=_gdaff {_ebcfd .Alt =_dd .MakeString (_cdaeb .AltText );};_ecdfb =_ebcfd ;};_ddeeb .appendChunk (_bcbd );return _bcbd ,_ecdfb ,nil ;};

// Scale scales the ellipse dimensions by the specified factors.
func (_ebaf *Ellipse )Scale (xFactor ,yFactor float64 ){_ebaf ._fdgf =xFactor *_ebaf ._fdgf ;_ebaf ._gaabd =yFactor *_ebaf ._gaabd ;};

// SetMarkedContentID sets marked content ID.
func (_cecba *Invoice )SetMarkedContentID (id int64 ){};

// SetLinePageStyle sets the style for the page part of all new lines
// of the table of contents.
func (_gfacd *TOC )SetLinePageStyle (style TextStyle ){_gfacd ._dfbae =style };

// ScaleToWidth scales the ellipse to the specified width. The height of
// the ellipse is scaled so that the aspect ratio is maintained.
func (_ggcf *Ellipse )ScaleToWidth (w float64 ){_fdgb :=_ggcf ._gaabd /_ggcf ._fdgf ;_ggcf ._fdgf =w ;_ggcf ._gaabd =w *_fdgb ;};

// SetEnableWrap sets the line wrapping enabled flag.
func (_bbdc *StyledParagraph )SetEnableWrap (enableWrap bool ){_bbdc ._gfce =enableWrap ;_bbdc ._afdg =false ;};

// SetFillOpacity sets the fill opacity.
func (_gafg *PolyBezierCurve )SetFillOpacity (opacity float64 ){_gafg ._dfdf =opacity };var _bagc =_bc .MustCompile ("\u005c\u0064\u002b");

// SetShowLinks sets visibility of links for the TOC lines.
func (_dfff *TOC )SetShowLinks (showLinks bool ){_dfff ._dace =showLinks };

// ColorGrayFromArithmetic creates a Color from a grayscale value (0-1).
// Example:
//
//	gray := ColorGrayFromArithmetic(0.7)
func ColorGrayFromArithmetic (g float64 )Color {return grayColor {g }};

// GeneratePageBlocks draws the filled curve on page blocks.
func (_cbceg *FilledCurve )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_fdgd :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_gaff ,_ ,_eddeb :=_cbceg .draw (_fdgd ,"");if _eddeb !=nil {return nil ,ctx ,_eddeb ;};_eddeb =_fdgd .addContentsByString (string (_gaff ));
if _eddeb !=nil {return nil ,ctx ,_eddeb ;};return []*Block {_fdgd },ctx ,nil ;};

// AddShadingResource adds shading dictionary inside the resources dictionary.
func (_dcbd *LinearShading )AddShadingResource (block *Block )(_bdceb _dd .PdfObjectName ,_dgeb error ){_gegec :=1;_bdceb =_dd .PdfObjectName ("\u0053\u0068"+_fbf .Itoa (_gegec ));for block ._faa .HasShadingByName (_bdceb ){_gegec ++;_bdceb =_dd .PdfObjectName ("\u0053\u0068"+_fbf .Itoa (_gegec ));
};if _baaca :=block ._faa .SetShadingByName (_bdceb ,_dcbd .shadingModel ().ToPdfObject ());_baaca !=nil {return "",_baaca ;};return _bdceb ,nil ;};

// SetMarkedContentID sets the marked content id for the grid.
func (_cgbd *Grid )SetMarkedContentID (mcid int64 ){};func _cbcaf (_gdfe string )(*Path ,error ){_gfdbca =_fcegf ();_gdadd ,_gcaab :=_bgcca (_ecgg (_gdfe ));if _gcaab !=nil {return nil ,_gcaab ;};return _efaca (_gdadd ),nil ;};

// SetMarkedContentID sets marked content ID.
func (_edgd *CurvePolygon )SetMarkedContentID (mcid int64 ){if _edgd ._ceada ==nil {_edgd ._ceada =_db .NewStructureTagInfo ();};_edgd ._ceada .Mcid =mcid ;};func (_dcga *GraphicSVGElement )drawCircle (_aeeaca *_eg .ContentCreator ,_dffbd *_db .PdfPageResources ){_aeeaca .Add_q ();
_dcga .Style .toContentStream (_aeeaca ,_dffbd ,_dcga );_eced ,_ddeg :=_cacac (_dcga .Attributes ["\u0063\u0078"],64);if _ddeg !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ddeg .Error ());
};_dbae ,_ddeg :=_cacac (_dcga .Attributes ["\u0063\u0079"],64);if _ddeg !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ddeg .Error ());
};_afecd ,_ddeg :=_cacac (_dcga .Attributes ["\u0072"],64);if _ddeg !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0072\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_ddeg .Error ());
};_bgbca :=_afecd *_dcga ._aece ;_cgef :=_afecd *_dcga ._aece ;_ffecg :=_bgbca *_aebg ;_dagfe :=_cgef *_aebg ;_bffcd :=_gc .NewCubicBezierPath ();_bffcd =_bffcd .AppendCurve (_gc .NewCubicBezierCurve (-_bgbca ,0,-_bgbca ,_dagfe ,-_ffecg ,_cgef ,0,_cgef ));
_bffcd =_bffcd .AppendCurve (_gc .NewCubicBezierCurve (0,_cgef ,_ffecg ,_cgef ,_bgbca ,_dagfe ,_bgbca ,0));_bffcd =_bffcd .AppendCurve (_gc .NewCubicBezierCurve (_bgbca ,0,_bgbca ,-_dagfe ,_ffecg ,-_cgef ,0,-_cgef ));_bffcd =_bffcd .AppendCurve (_gc .NewCubicBezierCurve (0,-_cgef ,-_ffecg ,-_cgef ,-_bgbca ,-_dagfe ,-_bgbca ,0));
_bffcd =_bffcd .Offset (_eced *_dcga ._aece ,_dbae *_dcga ._aece );if _dcga .Style .StrokeWidth > 0{_bffcd =_bffcd .Offset (_dcga .Style .StrokeWidth /2,_dcga .Style .StrokeWidth /2);};_gc .DrawBezierPathWithCreator (_bffcd ,_aeeaca );_dcga .Style .fillStroke (_aeeaca );
_aeeaca .Add_h ();_aeeaca .Add_Q ();};

// SetNumber sets the number of the invoice.
func (_bcef *Invoice )SetNumber (number string )(*InvoiceCell ,*InvoiceCell ){_bcef ._eddg [1].Value =number ;return _bcef ._eddg [0],_bcef ._eddg [1];};

// Wrap wraps the text of the chunk into lines based on its style and the
// specified width.
func (_aage *TextChunk )Wrap (width float64 )([]string ,error ){if int (width )<=0{return []string {_aage .Text },nil ;};var _cbdfd []string ;var _bdfed []rune ;var _gfgge float64 ;var _bgbg []float64 ;_ecdfae :=_aage .Style ;_cacda :=_dd .IsTextWriteDirectionLTR (_aage .Text );
for _ ,_abeff :=range _aage .Text {if _abeff =='\u000A'{_aecdd :=_dd .FormatWriteDirectionLTR (string (_bdfed ),_cacda );_cbdfd =append (_cbdfd ,_fc .TrimRightFunc (_aecdd ,_ec .IsSpace )+string (_abeff ));_bdfed =nil ;_gfgge =0;_bgbg =nil ;continue ;};
_ddgcf :=_abeff ==' ';_fecce ,_fgfb :=_ecdfae .Font .GetRuneMetrics (_abeff );if !_fgfb {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0052\u0075\u006e\u0065\u0020\u0063\u0068\u0061\u0072\u0020\u006det\u0072i\u0063\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064!\u0020\u0072\u0075\u006e\u0065\u003d\u0030\u0078\u0025\u0030\u0034\u0078\u003d\u0025\u0063\u0020\u0066o\u006e\u0074\u003d\u0025\u0073\u0020\u0025\u0023\u0071",_abeff ,_abeff ,_ecdfae .Font .BaseFont (),_ecdfae .Font .Subtype ());
_a .Log .Trace ("\u0046o\u006e\u0074\u003a\u0020\u0025\u0023v",_ecdfae .Font );_a .Log .Trace ("\u0045\u006e\u0063o\u0064\u0065\u0072\u003a\u0020\u0025\u0023\u0076",_ecdfae .Font .Encoder ());return nil ,_ee .New ("\u0067\u006c\u0079\u0070\u0068\u0020\u0063\u0068\u0061\u0072\u0020m\u0065\u0074\u0072\u0069\u0063\u0073\u0020\u006d\u0069\u0073s\u0069\u006e\u0067");
};_gfcg :=_ecdfae .FontSize *_fecce .Wx ;_gfbe :=_gfcg ;if !_ddgcf {_gfbe =_gfcg +_ecdfae .CharSpacing *1000.0;};if _gfgge +_gfcg > width *1000.0{_acdaa :=-1;if !_ddgcf {for _debffa :=len (_bdfed )-1;_debffa >=0;_debffa --{if _bdfed [_debffa ]==' '{_acdaa =_debffa ;
break ;};};};_bbdfe :=string (_bdfed );if _acdaa > 0{_bbdfe =string (_bdfed [0:_acdaa +1]);_bdfed =append (_bdfed [_acdaa +1:],_abeff );_bgbg =append (_bgbg [_acdaa +1:],_gfbe );_gfgge =0;for _ ,_ddbb :=range _bgbg {_gfgge +=_ddbb ;};}else {if _ddgcf {_bdfed =[]rune {};
_bgbg =[]float64 {};_gfgge =0;}else {_bdfed =[]rune {_abeff };_bgbg =[]float64 {_gfbe };_gfgge =_gfbe ;};};_bbdfe =_dd .FormatWriteDirectionLTR (_bbdfe ,_cacda );_cbdfd =append (_cbdfd ,_fc .TrimRightFunc (_bbdfe ,_ec .IsSpace ));}else {_bdfed =append (_bdfed ,_abeff );
_gfgge +=_gfbe ;_bgbg =append (_bgbg ,_gfbe );};};if len (_bdfed )> 0{_faabd :=string (_bdfed );_faabd =_dd .FormatWriteDirectionLTR (_faabd ,_cacda );_cbdfd =append (_cbdfd ,_faabd );};return _cbdfd ,nil ;};

// Style returns the style of the line.
func (_bcbc *Line )Style ()_gc .LineStyle {return _bcbc ._efabg };

// BorderOpacity returns the border opacity of the ellipse (0-1).
func (_bcgb *Ellipse )BorderOpacity ()float64 {return _bcgb ._deee };

// AddPatternResource adds pattern dictionary inside the resources dictionary.
func (_ccbad *RadialShading )AddPatternResource (block *Block )(_gfdcb _dd .PdfObjectName ,_eefag error ){_ccbd :=1;_ecefc :=_dd .PdfObjectName ("\u0050"+_fbf .Itoa (_ccbd ));for block ._faa .HasPatternByName (_ecefc ){_ccbd ++;_ecefc =_dd .PdfObjectName ("\u0050"+_fbf .Itoa (_ccbd ));
};if _eadec :=block ._faa .SetPatternByName (_ecefc ,_ccbad .ToPdfShadingPattern ().ToPdfObject ());_eadec !=nil {return "",_eadec ;};return _ecefc ,nil ;};func _cgcba (_cageag string )(*_db .PdfFont ,error ){_bfgb ,_ggdd :=map[string ]_db .StdFontName {"\u0063o\u0075\u0072\u0069\u0065\u0072":_db .CourierName ,"\u0063\u006f\u0075r\u0069\u0065\u0072\u002d\u0062\u006f\u006c\u0064":_db .CourierBoldName ,"\u0063o\u0075r\u0069\u0065\u0072\u002d\u006f\u0062\u006c\u0069\u0071\u0075\u0065":_db .CourierObliqueName ,"c\u006fu\u0072\u0069\u0065\u0072\u002d\u0062\u006f\u006cd\u002d\u006f\u0062\u006ciq\u0075\u0065":_db .CourierBoldObliqueName ,"\u0068e\u006c\u0076\u0065\u0074\u0069\u0063a":_db .HelveticaName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0062\u006f\u006c\u0064":_db .HelveticaBoldName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u006f\u0062l\u0069\u0071\u0075\u0065":_db .HelveticaObliqueName ,"\u0068\u0065\u006c\u0076et\u0069\u0063\u0061\u002d\u0062\u006f\u006c\u0064\u002d\u006f\u0062\u006c\u0069\u0071u\u0065":_db .HelveticaBoldObliqueName ,"\u0073\u0079\u006d\u0062\u006f\u006c":_db .SymbolName ,"\u007a\u0061\u0070\u0066\u002d\u0064\u0069\u006e\u0067\u0062\u0061\u0074\u0073":_db .ZapfDingbatsName ,"\u0074\u0069\u006de\u0073":_db .TimesRomanName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064":_db .TimesBoldName ,"\u0074\u0069\u006de\u0073\u002d\u0069\u0074\u0061\u006c\u0069\u0063":_db .TimesItalicName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064\u002d\u0069t\u0061\u006c\u0069\u0063":_db .TimesBoldItalicName }[_cageag ];
if !_ggdd {return nil ,_g .Errorf ("\u0066\u006f\u006e\u0074\u002df\u0061\u006d\u0069\u006c\u0079\u0020\u0025\u0073\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u006c\u0069\u0073t",_cageag );
};_gdaeg ,_bcgg :=_db .NewStandard14Font (_bfgb );if _bcgg !=nil {return nil ,_bcgg ;};return _gdaeg ,nil ;};

// Positioning returns the type of positioning the line is set to use.
func (_becae *Line )Positioning ()Positioning {return _becae ._eddaa };var (_bfefc =_bc .MustCompile ("\u0028[\u005cw\u002d\u005d\u002b\u0029\u005c(\u0027\u0028.\u002b\u0029\u0027\u005c\u0029");_adbad =_ee .New ("\u0069\u006e\u0076\u0061\u006c\u0069d\u0020\u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0063\u0072\u0065a\u0074\u006f\u0072\u0020\u0069\u006e\u0073t\u0061\u006e\u0063\u0065");
_bbcfc =_ee .New ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0074\u0065\u006d\u0070\u006c\u0061\u0074e\u0020p\u0061\u0072\u0065\u006e\u0074\u0020\u006eo\u0064\u0065");_gcaaa =_ee .New ("i\u006e\u0076\u0061\u006c\u0069\u0064 \u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020c\u0068\u0069\u006cd\u0020n\u006f\u0064\u0065");
_cbabd =_ee .New ("\u0069n\u0076\u0061\u006c\u0069d\u0020\u0074\u0065\u006d\u0070l\u0061t\u0065 \u0072\u0065\u0073\u006f\u0075\u0072\u0063e"););func (_acfc *GraphicSVGElement )drawEllipse (_dece *_eg .ContentCreator ,_bfbc *_db .PdfPageResources ){_dece .Add_q ();
_acfc .Style .toContentStream (_dece ,_bfbc ,_acfc );_ebgee ,_caba :=_cacac (_acfc .Attributes ["\u0063\u0078"],64);if _caba !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_caba .Error ());
};_cdeea ,_caba :=_cacac (_acfc .Attributes ["\u0063\u0079"],64);if _caba !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_caba .Error ());
};_eedb ,_caba :=_cacac (_acfc .Attributes ["\u0072\u0078"],64);if _caba !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_caba .Error ());
};_acdde ,_caba :=_cacac (_acfc .Attributes ["\u0072\u0079"],64);if _caba !=nil {_a .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_caba .Error ());
};_dceg :=_eedb *_acfc ._aece ;_eddaaf :=_acdde *_acfc ._aece ;_befeb :=_ebgee *_acfc ._aece ;_accf :=_cdeea *_acfc ._aece ;_gfbag :=_dceg *_aebg ;_dgecb :=_eddaaf *_aebg ;_aegaf :=_gc .NewCubicBezierPath ();_aegaf =_aegaf .AppendCurve (_gc .NewCubicBezierCurve (-_dceg ,0,-_dceg ,_dgecb ,-_gfbag ,_eddaaf ,0,_eddaaf ));
_aegaf =_aegaf .AppendCurve (_gc .NewCubicBezierCurve (0,_eddaaf ,_gfbag ,_eddaaf ,_dceg ,_dgecb ,_dceg ,0));_aegaf =_aegaf .AppendCurve (_gc .NewCubicBezierCurve (_dceg ,0,_dceg ,-_dgecb ,_gfbag ,-_eddaaf ,0,-_eddaaf ));_aegaf =_aegaf .AppendCurve (_gc .NewCubicBezierCurve (0,-_eddaaf ,-_gfbag ,-_eddaaf ,-_dceg ,-_dgecb ,-_dceg ,0));
_aegaf =_aegaf .Offset (_befeb ,_accf );if _acfc .Style .StrokeWidth > 0{_aegaf =_aegaf .Offset (_acfc .Style .StrokeWidth /2,_acfc .Style .StrokeWidth /2);};_gc .DrawBezierPathWithCreator (_aegaf ,_dece );_acfc .Style .fillStroke (_dece );_dece .Add_h ();
_dece .Add_Q ();};

// Line defines a line between point 1 (X1, Y1) and point 2 (X2, Y2).
// The line width, color, style (solid or dashed) and opacity can be
// configured. Implements the Drawable interface.
type Line struct{_ggdbc float64 ;_geega float64 ;_cacg float64 ;_degba float64 ;_feedb Color ;_efabg _gc .LineStyle ;_ffade float64 ;_feda []int64 ;_fafgc int64 ;_gbgbb float64 ;_eddaa Positioning ;_gagab FitMode ;_fffd Margins ;_eegb *_db .StructureTagInfo ;
};

// GetMargins returns the margins of the line: left, right, top, bottom.
func (_cafce *Line )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _cafce ._fffd .Left ,_cafce ._fffd .Right ,_cafce ._fffd .Top ,_cafce ._fffd .Bottom ;};

// SetBorderWidth sets the border width of the rectangle.
func (_ddbf *Rectangle )SetBorderWidth (bw float64 ){_ddbf ._dfbcd =bw };

// SetMarkedContentID sets marked content ID.
func (_bbca *GraphicSVG )SetMarkedContentID (mcid int64 ){if _bbca ._fdcf ==nil {_bbca ._fdcf =_db .NewStructureTagInfo ();_bbca ._fdcf .StructureType =_db .StructureTypeFigure ;};_bbca ._fdcf .Mcid =mcid ;};

// CellBorderSide defines the table cell's border side.
type CellBorderSide int ;

// DrawContext defines the drawing context. The DrawContext is continuously used and updated when
// drawing the page contents in relative mode.  Keeps track of current X, Y position, available
// height as well as other page parameters such as margins and dimensions.
type DrawContext struct{

// Current page number.
Page int ;

// Current position.  In a relative positioning mode, a drawable will be placed at these coordinates.
X ,Y float64 ;

// Context dimensions.  Available width and height (on current page).
Width ,Height float64 ;

// Page Margins.
Margins Margins ;

// Absolute Page size, widths and height.
PageWidth float64 ;PageHeight float64 ;

// Controls whether the components are stacked horizontally
Inline bool ;_bcbgg rune ;_aeag []error ;};

// SetFillOpacity sets the fill opacity of the rectangle.
func (_gage *Rectangle )SetFillOpacity (opacity float64 ){_gage ._dfecg =opacity };

// SetTextExpansion sets the text expansion for the text chunk.
func (_agaff *TextChunk )SetTextExpansion (text string ){_agaff ._aabc =&text };func (_dbfg *pageTransformations )transformBlock (_fddb *Block ){if _dbfg ._ddae !=nil {_fddb .transform (*_dbfg ._ddae );};};

// LinearShading holds data for rendering a linear shading gradient.
type LinearShading struct{_afdb *shading ;_effcg *_db .PdfRectangle ;_bfdd float64 ;};

// SetBorderLineStyle sets border style (currently dashed or plain).
func (_ggdg *TableCell )SetBorderLineStyle (style _gc .LineStyle ){_ggdg ._cdadf =style };

// ScaleToHeight scales the Block to a specified height, maintaining the same aspect ratio.
func (_dba *Block )ScaleToHeight (h float64 ){_dbd :=h /_dba ._dbe ;_dba .Scale (_dbd ,_dbd )};

// Width returns the width of the line.
// NOTE: Depending on the fit mode the line is set to use, its width may be
// calculated at runtime (e.g. when using FitModeFillWidth).
func (_cbca *Line )Width ()float64 {return _fa .Abs (_cbca ._cacg -_cbca ._ggdbc )};

// GeneratePageBlocks generate the Page blocks. Multiple blocks are generated
// if the contents wrap over multiple pages.
func (_cfga *List )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var _dedd float64 ;var _gdedg []*StyledParagraph ;for _ ,_cadd :=range _cfga ._bbbe {_efcc :=_gcefe (_cfga ._afeff );_efcc .SetEnableWrap (false );_efcc .SetTextAlignment (TextAlignmentRight );
_efcc .Append (_cadd ._daedg .Text ).Style =_cadd ._daedg .Style ;_caebf :=_efcc .getTextWidth ()/1000.0/ctx .Width ;if _dedd < _caebf {_dedd =_caebf ;};_gdedg =append (_gdedg ,_efcc );};_edfdf :=_cgdd (2);_edfdf .SetColumnWidths (_dedd ,1-_dedd );_edfdf .SetMargins (_cfga ._ddggd .Left +_cfga ._gagc ,_cfga ._ddggd .Right ,_cfga ._ddggd .Top ,_cfga ._ddggd .Bottom );
_edfdf .EnableRowWrap (true );for _ddee ,_dced :=range _cfga ._bbbe {_cfag :=_edfdf .NewCell ();_cfag .SetIndent (0);_cfag .SetContent (_gdedg [_ddee ]);_cfag =_edfdf .NewCell ();_cfag .SetIndent (0);_cfag .SetContent (_dced ._agafc );};return _edfdf .GeneratePageBlocks (ctx );
};

// ColorPoint is a pair of Color and a relative point where the color
// would be rendered.
type ColorPoint struct{_efgba Color ;_acbfb float64 ;};

// SetMarkedContentID sets marked content ID.
func (_bgce *Polyline )SetMarkedContentID (mcid int64 ){if _bgce ._bafaf ==nil {_bgce ._bafaf =_db .NewStructureTagInfo ();};_bgce ._bafaf .Mcid =mcid ;};func (_ggfed *TableCell )width (_fbaf []float64 ,_ddfgf float64 )float64 {_abcbe :=float64 (0.0);for _cbbff :=0;
_cbbff < _ggfed ._edece ;_cbbff ++{_abcbe +=_fbaf [_ggfed ._afdac +_cbbff -1];};return _abcbe *_ddfgf ;};

// GenerateKDict generates a K dictionary for the graphic svg component.
func (_affc *GraphicSVG )GenerateKDict ()(*_db .KDict ,error ){if _affc ._fdcf ==nil {return nil ,_g .Errorf ("\u0063\u0065\u006c\u006c\u0020\u0073\u0074\u0072\u0075\u0063t\u0075\u0072\u0065\u0020\u0069\u006e\u0066o\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _affc ._fdcf .GenerateKDict (),nil ;};

// Scale scales Image by a constant factor, both width and height.
func (_aabd *Image )Scale (xFactor ,yFactor float64 ){_aabd ._bbg =xFactor *_aabd ._bbg ;_aabd ._fcbd =yFactor *_aabd ._fcbd ;};

// DrawFooter sets a function to draw a footer on created output pages.
func (_fde *Creator )DrawFooter (drawFooterFunc func (_ged *Block ,_ggbdb FooterFunctionArgs )){_fde ._dfe =drawFooterFunc ;};

// GetIndent get the cell's left indent.
func (_bgbce *TableCell )GetIndent ()float64 {return _bgbce ._eggag };

// SetHeight sets the Image's document height to specified h.
func (_gded *Image )SetHeight (h float64 ){_gded ._fcbd =h };

// SetStructureType sets the structure type for the paragraph.
func (_dbdf *Paragraph )SetStructureType (structureType _db .StructureType ){if _dbdf ._dfec ==nil {_dbdf ._dfec =_db .NewStructureTagInfo ();};_dbdf ._dfec .StructureType =structureType ;};

// SetMarkedContentID sets marked content ID.
func (_dgbb *TOC )SetMarkedContentID (mcid int64 ){};

// Date returns the invoice date description and value cells.
// The returned values can be used to customize the styles of the cells.
func (_cfcdb *Invoice )Date ()(*InvoiceCell ,*InvoiceCell ){return _cfcdb ._fgeaf [0],_cfcdb ._fgeaf [1]};

// NewColumn returns a new column for the line items invoice table.
func (_fadgc *Invoice )NewColumn (description string )*InvoiceCell {return _fadgc .newColumn (description ,CellHorizontalAlignmentLeft );};

// FitMode returns the fit mode of the line.
func (_cdab *Line )FitMode ()FitMode {return _cdab ._gagab };

// SetColorLeft sets border color for left.
func (_fec *border )SetColorLeft (col Color ){_fec ._decd =col };

// SetEncoder sets the encoding/compression mechanism for the image.
func (_fffg *Image )SetEncoder (encoder _dd .StreamEncoder ){_fffg ._fagc =encoder };func _ffgg (_eddfd ,_ebdg TextStyle )*Invoice {_bceea :=&Invoice {_gfegb :"\u0049N\u0056\u004f\u0049\u0043\u0045",_agae :"\u002c\u0020",_bcag :_eddfd ,_fcfb :_ebdg };_bceea ._eeddc =&InvoiceAddress {Separator :_bceea ._agae };
_bceea ._egde =&InvoiceAddress {Heading :"\u0042i\u006c\u006c\u0020\u0074\u006f",Separator :_bceea ._agae };_cbag :=ColorRGBFrom8bit (245,245,245);_gdfcg :=ColorRGBFrom8bit (155,155,155);_bceea ._gecfa =_ebdg ;_bceea ._gecfa .Color =_gdfcg ;_bceea ._gecfa .FontSize =20;
_bceea ._eefg =_eddfd ;_bceea ._adbac =_ebdg ;_bceea ._afafg =_eddfd ;_bceea ._bgcc =_ebdg ;_bceea ._eeeg =_bceea .NewCellProps ();_bceea ._eeeg .BackgroundColor =_cbag ;_bceea ._eeeg .TextStyle =_ebdg ;_bceea ._ebac =_bceea .NewCellProps ();_bceea ._ebac .TextStyle =_ebdg ;
_bceea ._ebac .BackgroundColor =_cbag ;_bceea ._ebac .BorderColor =_cbag ;_bceea ._cbdg =_bceea .NewCellProps ();_bceea ._cbdg .BorderColor =_cbag ;_bceea ._cbdg .BorderSides =[]CellBorderSide {CellBorderSideBottom };_bceea ._cbdg .Alignment =CellHorizontalAlignmentRight ;
_bceea ._gdbce =_bceea .NewCellProps ();_bceea ._gdbce .Alignment =CellHorizontalAlignmentRight ;_bceea ._eddg =[2]*InvoiceCell {_bceea .newCell ("\u0049\u006e\u0076\u006f\u0069\u0063\u0065\u0020\u006eu\u006d\u0062\u0065\u0072",_bceea ._eeeg ),_bceea .newCell ("",_bceea ._eeeg )};
_bceea ._fgeaf =[2]*InvoiceCell {_bceea .newCell ("\u0044\u0061\u0074\u0065",_bceea ._eeeg ),_bceea .newCell ("",_bceea ._eeeg )};_bceea ._fbbc =[2]*InvoiceCell {_bceea .newCell ("\u0044\u0075\u0065\u0020\u0044\u0061\u0074\u0065",_bceea ._eeeg ),_bceea .newCell ("",_bceea ._eeeg )};
_bceea ._ageb =[2]*InvoiceCell {_bceea .newCell ("\u0053\u0075\u0062\u0074\u006f\u0074\u0061\u006c",_bceea ._gdbce ),_bceea .newCell ("",_bceea ._gdbce )};_gdebe :=_bceea ._gdbce ;_gdebe .TextStyle =_ebdg ;_gdebe .BackgroundColor =_cbag ;_gdebe .BorderColor =_cbag ;
_bceea ._adgc =[2]*InvoiceCell {_bceea .newCell ("\u0054\u006f\u0074a\u006c",_gdebe ),_bceea .newCell ("",_gdebe )};_bceea ._egfgg =[2]string {"\u004e\u006f\u0074e\u0073",""};_bceea ._ddgeg =[2]string {"T\u0065r\u006d\u0073\u0020\u0061\u006e\u0064\u0020\u0063o\u006e\u0064\u0069\u0074io\u006e\u0073",""};
_bceea ._egfdb =[]*InvoiceCell {_bceea .newColumn ("D\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u0069\u006f\u006e",CellHorizontalAlignmentLeft ),_bceea .newColumn ("\u0051\u0075\u0061\u006e\u0074\u0069\u0074\u0079",CellHorizontalAlignmentRight ),_bceea .newColumn ("\u0055\u006e\u0069\u0074\u0020\u0070\u0072\u0069\u0063\u0065",CellHorizontalAlignmentRight ),_bceea .newColumn ("\u0041\u006d\u006f\u0075\u006e\u0074",CellHorizontalAlignmentRight )};
return _bceea ;};

// SetMakedContentID sets the marked content id for the table.
func (_gfff *Table )SetMarkedContentID (mcid int64 ){if _gfff ._fefdd ==nil {_gfff ._fefdd =_db .NewStructureTagInfo ();_gfff ._fefdd .StructureType =_db .StructureTypeTable ;};_gfff ._fefdd .Mcid =mcid ;};func (_fbabf *GraphicSVGElement )drawText (_ggfbe *_eg .ContentCreator ,_cged *_db .PdfPageResources ){_ggfbe .Add_BT ();
_fedef ,_fcdbd :=_cacac (_fbabf .Attributes ["\u0078"],64);if _fcdbd !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0078\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_fcdbd .Error ());
};_acdb ,_fcdbd :=_cacac (_fbabf .Attributes ["\u0079"],64);if _fcdbd !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0079\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_fcdbd .Error ());
};_beged :=_fbabf .Attributes ["\u0066\u0069\u006c\u006c"];var _dcae ,_dface ,_gcgdg float64 ;if _becbc ,_bdbdf :=_ba .ColorMap [_beged ];_bdbdf {_gagfg ,_affe ,_fbcfc ,_ :=_becbc .RGBA ();_dcae ,_dface ,_gcgdg =float64 (_gagfg ),float64 (_affe ),float64 (_fbcfc );
}else if _fc .HasPrefix (_beged ,"\u0072\u0067\u0062\u0028"){_dcae ,_dface ,_gcgdg =_badge (_beged );}else {_dcae ,_dface ,_gcgdg =ColorRGBFromHex (_beged ).ToRGB ();};_ggfbe .Add_rg (_dcae ,_dface ,_gcgdg );_bdgfe :=_fad ;if _gfec ,_dcdac :=_fbabf .Attributes ["\u0066o\u006e\u0074\u002d\u0073\u0069\u007ae"];
_dcdac {_bdgfe ,_fcdbd =_fbf .ParseFloat (_gfec ,64);if _fcdbd !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067 \u0060\u0066\u006f\u006e\u0074\u002d\u0073\u0069\u007a\u0065\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_fcdbd .Error ());
_bdgfe =_fad ;};};_cgggc :=_fbabf ._aece *_bdgfe *PPI /_ddfb ;_fgeae :=_dd .PdfObjectName ("\u0053\u0046\u006fn\u0074");_ffbc :=_db .DefaultFont ();_bada ,_fedg :=_fbabf .Attributes ["f\u006f\u006e\u0074\u002d\u0066\u0061\u006d\u0069\u006c\u0079"];if _fedg {if _eefcb ,_dbfea :=_cgcba (_bada );
_dbfea ==nil {_ffbc =_eefcb ;_bceec :=1;for _cged .HasFontByName (_fgeae ){_fgeae =_dd .PdfObjectName ("\u0053\u0046\u006fn\u0074"+_fbf .Itoa (_bceec ));_bceec ++;};};};_gebe :=0.0;_eddcc ,_fedg :=_fbabf .Attributes ["t\u0065\u0078\u0074\u002d\u0061\u006e\u0063\u0068\u006f\u0072"];
if _fedg &&_eddcc !="\u0073\u0074\u0061r\u0074"{var _eedfda float64 ;for _ ,_cgfcb :=range _fbabf .Content {_aeabd ,_gdca :=_ffbc .GetRuneMetrics (_cgfcb );if !_gdca {_a .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074e\u0064 \u0072u\u006e\u0065\u0020\u0025\u0076\u0020\u0069\u006e\u0020\u0066\u006f\u006e\u0074",_cgfcb );
};_eedfda +=_aeabd .Wx ;};_eedfda =_eedfda *_cgggc /1000.0;if _eddcc =="\u006d\u0069\u0064\u0064\u006c\u0065"{_gebe =-_eedfda /2;}else if _eddcc =="\u0065\u006e\u0064"{_gebe =-_eedfda ;};};_ggfbe .Add_Tm (1,0,0,-1,_fedef *_fbabf ._aece +_gebe ,_acdb *_fbabf ._aece );
_cged .SetFontByName (_fgeae ,_ffbc .ToPdfObject ());_ggfbe .Add_Tf (_fgeae ,_cgggc );_fefae :=_fbabf .Content ;_gdafb :=_dd .MakeString (_fefae );_ggfbe .Add_Tj (*_gdafb );_ggfbe .Add_ET ();};func (_baed *GraphicSVGElement )drawRect (_cgge *_eg .ContentCreator ,_bebfg *_db .PdfPageResources ){_cgge .Add_q ();
_baed .Style .toContentStream (_cgge ,_bebfg ,_baed );_ggea ,_geed :=_cacac (_baed .Attributes ["\u0078"],64);if _geed !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0078\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_geed .Error ());
};_cbbb ,_geed :=_cacac (_baed .Attributes ["\u0079"],64);if _geed !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0079\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_geed .Error ());
};_bfdac ,_geed :=_cacac (_baed .Attributes ["\u0077\u0069\u0064t\u0068"],64);if _geed !=nil {_a .Log .Debug ("\u0045\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0073\u0074\u0072\u006f\u006b\u0065\u0020\u0077\u0069\u0064\u0074\u0068\u0020v\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_geed .Error ());
};_dgbg ,_geed :=_cacac (_baed .Attributes ["\u0068\u0065\u0069\u0067\u0068\u0074"],64);if _geed !=nil {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077h\u0069\u006c\u0065 \u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0073\u0074\u0072\u006f\u006b\u0065\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_geed .Error ());
};_cgge .Add_re (_ggea *_baed ._aece ,_cbbb *_baed ._aece ,_bfdac *_baed ._aece ,_dgbg *_baed ._aece );_baed .Style .fillStroke (_cgge );_cgge .Add_Q ();};

// NewCurvePolygon creates a new curve polygon.
func (_gcef *Creator )NewCurvePolygon (rings [][]_gc .CubicBezierCurve )*CurvePolygon {return _bfg (rings );};

// SetOpacity sets the opacity of the line (0-1).
func (_gefce *Line )SetOpacity (opacity float64 ){_gefce ._ffade =opacity };

// NewGraphicSVGFromFile creates a graphic SVG from a file.
func NewGraphicSVGFromFile (path string )(*GraphicSVG ,error ){return _ecdf (path )};

// SetBorder sets the cell's border style.
func (_feccf *GridCell )SetBorder (side CellBorderSide ,style CellBorderStyle ,width float64 ){if style ==CellBorderStyleSingle &&side ==CellBorderSideAll {_feccf ._bddf =CellBorderStyleSingle ;_feccf ._gffc =width ;_feccf ._eaeb =CellBorderStyleSingle ;
_feccf ._acbe =width ;_feccf ._eefb =CellBorderStyleSingle ;_feccf ._feee =width ;_feccf ._egca =CellBorderStyleSingle ;_feccf ._bfcc =width ;}else if style ==CellBorderStyleDouble &&side ==CellBorderSideAll {_feccf ._bddf =CellBorderStyleDouble ;_feccf ._gffc =width ;
_feccf ._eaeb =CellBorderStyleDouble ;_feccf ._acbe =width ;_feccf ._eefb =CellBorderStyleDouble ;_feccf ._feee =width ;_feccf ._egca =CellBorderStyleDouble ;_feccf ._bfcc =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideLeft {_feccf ._bddf =style ;
_feccf ._gffc =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideBottom {_feccf ._eaeb =style ;_feccf ._acbe =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideRight {_feccf ._eefb =style ;
_feccf ._feee =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideTop {_feccf ._egca =style ;_feccf ._bfcc =width ;};};

// SetBackgroundColor sets the cell's background color.
func (_fabgg *TableCell )SetBackgroundColor (col Color ){_fabgg ._bfcef =col };

// SetLineStyle sets the style for all the line components: number, title,
// separator, page. The style is applied only for new lines added to the
// TOC component.
func (_gaec *TOC )SetLineStyle (style TextStyle ){_gaec .SetLineNumberStyle (style );_gaec .SetLineTitleStyle (style );_gaec .SetLineSeparatorStyle (style );_gaec .SetLinePageStyle (style );};

// This method is not supported by Chapter component and exists solely to satisfy the Drawable interface.
func (_aea *Chapter )SetMarkedContentID (id int64 ){};

// Background contains properties related to the background of a component.
type Background struct{FillColor Color ;BorderColor Color ;BorderSize float64 ;BorderRadiusTopLeft float64 ;BorderRadiusTopRight float64 ;BorderRadiusBottomLeft float64 ;BorderRadiusBottomRight float64 ;};func _egcgc (_cadf string )(*Image ,error ){_cecf ,_eggf :=_eb .Open (_cadf );
if _eggf !=nil {return nil ,_eggf ;};defer _cecf .Close ();_gaad ,_eggf :=_db .ImageHandling .Read (_cecf );if _eggf !=nil {_a .Log .Error ("\u0045\u0072\u0072or\u0020\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_eggf );
return nil ,_eggf ;};return _cefg (_gaad );};

// Height returns the height of the graphic svg.
func (_bfgf *GraphicSVG )Height ()float64 {return _bfgf ._bdca .Height };

// SetContent sets the cell's content.  The content is a VectorDrawable, i.e.
// a Drawable with a known height and width.
// Currently supported VectorDrawables:
// - *Paragraph
// - *StyledParagraph
// - *Image
// - *Chart
// - *Table
// - *Division
// - *List
// - *Rectangle
// - *Ellipse
// - *Line
func (_gbbeg *GridCell )SetContent (vd VectorDrawable )error {switch _dcacd :=vd .(type ){case *Paragraph :if _dcacd ._gcfc {_dcacd ._gcdc =true ;};_gbbeg ._cdaac =vd ;case *StyledParagraph :if _dcacd ._afdg {_dcacd ._gfce =true ;};_gbbeg ._cdaac =vd ;
case *Image ,*Chart ,*Table ,*Division ,*List ,*Rectangle ,*Ellipse ,*Line :_gbbeg ._cdaac =vd ;default:_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0063e\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0079p\u0065\u0020\u0025\u0054",vd );
return _dd .ErrTypeError ;};return nil ;};func _cdfe (_ggdb Color )_db .PdfColor {if _ggdb ==nil {_ggdb =ColorBlack ;};switch _faed :=_ggdb .(type ){case grayColor :return _db .NewPdfColorDeviceGray (_faed ._dfa );case cmykColor :return _db .NewPdfColorDeviceCMYK (_faed ._dgfd ,_faed ._dcb ,_faed ._eead ,_faed ._edb );
case *LinearShading :return _db .NewPdfColorPatternType2 ();case *RadialShading :return _db .NewPdfColorPatternType3 ();};return _db .NewPdfColorDeviceRGB (_ggdb .ToRGB ());};

// Reset removes all the text chunks the paragraph contains.
func (_eegg *StyledParagraph )Reset (){_eegg ._dgaca =[]*TextChunk {}};

// CellVerticalAlignment defines the table cell's vertical alignment.
type CellVerticalAlignment int ;

// AddInfo is used to append a piece of invoice information in the template
// information table.
func (_dbcg *Invoice )AddInfo (description ,value string )(*InvoiceCell ,*InvoiceCell ){_abgde :=[2]*InvoiceCell {_dbcg .newCell (description ,_dbcg ._eeeg ),_dbcg .newCell (value ,_dbcg ._eeeg )};_dbcg ._febf =append (_dbcg ._febf ,_abgde );return _abgde [0],_abgde [1];
};func _acec (_geggd *templateProcessor ,_dbaef *templateNode )(interface{},error ){return _geggd .parseBackground (_dbaef );};func _efaca (_gdaee []*Command )*Path {_dfba :=&Path {};var _bcgac []*Command ;for _bgdef ,_ddff :=range _gdaee {switch _fc .ToLower (_ddff .Symbol ){case _gfdbca ._ggfec :if len (_bcgac )> 0{_dfba .Subpaths =append (_dfba .Subpaths ,&Subpath {_bcgac });
};_bcgac =[]*Command {_ddff };case _gfdbca ._gfggb :_bcgac =append (_bcgac ,_ddff );_dfba .Subpaths =append (_dfba .Subpaths ,&Subpath {_bcgac });_bcgac =[]*Command {};default:_bcgac =append (_bcgac ,_ddff );if len (_gdaee )==_bgdef +1{_dfba .Subpaths =append (_dfba .Subpaths ,&Subpath {_bcgac });
};};};return _dfba ;};

// SetPositioning sets the positioning of the rectangle (absolute or relative).
func (_bbda *Rectangle )SetPositioning (position Positioning ){_bbda ._fbbdf =position };type grayColor struct{_dfa float64 };func _gdbg ()*GraphicSVGStyle {return &GraphicSVGStyle {FillColor :"\u00230\u0030\u0030\u0030\u0030\u0030",StrokeColor :"",StrokeWidth :0,FillOpacity :1.0};
};func _abgdb (_debd _ea .Image )(*Image ,error ){_aebc ,_ddfge :=_db .ImageHandling .NewImageFromGoImage (_debd );if _ddfge !=nil {return nil ,_ddfge ;};return _cefg (_aebc );};

// ColorGrayFromHex converts color hex code to gray color for using with creator.
// NOTE: If there is a problem interpreting the string, then will use black color and log a debug message.
// Example hex code: #ff -> white.
func ColorGrayFromHex (hexStr string )Color {_dbgb :=grayColor {};if (len (hexStr )!=2&&len (hexStr )!=3)||hexStr [0]!='#'{_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _dbgb ;
};var _caff int ;if len (hexStr )==2{var _ffcc int ;_bbc ,_cfcd :=_g .Sscanf (hexStr ,"\u0023\u0025\u0031\u0078",&_ffcc );if _cfcd !=nil {_a .Log .Debug ("\u0049\u006e\u0076a\u006c\u0069\u0064\u0020h\u0065\u0078\u0020\u0063\u006f\u0064\u0065:\u0020\u0025\u0073\u002c\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",hexStr ,_cfcd );
return _dbgb ;};if _bbc !=1{_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _dbgb ;};_caff =_ffcc *16+_ffcc ;}else {_fgag ,_geag :=_g .Sscanf (hexStr ,"\u0023\u0025\u0032\u0078",&_caff );
if _geag !=nil {_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _dbgb ;};if _fgag !=1{_a .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u0068\u0065\u0078\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0073,\u0020\u006e\u0020\u0021\u003d\u0020\u0033 \u0028\u0025\u0064\u0029",hexStr ,_fgag );
return _dbgb ;};};_dbgb ._dfa =float64 (_caff )/255.0;return _dbgb ;};func _abbgb (_cbbc string )(float64 ,error ){_cbbc =_fc .TrimSpace (_cbbc );var _aaggae float64 ;if _fc .HasSuffix (_cbbc ,"\u0025"){_ggbbg ,_cbffc :=_fbf .ParseFloat (_fc .TrimSuffix (_cbbc ,"\u0025"),64);
if _cbffc !=nil {return 0,_cbffc ;};_aaggae =_ggbbg /100.0;}else {_efddc ,_acefd :=_fbf .ParseFloat (_cbbc ,64);if _acefd !=nil {return 0,_acefd ;};_aaggae =_efddc ;};return _aaggae ,nil ;};

// GetCoords returns the upper left corner coordinates of the rectangle (`x`, `y`).
func (_efda *Rectangle )GetCoords ()(float64 ,float64 ){return _efda ._eeadac ,_efda ._abegd };func (_aegeg *StyledParagraph )getTextHeight ()float64 {var _adfg float64 ;for _ ,_ggace :=range _aegeg ._dgaca {_cbfbe :=_ggace .Style .FontSize *_aegeg ._abbd ;
if _cbfbe > _adfg {_adfg =_cbfbe ;};};return _adfg ;};

// SetSideBorderStyle sets the cell's side border style.
func (_abacc *TableCell )SetSideBorderStyle (side CellBorderSide ,style CellBorderStyle ){switch side {case CellBorderSideAll :_abacc ._eafce =style ;_abacc ._gecgee =style ;_abacc ._cabbe =style ;_abacc ._bfaea =style ;case CellBorderSideTop :_abacc ._eafce =style ;
case CellBorderSideBottom :_abacc ._gecgee =style ;case CellBorderSideLeft :_abacc ._cabbe =style ;case CellBorderSideRight :_abacc ._bfaea =style ;};};

// GeneratePageBlocks generate the Page blocks. Multiple blocks are generated
// if the contents wrap over multiple pages.
func (_ecdd *Invoice )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_ccadf :=ctx ;_dcda :=[]func (_geaac DrawContext )([]*Block ,DrawContext ,error ){_ecdd .generateHeaderBlocks ,_ecdd .generateInformationBlocks ,_ecdd .generateLineBlocks ,_ecdd .generateTotalBlocks ,_ecdd .generateNoteBlocks };
var _cdbge []*Block ;for _ ,_acac :=range _dcda {_geac ,_fbgde ,_fgc :=_acac (ctx );if _fgc !=nil {return _cdbge ,ctx ,_fgc ;};if len (_cdbge )==0{_cdbge =_geac ;}else if len (_geac )> 0{_cdbge [len (_cdbge )-1].mergeBlocks (_geac [0]);_cdbge =append (_cdbge ,_geac [1:]...);
};ctx =_fbgde ;};if _ecdd ._efcbb .IsRelative (){ctx .X =_ccadf .X ;};if _ecdd ._efcbb .IsAbsolute (){return _cdbge ,_ccadf ,nil ;};return _cdbge ,ctx ,nil ;};

// SetFont sets the font for the paragraph.
func (_cbbd *StyledParagraph )SetFont (font *_db .PdfFont ){_cbbd ._acfg .Font =font ;for _ ,_bdac :=range _cbbd ._dgaca {_bdac .Style .Font =font ;};};

// SetStructureInfo sets the structure tag info for the table cell.
func (_bacbe *TableCell )GenerateKDict ()(*_db .KDict ,error ){if _bacbe ._cdffb ==nil {return nil ,_g .Errorf ("\u0063\u0065\u006c\u006c\u0020\u0073\u0074\u0072\u0075\u0063t\u0075\u0072\u0065\u0020\u0069\u006e\u0066o\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _bacbe ._cdffb .GenerateKDict (),nil ;};func (_dgge *templateProcessor )nodeError (_gfdce *templateNode ,_gbdc string ,_ecfcc ...interface{})error {return _g .Errorf ("\u0025\u0073",_dgge .getNodeErrorLocation (_gfdce ,_gbdc ,_ecfcc ...));};

// GraphicSVG represents a drawable graphic SVG.
// It is used to render the graphic SVG components using a creator instance.
type GraphicSVG struct{_bdca *GraphicSVGElement ;_cgd Positioning ;_edbc float64 ;_cbdfc float64 ;_edeb Margins ;_fdcf *_db .StructureTagInfo ;};

// GeneratePageBlocks generate the Page blocks. Multiple blocks are generated
// if the contents wrap over multiple pages.
func (_baee *TOC )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_dcfag :=ctx ;_dedgf ,ctx ,_cbfgf :=_baee ._bcbeb .GeneratePageBlocks (ctx );if _cbfgf !=nil {return _dedgf ,ctx ,_cbfgf ;};for _ ,_ecgbg :=range _baee ._bebaa {_aegae :=_ecgbg ._ageffa ;
if !_baee ._dace {_ecgbg ._ageffa =0;};_cgfeg ,_aegg ,_ffgag :=_ecgbg .GeneratePageBlocks (ctx );_ecgbg ._ageffa =_aegae ;if _ffgag !=nil {return _dedgf ,ctx ,_ffgag ;};if len (_cgfeg )< 1{continue ;};_dedgf [len (_dedgf )-1].mergeBlocks (_cgfeg [0]);_dedgf =append (_dedgf ,_cgfeg [1:]...);
ctx =_aegg ;};if _baee ._bdcca .IsRelative (){ctx .X =_dcfag .X ;};if _baee ._bdcca .IsAbsolute (){return _dedgf ,_dcfag ,nil ;};return _dedgf ,ctx ,nil ;};func (_cbefe *templateProcessor )run ()error {_adad :=_e .NewDecoder (_c .NewReader (_cbefe ._ccgdc ));
var _gfgbg *templateNode ;for {_fbfbdd ,_ggeg :=_adad .Token ();if _ggeg !=nil {if _ggeg ==_cc .EOF {return nil ;};return _ggeg ;};if _fbfbdd ==nil {break ;};_badaa ,_eeaba :=_bgdaf (_adad );_adfebb :=_adad .InputOffset ();switch _gacb :=_fbfbdd .(type ){case _e .StartElement :_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006eg\u0020\u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0073\u0074\u0061r\u0074\u0020\u0074\u0061\u0067\u003a\u0020`\u0025\u0073\u0060\u002e",_gacb .Name .Local );
_eafca ,_ebgba :=_cgacc [_gacb .Name .Local ];if !_ebgba {if _cbefe ._edcc ==""{if _badaa !=0{_a .Log .Debug ("\u0055n\u0073u\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u006dp\u006c\u0061\u0074\u0065 \u0074\u0061\u0067\u0020\u003c%\u0073\u003e\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e\u0020\u005b%\u0064\u003a\u0025\u0064\u005d",_gacb .Name .Local ,_badaa ,_eeaba );
}else {_a .Log .Debug ("\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u0074\u0061\u0067\u0020\u003c\u0025\u0073\u003e\u002e\u0020\u0053\u006b\u0069\u0070\u0070i\u006e\u0067\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072e\u0063\u0074\u002e\u0020\u005b%\u0064\u005d",_gacb .Name .Local ,_adfebb );
};}else {if _badaa !=0{_a .Log .Debug ("\u0055\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0074e\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0074\u0061\u0067\u0020\u003c\u0025\u0073\u003e\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065 \u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e\u0020\u005b%\u0073\u003a\u0025\u0064\u003a\u0025d\u005d",_gacb .Name .Local ,_cbefe ._edcc ,_badaa ,_eeaba );
}else {_a .Log .Debug ("\u0055n\u0073u\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u006dp\u006c\u0061\u0074\u0065 \u0074\u0061\u0067\u0020\u003c%\u0073\u003e\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e\u0020\u005b%\u0073\u003a\u0025\u0064\u005d",_gacb .Name .Local ,_cbefe ._edcc ,_adfebb );
};};continue ;};_gfgbg =&templateNode {_ccge :_gacb ,_gafge :_gfgbg ,_eagge :_badaa ,_adafb :_eeaba ,_abbc :_adfebb };if _deebf :=_eafca ._ecdfa ;_deebf !=nil {_gfgbg ._aeceb ,_ggeg =_deebf (_cbefe ,_gfgbg );if _ggeg !=nil {return _ggeg ;};};case _e .EndElement :_a .Log .Debug ("\u0050\u0061\u0072s\u0069\u006e\u0067\u0020t\u0065\u006d\u0070\u006c\u0061\u0074\u0065 \u0065\u006e\u0064\u0020\u0074\u0061\u0067\u003a\u0020\u0060\u0025\u0073\u0060\u002e",_gacb .Name .Local );
if _gfgbg !=nil {if _gfgbg ._aeceb !=nil {if _egfcg :=_cbefe .renderNode (_gfgbg );_egfcg !=nil {return _egfcg ;};};_gfgbg =_gfgbg ._gafge ;};case _e .CharData :if _gfgbg !=nil &&_gfgbg ._aeceb !=nil {if _fbgf :=_cbefe .addNodeText (_gfgbg ,string (_gacb ));
_fbgf !=nil {return _fbgf ;};};case _e .Comment :_a .Log .Debug ("\u0050\u0061\u0072s\u0069\u006e\u0067\u0020t\u0065\u006d\u0070\u006c\u0061\u0074\u0065 \u0063\u006f\u006d\u006d\u0065\u006e\u0074\u003a\u0020\u0060\u0025\u0073\u0060\u002e",string (_gacb ));
};};return nil ;};type rgbColor struct{_dbbe ,_bggf ,_aec float64 };

// AppendColumn appends a column to the line items table.
func (_abcfee *Invoice )AppendColumn (description string )*InvoiceCell {_gbad :=_abcfee .NewColumn (description );_abcfee ._egfdb =append (_abcfee ._egfdb ,_gbad );return _gbad ;};

// SetForms adds an Acroform to a PDF file.  Sets the specified form for writing.
func (_fbfbd *Creator )SetForms (form *_db .PdfAcroForm )error {_fbfbd ._edbf =form ;return nil };

// SetPageSize sets the Creator's page size.  Pages that are added after this will be created with
// this Page size.
// Does not affect pages already created.
//
// Common page sizes are defined as constants.
// Examples:
// 1. c.SetPageSize(creator.PageSizeA4)
// 2. c.SetPageSize(creator.PageSizeA3)
// 3. c.SetPageSize(creator.PageSizeLegal)
// 4. c.SetPageSize(creator.PageSizeLetter)
//
// For custom sizes: Use the PPMM (points per mm) and PPI (points per inch) when defining those based on
// physical page sizes:
//
// Examples:
// 1. 10x15 sq. mm: SetPageSize(PageSize{10*creator.PPMM, 15*creator.PPMM}) where PPMM is points per mm.
// 2. 3x2 sq. inches: SetPageSize(PageSize{3*creator.PPI, 2*creator.PPI}) where PPI is points per inch.
func (_afff *Creator )SetPageSize (size PageSize ){_afff ._bggg =size ;_afff ._cfgg =size [0];_afff ._adde =size [1];_efa :=0.1*_afff ._cfgg ;_afff ._gecf .Left =_efa ;_afff ._gecf .Right =_efa ;_afff ._gecf .Top =_efa ;_afff ._gecf .Bottom =_efa ;};

// AnchorPoint defines anchor point where the center position of the radial gradient would be calculated.
type AnchorPoint int ;

// SetBorderColor sets the border color of the ellipse.
func (_eeag *Ellipse )SetBorderColor (col Color ){_eeag ._bcgd =col };func _eggb (_fdcac *templateProcessor ,_bdcgd *templateNode )(interface{},error ){return _fdcac .parseListItem (_bdcgd );};

// NewImageFromGoImage creates an Image from a go image.Image data structure.
func (_gdce *Creator )NewImageFromGoImage (goimg _ea .Image )(*Image ,error ){return _abgdb (goimg )};

// String implements error interface.
func (_aedb UnsupportedRuneError )Error ()string {return _aedb .Message };

// This method is not supported by Block component and exists solely to satisfy the Drawable interface.
func (_gf *Block )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };

// ToPdfShadingPattern generates a new model.PdfShadingPatternType2 object.
func (_fgfa *LinearShading )ToPdfShadingPattern ()*_db .PdfShadingPatternType2 {_bfbe ,_aeaea ,_edcgc :=_fgfa ._afdb ._cgbf .ToRGB ();_fcdbc :=_fgfa .shadingModel ();_fcdbc .PdfShading .Background =_dd .MakeArrayFromFloats ([]float64 {_bfbe ,_aeaea ,_edcgc });
_bddca :=_db .NewPdfShadingPatternType2 ();_bddca .Shading =_fcdbc ;return _bddca ;};

// AppendCurve appends a Bezier curve to the filled curve.
func (_ceba *FilledCurve )AppendCurve (curve _gc .CubicBezierCurve )*FilledCurve {_ceba ._bec =append (_ceba ._bec ,curve );return _ceba ;};

// Margins returns the margins of the component.
func (_aeadc *Division )Margins ()(_aeead ,_bcce ,_abgd ,_fgfg float64 ){return _aeadc ._fccaf .Left ,_aeadc ._fccaf .Right ,_aeadc ._fccaf .Top ,_aeadc ._fccaf .Bottom ;};

// This method is not supported by PageBreak component and exists solely to satisfy the Drawable interface.
func (_gbaf *PageBreak )SetStructureType (structureType _db .StructureType ){};

// GeneratePageBlocks draws the polygon on a new block representing the page.
// Implements the Drawable interface.
func (_gfbc *Polygon )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_cccd :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_fedcd ,_gfbfd :=_cccd .setOpacity (_gfbc ._eddfc ,_gfbc ._bbdb );if _gfbfd !=nil {return nil ,ctx ,_gfbfd ;};
_ffada :=_gfbc ._addde ;_ffada .FillEnabled =_ffada .FillColor !=nil ;_ffada .BorderEnabled =_ffada .BorderColor !=nil &&_ffada .BorderWidth > 0;_cdece :=_ffada .Points ;_gfbg :=_db .PdfRectangle {};_ccdffc :=false ;for _gefg :=range _cdece {for _gcgcc :=range _cdece [_gefg ]{_debfb :=&_cdece [_gefg ][_gcgcc ];
_debfb .Y =ctx .PageHeight -_debfb .Y ;if !_ccdffc {_gfbg .Llx =_debfb .X ;_gfbg .Lly =_debfb .Y ;_gfbg .Urx =_debfb .X ;_gfbg .Ury =_debfb .Y ;_ccdffc =true ;}else {_gfbg .Llx =_fa .Min (_gfbg .Llx ,_debfb .X );_gfbg .Lly =_fa .Min (_gfbg .Lly ,_debfb .Y );
_gfbg .Urx =_fa .Max (_gfbg .Urx ,_debfb .X );_gfbg .Ury =_fa .Max (_gfbg .Ury ,_debfb .Y );};};};if _ffada .FillEnabled {_efgg :=_gfae (_cccd ,_gfbc ._addde .FillColor ,_gfbc ._fbef ,func ()Rectangle {return Rectangle {_eeadac :_gfbg .Llx ,_abegd :_gfbg .Lly ,_cdcgg :_gfbg .Width (),_cfedb :_gfbg .Height ()};
});if _efgg !=nil {return nil ,ctx ,_efgg ;};};_fbdefa ,_ ,_gfbfd :=_ffada .MarkedDraw (_fedcd ,_gfbc ._daega );if _gfbfd !=nil {return nil ,ctx ,_gfbfd ;};if _gfbfd =_cccd .addContentsByString (string (_fbdefa ));_gfbfd !=nil {return nil ,ctx ,_gfbfd ;
};return []*Block {_cccd },ctx ,nil ;};const (PositionRelative Positioning =iota ;PositionAbsolute ;);

// GridCell defines a cell which can contain a Drawable as content.
type GridCell struct{_ddgeb Color ;_ffgf float64 ;_ccc _gc .LineStyle ;_bddf CellBorderStyle ;_bdcg Color ;_gffc float64 ;_eaeb CellBorderStyle ;_cbcd Color ;_acbe float64 ;_eefb CellBorderStyle ;_gegc Color ;_feee float64 ;_egca CellBorderStyle ;_edee Color ;
_bfcc float64 ;_cdaac VectorDrawable ;_fcfg ,_gcfa int ;_fcbe int ;_caffg int ;_efab CellHorizontalAlignment ;_fbbd CellVerticalAlignment ;_ddca float64 ;_dadg *_db .StructureTagInfo ;};

// FooterFunctionArgs holds the input arguments to a footer drawing function.
// It is designed as a struct, so additional parameters can be added in the future with backwards
// compatibility.
type FooterFunctionArgs struct{PageNum int ;TotalPages int ;};func (_cfabfd *templateProcessor )parseTextRenderingModeAttr (_geade ,_abaae string )TextRenderingMode {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0074\u0065\u0078\u0074\u0020\u0072\u0065\u006e\u0064\u0065r\u0069\u006e\u0067\u0020\u006d\u006f\u0064e\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a \u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_geade ,_abaae );
_ecbgd :=map[string ]TextRenderingMode {"\u0066\u0069\u006c\u006c":TextRenderingModeFill ,"\u0073\u0074\u0072\u006f\u006b\u0065":TextRenderingModeStroke ,"f\u0069\u006c\u006c\u002d\u0073\u0074\u0072\u006f\u006b\u0065":TextRenderingModeFillStroke ,"\u0069n\u0076\u0069\u0073\u0069\u0062\u006ce":TextRenderingModeInvisible ,"\u0066i\u006c\u006c\u002d\u0063\u006c\u0069p":TextRenderingModeFillClip ,"s\u0074\u0072\u006f\u006b\u0065\u002d\u0063\u006c\u0069\u0070":TextRenderingModeStrokeClip ,"\u0066\u0069l\u006c\u002d\u0073t\u0072\u006f\u006b\u0065\u002d\u0063\u006c\u0069\u0070":TextRenderingModeFillStrokeClip ,"\u0063\u006c\u0069\u0070":TextRenderingModeClip }[_abaae ];
return _ecbgd ;};

// SetColumnWidths sets the fractional column widths.
// Each width should be in the range 0-1 and is a fraction of the table width.
// The number of width inputs must match number of columns, otherwise an error is returned.
func (_aabaab *Grid )SetColumnWidths (widths ...float64 )error {if len (widths )!=_aabaab ._cecb {_a .Log .Debug ("M\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0069\u006e\u0067\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066\u0020\u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0061nd\u0020\u0063\u006fl\u0075m\u006e\u0073");
return _ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");};_aabaab ._daad =widths ;return nil ;};

// AddPage adds the specified page to the creator.
// NOTE: If the page has a Rotate flag, the creator will take care of
// transforming the contents to maintain the correct orientation.
func (_fdf *Creator )AddPage (page *_db .PdfPage )error {_bccf ,_bedd :=_fdf .wrapPageIfNeeded (page );if _bedd !=nil {return _bedd ;};if _bccf !=nil {page =_bccf ;};_edc ,_bedd :=page .GetMediaBox ();if _bedd !=nil {_a .Log .Debug ("\u0046\u0061\u0069l\u0065\u0064\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u0070\u0061g\u0065\u0020\u006d\u0065\u0064\u0069\u0061\u0062\u006f\u0078\u003a\u0020\u0025\u0076",_bedd );
return _bedd ;};_edc .Normalize ();_abee ,_ffcca :=_edc .Llx ,_edc .Lly ;_eacf :=_edc ;if _ccfaa :=page .CropBox ;_ccfaa !=nil &&*_ccfaa !=*_edc {_ccfaa .Normalize ();_abee ,_ffcca =_ccfaa .Llx ,_ccfaa .Lly ;_eacf =_ccfaa ;};_ecad :=_bce .IdentityMatrix ();
_cea ,_bedd :=page .GetRotate ();if _bedd !=nil {_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0025\u0073\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0061\u006e\u0064\u0020\u0061\u0073\u0073\u0075\u006d\u0069\u006e\u0067\u0020\u006e\u006f\u0020\u0072\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u000a",_bedd .Error ());
};_efg :=_cea %360!=0&&_cea %90==0;if _efg {_daf :=float64 ((360+_cea %360)%360);if _daf ==90{_ecad =_ecad .Translate (_eacf .Width (),0);}else if _daf ==180{_ecad =_ecad .Translate (_eacf .Width (),_eacf .Height ());}else if _daf ==270{_ecad =_ecad .Translate (0,_eacf .Height ());
};_ecad =_ecad .Mult (_bce .RotationMatrix (_daf *_fa .Pi /180));_ecad =_ecad .Round (0.000001);_edag :=_aaafa (_eacf ,_ecad );_eacf =_edag ;_eacf .Normalize ();};if _abee !=0||_ffcca !=0{_ecad =_bce .TranslationMatrix (_abee ,_ffcca ).Mult (_ecad );};
if !_ecad .Identity (){_ecad =_ecad .Round (0.000001);_fdf ._fbgd [page ]=&pageTransformations {_ddae :&_ecad };};_fdf ._cfgg =_eacf .Width ();_fdf ._adde =_eacf .Height ();_fdf .initContext ();_fdf ._bcbe =append (_fdf ._bcbe ,page );_fdf ._fcga .Page ++;
return nil ;};func _fcee (_cbgae *_db .PdfAnnotationLink )*_db .PdfAnnotationLink {if _cbgae ==nil {return nil ;};_cfagg :=_db .NewPdfAnnotationLink ();_cfagg .PdfAnnotation =_cbgae .PdfAnnotation ;_cfagg .BS =_cbgae .BS ;_cfagg .A =_cbgae .A ;if _abbcg ,_bbcea :=_cbgae .GetAction ();
_bbcea ==nil &&_abbcg !=nil {_cfagg .SetAction (_abbcg );};if _fdfea ,_bcffa :=_cbgae .Dest .(*_dd .PdfObjectArray );_bcffa {_cfagg .Dest =_dd .MakeArray (_fdfea .Elements ()...);};return _cfagg ;};func (_aegfd pathParserError )Error ()string {return _aegfd ._cfbda };


// Width is not used. The list component is designed to fill into the available
// width depending on the context. Returns 0.
func (_cbde *List )Width ()float64 {return 0};

// SetMargins sets the margins of the line.
// NOTE: line margins are only applied if relative positioning is used.
func (_ddaeb *Line )SetMargins (left ,right ,top ,bottom float64 ){_ddaeb ._fffd .Left =left ;_ddaeb ._fffd .Right =right ;_ddaeb ._fffd .Top =top ;_ddaeb ._fffd .Bottom =bottom ;};func _eedac (_adbcf string )(_bdfeb []float64 ,_decab error ){var _gfcd float64 ;
_bcece :=0;_daac :=true ;for _fbdff ,_fcdbda :=range _adbcf {if _fcdbda =='.'{if _daac {_daac =false ;continue ;};_gfcd ,_decab =_cacac (_adbcf [_bcece :_fbdff ],64);if _decab !=nil {return ;};_bdfeb =append (_bdfeb ,_gfcd );_bcece =_fbdff ;};};_gfcd ,_decab =_cacac (_adbcf [_bcece :],64);
if _decab !=nil {return ;};_bdfeb =append (_bdfeb ,_gfcd );return ;};

// SetVerticalAlignment set the cell's vertical alignment of content.
// Can be one of:
// - CellHorizontalAlignmentTop
// - CellHorizontalAlignmentMiddle
// - CellHorizontalAlignmentBottom
func (_efbc *GridCell )SetVerticalAlignment (valign CellVerticalAlignment ){_efbc ._fbbd =valign };

// SetMarkedContentID sets the marked content id for the line.
func (_gafeb *Line )SetMarkedContentID (mcid int64 ){if _gafeb ._eegb ==nil {_gafeb ._eegb =_db .NewStructureTagInfo ();};_gafeb ._eegb .Mcid =mcid ;};

// AddExternalLinkWithTag adds a new external link to the paragraph with proper tagging for accessibility.
// The text parameter represents the text that is displayed and the url parameter sets the destination.
// The options parameter contains accessibility properties like tooltip, altText, and mcid.
func (_bege *StyledParagraph )AddExternalLinkWithTag (text ,url string ,options LinkTagOptions )(*TextChunk ,*_db .KDict ){_aeaeaf :=_gdfba (url ,options .Tooltip );_gdga ,_efeg ,_eacfa :=_bege .createAccessibleLinkChunk (text ,_aeaeaf ,options );if _eacfa !=nil {_a .Log .Error ("F\u0061\u0069\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u0065\u0078\u0074\u0065\u0072n\u0061\u006c\u0020\u006c\u0069\u006e\u006b\u0020\u0077\u0069th\u0020\u0074\u0061g\u003a \u0025\u0076",_eacfa );
return nil ,nil ;};return _gdga ,_efeg ;};func (_debf *Invoice )generateLineBlocks (_cfae DrawContext )([]*Block ,DrawContext ,error ){_ccee :=_cgdd (len (_debf ._egfdb ));_ccee .SetMargins (0,0,25,0);for _ ,_dedc :=range _debf ._egfdb {_abce :=_gcefe (_dedc .TextStyle );
_abce .SetMargins (0,0,1,0);_abce .Append (_dedc .Value );_adcff :=_ccee .NewCell ();_adcff .SetHorizontalAlignment (_dedc .Alignment );_adcff .SetBackgroundColor (_dedc .BackgroundColor );_debf .setCellBorder (_adcff ,_dedc );_adcff .SetContent (_abce );
};for _ ,_bgfb :=range _debf ._gbeaa {for _ ,_aagb :=range _bgfb {_defce :=_gcefe (_aagb .TextStyle );_defce .SetMargins (0,0,3,2);_defce .Append (_aagb .Value );_afcga :=_ccee .NewCell ();_afcga .SetHorizontalAlignment (_aagb .Alignment );_afcga .SetBackgroundColor (_aagb .BackgroundColor );
_debf .setCellBorder (_afcga ,_aagb );_afcga .SetContent (_defce );};};return _ccee .GeneratePageBlocks (_cfae );};func (_bgadg *Table )moveToNextAvailableCell ()int {_cfcbcg :=(_bgadg ._gfcb -1)%(_bgadg ._fcegg )+1;for {if _cfcbcg -1>=len (_bgadg ._ggee ){if _bgadg ._ggee [0]==0{return _cfcbcg ;
};_cfcbcg =1;}else if _bgadg ._ggee [_cfcbcg -1]==0{return _cfcbcg ;};_bgadg ._gfcb ++;_bgadg ._ggee [_cfcbcg -1]--;_cfcbcg ++;};};

// Width returns the current page width.
func (_ffe *Creator )Width ()float64 {return _ffe ._cfgg };

// SetViewerPreferences sets the viewer preferences for the PDF document.
func (_cbcc *Creator )SetViewerPreferences (viewerPreferences *_db .ViewerPreferences ){_cbcc ._gbf =viewerPreferences ;};

// StyledParagraph represents text drawn with a specified font and can wrap across lines and pages.
// By default occupies the available width in the drawing context.
type StyledParagraph struct{_dgaca []*TextChunk ;_acfg TextStyle ;_ecff TextStyle ;_deace TextAlignment ;_bded TextVerticalAlignment ;_abbd float64 ;_gfce bool ;_gfba float64 ;_fbfd bool ;_ffeg int ;_afdg bool ;_gbafb TextOverflow ;_bbacbd float64 ;_feeea Margins ;
_dfedd Positioning ;_ecgb float64 ;_gbdad float64 ;_eeadace float64 ;_faeag float64 ;_cbba [][]*TextChunk ;_gcggg func (_cgfg *StyledParagraph ,_geec DrawContext );_eafc string ;_gbfc *_db .StructureTagInfo ;};func _ecefd (_fafbc float64 ,_dedac float64 )float64 {return _fa .Round (_fafbc /_dedac )*_dedac };


// SetColPosition sets cell column position.
func (_fgeb *TableCell )SetColPosition (col int ){_fgeb ._afdac =col };func (_cfcg *GridCell )height (_gfgfb float64 )float64 {var _ecfa float64 ;switch _egfdd :=_cfcg ._cdaac .(type ){case *Paragraph :if _egfdd ._gcdc {_egfdd .SetWidth (_gfgfb -_cfcg ._ddca -_egfdd ._fbgdf .Left -_egfdd ._fbgdf .Right );
};_ecfa =_egfdd .Height ()+_egfdd ._fbgdf .Top +_egfdd ._fbgdf .Bottom ;case *StyledParagraph :if _egfdd ._gfce {_egfdd .SetWidth (_gfgfb -_cfcg ._ddca -_egfdd ._feeea .Left -_egfdd ._feeea .Right );};_ecfa =_egfdd .Height ()+_egfdd ._feeea .Top +_egfdd ._feeea .Bottom ;
case *Image :_egfdd .applyFitMode (_gfgfb -_cfcg ._ddca );_ecfa =_egfdd .Height ()+_egfdd ._efde .Top +_egfdd ._efde .Bottom ;case *Table :_egfdd .updateRowHeights (_gfgfb -_cfcg ._ddca -_egfdd ._acbac .Left -_egfdd ._acbac .Right );_ecfa =_egfdd .Height ()+_egfdd ._acbac .Top +_egfdd ._acbac .Bottom ;
case *List :_ecfa =_egfdd .ctxHeight (_gfgfb -_cfcg ._ddca )+_egfdd ._ddggd .Top +_egfdd ._ddggd .Bottom ;case *Division :_ecfa =_egfdd .ctxHeight (_gfgfb -_cfcg ._ddca )+_egfdd ._fccaf .Top +_egfdd ._fccaf .Bottom +_egfdd ._ddcg .Top +_egfdd ._ddcg .Bottom ;
case *Chart :_ecfa =_egfdd .Height ()+_egfdd ._ace .Top +_egfdd ._ace .Bottom ;case *Rectangle :_egfdd .applyFitMode (_gfgfb -_cfcg ._ddca );_ecfa =_egfdd .Height ()+_egfdd ._abeec .Top +_egfdd ._abeec .Bottom +_egfdd ._dfbcd ;case *Ellipse :_egfdd .applyFitMode (_gfgfb -_cfcg ._ddca );
_ecfa =_egfdd .Height ()+_egfdd ._dafc .Top +_egfdd ._dafc .Bottom ;case *Line :_ecfa =_egfdd .Height ()+_egfdd ._fffd .Top +_egfdd ._fffd .Bottom ;};return _ecfa ;};func _ecfb (_gefcd ,_eddfeb interface{})(interface{},error ){_gfeab ,_gdcef :=_cebab (_gefcd );
if _gdcef !=nil {return nil ,_gdcef ;};switch _ebdc :=_gfeab .(type ){case int64 :_fdcaf ,_cddcd :=_cebab (_eddfeb );if _cddcd !=nil {return nil ,_cddcd ;};switch _ddcdd :=_fdcaf .(type ){case int64 :return _ebdc +_ddcdd ,nil ;case float64 :return float64 (_ebdc )+_ddcdd ,nil ;
};case float64 :_cggfa ,_gddccb :=_cebab (_eddfeb );if _gddccb !=nil {return nil ,_gddccb ;};switch _dbedd :=_cggfa .(type ){case int64 :return _ebdc +float64 (_dbedd ),nil ;case float64 :return _ebdc +_dbedd ,nil ;};};return nil ,_g .Errorf ("\u0066\u0061\u0069le\u0064\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u0025\u0076\u0020\u0061\u006e\u0064\u0020\u0025\u0076",_gefcd ,_eddfeb );
};func _eeac ()*FilledCurve {_cebcd :=FilledCurve {};_cebcd ._bec =[]_gc .CubicBezierCurve {};return &_cebcd ;};func (_bebfc *templateProcessor )parseLineStyleAttr (_bccfcb ,_dbfa string )_gc .LineStyle {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069n\u0067\u0020\u006c\u0069\u006e\u0065\u0020\u0073\u0074\u0079\u006c\u0065\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_bccfcb ,_dbfa );
_fbbbb :=map[string ]_gc .LineStyle {"\u0073\u006f\u006ci\u0064":_gc .LineStyleSolid ,"\u0064\u0061\u0073\u0068\u0065\u0064":_gc .LineStyleDashed }[_dbfa ];return _fbbbb ;};func (_bebe *Invoice )newColumn (_fabb string ,_afgg CellHorizontalAlignment )*InvoiceCell {_bgaa :=&InvoiceCell {_bebe ._ebac ,_fabb };
_bgaa .Alignment =_afgg ;return _bgaa ;};

// FillColor returns the fill color of the ellipse.
func (_afbc *Ellipse )FillColor ()Color {return _afbc ._efcb };

// SetFitMode sets the fit mode of the line.
// NOTE: The fit mode is only applied if relative positioning is used.
func (_dbadf *Line )SetFitMode (fitMode FitMode ){_dbadf ._gagab =fitMode };

// SetDueDate sets the due date of the invoice.
func (_gccgd *Invoice )SetDueDate (dueDate string )(*InvoiceCell ,*InvoiceCell ){_gccgd ._fbbc [1].Value =dueDate ;return _gccgd ._fbbc [0],_gccgd ._fbbc [1];};

// DrawWithContext draws the Block using the specified drawing context.
func (_bgg *Block )DrawWithContext (d Drawable ,ctx DrawContext )error {_acg ,_ ,_gfe :=d .GeneratePageBlocks (ctx );if _gfe !=nil {return _gfe ;};if len (_acg )!=1{return ErrContentNotFit ;};for _ ,_acag :=range _acg {if _abd :=_bgg .mergeBlocks (_acag );
_abd !=nil {return _abd ;};};return nil ;};

// SetScaling sets scaling value for graphic SVG and maintain the aspect ratio.
func (_eedd *GraphicSVGElement )SetScaling (xFactor ,yFactor float64 ){_dcce :=_eedd .Width /_eedd .ViewBox .W ;_dgea :=_eedd .Height /_eedd .ViewBox .H ;_eedd .setDefaultScaling (_fa .Max (_dcce ,_dgea ));for _ ,_gaae :=range _eedd .Children {_gaae .SetScaling (xFactor ,yFactor );
};};func (_ecfad *templateProcessor )parseInt64Attr (_abaa ,_abab string )int64 {_a .Log .Debug ("\u0050\u0061rs\u0069\u006e\u0067 \u0069\u006e\u0074\u00364 a\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020(`\u0025\u0073\u0060\u002c\u0020\u0025\u0073)\u002e",_abaa ,_abab );
_bccfe ,_ :=_fbf .ParseInt (_abab ,10,64);return _bccfe ;};

// NewFilledCurve returns a instance of filled curve.
func (_ecgc *Creator )NewFilledCurve ()*FilledCurve {return _eeac ()};

// MoveX moves the drawing context to absolute position x.
func (_ccea *Creator )MoveX (x float64 ){_ccea ._fcga .X =x };

// Polygon represents a polygon shape.
// Implements the Drawable interface and can be drawn on PDF using the Creator.
type Polygon struct{_addde *_gc .Polygon ;_eddfc float64 ;_bbdb float64 ;_fbef Color ;_daega *_db .StructureTagInfo ;};

// NewDivision returns a new Division container component.
func (_gdaa *Creator )NewDivision ()*Division {return _dfge ()};const (TextOverflowVisible TextOverflow =iota ;TextOverflowHidden ;);

// Width returns the width of the specified text chunk.
func (_gdef *TextChunk )Width ()float64 {var (_dbefc float64 ;_ebgd =_gdef .Style ;);for _ ,_cedeab :=range _gdef .Text {_afgfe ,_dacga :=_ebgd .Font .GetRuneMetrics (_cedeab );if !_dacga {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0052\u0075\u006e\u0065\u0020\u0063\u0068\u0061\u0072\u0020\u006det\u0072i\u0063\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064!\u0020\u0072\u0075\u006e\u0065\u003d\u0030\u0078\u0025\u0030\u0034\u0078\u003d\u0025\u0063\u0020\u0066o\u006e\u0074\u003d\u0025\u0073\u0020\u0025\u0023\u0071",_cedeab ,_cedeab ,_ebgd .Font .BaseFont (),_ebgd .Font .Subtype ());
_a .Log .Trace ("\u0046o\u006e\u0074\u003a\u0020\u0025\u0023v",_ebgd .Font );_a .Log .Trace ("\u0045\u006e\u0063o\u0064\u0065\u0072\u003a\u0020\u0025\u0023\u0076",_ebgd .Font .Encoder ());};_abacd :=_ebgd .FontSize *_afgfe .Wx ;_gbaed :=_abacd ;if _cedeab !=' '{_gbaed =_abacd +_ebgd .CharSpacing *1000.0;
};_dbefc +=_gbaed ;};return _dbefc /1000.0;};

// Total returns the invoice total description and value cells.
// The returned values can be used to customize the styles of the cells.
func (_ccae *Invoice )Total ()(*InvoiceCell ,*InvoiceCell ){return _ccae ._adgc [0],_ccae ._adgc [1]};

// SetOutlineTree adds the specified outline tree to the PDF file generated
// by the creator. Adding an external outline tree disables the automatic
// generation of outlines done by the creator for the relevant components.
func (_cgaa *Creator )SetOutlineTree (outlineTree *_db .PdfOutlineTreeNode ){_cgaa ._gdc =outlineTree };

// GetCoords returns the (x1, y1), (x2, y2) points defining the Line.
func (_gaaf *Line )GetCoords ()(float64 ,float64 ,float64 ,float64 ){return _gaaf ._ggdbc ,_gaaf ._geega ,_gaaf ._cacg ,_gaaf ._degba ;};func (_fbga *TextChunk )clone ()*TextChunk {_bfdgf :=*_fbga ;_bfdgf ._aecg =_afbb (_fbga ._aecg );return &_bfdgf ;};


// Height returns the height of the list.
func (_caeb *List )Height ()float64 {var _agefb float64 ;for _ ,_babc :=range _caeb ._bbbe {_agefb +=_babc .ctxHeight (_caeb .Width ());};return _agefb ;};func (_dbf *Chapter )headingNumber ()string {var _cfdc string ;if _dbf ._ebfb {if _dbf ._eab !=0{_cfdc =_fbf .Itoa (_dbf ._eab )+"\u002e";
};if _dbf ._age !=nil {_dcge :=_dbf ._age .headingNumber ();if _dcge !=""{_cfdc =_dcge +_cfdc ;};};};return _cfdc ;};type templateProcessor struct{creator *Creator ;_ccgdc []byte ;_bgggf *TemplateOptions ;_cgdcf componentRenderer ;_edcc string ;};func _fabg (_adafc float64 ,_baag float64 ,_adddb float64 ,_debdd float64 ,_egee []*ColorPoint )*RadialShading {return &RadialShading {_aada :&shading {_cgbf :ColorWhite ,_efbd :false ,_edga :[]bool {false ,false },_bacgg :_egee },_ccdef :_adafc ,_agafb :_baag ,_begab :_adddb ,_fceg :_debdd ,_edfg :AnchorCenter };
};

// NewTextStyle creates a new text style object which can be used to style
// chunks of text.
// Default attributes:
// Font: Helvetica
// Font size: 10
// Encoding: WinAnsiEncoding
// Text color: black
func (_bgdd *Creator )NewTextStyle ()TextStyle {return _bffba (_bgdd ._bae )};

// ColorCMYKFrom8bit creates a Color from c,m,y,k values (0-100).
// Example:
//
//	red := ColorCMYKFrom8Bit(0, 100, 100, 0)
func ColorCMYKFrom8bit (c ,m ,y ,k byte )Color {return cmykColor {_dgfd :_fa .Min (float64 (c ),100)/100.0,_dcb :_fa .Min (float64 (m ),100)/100.0,_eead :_fa .Min (float64 (y ),100)/100.0,_edb :_fa .Min (float64 (k ),100)/100.0};};

// SetWidthBottom sets border width for bottom.
func (_feb *border )SetWidthBottom (bw float64 ){_feb ._eaag =bw };

// Width is not used as the division component is designed to fill all the
// available space, depending on the context. Returns 0.
func (_cbfb *Division )Width ()float64 {return 0};func (_dafg *Table )openTag (_ebdb *Block ,_fafee *_db .StructureTagInfo ){_bffa :=_eg .NewContentCreator ();_ccefa :=map[string ]_dd .PdfObject {};if _fafee !=nil {_ccefa ["\u004d\u0043\u0049\u0044"]=_dd .MakeInteger (_fafee .Mcid );
};_bffa .Add_BDC (*_dd .MakeName (string (_fafee .StructureType )),_ccefa );_ebdb .addContents (_bffa .Operations ());};

// GeneratePageBlocks draws the curve onto page blocks.
func (_faab *Curve )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_gecge :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_afed :=_eg .NewContentCreator ();if _faab ._fefg !=nil {_afed .Add_BDC (*_dd .MakeName (string (_faab ._fefg .StructureType )),map[string ]_dd .PdfObject {"\u004d\u0043\u0049\u0044":_dd .MakeInteger (_faab ._fefg .Mcid )});
};_afed .Add_q ().Add_w (_faab ._aged ).SetStrokingColor (_cdfe (_faab ._gaab )).Add_m (_faab ._cfcbc ,ctx .PageHeight -_faab ._fafc ).Add_v (_faab ._gebb ,ctx .PageHeight -_faab ._feaa ,_faab ._cbdb ,ctx .PageHeight -_faab ._ddad ).Add_S ().Add_Q ();if _faab ._fefg !=nil {_afed .Add_EMC ();
};_ffdd :=_gecge .addContentsByString (_afed .String ());if _ffdd !=nil {return nil ,ctx ,_ffdd ;};return []*Block {_gecge },ctx ,nil ;};

// SetMarkedContentID sets the marked content ID.
func (_gcgec *Rectangle )SetMarkedContentID (mcid int64 ){if _gcgec ._bgbc ==nil {_gcgec ._bgbc =_db .NewStructureTagInfo ();};_gcgec ._bgbc .Mcid =mcid ;};

// SetBoundingBox set gradient color bounding box where the gradient would be rendered.
func (_fdec *RadialShading )SetBoundingBox (x ,y ,width ,height float64 ){_fdec ._gcdbd =&_db .PdfRectangle {Llx :x ,Lly :y ,Urx :x +width ,Ury :y +height };};

// SetMarkedContentID sets the marked content id for the paragraph.
func (_fdaac *Paragraph )SetMarkedContentID (mcid int64 ){if _fdaac ._dfec ==nil {_fdaac ._dfec =_db .NewStructureTagInfo ();_fdaac ._dfec .StructureType =_db .StructureTypeParagraph ;};_fdaac ._dfec .Mcid =mcid ;};

// Indent returns the left offset of the list when nested into another list.
func (_egfda *List )Indent ()float64 {return _egfda ._gagc };

// SetFitMode sets the fit mode of the rectangle.
// NOTE: The fit mode is only applied if relative positioning is used.
func (_bbgee *Rectangle )SetFitMode (fitMode FitMode ){_bbgee ._dfced =fitMode };

// GenerateKDict generates a K dictionary for the paragraph.
func (_abac *StyledParagraph )GenerateKDict ()(*_db .KDict ,error ){if _abac ._gbfc ==nil {return nil ,_g .Errorf ("p\u0061\u0072\u0061\u0067\u0072\u0061p\u0068\u0020\u0073\u0074\u0072\u0075c\u0074\u0075\u0072\u0065\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _abac ._gbfc .GenerateKDict (),nil ;};

// SetBorderOpacity sets the border opacity.
func (_ggga *CurvePolygon )SetBorderOpacity (opacity float64 ){_ggga ._gafd =opacity };

// SetLineNumberStyle sets the style for the numbers part of all new lines
// of the table of contents.
func (_cefge *TOC )SetLineNumberStyle (style TextStyle ){_cefge ._fabcf =style };

// SetLineSeparatorStyle sets the style for the separator part of all new
// lines of the table of contents.
func (_fdcbb *TOC )SetLineSeparatorStyle (style TextStyle ){_fdcbb ._fbgcd =style };

// AddPatternResource adds pattern dictionary inside the resources dictionary.
func (_gggb *LinearShading )AddPatternResource (block *Block )(_cddf _dd .PdfObjectName ,_ageec error ){_dbbea :=1;_dgec :=_dd .PdfObjectName ("\u0050"+_fbf .Itoa (_dbbea ));for block ._faa .HasPatternByName (_dgec ){_dbbea ++;_dgec =_dd .PdfObjectName ("\u0050"+_fbf .Itoa (_dbbea ));
};if _egcec :=block ._faa .SetPatternByName (_dgec ,_gggb .ToPdfShadingPattern ().ToPdfObject ());_egcec !=nil {return "",_egcec ;};return _dgec ,nil ;};

// ScaleToHeight scales the rectangle to the specified height. The width of
// the rectangle is scaled so that the aspect ratio is maintained.
func (_efgfc *Rectangle )ScaleToHeight (h float64 ){_agfb :=_efgfc ._cdcgg /_efgfc ._cfedb ;_efgfc ._cfedb =h ;_efgfc ._cdcgg =h *_agfb ;};func _aebfa (_bgfcd *_db .PdfRectangle ,_dcfcab float64 )[4]_gc .Point {_faaeb :=_dcfcab *_fa .Pi /180.0;_fefab :=_bgfcd .Width ();
_dbefe :=_bgfcd .Height ();_aeeag :=_fa .Sin (_faaeb );_geeee :=_fa .Cos (_faaeb );_acfdb :=[4]_gc .Point {{X :_cg .RoundFloat (_bgfcd .Llx ,3),Y :_cg .RoundFloat (_bgfcd .Lly ,3)},{X :_cg .RoundFloat (_bgfcd .Llx +_fefab *_geeee ,3),Y :_cg .RoundFloat (_bgfcd .Lly +_fefab *_aeeag ,3)},{X :_cg .RoundFloat (_bgfcd .Llx +_fefab *_geeee -_dbefe *_aeeag ,3),Y :_cg .RoundFloat (_bgfcd .Lly +_fefab *_aeeag +_dbefe *_geeee ,3)},{X :_cg .RoundFloat (_bgfcd .Llx -_dbefe *_aeeag ,3),Y :_cg .RoundFloat (_bgfcd .Lly +_dbefe *_geeee ,3)}};
return _acfdb ;};

// SetLineHeight sets the line height (1.0 default).
func (_dccf *Paragraph )SetLineHeight (lineheight float64 ){_dccf ._gafdf =lineheight };func (_dga *Block )addContents (_geg *_eg .ContentStreamOperations ){*_dga ._fd =append (*_dga ._fd ,*_geg ...);};

// PageFinalize sets a function to be called for each page before finalization
// (i.e. the last stage of page processing before they get written out).
// The callback function allows final touch-ups for each page, and it
// provides information that might not be known at other stages of designing
// the document (e.g. the total number of pages). Unlike the header/footer
// functions, which are limited to the top/bottom margins of the page, the
// finalize function can be used draw components anywhere on the current page.
func (_agded *Creator )PageFinalize (pageFinalizeFunc func (_abcf PageFinalizeFunctionArgs )error ){_agded ._aebd =pageFinalizeFunc ;};func _eadae (_gedge []_gc .CubicBezierCurve )*PolyBezierCurve {return &PolyBezierCurve {_gefa :&_gc .PolyBezierCurve {Curves :_gedge ,BorderColor :_db .NewPdfColorDeviceRGB (0,0,0),BorderWidth :1.0},_dfdf :1.0,_efcf :1.0};
};

// Height returns the current page height.
func (_dfbf *Creator )Height ()float64 {return _dfbf ._adde };

// SetNoteStyle sets the style properties used to render the content of the
// invoice note sections.
func (_dbge *Invoice )SetNoteStyle (style TextStyle ){_dbge ._afafg =style };

// PageBreak represents a page break for a chapter.
type PageBreak struct{};

// LineWidth returns the width of the line.
func (_dgae *Line )LineWidth ()float64 {return _dgae ._gbgbb };

// SetPos sets absolute positioning with specified coordinates.
func (_fdeb *Paragraph )SetPos (x ,y float64 ){_fdeb ._aecc =PositionAbsolute ;_fdeb ._eedf =x ;_fdeb ._cgdg =y ;};

// Height returns the height of the Paragraph. The height is calculated based on the input text and
// how it is wrapped within the container. Does not include Margins.
func (_fdaf *Paragraph )Height ()float64 {_fdaf .wrapText ();return _cg .RoundDefault (float64 (len (_fdaf ._gbde ))*_fdaf ._gafdf *_fdaf ._febee );};

// SetSideBorderWidth sets the cell's side border width.
func (_cfddf *TableCell )SetSideBorderWidth (side CellBorderSide ,width float64 ){switch side {case CellBorderSideAll :_cfddf ._ebde =width ;_cfddf ._cfcab =width ;_cfddf ._gbgfg =width ;_cfddf ._dccbf =width ;case CellBorderSideTop :_cfddf ._ebde =width ;
case CellBorderSideBottom :_cfddf ._cfcab =width ;case CellBorderSideLeft :_cfddf ._gbgfg =width ;case CellBorderSideRight :_cfddf ._dccbf =width ;};};

// SetWidth sets line width.
func (_bcec *Curve )SetWidth (width float64 ){_bcec ._aged =width };

// SetPos sets the Block's positioning to absolute mode with the specified coordinates.
func (_bff *Block )SetPos (x ,y float64 ){_bff ._fbb =PositionAbsolute ;_bff ._ced =x ;_bff ._fda =y };

// DueDate returns the invoice due date description and value cells.
// The returned values can be used to customize the styles of the cells.
func (_eafg *Invoice )DueDate ()(*InvoiceCell ,*InvoiceCell ){return _eafg ._fbbc [0],_eafg ._fbbc [1]};func (_ecafa *Table )AddTag (rootKObj *_db .KDict ){if rootKObj ==nil {_a .Log .Debug ("\u0054\u0061\u0062\u006ce\u003a\u0020\u0041\u0064\u0064\u0054\u0061\u0067\u0020c\u0061\u006c\u006c\u0065\u0064\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u0069\u006c\u0020\u0072\u006f\u006ft\u004b\u004f\u0062\u006a\u002c \u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0074\u0061\u0067\u0067\u0069\u006e\u0067\u002e");
return ;};_ecafa ._ebea =true ;if _ecafa ._fefdd ==nil {_ecafa ._fefdd =_db .NewStructureTagInfo ();_ecafa ._fefdd .StructureType =_db .StructureTypeTable ;};_ecafa ._bcbfe =_db .NewKDictionary ();_ecafa ._bcbfe .S =_dd .MakeName (string (_db .StructureTypeTable ));
rootKObj .AddKChild (_ecafa ._bcbfe );};func (_bcff *templateProcessor )parseFloatAttr (_bfegc ,_gbcc string )float64 {_a .Log .Debug ("\u0050\u0061rs\u0069\u006e\u0067 \u0066\u006c\u006f\u0061t a\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020(`\u0025\u0073\u0060\u002c\u0020\u0025\u0073)\u002e",_bfegc ,_gbcc );
_faeb ,_ :=_fbf .ParseFloat (_gbcc ,64);return _faeb ;};

// ToPdfShadingPattern generates a new model.PdfShadingPatternType3 object.
func (_cdffd *RadialShading )ToPdfShadingPattern ()*_db .PdfShadingPatternType3 {_gadf ,_bage ,_dcgec :=_cdffd ._aada ._cgbf .ToRGB ();_cgbdc :=_cdffd .shadingModel ();_cgbdc .PdfShading .Background =_dd .MakeArrayFromFloats ([]float64 {_gadf ,_bage ,_dcgec });
_fefa :=_db .NewPdfShadingPatternType3 ();_fefa .Shading =_cgbdc ;return _fefa ;};

// SetPos sets absolute positioning with specified coordinates.
func (_bdacb *StyledParagraph )SetPos (x ,y float64 ){_bdacb ._dfedd =PositionAbsolute ;_bdacb ._ecgb =x ;_bdacb ._gbdad =y ;};func (_ecedb *templateProcessor )parseTableCell (_agfac *templateNode )(interface{},error ){if _agfac ._gafge ==nil {_ecedb .nodeLogError (_agfac ,"\u0054\u0061\u0062\u006c\u0065\u0020\u0063\u0065\u006c\u006c\u0020\u0070\u0061\u0072\u0065n\u0074 \u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c\u002e");
return nil ,_bbcfc ;};_gcgaf ,_abba :=_agfac ._gafge ._aeceb .(*Table );if !_abba {_ecedb .nodeLogError (_agfac ,"\u0054\u0061\u0062\u006c\u0065\u0020\u0063\u0065\u006c\u006c\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u0028\u0025\u0054\u0029\u0020\u0069s\u0020\u006e\u006f\u0074\u0020a\u0020\u0074a\u0062\u006c\u0065\u002e",_agfac ._gafge ._aeceb );
return nil ,_bbcfc ;};var _cfcea ,_cacee int64 ;for _ ,_dbabc :=range _agfac ._ccge .Attr {_bccfce :=_dbabc .Value ;switch _aefb :=_dbabc .Name .Local ;_aefb {case "\u0063o\u006c\u0073\u0070\u0061\u006e":_cfcea =_ecedb .parseInt64Attr (_aefb ,_bccfce );
case "\u0072o\u0077\u0073\u0070\u0061\u006e":_cacee =_ecedb .parseInt64Attr (_aefb ,_bccfce );};};if _cfcea <=0{_cfcea =1;};if _cacee <=0{_cacee =1;};_beea :=_gcgaf .MultiCell (int (_cacee ),int (_cfcea ));for _ ,_gbbfb :=range _agfac ._ccge .Attr {_gbcdf :=_gbbfb .Value ;
switch _bgec :=_gbbfb .Name .Local ;_bgec {case "\u0069\u006e\u0064\u0065\u006e\u0074":_beea .SetIndent (_ecedb .parseFloatAttr (_bgec ,_gbcdf ));case "\u0061\u006c\u0069g\u006e":_beea .SetHorizontalAlignment (_ecedb .parseCellAlignmentAttr (_bgec ,_gbcdf ));
case "\u0076\u0065\u0072\u0074\u0069\u0063\u0061\u006c\u002da\u006c\u0069\u0067\u006e":_beea .SetVerticalAlignment (_ecedb .parseCellVerticalAlignmentAttr (_bgec ,_gbcdf ));case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0073\u0074\u0079\u006c\u0065":_beea .SetSideBorderStyle (CellBorderSideAll ,_ecedb .parseCellBorderStyleAttr (_bgec ,_gbcdf ));
case "\u0062\u006fr\u0064\u0065\u0072-\u0073\u0074\u0079\u006c\u0065\u002d\u0074\u006f\u0070":_beea .SetSideBorderStyle (CellBorderSideTop ,_ecedb .parseCellBorderStyleAttr (_bgec ,_gbcdf ));case "\u0062\u006f\u0072\u0064er\u002d\u0073\u0074\u0079\u006c\u0065\u002d\u0062\u006f\u0074\u0074\u006f\u006d":_beea .SetSideBorderStyle (CellBorderSideBottom ,_ecedb .parseCellBorderStyleAttr (_bgec ,_gbcdf ));
case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0073\u0074\u0079\u006c\u0065-\u006c\u0065\u0066\u0074":_beea .SetSideBorderStyle (CellBorderSideLeft ,_ecedb .parseCellBorderStyleAttr (_bgec ,_gbcdf ));case "\u0062o\u0072d\u0065\u0072\u002d\u0073\u0074y\u006c\u0065-\u0072\u0069\u0067\u0068\u0074":_beea .SetSideBorderStyle (CellBorderSideRight ,_ecedb .parseCellBorderStyleAttr (_bgec ,_gbcdf ));
case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0077\u0069\u0064\u0074\u0068":_beea .SetSideBorderWidth (CellBorderSideAll ,_ecedb .parseFloatAttr (_bgec ,_gbcdf ));case "\u0062\u006fr\u0064\u0065\u0072-\u0077\u0069\u0064\u0074\u0068\u002d\u0074\u006f\u0070":_beea .SetSideBorderWidth (CellBorderSideTop ,_ecedb .parseFloatAttr (_bgec ,_gbcdf ));
case "\u0062\u006f\u0072\u0064er\u002d\u0077\u0069\u0064\u0074\u0068\u002d\u0062\u006f\u0074\u0074\u006f\u006d":_beea .SetSideBorderWidth (CellBorderSideBottom ,_ecedb .parseFloatAttr (_bgec ,_gbcdf ));case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0077\u0069\u0064\u0074\u0068-\u006c\u0065\u0066\u0074":_beea .SetSideBorderWidth (CellBorderSideLeft ,_ecedb .parseFloatAttr (_bgec ,_gbcdf ));
case "\u0062o\u0072d\u0065\u0072\u002d\u0077\u0069d\u0074\u0068-\u0072\u0069\u0067\u0068\u0074":_beea .SetSideBorderWidth (CellBorderSideRight ,_ecedb .parseFloatAttr (_bgec ,_gbcdf ));case "\u0062\u006f\u0072d\u0065\u0072\u002d\u0063\u006f\u006c\u006f\u0072":_beea .SetSideBorderColor (CellBorderSideAll ,_ecedb .parseColorAttr (_bgec ,_gbcdf ));
case "\u0062\u006fr\u0064\u0065\u0072-\u0063\u006f\u006c\u006f\u0072\u002d\u0074\u006f\u0070":_beea .SetSideBorderColor (CellBorderSideTop ,_ecedb .parseColorAttr (_bgec ,_gbcdf ));case "\u0062\u006f\u0072\u0064er\u002d\u0063\u006f\u006c\u006f\u0072\u002d\u0062\u006f\u0074\u0074\u006f\u006d":_beea .SetSideBorderColor (CellBorderSideBottom ,_ecedb .parseColorAttr (_bgec ,_gbcdf ));
case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u0063\u006f\u006c\u006f\u0072-\u006c\u0065\u0066\u0074":_beea .SetSideBorderColor (CellBorderSideLeft ,_ecedb .parseColorAttr (_bgec ,_gbcdf ));case "\u0062o\u0072d\u0065\u0072\u002d\u0063\u006fl\u006f\u0072-\u0072\u0069\u0067\u0068\u0074":_beea .SetSideBorderColor (CellBorderSideRight ,_ecedb .parseColorAttr (_bgec ,_gbcdf ));
case "\u0062\u006f\u0072\u0064\u0065\u0072\u002d\u006c\u0069\u006e\u0065\u002ds\u0074\u0079\u006c\u0065":_beea .SetBorderLineStyle (_ecedb .parseLineStyleAttr (_bgec ,_gbcdf ));case "\u0062\u0061c\u006b\u0067\u0072o\u0075\u006e\u0064\u002d\u0063\u006f\u006c\u006f\u0072":_beea .SetBackgroundColor (_ecedb .parseColorAttr (_bgec ,_gbcdf ));
case "\u0063o\u006c\u0073\u0070\u0061\u006e","\u0072o\u0077\u0073\u0070\u0061\u006e":break ;default:_ecedb .nodeLogDebug (_agfac ,"\u0055\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0074\u0061\u0062\u006c\u0065\u0020\u0063\u0065\u006c\u006c\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006bi\u0070p\u0069\u006e\u0067\u002e",_bgec );
};};return _beea ,nil ;};func _fefaa (_acgcd *templateProcessor ,_caffa *templateNode )(interface{},error ){return _acgcd .parseTextChunk (_caffa ,nil );};

// GenerateKDict generates a K dictionary for the rectangle.
func (_eddbd *Rectangle )GenerateKDict ()(*_db .KDict ,error ){if _eddbd ._bgbc ==nil {return nil ,_g .Errorf ("r\u0065\u0063\u0074\u0061\u006e\u0067l\u0065\u0020\u0073\u0074\u0072\u0075c\u0074\u0075\u0072\u0065\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _eddbd ._bgbc .GenerateKDict (),nil ;};

// ScaleToHeight scale Image to a specified height h, maintaining the aspect ratio.
func (_cfbb *Image )ScaleToHeight (h float64 ){_ceef :=_cfbb ._bbg /_cfbb ._fcbd ;_cfbb ._fcbd =h ;_cfbb ._bbg =h *_ceef ;};

// GenerateKDict generates a K dictionary for the curve polygon.
func (_gdbc *CurvePolygon )GenerateKDict ()(*_db .KDict ,error ){if _gdbc ._ceada ==nil {return nil ,_g .Errorf ("\u0063\u0075\u0072\u0076\u0065\u0020\u0070\u006f\u006c\u0079\u0067\u006f\u006e \u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065\u0020\u0069\u006e\u0066\u006f\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");
};return _gdbc ._ceada .GenerateKDict (),nil ;};func _abcfe (_cbed VectorDrawable ,_adba float64 )float64 {switch _eeab :=_cbed .(type ){case *Paragraph :if _eeab ._gcdc {_eeab .SetWidth (_adba -_eeab ._fbgdf .Left -_eeab ._fbgdf .Right );};return _eeab .Height ()+_eeab ._fbgdf .Top +_eeab ._fbgdf .Bottom +(0.5*_eeab ._febee *_eeab ._gafdf );
case *StyledParagraph :if _eeab ._gfce {_eeab .SetWidth (_adba -_eeab ._feeea .Left -_eeab ._feeea .Right );};return _eeab .Height ()+_eeab ._feeea .Top +_eeab ._feeea .Bottom +(0.5*_eeab .getTextHeight ());case *Image :_eeab .applyFitMode (_adba );return _eeab .Height ()+_eeab ._efde .Top +_eeab ._efde .Bottom ;
case *Rectangle :_eeab .applyFitMode (_adba );return _eeab .Height ()+_eeab ._abeec .Top +_eeab ._abeec .Bottom +_eeab ._dfbcd ;case *Ellipse :_eeab .applyFitMode (_adba );return _eeab .Height ()+_eeab ._dafc .Top +_eeab ._dafc .Bottom ;case *Division :return _eeab .ctxHeight (_adba )+_eeab ._fccaf .Top +_eeab ._fccaf .Bottom +_eeab ._ddcg .Top +_eeab ._ddcg .Bottom ;
case *Table :_eeab .updateRowHeights (_adba -_eeab ._acbac .Left -_eeab ._acbac .Right );return _eeab .Height ()+_eeab ._acbac .Top +_eeab ._acbac .Bottom ;case *List :return _eeab .ctxHeight (_adba )+_eeab ._ddggd .Top +_eeab ._ddggd .Bottom ;case marginDrawable :_ ,_ ,_gbge ,_dacba :=_eeab .GetMargins ();
return _eeab .Height ()+_gbge +_dacba ;default:return _eeab .Height ();};};type shading struct{_cgbf Color ;_efbd bool ;_edga []bool ;_bacgg []*ColorPoint ;};func (_gaadf *Invoice )drawInformation ()*Table {_gecfb :=_cgdd (2);_egef :=append ([][2]*InvoiceCell {_gaadf ._eddg ,_gaadf ._fgeaf ,_gaadf ._fbbc },_gaadf ._febf ...);
for _ ,_aabg :=range _egef {_geff ,_fdbbc :=_aabg [0],_aabg [1];if _fdbbc .Value ==""{continue ;};_cgfe :=_gecfb .NewCell ();_cgfe .SetBackgroundColor (_geff .BackgroundColor );_gaadf .setCellBorder (_cgfe ,_geff );_ecee :=_gcefe (_geff .TextStyle );_ecee .Append (_geff .Value );
_ecee .SetMargins (0,0,2,1);_cgfe .SetContent (_ecee );_cgfe =_gecfb .NewCell ();_cgfe .SetBackgroundColor (_fdbbc .BackgroundColor );_gaadf .setCellBorder (_cgfe ,_fdbbc );_ecee =_gcefe (_fdbbc .TextStyle );_ecee .Append (_fdbbc .Value );_ecee .SetMargins (0,0,2,1);
_cgfe .SetContent (_ecee );};return _gecfb ;};func _gfbde ()*listItem {return &listItem {}};func (_gcfcg *templateProcessor )parsePositioningAttr (_egbd ,_gdcec string )Positioning {_a .Log .Debug ("\u0050\u0061\u0072s\u0069\u006e\u0067\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e\u0069\u006e\u0067\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060%\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_egbd ,_gdcec );
_edecea :=map[string ]Positioning {"\u0072\u0065\u006c\u0061\u0074\u0069\u0076\u0065":PositionRelative ,"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065":PositionAbsolute }[_gdcec ];return _edecea ;};func _bdfa (_ffbg *templateProcessor ,_feefb *templateNode )(interface{},error ){return _ffbg .parseListMarker (_feefb );
};func (_dadec *templateProcessor )parseDivision (_dfda *templateNode )(interface{},error ){_efdae :=_dadec .creator .NewDivision ();for _ ,_fddbc :=range _dfda ._ccge .Attr {_ebdeg :=_fddbc .Value ;switch _dgebe :=_fddbc .Name .Local ;_dgebe {case "\u0065\u006ea\u0062\u006c\u0065-\u0070\u0061\u0067\u0065\u002d\u0077\u0072\u0061\u0070":_efdae .EnablePageWrap (_dadec .parseBoolAttr (_dgebe ,_ebdeg ));
case "\u006d\u0061\u0072\u0067\u0069\u006e":_eeca :=_dadec .parseMarginAttr (_dgebe ,_ebdeg );_efdae .SetMargins (_eeca .Left ,_eeca .Right ,_eeca .Top ,_eeca .Bottom );case "\u0070a\u0064\u0064\u0069\u006e\u0067":_bgega :=_dadec .parseMarginAttr (_dgebe ,_ebdeg );
_efdae .SetPadding (_bgega .Left ,_bgega .Right ,_bgega .Top ,_bgega .Bottom );default:_dadec .nodeLogDebug (_dfda ,"U\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0064\u0069\u0076\u0069\u0073\u0069on\u0020\u0061\u0074\u0074r\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025s`\u002e\u0020S\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_dgebe );
};};return _efdae ,nil ;};func _aaafa (_acefa *_db .PdfRectangle ,_dfffe _bce .Matrix )*_db .PdfRectangle {var _gabeg _db .PdfRectangle ;_gabeg .Llx ,_gabeg .Lly =_dfffe .Transform (_acefa .Llx ,_acefa .Lly );_gabeg .Urx ,_gabeg .Ury =_dfffe .Transform (_acefa .Urx ,_acefa .Ury );
_gabeg .Normalize ();return &_gabeg ;};

// FitMode returns the fit mode of the rectangle.
func (_cadg *Rectangle )FitMode ()FitMode {return _cadg ._dfced };func (_dfabg *StyledParagraph )getLineMetrics (_gbdg int )(_dbff ,_bafge ,_debfbd float64 ){if _dfabg ._cbba ==nil ||(_dfabg ._cbba !=nil &&len (_dfabg ._cbba )==0){_dfabg .wrapText ();};
if _gbdg < 0||_gbdg > len (_dfabg ._cbba )-1{_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020p\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0020\u006c\u0069\u006e\u0065 \u0069\u006e\u0064\u0065\u0078\u0020\u0025\u0064\u002e\u0020\u0052\u0065tu\u0072\u006e\u0069\u006e\u0067\u0020\u0030\u002c\u0020\u0030",_gbdg );
return 0,0,0;};_bgdag :=_dfabg ._cbba [_gbdg ];for _ ,_dfdee :=range _bgdag {_eccf :=_dfdcc (_dfdee .Style .Font ,_dfdee .Style .FontSize );if _eccf ._eggd > _dbff {_dbff =_eccf ._eggd ;};if _eccf ._agcc < _debfbd {_debfbd =_eccf ._agcc ;};if _gagf :=_dfdee .Style .FontSize ;
_gagf > _bafge {_bafge =_gagf ;};};return _dbff ,_bafge ,_debfbd ;};

// AddressStyle returns the style properties used to render the content of
// the invoice address sections.
func (_gddd *Invoice )AddressStyle ()TextStyle {return _gddd ._eefg };func _egfe (_adda []float64 )[]float64 {for _aaee ,_cabea :=0,len (_adda )-1;_aaee < _cabea ;_aaee ,_cabea =_aaee +1,_cabea -1{_adda [_aaee ],_adda [_cabea ]=_adda [_cabea ],_adda [_aaee ];
};return _adda ;};

// List represents a list of items.
// The representation of a list item is as follows:
//
//	[marker] [content]
//
// e.g.:        • This is the content of the item.
// The supported components to add content to list items are:
// - Paragraph
// - StyledParagraph
// - List
type List struct{_bbbe []*listItem ;_ddggd Margins ;_debc TextChunk ;_gagc float64 ;_dgbd bool ;_gdcf Positioning ;_afeff TextStyle ;_aeec *_db .StructureTagInfo ;};func _cgdd (_dfgee int )*Table {_edeed :=&Table {_fcegg :_dfgee ,_gfafc :10.0,_bdggd :[]float64 {},_gdbgf :[]float64 {},_ccgf :[]*TableCell {},_ggee :make ([]int ,_dfgee ),_aecdf :true ,_ebea :false };
_edeed .resetColumnWidths ();return _edeed ;};

// SetBackground sets the background properties of the component.
func (_begfe *Division )SetBackground (background *Background ){_begfe ._gfef =background };func _gfae (_edec *Block ,_ddfeb _db .PdfColor ,_ffgb Color ,_fddg func ()Rectangle )error {switch _ccdb :=_ddfeb .(type ){case *_db .PdfColorPatternType2 :_bcdea ,_fddf :=_ffgb .(*LinearShading );
if !_fddf {return _g .Errorf ("\u0043\u006f\u006c\u006f\u0072\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u004c\u0069\u006e\u0065\u0061\u0072\u0053\u0068\u0061d\u0069\u006e\u0067");};_bfdc :=_fddg ();_bcdea .SetBoundingBox (_bfdc ._eeadac ,_bfdc ._abegd ,_bfdc ._cdcgg ,_bfdc ._cfedb );
_gdda ,_gecgd :=_bcdea .AddPatternResource (_edec );if _gecgd !=nil {return _g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0061\u0064\u0064\u0069\u006e\u0067\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u0074\u006f \u0072\u0065\u0073\u006f\u0075r\u0063\u0065s\u003a\u0020\u0025\u0076",_gecgd );
};_ccdb .PatternName =_gdda ;case *_db .PdfColorPatternType3 :_gcgdd ,_adcee :=_ffgb .(*RadialShading );if !_adcee {return _g .Errorf ("\u0043\u006f\u006c\u006f\u0072\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0052\u0061\u0064\u0069\u0061\u006c\u0053\u0068\u0061d\u0069\u006e\u0067");
};_abcad :=_fddg ();_gcgdd .SetBoundingBox (_abcad ._eeadac ,_abcad ._abegd ,_abcad ._cdcgg ,_abcad ._cfedb );_dacc ,_gfdad :=_gcgdd .AddPatternResource (_edec );if _gfdad !=nil {return _g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0061\u0064\u0064\u0069\u006e\u0067\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u0074\u006f \u0072\u0065\u0073\u006f\u0075r\u0063\u0065s\u003a\u0020\u0025\u0076",_gfdad );
};_ccdb .PatternName =_dacc ;};return nil ;};

// Terms returns the terms and conditions section of the invoice as a
// title-content pair.
func (_bgda *Invoice )Terms ()(string ,string ){return _bgda ._ddgeg [0],_bgda ._ddgeg [1]};

// TitleStyle returns the style properties used to render the invoice title.
func (_gaag *Invoice )TitleStyle ()TextStyle {return _gaag ._gecfa };func _badge (_cccdf string )(float64 ,float64 ,float64 ){_cddcf :=_fc .TrimPrefix (_cccdf ,"\u0072\u0067\u0062\u0028");_cddcf =_fc .TrimSuffix (_cddcf ,"\u0029");_gbbfa :=_fc .Split (_cddcf ,"\u002c");
if len (_gbbfa )!=3{_a .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_cccdf );return 0,0,0;};var _ceffe ,_egafd ,_caaadf float64 ;
_ceffe ,_gcbfa :=_fcde (_gbbfa [0]);if _gcbfa !=nil {_a .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_cccdf );
return 0,0,0;};_egafd ,_gcbfa =_fcde (_gbbfa [1]);if _gcbfa !=nil {_a .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_cccdf );
return 0,0,0;};_caaadf ,_gcbfa =_fcde (_gbbfa [2]);if _gcbfa !=nil {_a .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_cccdf );
return 0,0,0;};_gdeag :=_ceffe /255.0;_dgcbe :=_egafd /255.0;_gcfbg :=_caaadf /255.0;return _gdeag ,_dgcbe ,_gcfbg ;};func (_gdbcg *listItem )ctxHeight (_ecca float64 )float64 {var _fbdd float64 ;switch _ebafg :=_gdbcg ._agafc .(type ){case *Paragraph :if _ebafg ._gcdc {_ebafg .SetWidth (_ecca -_ebafg ._fbgdf .Horizontal ());
};_fbdd =_ebafg .Height ()+_ebafg ._fbgdf .Vertical ();_fbdd +=0.5*_ebafg ._febee *_ebafg ._gafdf ;case *StyledParagraph :if _ebafg ._gfce {_ebafg .SetWidth (_ecca -_ebafg ._feeea .Horizontal ());};_fbdd =_ebafg .Height ()+_ebafg ._feeea .Vertical ();_fbdd +=0.5*_ebafg .getTextHeight ();
case *List :_ffff :=_ecca -_gdbcg ._daedg .Width ()-_ebafg ._ddggd .Horizontal ()-_ebafg ._gagc ;_fbdd =_ebafg .ctxHeight (_ffff )+_ebafg ._ddggd .Vertical ();case *Image :_fbdd =_ebafg .Height ()+_ebafg ._efde .Vertical ();case *Division :_ceade :=_ecca -_gdbcg ._daedg .Width ()-_ebafg ._fccaf .Horizontal ();
_fbdd =_ebafg .ctxHeight (_ceade )+_ebafg ._fccaf .Vertical ();case *Table :_ebad :=_ecca -_gdbcg ._daedg .Width ()-_ebafg ._acbac .Horizontal ();_ebafg .updateRowHeights (_ebad );_fbdd =_ebafg .Height ()+_ebafg ._acbac .Vertical ();default:_fbdd =_gdbcg ._agafc .Height ();
};return _fbdd ;};func _gdfbe (_cbbf rune )bool {return _cbbf =='('||_cbbf ==','||_cbbf ==')'};func _faafc (_fcaf *Block ,_eaec *StyledParagraph ,_efffc [][]*TextChunk ,_gbfe DrawContext )(DrawContext ,[][]*TextChunk ,error ){_eeedc :=1;_aceg :=_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));
for _fcaf ._faa .HasFontByName (_aceg ){_eeedc ++;_aceg =_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));};_fafdf :=_fcaf ._faa .SetFontByName (_aceg ,_eaec ._acfg .Font .ToPdfObject ());if _fafdf !=nil {return _gbfe ,nil ,_fafdf ;
};_eeedc ++;_cgcb :=_aceg ;_ccce :=_eaec ._acfg .FontSize ;_gdbda :=_eaec ._dfedd .IsRelative ();var _dfdeb [][]_dd .PdfObjectName ;var _gafed [][]*TextChunk ;var _cefa float64 ;for _dbaaa ,_eadbb :=range _efffc {var _dfcf []_dd .PdfObjectName ;var _eecb float64 ;
if len (_eadbb )> 0{_eecb =_eadbb [0].Style .FontSize ;};for _ ,_efed :=range _eadbb {_becd :=_efed .Style ;if _efed .Text !=""&&_becd .FontSize > _eecb {_eecb =_becd .FontSize ;};if _eecb > _gbfe .PageHeight {return _gbfe ,nil ,_ee .New ("\u0050\u0061\u0072\u0061\u0067\u0072a\u0070\u0068\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020\u0063\u0061\u006e\u0027\u0074\u0020\u0062\u0065\u0020\u006ca\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0070\u0061\u0067\u0065 \u0068e\u0069\u0067\u0068\u0074");
};_aceg =_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));_fffe :=_fcaf ._faa .SetFontByName (_aceg ,_becd .Font .ToPdfObject ());if _fffe !=nil {return _gbfe ,nil ,_fffe ;};_dfcf =append (_dfcf ,_aceg );_eeedc ++;};_eecb *=_eaec ._abbd ;
if _gdbda &&_cefa +_eecb > _gbfe .Height {_gafed =_efffc [_dbaaa :];_efffc =_efffc [:_dbaaa ];break ;};_cefa +=_eecb ;_dfdeb =append (_dfdeb ,_dfcf );};_gdcee ,_ffae ,_bdabf :=_eaec .getLineMetrics (0);_fgba ,_eabd :=_gdcee *_eaec ._abbd ,_ffae *_eaec ._abbd ;
if len (_efffc )==0{return _gbfe ,_gafed ,nil ;};_cgca :=_eg .NewContentCreator ();_cgca .Add_q ();_ccfe :=_eabd ;if _eaec ._bded ==TextVerticalAlignmentCenter {_ccfe =_ffae +(_gdcee +_bdabf -_ffae )/2+(_eabd -_ffae )/2;};_bbaf :=_gbfe .PageHeight -_gbfe .Y -_ccfe ;
_cgca .Translate (_gbfe .X ,_bbaf );_gddbb :=_bbaf ;if _eaec ._bbacbd !=0{_cgca .RotateDeg (_eaec ._bbacbd );};if _eaec ._gbafb ==TextOverflowHidden {_cgca .Add_re (0,-_cefa +_fgba +1,_eaec ._gfba ,_cefa ).Add_W ().Add_n ();};_cgca .Add_BT ();_gfdd :=map[string ]_dd .PdfObject {};
if _eaec ._gbfc !=nil {_gfdd ["\u004d\u0043\u0049\u0044"]=_dd .MakeInteger (_eaec ._gbfc .Mcid );};if _eaec ._eafc !=""{_gfdd ["\u004c\u0061\u006e\u0067"]=_dd .MakeString (_eaec ._eafc );};if len (_gfdd )> 0{_cgca .Add_BDC (*_dd .MakeName (string (_eaec ._gbfc .StructureType )),_gfdd );
};var _aaga []*_gc .BasicLine ;for _baafc ,_edecc :=range _efffc {_fdbcc :=_gbfe .X ;var _aggc float64 ;if len (_edecc )> 0{_aggc =_edecc [0].Style .FontSize ;};_gdcee ,_ ,_bdabf =_eaec .getLineMetrics (_baafc );_eabd =(_gdcee +_bdabf );for _ ,_acfgc :=range _edecc {_feeef :=&_acfgc .Style ;
if _acfgc .Text !=""&&_feeef .FontSize > _aggc {_aggc =_feeef .FontSize ;};if _eabd > _aggc {_aggc =_eabd ;};};if _baafc !=0{_cgca .Add_TD (0,-_aggc *_eaec ._abbd );_gddbb -=_aggc *_eaec ._abbd ;};_bbfgc :=_baafc ==len (_efffc )-1;var (_abdf float64 ;_fddgg float64 ;
_ggdcf *fontMetrics ;_edeeac float64 ;_dgdcd uint ;);var _edaaff []float64 ;for _ ,_ffbbe :=range _edecc {_dadab :=&_ffbbe .Style ;if _dadab .FontSize > _fddgg {_fddgg =_dadab .FontSize ;_ggdcf =_dfdcc (_ffbbe .Style .Font ,_dadab .FontSize );};if _eabd > _fddgg {_fddgg =_eabd ;
};_bffbf ,_fecb :=_dadab .Font .GetRuneMetrics (' ');if _bffbf .Wx ==0&&_dadab .MultiFont !=nil {_bffbf ,_fecb =_dadab .MultiFont .GetRuneMetrics (' ');_dadab .MultiFont .Reset ();};if !_fecb {return _gbfe ,nil ,_ee .New ("\u0074\u0068e \u0066\u006f\u006et\u0020\u0064\u006f\u0065s n\u006ft \u0068\u0061\u0076\u0065\u0020\u0061\u0020sp\u0061\u0063\u0065\u0020\u0067\u006c\u0079p\u0068");
};var _feebg uint ;var _gefgf float64 ;_fefd :=len (_ffbbe .Text );for _fgca ,_gffdc :=range _ffbbe .Text {if _gffdc ==' '{_feebg ++;continue ;};if _gffdc =='\u000A'{continue ;};_gcea ,_eafbd :=_dadab .Font .GetRuneMetrics (_gffdc );if _gcea .Wx ==0&&_dadab .MultiFont !=nil {_gcea ,_eafbd =_dadab .MultiFont .GetRuneMetrics (' ');
_dadab .MultiFont .Reset ();};if !_eafbd {_a .Log .Debug ("\u0055\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0072\u0075\u006ee\u0020%\u0076\u0020\u0069\u006e\u0020\u0066\u006fn\u0074\u000a",_gffdc );return _gbfe ,nil ,_ee .New ("\u0075\u006e\u0073\u0075pp\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u0078\u0074\u0020\u0067\u006c\u0079p\u0068");
};_gefgf +=_dadab .FontSize *_gcea .Wx *_dadab .horizontalScale ();if _fgca !=_fefd -1{_gefgf +=_dadab .CharSpacing *1000.0;};};_edaaff =append (_edaaff ,_gefgf );_abdf +=_gefgf ;_edeeac +=float64 (_feebg )*_bffbf .Wx *_dadab .FontSize *_dadab .horizontalScale ();
_dgdcd +=_feebg ;};_fddgg *=_eaec ._abbd ;var _dcdgb []_dd .PdfObject ;_cbdbcg :=_eaec ._gfba *1000.0;if _eaec ._deace ==TextAlignmentJustify {if _dgdcd > 0&&!_bbfgc {_edeeac =(_cbdbcg -_abdf )/float64 (_dgdcd )/_ccce ;};}else if _eaec ._deace ==TextAlignmentCenter {_afcf :=(_cbdbcg -_abdf -_edeeac )/2;
_dgggg :=_afcf /_ccce ;_dcdgb =append (_dcdgb ,_dd .MakeFloat (-_dgggg ));_fdbcc +=_afcf /1000.0;}else if _eaec ._deace ==TextAlignmentRight {_bdbbb :=(_cbdbcg -_abdf -_edeeac );_cgeed :=_bdbbb /_ccce ;_dcdgb =append (_dcdgb ,_dd .MakeFloat (-_cgeed ));
_fdbcc +=_bdbbb /1000.0;};if len (_dcdgb )> 0{_cgca .Add_Tf (_cgcb ,_ccce ).Add_TL (_ccce *_eaec ._abbd ).Add_TJ (_dcdgb ...);};_cgfa :=0.0;for _gaded ,_effa :=range _edecc {_ffaf :=&_effa .Style ;_bdfe :=_cgcb ;_faga :=_ccce ;_dabff :=_ffaf .OutlineColor !=nil ;
_eacgb :=_ffaf .HorizontalScaling !=DefaultHorizontalScaling ;_ecdbe :=_ffaf .OutlineSize !=1;if _ecdbe {_cgca .Add_w (_ffaf .OutlineSize );};_ccbac :=_ffaf .RenderingMode !=TextRenderingModeFill ;if _ccbac {_cgca .Add_Tr (int64 (_ffaf .RenderingMode ));
};_fcedf :=_ffaf .CharSpacing !=0;if _fcedf {_cgca .Add_Tc (_ffaf .CharSpacing );};_feebb :=_ffaf .TextRise !=0;if _feebb {_cgca .Add_Ts (_ffaf .TextRise );};if _effa .VerticalAlignment !=TextVerticalAlignmentBaseline {_efbb :=_dfdcc (_effa .Style .Font ,_ffaf .FontSize );
switch _effa .VerticalAlignment {case TextVerticalAlignmentCenter :_cgfa =_ggdcf ._dcba /2-_efbb ._dcba /2;case TextVerticalAlignmentBottom :_cgfa =_ggdcf ._agcc -_efbb ._agcc ;case TextVerticalAlignmentTop :_cgfa =_ffae -_ffaf .FontSize ;};if _cgfa !=0.0{_cgca .Translate (0,_cgfa );
};};if _eaec ._deace !=TextAlignmentJustify ||_bbfgc {_baacg ,_gaffgd :=_ffaf .Font .GetRuneMetrics (' ');if !_gaffgd {return _gbfe ,nil ,_ee .New ("\u0074\u0068e \u0066\u006f\u006et\u0020\u0064\u006f\u0065s n\u006ft \u0068\u0061\u0076\u0065\u0020\u0061\u0020sp\u0061\u0063\u0065\u0020\u0067\u006c\u0079p\u0068");
};_bdfe =_dfdeb [_baafc ][_gaded ];_faga =_ffaf .FontSize ;_edeeac =_baacg .Wx *_ffaf .horizontalScale ();};_gegfg :=_ffaf .Font .Encoder ();var _ddfcb []byte ;var _bcbcf bool ;_befag :=_ffaf .Font ;_bgbcc :=map[string ]_dd .PdfObject {};if _effa ._aabc !=nil {_bgbcc ["\u0045"]=_dd .MakeString (*_effa ._aabc );
};if _effa ._cbea !=nil {_bgbcc ["\u0041\u0063\u0074\u0075\u0061\u006c\u0054\u0065\u0078\u0074"]=_dd .MakeString (*_effa ._cbea );};if _effa ._fcgd !=nil {_bgbcc ["\u0041\u006c\u0074"]=_dd .MakeString (*_effa ._fcgd );};if _effa ._gefgc !=nil {_bgbcc ["\u004d\u0043\u0049\u0044"]=_dd .MakeInteger (_effa ._gefgc .Mcid );
};if len (_bgbcc )> 0{if _effa ._gefgc !=nil &&_effa ._gefgc .StructureType !=_db .StructureTypeUnknown {_cgca .Add_BDC (*_dd .MakeName (string (_effa ._gefgc .StructureType )),_bgbcc );}else {_cgca .Add_BDC (*_dd .MakeName (string (_db .StructureTypeSpan )),_bgbcc );
};};for _ ,_ebegb :=range _effa .Text {if _ebegb =='\u000A'{continue ;};if _ebegb ==' '{if len (_ddfcb )> 0{if _dabff {_cgca .SetStrokingColor (_cdfe (_ffaf .OutlineColor ));};if _eacgb {_cgca .Add_Tz (_ffaf .HorizontalScaling );};_ddga :=_dfdeb [_baafc ][_gaded ];
if _bcbcf {_ddga =_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));_agfa :=_fcaf ._faa .SetFontByName (_ddga ,_befag .ToPdfObject ());if _agfa !=nil {return _gbfe ,nil ,_agfa ;};_eeedc ++;_bcbcf =false ;_gegfg =_ffaf .Font .Encoder ();
};_cgca .SetNonStrokingColor (_cdfe (_ffaf .Color )).Add_Tf (_ddga ,_ffaf .FontSize ).Add_TJ ([]_dd .PdfObject {_dd .MakeStringFromBytes (_ddfcb )}...);_ddfcb =nil ;};if _eacgb {_cgca .Add_Tz (DefaultHorizontalScaling );};_cgca .Add_Tf (_bdfe ,_faga ).Add_TJ ([]_dd .PdfObject {_dd .MakeFloat (-_edeeac )}...);
_edaaff [_gaded ]+=_edeeac *_faga ;}else {if _ ,_adcad :=_gegfg .RuneToCharcode (_ebegb );!_adcad {if _ffaf .MultiFont !=nil {_aegc ,_ebeeb :=_ffaf .MultiFont .Encoder (_ebegb );if _ebeeb {if len (_ddfcb )!=0{_bbea :=_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));
_faeec :=_fcaf ._faa .SetFontByName (_bdfe ,_befag .ToPdfObject ());if _faeec !=nil {return _gbfe ,nil ,_faeec ;};_cgca .SetNonStrokingColor (_cdfe (_ffaf .Color )).Add_Tf (_bbea ,_ffaf .FontSize ).Add_TJ ([]_dd .PdfObject {_dd .MakeStringFromBytes (_ddfcb )}...);
_eeedc ++;_ddfcb =nil ;};_gegfg =_aegc ;_bcbcf =true ;_befag =_ffaf .MultiFont .CurrentFont ;};}else {_fafdf =UnsupportedRuneError {Message :_g .Sprintf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u0072\u0075\u006e\u0065 \u0069\u006e\u0020\u0074\u0065\u0078\u0074\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u003a\u0020\u0025\u0023\u0078\u0020\u0028\u0025\u0063\u0029",_ebegb ,_ebegb ),Rune :_ebegb };
_gbfe ._aeag =append (_gbfe ._aeag ,_fafdf );_a .Log .Debug (_fafdf .Error ());if _gbfe ._bcbgg <=0{continue ;};_ebegb =_gbfe ._bcbgg ;};};_bfbeg :=_gegfg .Encode (string (_ebegb ));_ddfcb =append (_ddfcb ,_bfbeg ...);};if _ffaf .MultiFont !=nil {_ffaf .MultiFont .Reset ();
};};if len (_ddfcb )> 0{if _dabff {_cgca .SetStrokingColor (_cdfe (_ffaf .OutlineColor ));};if _eacgb {_cgca .Add_Tz (_ffaf .HorizontalScaling );};_abead :=_dfdeb [_baafc ][_gaded ];if _bcbcf {_abead =_dd .PdfObjectName (_g .Sprintf ("\u0046\u006f\u006e\u0074\u0025\u0064",_eeedc ));
_gbbb :=_fcaf ._faa .SetFontByName (_abead ,_befag .ToPdfObject ());if _gbbb !=nil {return _gbfe ,nil ,_gbbb ;};_eeedc ++;_bcbcf =false ;};_cgca .SetNonStrokingColor (_cdfe (_ffaf .Color )).Add_Tf (_abead ,_ffaf .FontSize ).Add_TJ ([]_dd .PdfObject {_dd .MakeStringFromBytes (_ddfcb )}...);
};if len (_bgbcc )> 0{_cgca .Add_EMC ();};_eddgd :=_edaaff [_gaded ]/1000.0;if _ffaf .Underline {_acbbfa :=_ffaf .UnderlineStyle .Color ;if _acbbfa ==nil {_acbbfa =_effa .Style .Color ;};_ebdga ,_dcgg ,_abafa :=_acbbfa .ToRGB ();_gdbb :=_fdbcc -_gbfe .X ;
_cceae :=_gddbb -_bbaf +_ffaf .TextRise -_ffaf .UnderlineStyle .Offset ;_aaga =append (_aaga ,&_gc .BasicLine {X1 :_gdbb ,Y1 :_cceae ,X2 :_gdbb +_eddgd ,Y2 :_cceae ,LineWidth :_effa .Style .UnderlineStyle .Thickness ,LineColor :_db .NewPdfColorDeviceRGB (_ebdga ,_dcgg ,_abafa )});
};for _ebgag ,_afeda :=range _effa ._aecg {var _bdabb *_dd .PdfObjectArray ;if len (_effa ._eeddd )==_ebgag {switch _fegbd :=_afeda .GetContext ().(type ){case *_db .PdfAnnotationLink :_bdabb =_dd .MakeArray ();_fegbd .Rect =_bdabb ;_cfcgc ,_egcd :=_fegbd .Dest .(*_dd .PdfObjectArray );
if _egcd &&_cfcgc .Len ()==5{_gabc ,_dbded :=_cfcgc .Get (1).(*_dd .PdfObjectName );if _dbded &&_gabc .String ()=="\u0058\u0059\u005a"{_eefbc ,_gdfg :=_dd .GetNumberAsFloat (_cfcgc .Get (3));if _gdfg ==nil {_cfcgc .Set (3,_dd .MakeFloat (_gbfe .PageHeight -_eefbc ));
};};};case *_db .PdfAnnotationHighlight :_bdabb =_dd .MakeArray ();_fegbd .Rect =_bdabb ;_bgfd :=_fdbcc ;_dgdba :=_gddbb +_ffaf .TextRise ;_fdca :=_aebfa (&_db .PdfRectangle {Llx :_bgfd ,Lly :_dgdba ,Urx :_bgfd +_eddgd ,Ury :_dgdba +_fddgg },_eaec ._bbacbd );
_fegbd .QuadPoints =_dd .MakeArrayFromFloats ([]float64 {_fdca [0].X ,_fdca [0].Y ,_fdca [1].X ,_fdca [1].Y ,_fdca [3].X ,_fdca [3].Y ,_fdca [2].X ,_fdca [2].Y });};_effa ._eeddd =append (_effa ._eeddd ,true );};if _bdabb !=nil {_cfcfb :=_gc .NewPoint (_fdbcc -_gbfe .X ,_gddbb +_ffaf .TextRise -_bbaf ).Rotate (_eaec ._bbacbd );
_cfcfb .X +=_gbfe .X ;_cfcfb .Y +=_bbaf ;_acacc ,_daegg ,_dcgb ,_bcbda :=_adeg (_eddgd ,_fddgg ,_eaec ._bbacbd );_cfcfb .X +=_acacc ;_cfcfb .Y +=_daegg ;_bdabb .Clear ();_bdabb .Append (_dd .MakeFloat (_cfcfb .X ));_bdabb .Append (_dd .MakeFloat (_cfcfb .Y ));
_bdabb .Append (_dd .MakeFloat (_cfcfb .X +_dcgb ));_bdabb .Append (_dd .MakeFloat (_cfcfb .Y +_bcbda ));};_fcaf .AddAnnotation (_afeda );};_fdbcc +=_eddgd ;if _ecdbe {_cgca .Add_w (1.0);};if _dabff {_cgca .Add_RG (0.0,0.0,0.0);};if _ccbac {_cgca .Add_Tr (int64 (TextRenderingModeFill ));
};if _fcedf {_cgca .Add_Tc (0);};if _feebb {_cgca .Add_Ts (0);};if _eacgb {_cgca .Add_Tz (DefaultHorizontalScaling );};if _cgfa !=0.0{_cgca .Translate (0,-_cgfa );_cgfa =0.0;};};};if len (_gfdd )> 0{_cgca .Add_EMC ();};_cgca .Add_ET ();for _ ,_gbgad :=range _aaga {_cgca .SetStrokingColor (_gbgad .LineColor ).Add_w (_gbgad .LineWidth ).Add_m (_gbgad .X1 ,_gbgad .Y1 ).Add_l (_gbgad .X2 ,_gbgad .Y2 ).Add_s ();
};_cgca .Add_Q ();_dcacf :=_cgca .Operations ();_dcacf .WrapIfNeeded ();_fcaf .addWrappedContents (_dcacf );if _gdbda {_cdfb :=_cefa ;_gbfe .Y +=_cdfb ;_gbfe .Height -=_cdfb ;if _gbfe .Inline {_gbfe .X +=_eaec .Width ()+_eaec ._feeea .Right ;};};return _gbfe ,_gafed ,nil ;
};

// ConvertToBinary converts current image data into binary (Bi-level image) format.
// If provided image is RGB or GrayScale the function converts it into binary image
// using histogram auto threshold method.
func (_bdeea *Image )ConvertToBinary ()error {return _bdeea ._fbebf .ConvertToBinary ()};

// This method is not supported by Border component and exists solely to satisfy the Drawable interface.
func (_bga *border )SetStructureType (structureType _db .StructureType ){};

// SetBackgroundColor set background color of the shading area.
//
// By default the background color is set to white.
func (_fabcd *shading )SetBackgroundColor (backgroundColor Color ){_fabcd ._cgbf =backgroundColor };

// Block contains a portion of PDF Page contents. It has a width and a position and can
// be placed anywhere on a Page.  It can even contain a whole Page, and is used in the creator
// where each Drawable object can output one or more blocks, each representing content for separate pages
// (typically needed when Page breaks occur).
type Block struct{_fd *_eg .ContentStreamOperations ;_faa *_db .PdfPageResources ;_fbb Positioning ;_ced ,_fda float64 ;_fedd float64 ;_dbe float64 ;_da float64 ;_fg Margins ;_ecd []*_db .PdfAnnotation ;};

// SetStructureType sets the structure type for the text chunk.
func (_aceab *TextChunk )SetStructureType (structureType _db .StructureType ){if _aceab ._gefgc ==nil {_aceab ._gefgc =_db .NewStructureTagInfo ();};_aceab ._gefgc .StructureType =structureType ;};

// SetMarkedContentID sets the marked content id for the chart.
func (_fcb *Chart )SetMarkedContentID (mcid int64 ){if _fcb ._aaf ==nil {_fcb ._aaf =_db .NewStructureTagInfo ();_fcb ._aaf .StructureType =_db .StructureTypeFigure ;};_fcb ._aaf .Mcid =mcid ;};func _dfge ()*Division {return &Division {_gbfd :true }};

// TextStyle is a collection of properties that can be assigned to a text chunk.
type TextStyle struct{

// Color represents the color of the text.
Color Color ;

// OutlineColor represents the color of the text outline.
OutlineColor Color ;

// MultiFont represents an encoder that accepts multiple fonts and selects the correct font for encoding.
MultiFont *_db .MultipleFontEncoder ;

// Font represents the font the text will use.
Font *_db .PdfFont ;

// FontSize represents the size of the font.
FontSize float64 ;

// OutlineSize represents the thickness of the text outline.
OutlineSize float64 ;

// CharSpacing represents the character spacing.
CharSpacing float64 ;

// HorizontalScaling represents the percentage to horizontally scale
// characters by (default: 100). Values less than 100 will result in
// narrower text while values greater than 100 will result in wider text.
HorizontalScaling float64 ;

// RenderingMode represents the rendering mode.
RenderingMode TextRenderingMode ;

// Underline specifies if the text chunk is underlined.
Underline bool ;

// UnderlineStyle represents the style of the line used to underline text.
UnderlineStyle TextDecorationLineStyle ;

// TextRise specifies a vertical adjustment for text. It is useful for
// drawing subscripts/superscripts. A positive text rise value will
// produce superscript text, while a negative one will result in
// subscript text.
TextRise float64 ;};

// SetBorderWidth sets the border width.
func (_gcge *Polygon )SetBorderWidth (borderWidth float64 ){_gcge ._addde .BorderWidth =borderWidth };var (ColorBlack =ColorRGBFromArithmetic (0,0,0);ColorWhite =ColorRGBFromArithmetic (1,1,1);ColorRed =ColorRGBFromArithmetic (1,0,0);ColorGreen =ColorRGBFromArithmetic (0,1,0);
ColorBlue =ColorRGBFromArithmetic (0,0,1);ColorYellow =ColorRGBFromArithmetic (1,1,0););

// SetBorderOpacity sets the border opacity of the ellipse.
func (_fegd *Ellipse )SetBorderOpacity (opacity float64 ){_fegd ._deee =opacity };type commands struct{_egbaf []string ;_ddgaa map[string ]int ;_ggfec string ;_gfggb string ;};

// ColorGrayFrom8bit creates a Color from 8-bit (0-255) gray values.
// Example:
//
//	gray := ColorGrayFrom8bit(255, 0, 0)
func ColorGrayFrom8bit (g byte )Color {return grayColor {float64 (g )/255.0}};

// SetBorderColor sets the border color.
func (_gcgd *PolyBezierCurve )SetBorderColor (color Color ){_gcgd ._gefa .BorderColor =_cdfe (color )};

// SellerAddress returns the seller address used in the invoice template.
func (_gbac *Invoice )SellerAddress ()*InvoiceAddress {return _gbac ._eeddc };func (_bbffd *templateProcessor )parseParagraph (_deccd *templateNode ,_fffff *StyledParagraph )(interface{},error ){if _fffff ==nil {_fffff =_bbffd .creator .NewStyledParagraph ();
};for _ ,_gefaa :=range _deccd ._ccge .Attr {_bcbbb :=_gefaa .Value ;switch _feccfc :=_gefaa .Name .Local ;_feccfc {case "\u0066\u006f\u006e\u0074":_fffff .SetFont (_bbffd .parseFontAttr (_feccfc ,_bcbbb ));case "\u0066o\u006e\u0074\u002d\u0073\u0069\u007ae":_fffff .SetFontSize (_bbffd .parseFloatAttr (_feccfc ,_bcbbb ));
case "\u0074\u0065\u0078\u0074\u002d\u0061\u006c\u0069\u0067\u006e":_fffff .SetTextAlignment (_bbffd .parseTextAlignmentAttr (_feccfc ,_bcbbb ));case "l\u0069\u006e\u0065\u002d\u0068\u0065\u0069\u0067\u0068\u0074":_fffff .SetLineHeight (_bbffd .parseFloatAttr (_feccfc ,_bcbbb ));
case "e\u006e\u0061\u0062\u006c\u0065\u002d\u0077\u0072\u0061\u0070":_fffff .SetEnableWrap (_bbffd .parseBoolAttr (_feccfc ,_bcbbb ));case "\u0063\u006f\u006co\u0072":_fffff .SetFontColor (_bbffd .parseColorAttr (_feccfc ,_bcbbb ));case "\u0078":_fffff .SetPos (_bbffd .parseFloatAttr (_feccfc ,_bcbbb ),_fffff ._gbdad );
case "\u0079":_fffff .SetPos (_fffff ._ecgb ,_bbffd .parseFloatAttr (_feccfc ,_bcbbb ));case "\u0061\u006e\u0067l\u0065":_fffff .SetAngle (_bbffd .parseFloatAttr (_feccfc ,_bcbbb ));case "\u006d\u0061\u0072\u0067\u0069\u006e":_aafc :=_bbffd .parseMarginAttr (_feccfc ,_bcbbb );
_fffff .SetMargins (_aafc .Left ,_aafc .Right ,_aafc .Top ,_aafc .Bottom );case "\u006da\u0078\u002d\u006c\u0069\u006e\u0065s":_fffff .SetMaxLines (int (_bbffd .parseInt64Attr (_feccfc ,_bcbbb )));default:_bbffd .nodeLogDebug (_deccd ,"\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072t\u0065\u0064\u0020pa\u0072\u0061\u0067\u0072\u0061\u0070h\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073`\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069n\u0067\u002e",_feccfc );
};};return _fffff ,nil ;};

// CreateTableOfContents sets a function to generate table of contents.
func (_aed *Creator )CreateTableOfContents (genTOCFunc func (_adbg *TOC )error ){_aed ._bba =genTOCFunc };func _ecdf (_ddacg string )(*GraphicSVG ,error ){_aacd ,_dgfa :=ParseFromSVGFile (_ddacg );if _dgfa !=nil {return nil ,_dgfa ;};return _gcbe (_aacd );
};func (_gfcbe *Table )wrapContent (_cfffg DrawContext )error {if _gfcbe ._dffga {return nil ;};_gfcbe .sortCells ();_gdgaa :=func (_cbdbce *TableCell ,_cbgca int ,_bccbb int ,_eadg int )(_bfdg int ){if _eadg < 1{return -1;};_bcbdc :=0;for _ebce :=_bccbb +1;
_ebce < len (_gfcbe ._ccgf )-1;_ebce ++{_edbb :=_gfcbe ._ccgf [_ebce ];if _edbb ._efage ==_eadg &&_bcbdc !=_bccbb {_bcbdc =_ebce ;if (_edbb ._afdac < _cbdbce ._afdac &&_gfcbe ._fcegg > _edbb ._afdac )||_cbdbce ._afdac < _gfcbe ._fcegg {continue ;};break ;
};};_fbegc :=float64 (0.0);for _dcacg :=0;_dcacg < _cbdbce ._cgedc ;_dcacg ++{_fbegc +=_gfcbe ._gdbgf [_cbdbce ._efage +_dcacg -1];};_gaed :=_cbdbce .width (_gfcbe ._bdggd ,_cfffg .Width );var (_fgedd VectorDrawable ;_efabb =false ;);switch _afdba :=_cbdbce ._fbaee .(type ){case *StyledParagraph :_acabca :=_cfffg ;
_acabca .Height =_fa .Floor (_fbegc -_afdba ._feeea .Top -_afdba ._feeea .Bottom -0.5*_afdba .getTextHeight ());_acabca .Width =_gaed ;_adfeba ,_ecdbd ,_cfabf :=_afdba .split (_acabca );if _cfabf !=nil {_a .Log .Error ("\u0045\u0072\u0072o\u0072\u0020\u0077\u0072a\u0070\u0020\u0073\u0074\u0079\u006c\u0065d\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u003a\u0020\u0025\u0076",_cfabf .Error ());
};if _adfeba !=nil &&_ecdbd !=nil {_gfcbe ._ccgf [_bccbb ]._fbaee =_adfeba ;_fgedd =_ecdbd ;_efabb =true ;};};_gfcbe ._ccgf [_bccbb ]._cgedc =_cbdbce ._cgedc ;_cfffg .Height =_cfffg .PageHeight -_cfffg .Margins .Top -_cfffg .Margins .Bottom ;_acfga :=_cbdbce .cloneProps (nil );
if _efabb {_acfga ._fbaee =_fgedd ;};_acfga ._cgedc =_cbgca ;_acfga ._efage =_eadg +1;_acfga ._afdac =_cbdbce ._afdac ;if _acfga ._efage +_acfga ._cgedc -1> _gfcbe ._fgdc {for _ccbb :=_gfcbe ._fgdc ;_ccbb < _acfga ._efage +_acfga ._cgedc -1;_ccbb ++{_gfcbe ._fgdc ++;
_gfcbe ._gdbgf =append (_gfcbe ._gdbgf ,_gfcbe ._gfafc );};};_gfcbe ._ccgf =append (_gfcbe ._ccgf [:_bcbdc +1],append ([]*TableCell {_acfga },_gfcbe ._ccgf [_bcbdc +1:]...)...);return _bcbdc +1;};_bdcef :=func (_afbfd *TableCell ,_aacdb int ,_bgee int ,_acfce float64 )(_fedba int ){_gcgf :=_afbfd .width (_gfcbe ._bdggd ,_cfffg .Width );
_fccf :=_acfce ;_egbb :=1;_debdg :=_cfffg .Height ;if _debdg > 0{for _fccf > _debdg {_fccf -=_cfffg .Height ;_debdg =_cfffg .PageHeight -_cfffg .Margins .Top -_cfffg .Margins .Bottom ;_egbb ++;};};var (_edef VectorDrawable ;_fbbdd =false ;);switch _dgaa :=_afbfd ._fbaee .(type ){case *StyledParagraph :_agfca :=_cfffg ;
_agfca .Height =_fa .Floor (_cfffg .Height -_dgaa ._feeea .Top -_dgaa ._feeea .Bottom -0.5*_dgaa .getTextHeight ());_agfca .Width =_gcgf ;_fcba ,_faeaa ,_gccgg :=_dgaa .split (_agfca );if _gccgg !=nil {_a .Log .Error ("\u0045\u0072\u0072o\u0072\u0020\u0077\u0072a\u0070\u0020\u0073\u0074\u0079\u006c\u0065d\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u003a\u0020\u0025\u0076",_gccgg .Error ());
};if _fcba !=nil &&_faeaa !=nil {_gfcbe ._ccgf [_aacdb ]._fbaee =_fcba ;_edef =_faeaa ;_fbbdd =true ;};};if _egbb < 2{return -1;};if _gfcbe ._ccgf [_aacdb ]._efage +_egbb -1> _gfcbe ._fgdc {for _afde :=0;_afde < _egbb ;_afde ++{_gfcbe ._fgdc ++;_gfcbe ._gdbgf =append (_gfcbe ._gdbgf ,_gfcbe ._gfafc );
};};_cbgfb :=_acfce /float64 (_egbb );for _ffdce :=0;_ffdce < _egbb ;_ffdce ++{_gfcbe ._gdbgf [_bgee +_ffdce -1]=_cbgfb ;};_cfffg .Height =_cfffg .PageHeight -_cfffg .Margins .Top -_cfffg .Margins .Bottom ;_acagbb :=_afbfd .cloneProps (nil );if _fbbdd {_acagbb ._fbaee =_edef ;
};_acagbb ._cgedc =1;_acagbb ._efage =_bgee +_egbb -1;_acagbb ._afdac =_afbfd ._afdac ;_gfcbe ._ccgf =append (_gfcbe ._ccgf ,_acagbb );return len (_gfcbe ._ccgf );};_gegb :=1;_acef :=-1;for _babb :=0;_babb < len (_gfcbe ._ccgf );_babb ++{_dbfgg :=_gfcbe ._ccgf [_babb ];
if _acef ==_babb {_gegb =_dbfgg ._efage ;};if _dbfgg ._cgedc < 2{if _becg :=_gfcbe ._gdbgf [_dbfgg ._efage -1];_becg > _cfffg .Height {_acef =_bdcef (_dbfgg ,_babb ,_dbfgg ._efage ,_becg );continue ;};continue ;};_fdbd :=float64 (0);for _abfb :=0;_abfb < _dbfgg ._cgedc ;
_abfb ++{_fdbd +=_gfcbe ._gdbgf [_dbfgg ._efage +_abfb -1];};_fbfgb :=float64 (0);for _dabdf :=_gegb -1;_dabdf < _dbfgg ._efage -1;_dabdf ++{_fbfgb +=_gfcbe ._gdbgf [_dabdf ];};if _fdbd <=(_cfffg .Height -_fbfgb ){continue ;};_abag :=float64 (0.0);_bgegd :=_dbfgg ._cgedc ;
_badf :=-1;_agce :=1;for _dccc :=1;_dccc <=_dbfgg ._cgedc ;_dccc ++{if (_abag +_gfcbe ._gdbgf [_dbfgg ._efage +_dccc -2])> (_cfffg .Height -_fbfgb ){_agce --;break ;};_badf =_dbfgg ._efage +_dccc -1;_bgegd =_dbfgg ._cgedc -_dccc ;_abag +=_gfcbe ._gdbgf [_dbfgg ._efage +_dccc -2];
_agce ++;};if _dbfgg ._cgedc ==_bgegd {_cfffg .Height =_cfffg .PageHeight -_cfffg .Margins .Top -_cfffg .Margins .Bottom ;_gegb =_dbfgg ._efage ;_babb --;continue ;};if _bgegd > 0&&_dbfgg ._cgedc > _agce {_dbfgg ._cgedc =_agce ;_acef =_gdgaa (_dbfgg ,_bgegd ,_babb ,_badf );
if _babb +1==_acef {_babb --;};};_gegb =_dbfgg ._efage ;};_gfcbe .sortCells ();return nil ;};

// SetOpacity sets the cell's opacity in the range 0-1.
func (_bfdab *TableCell )SetOpacity (opacity float64 ){_bfdab ._ebbg =opacity };func (_gegdg *templateProcessor )parseBoolAttr (_agedg ,_efcg string )bool {_a .Log .Debug ("P\u0061\u0072\u0073\u0069\u006e\u0067 \u0062\u006f\u006f\u006c\u0020\u0061t\u0074\u0072\u0069\u0062\u0075\u0074\u0065:\u0020\u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073)\u002e",_agedg ,_efcg );
_ecde ,_ :=_fbf .ParseBool (_efcg );return _efcg ==""||_ecde ;};func (_gbee *GraphicSVGElement )setDefaultScaling (_agcd float64 ){_gbee ._aece =_agcd ;if _gbee .Style !=nil &&_gbee .Style .StrokeWidth > 0{_gbee .Style .StrokeWidth =_gbee .Style .StrokeWidth *_gbee ._aece ;
};for _ ,_ebeb :=range _gbee .Children {_ebeb .setDefaultScaling (_agcd );};};

// GenerateKDict generates a K dictionary for the PolyBezierCurve.
func (_adag *PolyBezierCurve )GenerateKDict ()(*_db .KDict ,error ){if _adag ._ceefc ==nil {return nil ,_g .Errorf ("\u0070\u006f\u006cy\u0062\u0065\u007a\u0069\u0065\u0072\u0020\u0063\u0075\u0072\u0076\u0065\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075\u0072\u0065\u0020\u0069\u006e\u0066\u006f\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _adag ._ceefc .GenerateKDict (),nil ;};func (_dbdc *GraphicSVGElement )drawPath (_bagf *_eg .ContentCreator ,_abcba *_db .PdfPageResources ){_bagf .Add_q ();_dbdc .Style .toContentStream (_bagf ,_abcba ,_dbdc );_dbdedf (_dbdc ,_bagf );_caga ,_bedf :=_cbcaf (_dbdc .Attributes ["\u0064"]);
if _bedf !=nil {_a .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025s",_bedf .Error ());};var (_ffdae ,_ebcbc =0.0,0.0;_abgdg ,_deef =0.0,0.0;_dfgb *Command ;);for _ ,_gdag :=range _caga .Subpaths {for _ ,_efeef :=range _gdag .Commands {switch _fc .ToLower (_efeef .Symbol ){case "\u006d":_abgdg ,_deef =_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ;
if !_efeef .isAbsolute (){_abgdg ,_deef =_ffdae +_abgdg -_dbdc .ViewBox .X ,_ebcbc +_deef -_dbdc .ViewBox .Y ;};_bagf .Add_m (_cg .RoundFloat (_abgdg ,3),_cg .RoundFloat (_deef ,3));_ffdae ,_ebcbc =_abgdg ,_deef ;case "\u0063":_acae ,_gaca ,_degdb ,_bccd ,_aggaa ,_cdgfe :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ,_efeef .Params [2]*_dbdc ._aece ,_efeef .Params [3]*_dbdc ._aece ,_efeef .Params [4]*_dbdc ._aece ,_efeef .Params [5]*_dbdc ._aece ;
if !_efeef .isAbsolute (){_acae ,_gaca ,_degdb ,_bccd ,_aggaa ,_cdgfe =_ffdae +_acae ,_ebcbc +_gaca ,_ffdae +_degdb ,_ebcbc +_bccd ,_ffdae +_aggaa ,_ebcbc +_cdgfe ;};_bagf .Add_c (_cg .RoundFloat (_acae ,3),_cg .RoundFloat (_gaca ,3),_cg .RoundFloat (_degdb ,3),_cg .RoundFloat (_bccd ,3),_cg .RoundFloat (_aggaa ,3),_cg .RoundFloat (_cdgfe ,3));
_ffdae ,_ebcbc =_aggaa ,_cdgfe ;case "\u0073":_acace ,_abebg ,_aacea ,_dabg :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ,_efeef .Params [2]*_dbdc ._aece ,_efeef .Params [3]*_dbdc ._aece ;if !_efeef .isAbsolute (){_acace ,_abebg ,_aacea ,_dabg =_ffdae +_acace ,_ebcbc +_abebg ,_ffdae +_aacea ,_ebcbc +_dabg ;
};_bagf .Add_c (_cg .RoundFloat (_ffdae ,3),_cg .RoundFloat (_ebcbc ,3),_cg .RoundFloat (_acace ,3),_cg .RoundFloat (_abebg ,3),_cg .RoundFloat (_aacea ,3),_cg .RoundFloat (_dabg ,3));_ffdae ,_ebcbc =_aacea ,_dabg ;case "\u006c":_becbf ,_eagcg :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ;
if !_efeef .isAbsolute (){_becbf ,_eagcg =_ffdae +_becbf ,_ebcbc +_eagcg ;};_bagf .Add_l (_cg .RoundFloat (_becbf ,3),_cg .RoundFloat (_eagcg ,3));_ffdae ,_ebcbc =_becbf ,_eagcg ;case "\u0068":_beab :=_efeef .Params [0]*_dbdc ._aece ;if !_efeef .isAbsolute (){_beab =_ffdae +_beab ;
};_bagf .Add_l (_cg .RoundFloat (_beab ,3),_cg .RoundFloat (_ebcbc ,3));_ffdae =_beab ;case "\u0076":_agaga :=_efeef .Params [0]*_dbdc ._aece ;if !_efeef .isAbsolute (){_agaga =_ebcbc +_agaga ;};_bagf .Add_l (_cg .RoundFloat (_ffdae ,3),_cg .RoundFloat (_agaga ,3));
_ebcbc =_agaga ;case "\u0071":_cceff ,_gdbea ,_ffec ,_eddaag :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ,_efeef .Params [2]*_dbdc ._aece ,_efeef .Params [3]*_dbdc ._aece ;if !_efeef .isAbsolute (){_cceff ,_gdbea ,_ffec ,_eddaag =_ffdae +_cceff ,_ebcbc +_gdbea ,_ffdae +_ffec ,_ebcbc +_eddaag ;
};_bcgf ,_facf :=_ba .QuadraticToCubicBezier (_ffdae ,_ebcbc ,_cceff ,_gdbea ,_ffec ,_eddaag );_bagf .Add_c (_cg .RoundFloat (_bcgf .X ,3),_cg .RoundFloat (_bcgf .Y ,3),_cg .RoundFloat (_facf .X ,3),_cg .RoundFloat (_facf .Y ,3),_cg .RoundFloat (_ffec ,3),_cg .RoundFloat (_eddaag ,3));
_ffdae ,_ebcbc =_ffec ,_eddaag ;case "\u0074":var _affb ,_dbbge _ba .Point ;_gdcfd ,_dbbbb :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ;if !_efeef .isAbsolute (){_gdcfd ,_dbbbb =_ffdae +_gdcfd ,_ebcbc +_dbbbb ;};if _dfgb !=nil &&_fc .ToLower (_dfgb .Symbol )=="\u0071"{_abcce :=_ba .Point {X :_dfgb .Params [0]*_dbdc ._aece ,Y :_dfgb .Params [1]*_dbdc ._aece };
_dcfe :=_ba .Point {X :_dfgb .Params [2]*_dbdc ._aece ,Y :_dfgb .Params [3]*_dbdc ._aece };_fgbd :=_dcfe .Mul (2.0).Sub (_abcce );_affb ,_dbbge =_ba .QuadraticToCubicBezier (_ffdae ,_ebcbc ,_fgbd .X ,_fgbd .Y ,_gdcfd ,_dbbbb );};_bagf .Add_c (_cg .RoundFloat (_affb .X ,3),_cg .RoundFloat (_affb .Y ,3),_cg .RoundFloat (_dbbge .X ,3),_cg .RoundFloat (_dbbge .Y ,3),_cg .RoundFloat (_gdcfd ,3),_cg .RoundFloat (_dbbbb ,3));
_ffdae ,_ebcbc =_gdcfd ,_dbbbb ;case "\u0061":_fegf ,_cfcbe :=_efeef .Params [0]*_dbdc ._aece ,_efeef .Params [1]*_dbdc ._aece ;_dafa :=_efeef .Params [2];_adfgd :=_efeef .Params [3]> 0;_eebbb :=_efeef .Params [4]> 0;_bdfdg ,_aegaa :=_efeef .Params [5]*_dbdc ._aece ,_efeef .Params [6]*_dbdc ._aece ;
if !_efeef .isAbsolute (){_bdfdg ,_aegaa =_ffdae +_bdfdg ,_ebcbc +_aegaa ;};_aaggd :=_ba .EllipseToCubicBeziers (_ffdae ,_ebcbc ,_fegf ,_cfcbe ,_dafa ,_adfgd ,_eebbb ,_bdfdg ,_aegaa );for _ ,_eabc :=range _aaggd {_bagf .Add_c (_cg .RoundFloat (_eabc [1].X ,3),_cg .RoundFloat ((_eabc [1].Y ),3),_cg .RoundFloat ((_eabc [2].X ),3),_cg .RoundFloat ((_eabc [2].Y ),3),_cg .RoundFloat ((_eabc [3].X ),3),_cg .RoundFloat ((_eabc [3].Y ),3));
};_ffdae ,_ebcbc =_bdfdg ,_aegaa ;case "\u007a":_bagf .Add_h ();};_dfgb =_efeef ;};};_dbdc .Style .fillStroke (_bagf );_bagf .Add_h ();_bagf .Add_Q ();};

// Link returns link information for this line.
func (_gdbbe *TOCLine )Link ()(_agbfc int64 ,_fgbbc ,_eddfcf float64 ){return _gdbbe ._ageffa ,_gdbbe ._fbcga ,_gdbbe ._ebafe ;};

// This method is not supported by Chapter component and exists solely to satisfy the Drawable interface.
func (_ddfe *Chapter )SetStructureType (structureType _db .StructureType ){};

// LevelOffset returns the amount of space an indentation level occupies.
func (_bcebc *TOCLine )LevelOffset ()float64 {return _bcebc ._acade };

// SetColor sets the color of the Paragraph text.
//
// Example:
//
//  1. p := NewParagraph("Red paragraph")
//     // Set to red color with a hex code:
//     p.SetColor(creator.ColorRGBFromHex("#ff0000"))
//
//  2. Make Paragraph green with 8-bit rgb values (0-255 each component)
//     p.SetColor(creator.ColorRGBFrom8bit(0, 255, 0)
//
//  3. Make Paragraph blue with arithmetic (0-1) rgb components.
//     p.SetColor(creator.ColorRGBFromArithmetic(0, 0, 1.0)
func (_cgdgg *Paragraph )SetColor (col Color ){_cgdgg ._ffgfg =col };func (_ggeef *templateProcessor )parseRadialGradientAttr (creator *Creator ,_dddcf string )Color {_adbef :=ColorBlack ;if _dddcf ==""{return _adbef ;};var (_ggeb error ;_eacbg =0.0;_ccbca =0.0;
_deacg =-1.0;_cegfe =_fc .Split (_dddcf [16:len (_dddcf )-1],"\u002c"););_defac :=_fc .Fields (_cegfe [0]);if len (_defac )==2&&_fc .TrimSpace (_defac [0])[0]!='#'{_eacbg ,_ggeb =_fbf .ParseFloat (_defac [0],64);if _ggeb !=nil {_a .Log .Debug ("\u0046a\u0069\u006ce\u0064\u0020\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0072a\u0064\u0069\u0061\u006c\u0020\u0067r\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0058\u0020\u0070\u006fs\u0069\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076",_ggeb );
};_ccbca ,_ggeb =_fbf .ParseFloat (_defac [1],64);if _ggeb !=nil {_a .Log .Debug ("\u0046a\u0069\u006ce\u0064\u0020\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0072a\u0064\u0069\u0061\u006c\u0020\u0067r\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0059\u0020\u0070\u006fs\u0069\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076",_ggeb );
};_cegfe =_cegfe [1:];};_efbdf :=_fc .TrimSpace (_cegfe [0]);if _efbdf [0]!='#'{_deacg ,_ggeb =_fbf .ParseFloat (_efbdf ,64);if _ggeb !=nil {_a .Log .Debug ("\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0070\u0061\u0072\u0073\u0069\u006eg\u0020\u0072\u0061\u0064\u0069\u0061l\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0073\u0069\u007ae\u003a\u0020\u0025\u0076",_ggeb );
};_cegfe =_cegfe [1:];};_bdfb ,_caebd :=_ggeef .processGradientColorPair (_cegfe );if _bdfb ==nil ||_caebd ==nil {return _adbef ;};_fbge :=creator .NewRadialGradientColor (_eacbg ,_ccbca ,0,_deacg ,[]*ColorPoint {});for _dgcg :=0;_dgcg < len (_bdfb );_dgcg ++{_fbge .AddColorStop (_bdfb [_dgcg ],_caebd [_dgcg ]);
};return _fbge ;};func (_gaba *Creator )getActivePage ()*_db .PdfPage {if _gaba ._abcd ==nil {if len (_gaba ._bcbe )==0{return nil ;};return _gaba ._bcbe [len (_gaba ._bcbe )-1];};return _gaba ._abcd ;};

// SetFillColor sets the fill color.
func (_efgde *PolyBezierCurve )SetFillColor (color Color ){_efgde ._eagb =color ;_efgde ._gefa .FillColor =_cdfe (color );};

// SetLineMargins sets the margins for all new lines of the table of contents.
func (_defgg *TOC )SetLineMargins (left ,right ,top ,bottom float64 ){_gbeec :=&_defgg ._dfcfb ;_gbeec .Left =left ;_gbeec .Right =right ;_gbeec .Top =top ;_gbeec .Bottom =bottom ;};

// NewLinearGradientColor creates a linear gradient color that could act as a color in other components.
func (_bedc *Creator )NewLinearGradientColor (colorPoints []*ColorPoint )*LinearShading {return _abcff (colorPoints );};func _bdf (_geeg *_eg .ContentStreamOperations ,_dbg *_db .PdfPageResources ,_gaf *_eg .ContentStreamOperations ,_dcfb *_db .PdfPageResources )error {_gda :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};
_fge :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};_acd :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};_effc :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};_add :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};_ddea :=map[_dd .PdfObjectName ]_dd .PdfObjectName {};
for _ ,_fac :=range *_gaf {switch _fac .Operand {case "\u0044\u006f":if len (_fac .Params )==1{if _ggec ,_fcg :=_fac .Params [0].(*_dd .PdfObjectName );_fcg {if _ ,_gcde :=_gda [*_ggec ];!_gcde {var _dca _dd .PdfObjectName ;_ebf ,_ :=_dcfb .GetXObjectByName (*_ggec );
if _ebf !=nil {_dca =*_ggec ;for {_eeb ,_ :=_dbg .GetXObjectByName (_dca );if _eeb ==nil ||_eeb ==_ebf {break ;};_dca =*_dd .MakeName (_gfdb (_dca .String ()));};};_dbg .SetXObjectByName (_dca ,_ebf );_gda [*_ggec ]=_dca ;};_gfd :=_gda [*_ggec ];_fac .Params [0]=&_gfd ;
};};case "\u0054\u0066":if len (_fac .Params )==2{if _dcfc ,_ffg :=_fac .Params [0].(*_dd .PdfObjectName );_ffg {if _ ,_eef :=_fge [*_dcfc ];!_eef {_gdab ,_edd :=_dcfb .GetFontByName (*_dcfc );_efe :=*_dcfc ;if _edd &&_gdab !=nil {_efe =_edf (_dcfc .String (),_gdab ,_dbg );
};_dbg .SetFontByName (_efe ,_gdab );_fge [*_dcfc ]=_efe ;};_gb :=_fge [*_dcfc ];_fac .Params [0]=&_gb ;};};case "\u0043\u0053","\u0063\u0073":if len (_fac .Params )==1{if _dec ,_ffd :=_fac .Params [0].(*_dd .PdfObjectName );_ffd {if _ ,_ag :=_acd [*_dec ];
!_ag {var _bfe _dd .PdfObjectName ;_fea ,_fccd :=_dcfb .GetColorspaceByName (*_dec );if _fccd {_bfe =*_dec ;for {_ddg ,_aac :=_dbg .GetColorspaceByName (_bfe );if !_aac ||_fea ==_ddg {break ;};_bfe =*_dd .MakeName (_gfdb (_bfe .String ()));};_dbg .SetColorspaceByName (_bfe ,_fea );
_acd [*_dec ]=_bfe ;}else {_a .Log .Debug ("C\u006fl\u006f\u0072\u0073\u0070\u0061\u0063\u0065\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064");};};if _bfa ,_cgbe :=_acd [*_dec ];_cgbe {_fac .Params [0]=&_bfa ;}else {_a .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u003a\u0020\u0043\u006f\u006co\u0072\u0073\u0070\u0061\u0063\u0065\u0020%\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",*_dec );
};};};case "\u0053\u0043\u004e","\u0073\u0063\u006e":if len (_fac .Params )==1{if _efef ,_dfc :=_fac .Params [0].(*_dd .PdfObjectName );_dfc {if _ ,_ccfc :=_effc [*_efef ];!_ccfc {var _bee _dd .PdfObjectName ;_ddc ,_gfa :=_dcfb .GetPatternByName (*_efef );
if _gfa {_bee =*_efef ;for {_ggag ,_dbc :=_dbg .GetPatternByName (_bee );if !_dbc ||_ggag ==_ddc {break ;};_bee =*_dd .MakeName (_gfdb (_bee .String ()));};_dbcf :=_dbg .SetPatternByName (_bee ,_ddc .ToPdfObject ());if _dbcf !=nil {return _dbcf ;};_effc [*_efef ]=_bee ;
};};if _afe ,_ffa :=_effc [*_efef ];_ffa {_fac .Params [0]=&_afe ;};};};case "\u0073\u0068":if len (_fac .Params )==1{if _dgc ,_ddag :=_fac .Params [0].(*_dd .PdfObjectName );_ddag {if _ ,_dagb :=_add [*_dgc ];!_dagb {var _dbb _dd .PdfObjectName ;_cecg ,_bcc :=_dcfb .GetShadingByName (*_dgc );
if _bcc {_dbb =*_dgc ;for {_dbbc ,_gab :=_dbg .GetShadingByName (_dbb );if !_gab ||_cecg ==_dbbc {break ;};_dbb =*_dd .MakeName (_gfdb (_dbb .String ()));};_gcf :=_dbg .SetShadingByName (_dbb ,_cecg .ToPdfObject ());if _gcf !=nil {_a .Log .Debug ("E\u0052\u0052\u004f\u0052 S\u0065t\u0020\u0073\u0068\u0061\u0064i\u006e\u0067\u003a\u0020\u0025\u0076",_gcf );
return _gcf ;};_add [*_dgc ]=_dbb ;}else {_a .Log .Debug ("\u0053\u0068\u0061\u0064\u0069\u006e\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};};if _eac ,_dea :=_add [*_dgc ];_dea {_fac .Params [0]=&_eac ;}else {_a .Log .Debug ("E\u0072\u0072\u006f\u0072\u003a\u0020S\u0068\u0061\u0064\u0069\u006e\u0067\u0020\u0025\u0073 \u006e\u006f\u0074 \u0066o\u0075\u006e\u0064",*_dgc );
};};};case "\u0067\u0073":if len (_fac .Params )==1{if _gea ,_gdba :=_fac .Params [0].(*_dd .PdfObjectName );_gdba {if _ ,_cbf :=_ddea [*_gea ];!_cbf {var _bdg _dd .PdfObjectName ;_dgf ,_ffb :=_dcfb .GetExtGState (*_gea );if _ffb {_bdg =*_gea ;for {_eag ,_geea :=_dbg .GetExtGState (_bdg );
if !_geea ||_dgf ==_eag {break ;};_bdg =*_dd .MakeName (_gfdb (_bdg .String ()));};};_dbg .AddExtGState (_bdg ,_dgf );_ddea [*_gea ]=_bdg ;};_ecdg :=_ddea [*_gea ];_fac .Params [0]=&_ecdg ;};};};*_geeg =append (*_geeg ,_fac );};return nil ;};

// NewLine creates a new line between (x1, y1) to (x2, y2),
// using default attributes.
// NOTE: In relative positioning mode, `x1` and `y1` are calculated using the
// current context and `x2`, `y2` are used only to calculate the position of
// the second point in relation to the first one (used just as a measurement
// of size). Furthermore, when the fit mode is set to fill the context width,
// `x2` is set to the right edge coordinate of the context.
func (_gcaa *Creator )NewLine (x1 ,y1 ,x2 ,y2 float64 )*Line {return _ecag (x1 ,y1 ,x2 ,y2 )};

// GenerateKDict generates a K dictionary for the curve component.
func (_eaad *Curve )GenerateKDict ()(*_db .KDict ,error ){if _eaad ._fefg ==nil {return nil ,_g .Errorf ("\u0063\u0075\u0072v\u0065\u0020\u0073\u0074r\u0075\u0063\u0074\u0075\u0072\u0065\u0020i\u006e\u0066\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _eaad ._fefg .GenerateKDict (),nil ;};func (_cae *FilledCurve )draw (_eeae *Block ,_agag string )([]byte ,*_db .PdfRectangle ,error ){_eeadd :=_gc .NewCubicBezierPath ();for _ ,_aebf :=range _cae ._bec {_eeadd =_eeadd .AppendCurve (_aebf );};creator :=_eg .NewContentCreator ();
if _cae ._eae !=nil {creator .Add_BDC (*_dd .MakeName (string (_cae ._eae .StructureType )),map[string ]_dd .PdfObject {"\u004d\u0043\u0049\u0044":_dd .MakeInteger (_cae ._eae .Mcid )});};creator .Add_q ();if _cae .FillEnabled &&_cae ._fcef !=nil {_dbafa :=_cdfe (_cae ._fcef );
_fdbfc :=_gfae (_eeae ,_dbafa ,_cae ._fcef ,func ()Rectangle {_effe :=_gc .NewCubicBezierPath ();for _ ,_adge :=range _cae ._bec {_effe =_effe .AppendCurve (_adge );};_defa :=_effe .GetBoundingBox ();if _cae .BorderEnabled {_defa .Height +=_cae .BorderWidth ;
_defa .Width +=_cae .BorderWidth ;_defa .X -=_cae .BorderWidth /2;_defa .Y -=_cae .BorderWidth /2;};return Rectangle {_eeadac :_defa .X ,_abegd :_defa .Y ,_cdcgg :_defa .Width ,_cfedb :_defa .Height };});if _fdbfc !=nil {return nil ,nil ,_fdbfc ;};creator .SetNonStrokingColor (_dbafa );
};if _cae .BorderEnabled {if _cae ._ccde !=nil {creator .SetStrokingColor (_cdfe (_cae ._ccde ));};creator .Add_w (_cae .BorderWidth );};if len (_agag )> 1{creator .Add_gs (_dd .PdfObjectName (_agag ));};_gc .DrawBezierPathWithCreator (_eeadd ,creator );
creator .Add_h ();if _cae .FillEnabled &&_cae .BorderEnabled {creator .Add_B ();}else if _cae .FillEnabled {creator .Add_f ();}else if _cae .BorderEnabled {creator .Add_S ();};creator .Add_Q ();if _cae ._eae !=nil {creator .Add_EMC ();};_cdfa :=_eeadd .GetBoundingBox ();
if _cae .BorderEnabled {_cdfa .Height +=_cae .BorderWidth ;_cdfa .Width +=_cae .BorderWidth ;_cdfa .X -=_cae .BorderWidth /2;_cdfa .Y -=_cae .BorderWidth /2;};_dedg :=&_db .PdfRectangle {};_dedg .Llx =_cdfa .X ;_dedg .Lly =_cdfa .Y ;_dedg .Urx =_cdfa .X +_cdfa .Width ;
_dedg .Ury =_cdfa .Y +_cdfa .Height ;return creator .Bytes (),_dedg ,nil ;};

// SetAngle sets Image rotation angle in degrees.
func (_cgec *Image )SetAngle (angle float64 ){_cgec ._edebf =angle };

// AddSubtable copies the cells of the subtable in the table, starting with the
// specified position. The table row and column indices are 1-based, which
// makes the position of the first cell of the first row of the table 1,1.
// The table is automatically extended if the subtable exceeds its columns.
// This can happen when the subtable has more columns than the table or when
// one or more columns of the subtable starting from the specified position
// exceed the last column of the table.
func (_befc *Table )AddSubtable (row ,col int ,subtable *Table ){for _ ,_bfebg :=range subtable ._ccgf {_acead :=&TableCell {};*_acead =*_bfebg ;_acead ._dcdcf =_befc ;_acead ._afdac +=col -1;if _eaga :=_befc ._fcegg -(_acead ._afdac -1);_eaga < _acead ._edece {_befc ._fcegg +=_acead ._edece -_eaga ;
_befc .resetColumnWidths ();_a .Log .Debug ("\u0054a\u0062l\u0065\u003a\u0020\u0073\u0075\u0062\u0074\u0061\u0062\u006c\u0065 \u0065\u0078\u0063\u0065e\u0064\u0073\u0020\u0064\u0065s\u0074\u0069\u006e\u0061\u0074\u0069\u006f\u006e\u0020\u0074\u0061\u0062\u006c\u0065\u002e\u0020\u0045\u0078\u0070\u0061\u006e\u0064\u0069\u006e\u0067\u0020\u0074\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0025\u0064\u0020\u0063\u006fl\u0075\u006d\u006e\u0073\u002e",_befc ._fcegg );
};_acead ._efage +=row -1;_bacgd :=subtable ._gdbgf [_bfebg ._efage -1];if _acead ._efage > _befc ._fgdc {for _acead ._efage > _befc ._fgdc {_befc ._fgdc ++;_befc ._gdbgf =append (_befc ._gdbgf ,_befc ._gfafc );};_befc ._gdbgf [_acead ._efage -1]=_bacgd ;
}else {_befc ._gdbgf [_acead ._efage -1]=_fa .Max (_befc ._gdbgf [_acead ._efage -1],_bacgd );};_befc ._ccgf =append (_befc ._ccgf ,_acead );};_befc .sortCells ();};type pathParserError struct{_cfbda string };

// NoteStyle returns the style properties used to render the content of the
// invoice note sections.
func (_cecdf *Invoice )NoteStyle ()TextStyle {return _cecdf ._afafg };

// SetMarkedContentID sets the marked content identifier.
func (_eggee *Polygon )SetMarkedContentID (mcid int64 ){if _eggee ._daega ==nil {_eggee ._daega =_db .NewStructureTagInfo ();};_eggee ._daega .Mcid =mcid ;};

// NewCurve returns new instance of Curve between points (x1,y1) and (x2, y2) with control point (cx,cy).
func (_bea *Creator )NewCurve (x1 ,y1 ,cx ,cy ,x2 ,y2 float64 )*Curve {return _gege (x1 ,y1 ,cx ,cy ,x2 ,y2 );};

// GeneratePageBlocks draws the line on a new block representing the page.
// Implements the Drawable interface.
func (_egceb *Line )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var (_efdee []*Block ;_afdc =NewBlock (ctx .PageWidth ,ctx .PageHeight );_bagcc =ctx ;_feea ,_aecad =_egceb ._ggdbc ,ctx .PageHeight -_egceb ._geega ;_dbeee ,_agaef =_egceb ._cacg ,ctx .PageHeight -_egceb ._degba ;
);_cbga :=_egceb ._eddaa .IsRelative ();if _cbga {ctx .X +=_egceb ._fffd .Left ;ctx .Y +=_egceb ._fffd .Top ;ctx .Width -=_egceb ._fffd .Left +_egceb ._fffd .Right ;ctx .Height -=_egceb ._fffd .Top +_egceb ._fffd .Bottom ;_feea ,_aecad ,_dbeee ,_agaef =_egceb .computeCoords (ctx );
if _egceb .Height ()> ctx .Height {_efdee =append (_efdee ,_afdc );_afdc =NewBlock (ctx .PageWidth ,ctx .PageHeight );ctx .Page ++;_fcdg :=ctx ;_fcdg .Y =ctx .Margins .Top +_egceb ._fffd .Top ;_fcdg .X =ctx .Margins .Left +_egceb ._fffd .Left ;_fcdg .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom -_egceb ._fffd .Top -_egceb ._fffd .Bottom ;
_fcdg .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_egceb ._fffd .Left -_egceb ._fffd .Right ;ctx =_fcdg ;_feea ,_aecad ,_dbeee ,_agaef =_egceb .computeCoords (ctx );};};_cfgba :=_gc .BasicLine {X1 :_feea ,Y1 :_aecad ,X2 :_dbeee ,Y2 :_agaef ,LineColor :_cdfe (_egceb ._feedb ),Opacity :_egceb ._ffade ,LineWidth :_egceb ._gbgbb ,LineStyle :_egceb ._efabg ,DashArray :_egceb ._feda ,DashPhase :_egceb ._fafgc };
_dgda ,_fbbg :=_afdc .setOpacity (1.0,_egceb ._ffade );if _fbbg !=nil {return nil ,ctx ,_fbbg ;};_efafa ,_ ,_fbbg :=_cfgba .MarkedDraw (_dgda ,_egceb ._eegb );if _fbbg !=nil {return nil ,ctx ,_fbbg ;};if _fbbg =_afdc .addContentsByString (string (_efafa ));
_fbbg !=nil {return nil ,ctx ,_fbbg ;};if _cbga {ctx .X =_bagcc .X ;ctx .Width =_bagcc .Width ;_deag :=_egceb .Height ();ctx .Y +=_deag +_egceb ._fffd .Bottom ;ctx .Height -=_deag ;}else {ctx =_bagcc ;};_efdee =append (_efdee ,_afdc );return _efdee ,ctx ,nil ;
};

// This method is not supported by Border component and exists solely to satisfy the Drawable interface.
func (_ceggf *border )SetMarkedContentID (id int64 ){};

// NewRow makes a new row and inserts it into the table at the current position.
func (_daeg *Grid )NewRow ()*GridRow {_deab :=&GridRow {_deege :_daeg ._gfeb ,_bacb :len (_daeg ._bgcb ),_dfdbg :_daeg };_daeg ._bgcb =append (_daeg ._bgcb ,_deab );return _deab ;};func _edf (_gcg string ,_cfg _dd .PdfObject ,_ecb *_db .PdfPageResources )_dd .PdfObjectName {_dee :=_fc .TrimRightFunc (_fc .TrimSpace (_gcg ),func (_bdbg rune )bool {return _ec .IsNumber (_bdbg )});
if _dee ==""{_dee ="\u0046\u006f\u006e\u0074";};_faad :=0;_fbc :=_dd .PdfObjectName (_gcg );for {_dgfe ,_bbd :=_ecb .GetFontByName (_fbc );if !_bbd ||_dgfe ==_cfg {break ;};_faad ++;_fbc =_dd .PdfObjectName (_g .Sprintf ("\u0025\u0073\u0025\u0064",_dee ,_faad ));
};return _fbc ;};func _dfdcc (_fgcc *_db .PdfFont ,_dceb float64 )*fontMetrics {_bdfae :=&fontMetrics {};if _fgcc ==nil {_a .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0066\u006f\u006e\u0074\u0020\u0069s\u0020\u006e\u0069\u006c");return _bdfae ;
};_cfebg ,_dfcd :=_fgcc .GetFontDescriptor ();if _dfcd !=nil {_a .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0067\u0065t\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063ri\u0070\u0074\u006fr\u003a \u0025\u0076",_dfcd );
return _bdfae ;};if _bdfae ._eggd ,_dfcd =_cfebg .GetCapHeight ();_dfcd !=nil {_a .Log .Trace ("\u0057\u0041\u0052\u004e\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0067\u0065\u0074\u0020f\u006f\u006e\u0074\u0020\u0063\u0061\u0070\u0020\u0068\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_dfcd );
};if int (_bdfae ._eggd )<=0{_a .Log .Trace ("\u0057\u0041\u0052\u004e\u003a\u0020\u0043\u0061p\u0020\u0048\u0065ig\u0068\u0074\u0020\u006e\u006f\u0074 \u0061\u0076\u0061\u0069\u006c\u0061\u0062\u006c\u0065\u0020\u002d\u0020\u0073\u0065\u0074t\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u00310\u0030\u0030");
_bdfae ._eggd =1000;};_bdfae ._eggd *=_dceb /1000.0;if _bdfae ._dcba ,_dfcd =_cfebg .GetXHeight ();_dfcd !=nil {_a .Log .Trace ("\u0057\u0041R\u004e\u003a\u0020\u0055n\u0061\u0062l\u0065\u0020\u0074\u006f\u0020\u0067\u0065\u0074 \u0066\u006f\u006e\u0074\u0020\u0078\u002d\u0068\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_dfcd );
};_bdfae ._dcba *=_dceb /1000.0;if _bdfae ._ecbbg ,_dfcd =_cfebg .GetAscent ();_dfcd !=nil {_a .Log .Trace ("W\u0041\u0052\u004e\u003a\u0020\u0055n\u0061\u0062\u006c\u0065\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u0066\u006fn\u0074\u0020\u0061\u0073\u0063\u0065\u006e\u0074\u003a\u0020%\u0076",_dfcd );
};_bdfae ._ecbbg *=_dceb /1000.0;if _bdfae ._agcc ,_dfcd =_cfebg .GetDescent ();_dfcd !=nil {_a .Log .Trace ("\u0057\u0041RN\u003a\u0020\u0055n\u0061\u0062\u006c\u0065 to\u0020ge\u0074\u0020\u0066\u006f\u006e\u0074\u0020de\u0073\u0063\u0065\u006e\u0074\u003a\u0020%\u0076",_dfcd );
};_bdfae ._agcc *=_dceb /1000.0;return _bdfae ;};var _gfdbca commands ;

// GetMargins returns the margins of the graphic svg (left, right, top, bottom).
func (_aagf *GraphicSVG )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _aagf ._edeb .Left ,_aagf ._edeb .Right ,_aagf ._edeb .Top ,_aagf ._edeb .Bottom ;};type containerDrawable interface{Drawable ;

// ContainerComponent checks if the component is allowed to be added into provided 'container' and returns
// preprocessed copy of itself. If the component is not changed it is allowed to return itself in a callback way.
// If the component is not compatible with provided container this method should return an error.
ContainerComponent (_edgf Drawable )(Drawable ,error );};

// NewImage create a new image from a unidoc image (model.Image).
func (_ggff *Creator )NewImage (img *_db .Image )(*Image ,error ){return _cefg (img )};func _gbgee (_ddbdc []token ,_cfcfe string )([]token ,string ){if _cfcfe !=""{_ddbdc =append (_ddbdc ,token {_cfcfe ,false });_cfcfe ="";};return _ddbdc ,_cfcfe ;};func (_cacab *GraphicSVGElement )drawPolyline (_cbebf *_eg .ContentCreator ,_cdcgb *_db .PdfPageResources ){_cbebf .Add_q ();
_cacab .Style .toContentStream (_cbebf ,_cdcgb ,_cacab );_deca ,_ffccf :=_bdgcc (_cacab .Attributes ["\u0070\u006f\u0069\u006e\u0074\u0073"]);if _ffccf !=nil {_a .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0025\u0076",_ffccf );
return ;};if len (_deca )%2> 0{_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0069n\u0076\u0061l\u0069\u0064\u0020\u0070\u006f\u0069\u006e\u0074s\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u006ce\u006e\u0067\u0074\u0068");return ;
};for _deegee :=0;_deegee < len (_deca );{if _deegee ==0{_cbebf .Add_m (_deca [_deegee ]*_cacab ._aece ,_deca [_deegee +1]*_cacab ._aece );}else {_cbebf .Add_l (_deca [_deegee ]*_cacab ._aece ,_deca [_deegee +1]*_cacab ._aece );};_deegee +=2;};_cacab .Style .fillStroke (_cbebf );
_cbebf .Add_h ();_cbebf .Add_Q ();};

// CellHorizontalAlignment defines the table cell's horizontal alignment.
type CellHorizontalAlignment int ;

// GenerateKDict generates a K dictionary for the filled curve component.
func (_eebb *FilledCurve )GenerateKDict ()(*_db .KDict ,error ){if _eebb ._eae ==nil {return nil ,_g .Errorf ("\u0066\u0069\u006c\u006c\u0065\u0064\u0020\u0063\u0075\u0072\u0076\u0065\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075\u0072\u0065\u0020\u0069n\u0066\u006f\u0020\u0069\u0073 \u006e\u006ft\u0020\u0073\u0065\u0074");
};return _eebb ._eae .GenerateKDict (),nil ;};func (_gebf *templateProcessor )parseCellVerticalAlignmentAttr (_gcda ,_cabac string )CellVerticalAlignment {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u0065\u006c\u006c\u0020\u0076\u0065r\u0074\u0069\u0063\u0061\u006c\u0020\u0061\u006c\u0069\u0067\u006e\u006d\u0065n\u0074\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a (\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_gcda ,_cabac );
_gggdb :=map[string ]CellVerticalAlignment {"\u0074\u006f\u0070":CellVerticalAlignmentTop ,"\u006d\u0069\u0064\u0064\u006c\u0065":CellVerticalAlignmentMiddle ,"\u0062\u006f\u0074\u0074\u006f\u006d":CellVerticalAlignmentBottom }[_cabac ];return _gggdb ;
};func (_eaagd *StyledParagraph )wrapChunks (_dacg bool )error {if !_eaagd ._gfce ||int (_eaagd ._gfba )<=0{_eaagd ._cbba =[][]*TextChunk {_eaagd ._dgaca };return nil ;};if _eaagd ._fbfd {_eaagd .wrapWordChunks ();};_eaagd ._cbba =[][]*TextChunk {};var _efafad []*TextChunk ;
var _bacef float64 ;_gfage :=_ec .IsSpace ;if !_dacg {_gfage =func (rune )bool {return false };};_ebag :=_ecefd (_eaagd ._gfba *1000.0,0.000001);for _ ,_adccb :=range _eaagd ._dgaca {_fcac :=_adccb .Style ;_fggga :=_adccb ._aecg ;_cbgd :=_adccb .VerticalAlignment ;
var (_bbfd []rune ;_face []float64 ;);_gbadf :=_dd .IsTextWriteDirectionLTR (_adccb .Text );for _ ,_bfff :=range _adccb .Text {if _bfff =='\u000A'{if !_dacg {_bbfd =append (_bbfd ,_bfff );};_efafad =append (_efafad ,&TextChunk {Text :_fc .TrimRightFunc (string (_bbfd ),_gfage ),Style :_fcac ,_aecg :_afbb (_fggga ),VerticalAlignment :_cbgd ,_aabc :_adccb ._aabc ,_gefgc :_adccb ._gefgc ,_cbea :_adccb ._cbea ,_fcgd :_adccb ._fcgd });
if _beebe :=_eaagd .addLine (_efafad );!_beebe {return nil ;};_efafad =nil ;_bacef =0;_bbfd =nil ;_face =nil ;continue ;};_dgafd :=_bfff ==' ';_bbceg ,_fbgdc :=_fcac .Font .GetRuneMetrics (_bfff );if _bbceg .Wx ==0&&_fcac .MultiFont !=nil ||_fcac .MultiFont !=nil &&!_fbgdc {_bbceg ,_fbgdc =_fcac .MultiFont .GetRuneMetrics (_bfff );
};if !_fbgdc {_a .Log .Debug ("\u0052\u0075\u006e\u0065\u0020\u0063\u0068\u0061\u0072\u0020\u006d\u0065\u0074\u0072\u0069c\u0073 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0021\u0020\u0025\u0076\u000a",_bfff );return _ee .New ("\u0067\u006c\u0079\u0070\u0068\u0020\u0063\u0068\u0061\u0072\u0020m\u0065\u0074\u0072\u0069\u0063\u0073\u0020\u006d\u0069\u0073s\u0069\u006e\u0067");
};_dccb :=_fcac .FontSize *_bbceg .Wx *_fcac .horizontalScale ();_bdbb :=_dccb ;if !_dgafd {_bdbb =_dccb +_fcac .CharSpacing *1000.0;};if _bacef +_dccb > _ebag {_dfbef :=-1;if !_dgafd {for _fceb :=len (_bbfd )-1;_fceb >=0;_fceb --{if _bbfd [_fceb ]==' '{_dfbef =_fceb ;
break ;};};};if _eaagd ._fbfd {_dffg :=len (_efafad );if _dffg > 0{_efafad [_dffg -1].Text =_fc .TrimRightFunc (_efafad [_dffg -1].Text ,_gfage );_eaagd ._cbba =append (_eaagd ._cbba ,_efafad );_efafad =[]*TextChunk {};};_bbfd =append (_bbfd ,_bfff );_face =append (_face ,_bdbb );
if _dfbef >=0{_bbfd =_bbfd [_dfbef +1:];_face =_face [_dfbef +1:];};_bacef =0;for _ ,_adgd :=range _face {_bacef +=_adgd ;};if _bacef > _ebag {_gccge :=string (_bbfd [:len (_bbfd )-1]);_gccge =_dd .FormatWriteDirectionLTR (_gccge ,_gbadf );if !_dacg &&_dgafd {_gccge +="\u0020";
};_efafad =append (_efafad ,&TextChunk {Text :_fc .TrimRightFunc (_gccge ,_gfage ),Style :_fcac ,_aecg :_afbb (_fggga ),VerticalAlignment :_cbgd ,_aabc :_adccb ._aabc ,_gefgc :_adccb ._gefgc ,_cbea :_adccb ._cbea ,_fcgd :_adccb ._fcgd });if _adfee :=_eaagd .addLine (_efafad );
!_adfee {return nil ;};_efafad =[]*TextChunk {};_bbfd =[]rune {_bfff };_face =[]float64 {_bdbb };_bacef =_bdbb ;};continue ;};_bggdb :=string (_bbfd );if _dfbef >=0{_bggdb =string (_bbfd [0:_dfbef +1]);_bbfd =_bbfd [_dfbef +1:];_bbfd =append (_bbfd ,_bfff );
_face =_face [_dfbef +1:];_face =append (_face ,_bdbb );_bacef =0;for _ ,_fecab :=range _face {_bacef +=_fecab ;};}else {if _dgafd {_bacef =0;_bbfd =[]rune {};_face =[]float64 {};}else {_bacef =_bdbb ;_bbfd =[]rune {_bfff };_face =[]float64 {_bdbb };};
};_bggdb =_dd .FormatWriteDirectionLTR (_bggdb ,_gbadf );if !_dacg &&_dgafd {_bggdb +="\u0020";};_efafad =append (_efafad ,&TextChunk {Text :_fc .TrimRightFunc (_bggdb ,_gfage ),Style :_fcac ,_aecg :_afbb (_fggga ),VerticalAlignment :_cbgd ,_aabc :_adccb ._aabc ,_gefgc :_adccb ._gefgc ,_cbea :_adccb ._cbea ,_fcgd :_adccb ._fcgd });
if _ggggf :=_eaagd .addLine (_efafad );!_ggggf {return nil ;};_efafad =[]*TextChunk {};}else {_bacef +=_bdbb ;_bbfd =append (_bbfd ,_bfff );_face =append (_face ,_bdbb );};};if len (_bbfd )> 0{_fefbe :=_dd .FormatWriteDirectionLTR (string (_bbfd ),_gbadf );
_efafad =append (_efafad ,&TextChunk {Text :_fefbe ,Style :_fcac ,_aecg :_afbb (_fggga ),VerticalAlignment :_cbgd ,_aabc :_adccb ._aabc ,_gefgc :_adccb ._gefgc ,_cbea :_adccb ._cbea ,_fcgd :_adccb ._fcgd });};};if len (_efafad )> 0{if _eecff :=_eaagd .addLine (_efafad );
!_eecff {return nil ;};};return nil ;};

// AddTextItem appends a new item with the specified text to the list.
// The method creates a styled paragraph with the specified text and returns
// it so that the item style can be customized.
// The method also returns the marker used for the newly added item.
// The marker object can be used to change the text and style of the marker
// for the current item.
func (_defd *List )AddTextItem (text string )(*StyledParagraph ,*TextChunk ,error ){_ecdc :=_gcefe (_defd ._afeff );_ecdc .Append (text );_abeg ,_gfdbc :=_defd .Add (_ecdc );return _ecdc ,_abeg ,_gfdbc ;};

// SetMarkedContentID sets the marked content id for the list.
func (_feae *List )SetMarkedContentID (id int64 ){};func (_ddgce *List )markerWidth ()float64 {var _aabea float64 ;for _ ,_ddebc :=range _ddgce ._bbbe {_cfdcf :=_gcefe (_ddgce ._afeff );_cfdcf .SetEnableWrap (false );_cfdcf .SetTextAlignment (TextAlignmentRight );
_cfdcf .Append (_ddebc ._daedg .Text ).Style =_ddebc ._daedg .Style ;_daff :=_cfdcf .getTextWidth ()/1000.0;if _aabea < _daff {_aabea =_daff ;};};return _aabea ;};

// Sections returns the custom content sections of the invoice as
// title-content pairs.
func (_gdfca *Invoice )Sections ()[][2]string {return _gdfca ._edgb };

// BorderWidth returns the border width of the rectangle.
func (_gcfe *Rectangle )BorderWidth ()float64 {return _gcfe ._dfbcd };

// GeneratePageBlocks generate the Page blocks. Multiple blocks are generated
// if the contents wrap over multiple pages.
func (_fffbc *TOCLine )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_eecad :=ctx ;_fadad ,ctx ,_ddfbb :=_fffbc ._geffaf .GeneratePageBlocks (ctx );if _ddfbb !=nil {return _fadad ,ctx ,_ddfbb ;};if _fffbc ._cgfgd .IsRelative (){ctx .X =_eecad .X ;
};if _fffbc ._cgfgd .IsAbsolute (){return _fadad ,_eecad ,nil ;};return _fadad ,ctx ,nil ;};

// SetWidthLeft sets border width for left.
func (_fbe *border )SetWidthLeft (bw float64 ){_fbe ._edfb =bw };

// SetAngle would set the angle at which the gradient is rendered.
//
// The default angle would be 0 where the gradient would be rendered from left to right side.
func (_deaba *LinearShading )SetAngle (angle float64 ){_deaba ._bfdd =angle };func (_beebc *GridCell )width (_egdb []float64 ,_cgee float64 )float64 {_gbea :=float64 (0.0);for _gbcd :=0;_gbcd < _beebc ._caffg ;_gbcd ++{_gbea +=_egdb [_beebc ._gcfa +_gbcd ];
};return _gbea *_cgee ;};

// NewEllipse creates a new ellipse with the center at (`xc`, `yc`),
// having the specified width and height.
// NOTE: In relative positioning mode, `xc` and `yc` are calculated using the
// current context. Furthermore, when the fit mode is set to fill the available
// space, the ellipse is scaled so that it occupies the entire context width
// while maintaining the original aspect ratio.
func (_ecg *Creator )NewEllipse (xc ,yc ,width ,height float64 )*Ellipse {return _gbda (xc ,yc ,width ,height );};type templateTag struct{_acadd map[string ]struct{};_ecdfa func (*templateProcessor ,*templateNode )(interface{},error );};func (_gdfbc *Table )clone ()*Table {_fcfaf :=*_gdfbc ;
_fcfaf ._gdbgf =make ([]float64 ,len (_gdfbc ._gdbgf ));copy (_fcfaf ._gdbgf ,_gdfbc ._gdbgf );_fcfaf ._bdggd =make ([]float64 ,len (_gdfbc ._bdggd ));copy (_fcfaf ._bdggd ,_gdfbc ._bdggd );_fcfaf ._ccgf =make ([]*TableCell ,0,len (_gdfbc ._ccgf ));for _ ,_aadf :=range _gdfbc ._ccgf {_ffbbf :=*_aadf ;
_ffbbf ._dcdcf =&_fcfaf ;_fcfaf ._ccgf =append (_fcfaf ._ccgf ,&_ffbbf );};return &_fcfaf ;};

// Append adds a new text chunk to the paragraph.
func (_caca *StyledParagraph )Append (text string )*TextChunk {_ddbd :=NewTextChunk (text ,_caca ._acfg );return _caca .appendChunk (_ddbd );};func (_eff *Block )transform (_cd _bce .Matrix ){_dfde :=_eg .NewContentCreator ().Add_cm (_cd [0],_cd [1],_cd [3],_cd [4],_cd [6],_cd [7]).Operations ();
*_eff ._fd =append (*_dfde ,*_eff ._fd ...);_eff ._fd .WrapIfNeeded ();};var _cgacc =map[string ]*templateTag {"\u0070a\u0072\u0061\u0067\u0072\u0061\u0070h":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_cbaf },"\u0074\u0065\u0078\u0074\u002d\u0063\u0068\u0075\u006e\u006b":&templateTag {_acadd :map[string ]struct{}{"\u0070a\u0072\u0061\u0067\u0072\u0061\u0070h":struct{}{}},_ecdfa :_fefaa },"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_gbecf },"\u0074\u0061\u0062l\u0065":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_caefg },"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":&templateTag {_acadd :map[string ]struct{}{"\u0074\u0061\u0062l\u0065":struct{}{}},_ecdfa :_aced },"\u006c\u0069\u006e\u0065":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_dedcd },"\u0072e\u0063\u0074\u0061\u006e\u0067\u006ce":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_bfefce },"\u0065l\u006c\u0069\u0070\u0073\u0065":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_babg },"\u0069\u006d\u0061g\u0065":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_baca },"\u0063h\u0061\u0070\u0074\u0065\u0072":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_agbb },"\u0063h\u0061p\u0074\u0065\u0072\u002d\u0068\u0065\u0061\u0064\u0069\u006e\u0067":&templateTag {_acadd :map[string ]struct{}{"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_fecfb },"\u0063\u0068\u0061r\u0074":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_egefc },"\u0070\u0061\u0067\u0065\u002d\u0062\u0072\u0065\u0061\u006b":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{}},_ecdfa :_efbe },"\u0062\u0061\u0063\u006b\u0067\u0072\u006f\u0075\u006e\u0064":&templateTag {_acadd :map[string ]struct{}{"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{}},_ecdfa :_acec },"\u006c\u0069\u0073\u0074":&templateTag {_acadd :map[string ]struct{}{"\u0063r\u0065\u0061\u0074\u006f\u0072":struct{}{},"\u0062\u006c\u006fc\u006b":struct{}{},"\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e":struct{}{},"\u0074\u0061\u0062\u006c\u0065\u002d\u0063\u0065\u006c\u006c":struct{}{},"\u0063h\u0061\u0070\u0074\u0065\u0072":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_cggab },"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":&templateTag {_acadd :map[string ]struct{}{"\u006c\u0069\u0073\u0074":struct{}{}},_ecdfa :_eggb },"l\u0069\u0073\u0074\u002d\u006d\u0061\u0072\u006b\u0065\u0072":&templateTag {_acadd :map[string ]struct{}{"\u006c\u0069\u0073\u0074":struct{}{},"\u006ci\u0073\u0074\u002d\u0069\u0074\u0065m":struct{}{}},_ecdfa :_bdfa }};


// SetLineTitleStyle sets the style for the title part of all new lines
// of the table of contents.
func (_efega *TOC )SetLineTitleStyle (style TextStyle ){_efega ._cdaad =style };

// ColorRGBFrom8bit creates a Color from 8-bit (0-255) r,g,b values.
// Example:
//
//	red := ColorRGBFrom8Bit(255, 0, 0)
func ColorRGBFrom8bit (r ,g ,b byte )Color {return rgbColor {_dbbe :float64 (r )/255.0,_bggf :float64 (g )/255.0,_aec :float64 (b )/255.0};};func (_cdad *Invoice )generateTotalBlocks (_bbacg DrawContext )([]*Block ,DrawContext ,error ){_defga :=_cgdd (4);
_defga .SetMargins (0,0,10,10);_gfca :=[][2]*InvoiceCell {_cdad ._ageb };_gfca =append (_gfca ,_cdad ._efgb ...);_gfca =append (_gfca ,_cdad ._adgc );for _ ,_bbga :=range _gfca {_dggf ,_afcd :=_bbga [0],_bbga [1];if _afcd .Value ==""{continue ;};_defga .SkipCells (2);
_cbb :=_defga .NewCell ();_cbb .SetBackgroundColor (_dggf .BackgroundColor );_cbb .SetHorizontalAlignment (_afcd .Alignment );_cdad .setCellBorder (_cbb ,_dggf );_egeca :=_gcefe (_dggf .TextStyle );_egeca .SetMargins (0,0,2,1);_egeca .Append (_dggf .Value );
_cbb .SetContent (_egeca );_cbb =_defga .NewCell ();_cbb .SetBackgroundColor (_afcd .BackgroundColor );_cbb .SetHorizontalAlignment (_afcd .Alignment );_cdad .setCellBorder (_cbb ,_dggf );_egeca =_gcefe (_afcd .TextStyle );_egeca .SetMargins (0,0,2,1);
_egeca .Append (_afcd .Value );_cbb .SetContent (_egeca );};return _defga .GeneratePageBlocks (_bbacg );};const (CellVerticalAlignmentTop CellVerticalAlignment =iota ;CellVerticalAlignmentMiddle ;CellVerticalAlignmentBottom ;);

// SetPos sets the absolute position. Changes object positioning to absolute.
func (_gdeb *Image )SetPos (x ,y float64 ){_gdeb ._dfbc =PositionAbsolute ;_gdeb ._fabc =x ;_gdeb ._dff =y ;};func (_bgaf *List )ctxHeight (_beefa float64 )float64 {_beefa -=_bgaf ._gagc ;var _fffb float64 ;for _ ,_dfdbd :=range _bgaf ._bbbe {_fffb +=_dfdbd .ctxHeight (_beefa );
};return _fffb ;};

// Height returns the height of the rectangle.
// NOTE: the returned value does not include the border width of the rectangle.
func (_dada *Rectangle )Height ()float64 {return _dada ._cfedb };

// Height returns the height of the line.
func (_ddcfa *Line )Height ()float64 {_febe :=_ddcfa ._gbgbb ;if _ddcfa ._ggdbc ==_ddcfa ._cacg {_febe /=2;};return _fa .Abs (_ddcfa ._degba -_ddcfa ._geega )+_febe ;};

// AddSection adds a new content section at the end of the invoice.
func (_efdf *Invoice )AddSection (title ,content string ){_efdf ._edgb =append (_efdf ._edgb ,[2]string {title ,content });};func (_gadd *Creator )setActivePage (_eebcc *_db .PdfPage ){_gadd ._abcd =_eebcc };func (_bfaca *shading )generatePdfFunctions ()[]_db .PdfFunction {if len (_bfaca ._bacgg )==0{return nil ;
}else if len (_bfaca ._bacgg )<=2{_ceecb ,_defaa ,_cdgcg :=_bfaca ._bacgg [0]._efgba .ToRGB ();_cgadg ,_ebggb ,_cbdfg :=_bfaca ._bacgg [len (_bfaca ._bacgg )-1]._efgba .ToRGB ();return []_db .PdfFunction {&_db .PdfFunctionType2 {Domain :[]float64 {0.0,1.0},Range :[]float64 {0.0,1.0,0.0,1.0,0.0,1.0},N :1,C0 :[]float64 {_ceecb ,_defaa ,_cdgcg },C1 :[]float64 {_cgadg ,_ebggb ,_cbdfg }}};
}else {_ddfgc :=[]_db .PdfFunction {};_cdgfb :=[]float64 {};for _fgfcaf :=0;_fgfcaf < len (_bfaca ._bacgg )-1;_fgfcaf ++{_gbadb ,_gegg ,_gdbd :=_bfaca ._bacgg [_fgfcaf ]._efgba .ToRGB ();_bbfg ,_fbdg ,_gaacf :=_bfaca ._bacgg [_fgfcaf +1]._efgba .ToRGB ();
_gbadd :=&_db .PdfFunctionType2 {Domain :[]float64 {0.0,1.0},Range :[]float64 {0.0,1.0,0.0,1.0,0.0,1.0},N :1,C0 :[]float64 {_gbadb ,_gegg ,_gdbd },C1 :[]float64 {_bbfg ,_fbdg ,_gaacf }};_ddfgc =append (_ddfgc ,_gbadd );if _fgfcaf > 0{_cdgfb =append (_cdgfb ,_bfaca ._bacgg [_fgfcaf ]._acbfb );
};};_acfbb :=[]float64 {};for range _ddfgc {_acfbb =append (_acfbb ,[]float64 {0.0,1.0}...);};return []_db .PdfFunction {&_db .PdfFunctionType3 {Domain :[]float64 {0.0,1.0},Range :[]float64 {0.0,1.0,0.0,1.0,0.0,1.0},Functions :_ddfgc ,Bounds :_cdgfb ,Encode :_acfbb }};
};};

// ColorRGBFromHex converts color hex code to rgb color for using with creator.
// NOTE: If there is a problem interpreting the string, then will use black color and log a debug message.
// Example hex code: #ffffff -> (1,1,1) white.
func ColorRGBFromHex (hexStr string )Color {_afc :=rgbColor {};if (len (hexStr )!=4&&len (hexStr )!=7)||hexStr [0]!='#'{_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _afc ;
};var _cdce ,_cedd ,_ggf int ;if len (hexStr )==4{var _dddg ,_gfea ,_bda int ;_gfdg ,_cbd :=_g .Sscanf (hexStr ,"\u0023\u0025\u0031\u0078\u0025\u0031\u0078\u0025\u0031\u0078",&_dddg ,&_gfea ,&_bda );if _cbd !=nil {_a .Log .Debug ("\u0049\u006e\u0076a\u006c\u0069\u0064\u0020h\u0065\u0078\u0020\u0063\u006f\u0064\u0065:\u0020\u0025\u0073\u002c\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",hexStr ,_cbd );
return _afc ;};if _gfdg !=3{_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _afc ;};_cdce =_dddg *16+_dddg ;_cedd =_gfea *16+_gfea ;_ggf =_bda *16+_bda ;}else {_cac ,_degd :=_g .Sscanf (hexStr ,"\u0023\u0025\u0032\u0078\u0025\u0032\u0078\u0025\u0032\u0078",&_cdce ,&_cedd ,&_ggf );
if _degd !=nil {_a .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",hexStr );return _afc ;};if _cac !=3{_a .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u0068\u0065\u0078\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0073,\u0020\u006e\u0020\u0021\u003d\u0020\u0033 \u0028\u0025\u0064\u0029",hexStr ,_cac );
return _afc ;};};_fcdb :=float64 (_cdce )/255.0;_gfaa :=float64 (_cedd )/255.0;_bcb :=float64 (_ggf )/255.0;_afc ._dbbe =_fcdb ;_afc ._bggf =_gfaa ;_afc ._aec =_bcb ;return _afc ;};func _bffba (_efdad *_db .PdfFont )TextStyle {return TextStyle {Color :ColorRGBFrom8bit (0,0,0),Font :_efdad ,FontSize :10,OutlineSize :1,HorizontalScaling :DefaultHorizontalScaling ,UnderlineStyle :TextDecorationLineStyle {Offset :1,Thickness :1}};
};

// Color interface represents colors in the PDF creator.
type Color interface{ToRGB ()(float64 ,float64 ,float64 );};

// GetRowHeight returns the height of the specified row.
func (_fegfg *Table )GetRowHeight (row int )(float64 ,error ){if row < 1||row > len (_fegfg ._gdbgf ){return 0,_ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");};return _fegfg ._gdbgf [row -1],nil ;
};

// NewChart creates a new creator drawable based on the provided
// unichart chart component.
func NewChart (chart _fed .ChartRenderable )*Chart {return _beed (chart )};func _ggfbg (_fagca *_db .PdfAnnotation )*_db .PdfAnnotation {if _fagca ==nil {return nil ;};var _daggb *_db .PdfAnnotation ;switch _ccgb :=_fagca .GetContext ().(type ){case *_db .PdfAnnotationLink :if _bdfcf :=_fcee (_ccgb );
_bdfcf !=nil {_daggb =_bdfcf .PdfAnnotation ;};case *_db .PdfAnnotationHighlight :if _ccdd :=_cecbae (_ccgb );_ccdd !=nil {_daggb =_ccdd .PdfAnnotation ;};};return _daggb ;};func _fecfb (_bfcfc *templateProcessor ,_gfaca *templateNode )(interface{},error ){return _bfcfc .parseChapterHeading (_gfaca );
};

// NewStyledTOCLine creates a new table of contents line with the provided style.
func (_efaf *Creator )NewStyledTOCLine (number ,title ,page TextChunk ,level uint ,style TextStyle )*TOCLine {return _fabba (number ,title ,page ,level ,style );};

// Padding returns the padding of the component.
func (_ggfb *Division )Padding ()(_caa ,_acdd ,_dacd ,_ddfd float64 ){return _ggfb ._ddcg .Left ,_ggfb ._ddcg .Right ,_ggfb ._ddcg .Top ,_ggfb ._ddcg .Bottom ;};

// Columns returns all the columns in the invoice line items table.
func (_dfgf *Invoice )Columns ()[]*InvoiceCell {return _dfgf ._egfdb };

// MoveY moves the drawing context to absolute position y.
func (_bdfd *Creator )MoveY (y float64 ){_bdfd ._fcga .Y =y };func (_ceff *templateProcessor )parseList (_eddgg *templateNode )(interface{},error ){_bdfgb :=_ceff .creator .NewList ();for _ ,_fcege :=range _eddgg ._ccge .Attr {_gdbcc :=_fcege .Value ;switch _gcfg :=_fcege .Name .Local ;
_gcfg {case "\u0069\u006e\u0064\u0065\u006e\u0074":_bdfgb .SetIndent (_ceff .parseFloatAttr (_gcfg ,_gdbcc ));case "\u006d\u0061\u0072\u0067\u0069\u006e":_ffbbg :=_ceff .parseMarginAttr (_gcfg ,_gdbcc );_bdfgb .SetMargins (_ffbbg .Left ,_ffbbg .Right ,_ffbbg .Top ,_ffbbg .Bottom );
default:_ceff .nodeLogDebug (_eddgg ,"\u0055\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u006c\u0069\u0073\u0074 \u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_gcfg );
};};return _bdfgb ,nil ;};

// SetPos sets the position of the chart to the specified coordinates.
// This method sets the chart to use absolute positioning.
func (_fdaa *Chart )SetPos (x ,y float64 ){_fdaa ._gcac =PositionAbsolute ;_fdaa ._dfcb =x ;_fdaa ._cda =y ;};func (_dgd *Block )addWrappedContents (_cegg *_eg .ContentStreamOperations ){_dgd ._fd .WrapIfNeeded ();_cegg .WrapIfNeeded ();*_dgd ._fd =append (*_dgd ._fd ,*_cegg ...);
};

// Draw draws the drawable d on the block.
// Note that the drawable must not wrap, i.e. only return one block. Otherwise an error is returned.
func (_cgb *Block )Draw (d Drawable )error {_edg :=DrawContext {};_edg .Width =_cgb ._fedd ;_edg .Height =_cgb ._dbe ;_edg .PageWidth =_cgb ._fedd ;_edg .PageHeight =_cgb ._dbe ;_edg .X =0;_edg .Y =0;_ca ,_ ,_dcff :=d .GeneratePageBlocks (_edg );if _dcff !=nil {return _dcff ;
};if len (_ca )!=1{return ErrContentNotFit ;};for _ ,_dda :=range _ca {if _deg :=_cgb .mergeBlocks (_dda );_deg !=nil {return _deg ;};};return nil ;};const (FitModeNone FitMode =iota ;FitModeFillWidth ;);

// SetCoords sets the center coordinates of the ellipse.
func (_cbdbe *Ellipse )SetCoords (xc ,yc float64 ){_cbdbe ._bace =xc ;_cbdbe ._gdabb =yc };

// This method is not supported by Division component and exists solely to satisfy the Drawable interface.
func (_egdf *Division )SetStructureType (structureType _db .StructureType ){};

// Width returns the width of the rectangle.
// NOTE: the returned value does not include the border width of the rectangle.
func (_dgde *Rectangle )Width ()float64 {return _dgde ._cdcgg };func (_abaef *templateProcessor )parseLinkAttr (_cdba ,_abbde string )*_db .PdfAnnotation {_abbde =_fc .TrimSpace (_abbde );if _fc .HasPrefix (_abbde ,"\u0075\u0072\u006c(\u0027")&&_fc .HasSuffix (_abbde ,"\u0027\u0029")&&len (_abbde )> 7{return _gdfba (_abbde [5:len (_abbde )-2],"");
};if _fc .HasPrefix (_abbde ,"\u0070\u0061\u0067e\u0028")&&_fc .HasSuffix (_abbde ,"\u0029")&&len (_abbde )> 6{var (_ffbff error ;_fadb int64 ;_fbcbe float64 ;_ccabd float64 ;_gfefb =1.0;_gcfb =_fc .Split (_abbde [5:len (_abbde )-1],"\u002c"););_fadb ,_ffbff =_fbf .ParseInt (_fc .TrimSpace (_gcfb [0]),10,64);
if _ffbff !=nil {_a .Log .Error ("\u0046\u0061\u0069\u006c\u0065\u0064 \u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0070\u0061\u0067\u0065\u0020p\u0061\u0072\u0061\u006d\u0065\u0074\u0065r\u003a\u0020\u0025\u0076",_ffbff );return nil ;};if len (_gcfb )>=2{_fbcbe ,_ffbff =_fbf .ParseFloat (_fc .TrimSpace (_gcfb [1]),64);
if _ffbff !=nil {_a .Log .Error ("\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0070\u0061\u0072\u0073\u0069\u006eg\u0020\u0058\u0020\u0070\u006f\u0073i\u0074\u0069\u006f\u006e\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065r\u003a\u0020\u0025\u0076",_ffbff );
return nil ;};};if len (_gcfb )>=3{_ccabd ,_ffbff =_fbf .ParseFloat (_fc .TrimSpace (_gcfb [2]),64);if _ffbff !=nil {_a .Log .Error ("\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0070\u0061\u0072\u0073\u0069\u006eg\u0020\u0059\u0020\u0070\u006f\u0073i\u0074\u0069\u006f\u006e\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065r\u003a\u0020\u0025\u0076",_ffbff );
return nil ;};};if len (_gcfb )>=4{_gfefb ,_ffbff =_fbf .ParseFloat (_fc .TrimSpace (_gcfb [3]),64);if _ffbff !=nil {_a .Log .Error ("\u0046\u0061\u0069\u006c\u0065\u0064 \u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u007a\u006f\u006f\u006d\u0020p\u0061\u0072\u0061\u006d\u0065\u0074\u0065r\u003a\u0020\u0025\u0076",_ffbff );
return nil ;};};return _bagfe (_fadb -1,_fbcbe ,_ccabd ,_gfefb ,"");};return nil ;};

// GetMargins returns the Paragraph's margins: left, right, top, bottom.
func (_ccgd *Paragraph )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _ccgd ._fbgdf .Left ,_ccgd ._fbgdf .Right ,_ccgd ._fbgdf .Top ,_ccgd ._fbgdf .Bottom ;};func (_afda *Creator )initContext (){_afda ._fcga .X =_cg .RoundDefault (_afda ._gecf .Left );
_afda ._fcga .Y =_cg .RoundDefault (_afda ._gecf .Top );_afda ._fcga .Width =_cg .RoundDefault (_afda ._cfgg -_afda ._gecf .Right -_afda ._gecf .Left );_afda ._fcga .Height =_cg .RoundDefault (_afda ._adde -_afda ._gecf .Bottom -_afda ._gecf .Top );_afda ._fcga .PageHeight =_cg .RoundDefault (_afda ._adde );
_afda ._fcga .PageWidth =_cg .RoundDefault (_afda ._cfgg );_afda ._fcga .Margins =_afda ._gecf ;_afda ._fcga ._bcbgg =_afda .UnsupportedCharacterReplacement ;};

// GraphicSVGStyle represents style attributes for `GraphicSVG`.
type GraphicSVGStyle struct{FillColor string ;StrokeColor string ;StrokeWidth float64 ;FillOpacity float64 ;};

// SetAngle sets the rotation angle of the text.
func (_cfbac *Paragraph )SetAngle (angle float64 ){_cfbac ._cagf =angle };

// Width returns the cell's width based on the input draw context.
func (_fbfcc *TableCell )Width (ctx DrawContext )float64 {_baface :=float64 (0.0);for _dgaea :=0;_dgaea < _fbfcc ._edece ;_dgaea ++{_baface +=_fbfcc ._dcdcf ._bdggd [_fbfcc ._afdac +_dgaea -1];};_ggecc :=ctx .Width *_baface ;return _ggecc ;};

// GenerateKDict generates a K dictionary for the polyline.
func (_eefa *Polyline )GenerateKDict ()(*_db .KDict ,error ){if _eefa ._bafaf ==nil {return nil ,_g .Errorf ("\u0070\u006f\u006cyl\u0069\u006e\u0065\u0020\u0073\u0074\u0072\u0075\u0063t\u0075r\u0065 \u0069n\u0066\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _eefa ._bafaf .GenerateKDict (),nil ;};

// AddExternalLink adds a new external link to the paragraph.
// The text parameter represents the text that is displayed and the url
// parameter sets the destination of the link.
func (_eagcc *StyledParagraph )AddExternalLink (text ,url string )*TextChunk {_caac :=url ;if _fc .HasPrefix (url ,"\u0068\u0074\u0074\u0070"){_caac =text ;};_eebde :=NewTextChunk (text ,_eagcc ._ecff );_eebde .AddAnnotation (_gdfba (url ,_caac ));return _eagcc .appendChunk (_eebde );
};func _aedgg (_dceae *_db .PdfFont )TextStyle {return TextStyle {Color :ColorRGBFrom8bit (0,0,238),Font :_dceae ,FontSize :10,OutlineSize :1,HorizontalScaling :DefaultHorizontalScaling ,UnderlineStyle :TextDecorationLineStyle {Offset :1,Thickness :1}};
};func _cacac (_gafa string ,_efefd int )(float64 ,error ){_ffgbc ,_eaecb :=_ggad (_gafa );_gcee ,_gbgc :=_fbf .ParseFloat (_ffgbc ,_efefd );if _gbgc !=nil {return 0,_gbgc ;};if _acfgce ,_ebbd :=_eca [_eaecb ];_ebbd {_gcee =_gcee *_acfgce ;}else {_gcee =_gcee *_gce ;
};return _gcee ,nil ;};

// AddLine adds a new line with the provided style to the table of contents.
func (_gecff *TOC )AddLine (line *TOCLine )*TOCLine {if line ==nil {return nil ;};_gecff ._bebaa =append (_gecff ._bebaa ,line );return line ;};

// SetCompactMode sets the compact mode flag for this table.
//
// By enabling compact mode, table cell that contains Paragraph/StyleParagraph
// would not add extra height when calculating it's height.
//
// The default value is false.
func (_geabb *Table )SetCompactMode (enable bool ){_geabb ._cbge =enable };

// SetStructureType sets the structure type for the filled curve.
func (_cbcea *FilledCurve )SetStructureType (structureType _db .StructureType ){if _cbcea ._eae ==nil {_cbcea ._eae =_db .NewStructureTagInfo ();};_cbcea ._eae .StructureType =structureType ;};func (_fced *Invoice )drawAddress (_ggfc *InvoiceAddress )[]*StyledParagraph {var _cfdd []*StyledParagraph ;
if _ggfc .Heading !=""{_cedda :=_gcefe (_fced ._adbac );_cedda .SetMargins (0,0,0,7);_cedda .Append (_ggfc .Heading );_cfdd =append (_cfdd ,_cedda );};_egecd :=_gcefe (_fced ._eefg );_egecd .SetLineHeight (1.2);_edbd :=_ggfc .Separator ;if _edbd ==""{_edbd =_fced ._agae ;
};_decc :=_ggfc .City ;if _ggfc .State !=""{if _decc !=""{_decc +=_edbd ;};_decc +=_ggfc .State ;};if _ggfc .Zip !=""{if _decc !=""{_decc +=_edbd ;};_decc +=_ggfc .Zip ;};if _ggfc .Name !=""{_egecd .Append (_ggfc .Name +"\u000a");};if _ggfc .Street !=""{_egecd .Append (_ggfc .Street +"\u000a");
};if _ggfc .Street2 !=""{_egecd .Append (_ggfc .Street2 +"\u000a");};if _decc !=""{_egecd .Append (_decc +"\u000a");};if _ggfc .Country !=""{_egecd .Append (_ggfc .Country +"\u000a");};_bdeb :=_gcefe (_fced ._eefg );_bdeb .SetLineHeight (1.2);_bdeb .SetMargins (0,0,7,0);
if _ggfc .Phone !=""{_bdeb .Append (_ggfc .fmtLine (_ggfc .Phone ,"\u0050h\u006f\u006e\u0065\u003a\u0020",_ggfc .HidePhoneLabel ));};if _ggfc .Email !=""{_bdeb .Append (_ggfc .fmtLine (_ggfc .Email ,"\u0045m\u0061\u0069\u006c\u003a\u0020",_ggfc .HideEmailLabel ));
};_cfdd =append (_cfdd ,_egecd ,_bdeb );return _cfdd ;};

// GeneratePageBlocks generates the table page blocks. Multiple blocks are
// generated if the contents wrap over multiple pages.
// Implements the Drawable interface.
func (_fbfbc *Table )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_afaca :=_fbfbc ;if _fbfbc ._dffga {_afaca =_fbfbc .clone ();};return _cafg (_afaca ,ctx );};

// Ellipse defines an ellipse with a center at (xc,yc) and a specified width and height.  The ellipse can have a colored
// fill and/or border with a specified width.
// Implements the Drawable interface and can be drawn on PDF using the Creator.
type Ellipse struct{_bace float64 ;_gdabb float64 ;_fdgf float64 ;_gaabd float64 ;_cddg Positioning ;_efcb Color ;_cebc float64 ;_bcgd Color ;_daa float64 ;_deee float64 ;_dafc Margins ;_dbcd FitMode ;_cgbg *_db .StructureTagInfo ;};

// SetMarkedContentID sets the marked content id for the table cell.
func (_aaded *TableCell )SetMarkedContentID (mcid int64 ){if _aaded ._cdffb ==nil {_aaded ._cdffb =_db .NewStructureTagInfo ();};_aaded ._cdffb .Mcid =mcid ;};

// SetBuyerAddress sets the buyer address of the invoice.
func (_ffdcd *Invoice )SetBuyerAddress (address *InvoiceAddress ){_ffdcd ._egde =address };

// This method is not supported by Block component and exists solely to satisfy the Drawable interface.
func (_fca *Block )SetMarkedContentID (id int64 ){};func _baefa (_fcccf *Creator ,_cgce string ,_cdda []byte ,_gedfgc *TemplateOptions ,_dfgaf componentRenderer )*templateProcessor {if _gedfgc ==nil {_gedfgc =&TemplateOptions {};};_gedfgc .init ();if _dfgaf ==nil {_dfgaf =_fcccf ;
};return &templateProcessor {creator :_fcccf ,_ccgdc :_cdda ,_bgggf :_gedfgc ,_cgdcf :_dfgaf ,_edcc :_cgce };};

// NewCell returns a new invoice table cell.
func (_deec *Invoice )NewCell (value string )*InvoiceCell {return _deec .newCell (value ,_deec .NewCellProps ());};

// Cols returns the total number of columns the table has.
func (_cbaa *Table )Cols ()int {return _cbaa ._fcegg };

// GetMargins returns the margins of the chart (left, right, top, bottom).
func (_gcb *Chart )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _gcb ._ace .Left ,_gcb ._ace .Right ,_gcb ._ace .Top ,_gcb ._ace .Bottom ;};func (_abacce *templateProcessor )parseTextVerticalAlignmentAttr (_cdbc ,_eeda string )TextVerticalAlignment {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0074\u0065\u0078\u0074\u0020\u0076\u0065r\u0074\u0069\u0063\u0061\u006c\u0020\u0061\u006c\u0069\u0067\u006e\u006d\u0065n\u0074\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a (\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_cdbc ,_eeda );
_cegdc :=map[string ]TextVerticalAlignment {"\u0062\u0061\u0073\u0065\u006c\u0069\u006e\u0065":TextVerticalAlignmentBaseline ,"\u0063\u0065\u006e\u0074\u0065\u0072":TextVerticalAlignmentCenter }[_eeda ];return _cegdc ;};func (_ceca *Grid )resetColumnWidths (){_ceca ._daad =[]float64 {};
_facd :=float64 (1.0)/float64 (_ceca ._cecb );for _bbfa :=0;_bbfa < _ceca ._cecb ;_bbfa ++{_ceca ._daad =append (_ceca ._daad ,_facd );};};

// NewPolyBezierCurve creates a new composite Bezier (polybezier) curve.
func (_cead *Creator )NewPolyBezierCurve (curves []_gc .CubicBezierCurve )*PolyBezierCurve {return _eadae (curves );};func (_ebaaf *StyledParagraph )getTextWidth ()float64 {var _ffac float64 ;_fcgg :=len (_ebaaf ._dgaca );for _cggad ,_agga :=range _ebaaf ._dgaca {_bgddf :=&_agga .Style ;
_fbacf :=len (_agga .Text );for _cbdgb ,_ebca :=range _agga .Text {if _ebca =='\u000A'{continue ;};_baef ,_deae :=_bgddf .Font .GetRuneMetrics (_ebca );if !_deae {_a .Log .Debug ("\u0052\u0075\u006e\u0065\u0020\u0063\u0068\u0061\u0072\u0020\u006d\u0065\u0074\u0072\u0069c\u0073 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0021\u0020\u0025\u0076\u000a",_ebca );
return -1;};_ffac +=_bgddf .FontSize *_baef .Wx *_bgddf .horizontalScale ();if _ebca !=' '&&(_cggad !=_fcgg -1||_cbdgb !=_fbacf -1){_ffac +=_bgddf .CharSpacing *1000.0;};};};return _ffac ;};func _eadag (_gdfa ,_efbg ,_abcffc string ,_abcfd uint ,_eaaeg TextStyle )*TOCLine {return _fabba (TextChunk {Text :_gdfa ,Style :_eaaeg },TextChunk {Text :_efbg ,Style :_eaaeg },TextChunk {Text :_abcffc ,Style :_eaaeg },_abcfd ,_eaaeg );
};func (_bega *Paragraph )getTextLineWidth (_ccadb string )float64 {var _fagd float64 ;for _ ,_dcfd :=range _ccadb {if _dcfd =='\u000A'{continue ;};_egge ,_eebeg :=_bega ._aad .GetRuneMetrics (_dcfd );if !_eebeg {_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0052u\u006e\u0065\u0020\u0063\u0068a\u0072\u0020\u006d\u0065\u0074\u0072\u0069\u0063\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0021\u0020\u0028\u0072\u0075\u006e\u0065\u0020\u0030\u0078\u0025\u0030\u0034\u0078\u003d\u0025\u0063\u0029",_dcfd ,_dcfd );
return -1;};_fagd +=_bega ._febee *_egge .Wx ;};return _fagd ;};type componentRenderer interface{Draw (_cgeb Drawable )error ;};

// SetPageLabels adds the specified page labels to the PDF file generated
// by the creator. See section 12.4.2 "Page Labels" (p. 382 PDF32000_2008).
// NOTE: for existing PDF files, the page label ranges object can be obtained
// using the model.PDFReader's GetPageLabels method.
func (_afd *Creator )SetPageLabels (pageLabels _dd .PdfObject ){_afd ._gfed =pageLabels };func (_agegd *templateProcessor )nodeLogError (_dcacfc *templateNode ,_fbedb string ,_ebcbb ...interface{}){_a .Log .Error (_agegd .getNodeErrorLocation (_dcacfc ,_fbedb ,_ebcbb ...));
};const (AnchorBottomLeft AnchorPoint =iota ;AnchorBottomRight ;AnchorTopLeft ;AnchorTopRight ;AnchorCenter ;AnchorLeft ;AnchorRight ;AnchorTop ;AnchorBottom ;);

// NewPageBreak create a new page break.
func (_ccba *Creator )NewPageBreak ()*PageBreak {return _aegb ()};

// Lazy gets the lazy mode for the image.
func (_gbebc *Image )Lazy ()bool {return _gbebc ._gbed };

// SetTextAlignment sets the horizontal alignment of the text within the space provided.
func (_bgbb *Paragraph )SetTextAlignment (align TextAlignment ){_bgbb ._aegda =align };

// GeneratePageBlocks implements drawable interface.
func (_bdc *border )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_bef :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_adb :=_bdc ._fbac ;_cfba :=ctx .PageHeight -_bdc ._gba ;if _bdc ._ffc !=nil {_baa :=_gc .Rectangle {Opacity :1.0,X :_bdc ._fbac ,Y :ctx .PageHeight -_bdc ._gba -_bdc ._agd ,Height :_bdc ._agd ,Width :_bdc ._geb };
_baa .FillEnabled =true ;_cad :=_cdfe (_bdc ._ffc );_eacb :=_gfae (_bef ,_cad ,_bdc ._ffc ,func ()Rectangle {return Rectangle {_eeadac :_baa .X ,_abegd :_baa .Y ,_cdcgg :_baa .Width ,_cfedb :_baa .Height };});if _eacb !=nil {return nil ,ctx ,_eacb ;};_baa .FillColor =_cad ;
_baa .BorderEnabled =false ;_fcd :="";if _bdc ._afec < 1{_acf :=_dd .MakeDict ();_acf .Set ("\u0063\u0061",_dd .MakeFloat (_bdc ._afec ));_acf .Set ("\u0043\u0041",_dd .MakeFloat (_bdc ._afec ));_bcd :=_bef ._faa .AddExtGState ("\u0067\u0073\u0031",_acf );
if _bcd ==nil {_fcd ="\u0067\u0073\u0031";};};_ddaa ,_ ,_eacb :=_baa .Draw (_fcd );if _eacb !=nil {return nil ,ctx ,_eacb ;};_eacb =_bef .addContentsByString (string (_ddaa ));if _eacb !=nil {return nil ,ctx ,_eacb ;};};_gae :=_bdc ._cfb ;_ede :=_bdc ._eaag ;
_acabf :=_bdc ._edfb ;_aee :=_bdc ._cbfa ;_cdee :=_bdc ._cfb ;if _bdc ._fdd ==CellBorderStyleDouble {_cdee +=2*_gae ;};_cdb :=_bdc ._eaag ;if _bdc ._cgbc ==CellBorderStyleDouble {_cdb +=2*_ede ;};_cba :=_bdc ._edfb ;if _bdc ._cdd ==CellBorderStyleDouble {_cba +=2*_acabf ;
};_gfeg :=_bdc ._cbfa ;if _bdc ._egc ==CellBorderStyleDouble {_gfeg +=2*_aee ;};_cddc :=(_cdee -_cba )/2;_ebfc :=(_cdee -_gfeg )/2;_deeb :=(_cdb -_cba )/2;_baf :=(_cdb -_gfeg )/2;if _bdc ._cfb !=0{_ebe :=_adb ;_gag :=_cfba ;if _bdc ._fdd ==CellBorderStyleDouble {_gag -=_gae ;
_dfga :=_gc .BasicLine {LineColor :_cdfe (_bdc ._geaa ),Opacity :1.0,LineWidth :_bdc ._cfb ,LineStyle :_bdc .LineStyle ,X1 :_ebe -_cdee /2+_cddc ,Y1 :_gag +2*_gae ,X2 :_ebe +_cdee /2-_ebfc +_bdc ._geb ,Y2 :_gag +2*_gae };_bbbc ,_ ,_aeea :=_dfga .Draw ("");
if _aeea !=nil {return nil ,ctx ,_aeea ;};_aeea =_bef .addContentsByString (string (_bbbc ));if _aeea !=nil {return nil ,ctx ,_aeea ;};};_abb :=_gc .BasicLine {LineWidth :_bdc ._cfb ,Opacity :1.0,LineColor :_cdfe (_bdc ._geaa ),LineStyle :_bdc .LineStyle ,X1 :_ebe -_cdee /2+_cddc +(_cba -_bdc ._edfb ),Y1 :_gag ,X2 :_ebe +_cdee /2-_ebfc +_bdc ._geb -(_gfeg -_bdc ._cbfa ),Y2 :_gag };
_cada ,_ ,_gad :=_abb .Draw ("");if _gad !=nil {return nil ,ctx ,_gad ;};_gad =_bef .addContentsByString (string (_cada ));if _gad !=nil {return nil ,ctx ,_gad ;};};if _bdc ._eaag !=0{_ccfa :=_adb ;_bdfc :=_cfba -_bdc ._agd ;if _bdc ._cgbc ==CellBorderStyleDouble {_bdfc +=_ede ;
_agf :=_gc .BasicLine {LineWidth :_bdc ._eaag ,Opacity :1.0,LineColor :_cdfe (_bdc ._dcg ),LineStyle :_bdc .LineStyle ,X1 :_ccfa -_cdb /2+_deeb ,Y1 :_bdfc -2*_ede ,X2 :_ccfa +_cdb /2-_baf +_bdc ._geb ,Y2 :_bdfc -2*_ede };_ggbb ,_ ,_gfdf :=_agf .Draw ("");
if _gfdf !=nil {return nil ,ctx ,_gfdf ;};_gfdf =_bef .addContentsByString (string (_ggbb ));if _gfdf !=nil {return nil ,ctx ,_gfdf ;};};_dcd :=_gc .BasicLine {LineWidth :_bdc ._eaag ,Opacity :1.0,LineColor :_cdfe (_bdc ._dcg ),LineStyle :_bdc .LineStyle ,X1 :_ccfa -_cdb /2+_deeb +(_cba -_bdc ._edfb ),Y1 :_bdfc ,X2 :_ccfa +_cdb /2-_baf +_bdc ._geb -(_gfeg -_bdc ._cbfa ),Y2 :_bdfc };
_fcff ,_ ,_dgcb :=_dcd .Draw ("");if _dgcb !=nil {return nil ,ctx ,_dgcb ;};_dgcb =_bef .addContentsByString (string (_fcff ));if _dgcb !=nil {return nil ,ctx ,_dgcb ;};};if _bdc ._edfb !=0{_egec :=_adb ;_fbfb :=_cfba ;if _bdc ._cdd ==CellBorderStyleDouble {_egec +=_acabf ;
_dbbf :=_gc .BasicLine {LineWidth :_bdc ._edfb ,Opacity :1.0,LineColor :_cdfe (_bdc ._decd ),LineStyle :_bdc .LineStyle ,X1 :_egec -2*_acabf ,Y1 :_fbfb +_cba /2+_cddc ,X2 :_egec -2*_acabf ,Y2 :_fbfb -_cba /2-_deeb -_bdc ._agd };_dgg ,_ ,_fgd :=_dbbf .Draw ("");
if _fgd !=nil {return nil ,ctx ,_fgd ;};_fgd =_bef .addContentsByString (string (_dgg ));if _fgd !=nil {return nil ,ctx ,_fgd ;};};_def :=_gc .BasicLine {LineWidth :_bdc ._edfb ,Opacity :1.0,LineColor :_cdfe (_bdc ._decd ),LineStyle :_bdc .LineStyle ,X1 :_egec ,Y1 :_fbfb +_cba /2+_cddc -(_cdee -_bdc ._cfb ),X2 :_egec ,Y2 :_fbfb -_cba /2-_deeb -_bdc ._agd +(_cdb -_bdc ._eaag )};
_bfb ,_ ,_ddgg :=_def .Draw ("");if _ddgg !=nil {return nil ,ctx ,_ddgg ;};_ddgg =_bef .addContentsByString (string (_bfb ));if _ddgg !=nil {return nil ,ctx ,_ddgg ;};};if _bdc ._cbfa !=0{_gcgc :=_adb +_bdc ._geb ;_eagg :=_cfba ;if _bdc ._egc ==CellBorderStyleDouble {_gcgc -=_aee ;
_fbd :=_gc .BasicLine {LineWidth :_bdc ._cbfa ,Opacity :1.0,LineColor :_cdfe (_bdc ._aff ),LineStyle :_bdc .LineStyle ,X1 :_gcgc +2*_aee ,Y1 :_eagg +_gfeg /2+_ebfc ,X2 :_gcgc +2*_aee ,Y2 :_eagg -_gfeg /2-_baf -_bdc ._agd };_gfg ,_ ,_cbg :=_fbd .Draw ("");
if _cbg !=nil {return nil ,ctx ,_cbg ;};_cbg =_bef .addContentsByString (string (_gfg ));if _cbg !=nil {return nil ,ctx ,_cbg ;};};_faag :=_gc .BasicLine {LineWidth :_bdc ._cbfa ,Opacity :1.0,LineColor :_cdfe (_bdc ._aff ),LineStyle :_bdc .LineStyle ,X1 :_gcgc ,Y1 :_eagg +_gfeg /2+_ebfc -(_cdee -_bdc ._cfb ),X2 :_gcgc ,Y2 :_eagg -_gfeg /2-_baf -_bdc ._agd +(_cdb -_bdc ._eaag )};
_ccd ,_ ,_cdg :=_faag .Draw ("");if _cdg !=nil {return nil ,ctx ,_cdg ;};_cdg =_bef .addContentsByString (string (_ccd ));if _cdg !=nil {return nil ,ctx ,_cdg ;};};return []*Block {_bef },ctx ,nil ;};

// ScaleToHeight sets the graphic svg scaling factor with the given height.
func (_beag *GraphicSVG )ScaleToHeight (h float64 ){_dbed :=_beag ._bdca .Width /_beag ._bdca .Height ;_beag ._bdca .Height =h ;_beag ._bdca .Width =h *_dbed ;_beag ._bdca .SetScaling (_dbed ,_dbed );};func (_bccaf *templateProcessor )addNodeText (_dcgede *templateNode ,_fgaa string )error {_fedefc :=_dcgede ._aeceb ;
if _fedefc ==nil {return nil ;};switch _ggcg :=_fedefc .(type ){case *TextChunk :_ggcg .Text =_fgaa ;case *StyledParagraph :switch _dcgede ._ccge .Name .Local {case "\u0063h\u0061p\u0074\u0065\u0072\u002d\u0068\u0065\u0061\u0064\u0069\u006e\u0067":if _dcgede ._gafge !=nil {if _dgga ,_cdfc :=_dcgede ._gafge ._aeceb .(*Chapter );
_cdfc {_dgga ._bfag =_fgaa ;_ggcg .SetText (_dgga .headingText ());};};};};return nil ;};

// TextAlignment options for paragraph.
type TextAlignment int ;

// Lines returns all the lines the table of contents has.
func (_fabe *TOC )Lines ()[]*TOCLine {return _fabe ._bebaa };

// GeneratePageBlocks draws the ellipse on a new block representing the page.
func (_dbaa *Ellipse )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var (_cgff []*Block ;_ccg =NewBlock (ctx .PageWidth ,ctx .PageHeight );_fcge =ctx ;);_eege :=_dbaa ._cddg .IsRelative ();if _eege {_dbaa .applyFitMode (ctx .Width );
ctx .X +=_dbaa ._dafc .Left ;ctx .Y +=_dbaa ._dafc .Top ;ctx .Width -=_dbaa ._dafc .Left +_dbaa ._dafc .Right ;ctx .Height -=_dbaa ._dafc .Top +_dbaa ._dafc .Bottom ;if _dbaa ._gaabd > ctx .Height {_cgff =append (_cgff ,_ccg );_ccg =NewBlock (ctx .PageWidth ,ctx .PageHeight );
ctx .Page ++;_dgdg :=ctx ;_dgdg .Y =ctx .Margins .Top +_dbaa ._dafc .Top ;_dgdg .X =ctx .Margins .Left +_dbaa ._dafc .Left ;_dgdg .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom -_dbaa ._dafc .Top -_dbaa ._dafc .Bottom ;_dgdg .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_dbaa ._dafc .Left -_dbaa ._dafc .Right ;
ctx =_dgdg ;};}else {ctx .X =_dbaa ._bace -_dbaa ._fdgf /2;ctx .Y =_dbaa ._gdabb -_dbaa ._gaabd /2;};_cdgc :=_gc .Circle {X :ctx .X ,Y :ctx .PageHeight -ctx .Y -_dbaa ._gaabd ,Width :_dbaa ._fdgf ,Height :_dbaa ._gaabd ,BorderWidth :_dbaa ._daa ,Opacity :1.0};
if _dbaa ._efcb !=nil {_cdgc .FillEnabled =true ;_feedd :=_cdfe (_dbaa ._efcb );_gdbaa :=_gfae (_ccg ,_feedd ,_dbaa ._efcb ,func ()Rectangle {return Rectangle {_eeadac :_cdgc .X ,_abegd :_cdgc .Y ,_cdcgg :_cdgc .Width ,_cfedb :_cdgc .Height };});if _gdbaa !=nil {return nil ,ctx ,_gdbaa ;
};_cdgc .FillColor =_feedd ;};if _dbaa ._bcgd !=nil {_cdgc .BorderEnabled =false ;if _dbaa ._daa > 0{_cdgc .BorderEnabled =true ;};_cdgc .BorderColor =_cdfe (_dbaa ._bcgd );_cdgc .BorderWidth =_dbaa ._daa ;};_bcdg ,_bafbd :=_ccg .setOpacity (_dbaa ._cebc ,_dbaa ._deee );
if _bafbd !=nil {return nil ,ctx ,_bafbd ;};_daec ,_ ,_bafbd :=_cdgc .MarkedDraw (_bcdg ,_dbaa ._cgbg );if _bafbd !=nil {return nil ,ctx ,_bafbd ;};_bafbd =_ccg .addContentsByString (string (_daec ));if _bafbd !=nil {return nil ,ctx ,_bafbd ;};if _eege {ctx .X =_fcge .X ;
ctx .Width =_fcge .Width ;ctx .Y +=_dbaa ._gaabd +_dbaa ._dafc .Bottom ;ctx .Height -=_dbaa ._gaabd ;}else {ctx =_fcge ;};_cgff =append (_cgff ,_ccg );return _cgff ,ctx ,nil ;};

// NewCell makes a new single cell and inserts it into the row at the current position.
func (_eadf *GridRow )NewCell ()(*GridCell ,error ){return _eadf .NewMultiCell (1,1)};

// Vertical returns total vertical (top + bottom) margin.
func (_badd *Margins )Vertical ()float64 {return _badd .Bottom +_badd .Top };

// SetPadding sets the padding of the component. The padding represents
// inner margins which are applied around the contents of the division.
// The background of the component is not affected by its padding.
func (_cfca *Division )SetPadding (left ,right ,top ,bottom float64 ){_cfca ._ddcg .Left =left ;_cfca ._ddcg .Right =right ;_cfca ._ddcg .Top =top ;_cfca ._ddcg .Bottom =bottom ;};

// ScaleToWidth scale Image to a specified width w, maintaining the aspect ratio.
func (_bgaec *Image )ScaleToWidth (w float64 ){_ddebf :=_bgaec ._fcbd /_bgaec ._bbg ;_bgaec ._bbg =w ;_bgaec ._fcbd =w *_ddebf ;};func _beed (_cge _fed .ChartRenderable )*Chart {return &Chart {_cegb :_cge ,_gcac :PositionRelative ,_ace :Margins {Top :10,Bottom :10}};
};

// SetFillColor sets the fill color of the ellipse.
func (_cdca *Ellipse )SetFillColor (col Color ){_cdca ._efcb =col };func _cefg (_bcbed *_db .Image )(*Image ,error ){_abdc :=float64 (_bcbed .Width );_adce :=float64 (_bcbed .Height );return &Image {_fbebf :_bcbed ,_gddb :_abdc ,_gaga :_adce ,_bbg :_abdc ,_fcbd :_adce ,_edebf :0,_efee :1.0,_dfbc :PositionRelative },nil ;
};func _efcbf (_fdbee ...interface{})(map[string ]interface{},error ){_bagbc :=len (_fdbee );if _bagbc %2!=0{_a .Log .Error ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020p\u0061\u0072\u0061\u006d\u0065\u0074\u0065r\u0073\u0020\u0066\u006f\u0072\u0020\u0063\u0072\u0065\u0061\u0074i\u006e\u0067\u0020\u006d\u0061\u0070\u003a\u0020\u0025\u0064\u002e",_bagbc );
return nil ,_dd .ErrRangeError ;};_deeec :=map[string ]interface{}{};for _fbdec :=0;_fbdec < _bagbc ;_fbdec +=2{_ccfdd ,_gdaad :=_fdbee [_fbdec ].(string );if !_gdaad {_a .Log .Error ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u006d\u0061\u0070 \u006b\u0065\u0079\u0020t\u0079\u0070\u0065\u0020\u0028\u0025\u0054\u0029\u002e\u0020\u0045\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u002e",_fdbee [_fbdec ]);
return nil ,_dd .ErrTypeError ;};_deeec [_ccfdd ]=_fdbee [_fbdec +1];};return _deeec ,nil ;};func _ggad (_cgcad string )(_eaacgc ,_cgfb string ){if _cgcad ==""||(_cgcad [len (_cgcad )-1]>='0'&&_cgcad [len (_cgcad )-1]<='9'){return _cgcad ,"";};_eaacgc =_cgcad ;
for _ ,_cgecc :=range _gdf {if _fc .Contains (_eaacgc ,_cgecc ){_cgfb =_cgecc ;};_eaacgc =_fc .TrimSuffix (_eaacgc ,_cgecc );};return ;};func (_baba *templateProcessor )parseAttrPropList (_defda string )map[string ]string {_gcdga :=_fc .Fields (_defda );
if len (_gcdga )==0{return nil ;};_edbeg :=map[string ]string {};for _ ,_cbcafd :=range _gcdga {_adddbb :=_bfefc .FindStringSubmatch (_cbcafd );if len (_adddbb )< 3{continue ;};_fggeb ,_agdbb :=_fc .TrimSpace (_adddbb [1]),_adddbb [2];if _fggeb ==""{continue ;
};_edbeg [_fggeb ]=_agdbb ;};return _edbeg ;};

// AssociateAnnotationWithStructure associates the given annotation with the text chunk's structure element.
// This is necessary for proper tag structure when using annotations in tagged PDFs.
func (_acabg *TextChunk )AssociateAnnotationWithStructure (annotation *_db .PdfAnnotation ){if _acabg ._gefgc ==nil {_acabg ._gefgc =_db .NewStructureTagInfo ();_acabg ._gefgc .StructureType =_db .StructureTypeSpan ;};_fggeg :=annotation .GetContainingPdfObject ();
_acabg ._gefgc .SetObjAttrib (_fggeg );if _afdcd ,_dedde :=_dd .GetDict (_fggeg );_dedde {_afdcd .Set ("\u0053\u0074\u0072u\u0063\u0074\u0050\u0061\u0072\u0065\u006e\u0074",_dd .MakeInteger (_acabg ._gefgc .Mcid ));};};const (CellBorderStyleNone CellBorderStyle =iota ;
CellBorderStyleSingle ;CellBorderStyleDouble ;);func _cecde (_eddf []byte )(*Image ,error ){_edba :=_c .NewReader (_eddf );_egebe ,_dabf :=_db .ImageHandling .Read (_edba );if _dabf !=nil {_a .Log .Error ("\u0045\u0072\u0072or\u0020\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_dabf );
return nil ,_dabf ;};return _cefg (_egebe );};

// SetHeaderRows turns the selected table rows into headers that are repeated
// for every page the table spans. startRow and endRow are inclusive.
func (_bgdb *Table )SetHeaderRows (startRow ,endRow int )error {if startRow <=0{return _ee .New ("\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0073\u0074\u0061\u0072\u0074\u0020r\u006f\u0077\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074h\u0061\u006e\u0020\u0030");
};if endRow <=0{return _ee .New ("\u0068\u0065a\u0064\u0065\u0072\u0020e\u006e\u0064 \u0072\u006f\u0077\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074h\u0061\u006e\u0020\u0030");};if startRow > endRow {return _ee .New ("\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0072\u006f\u0077\u0020\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0074\u0068\u0065 \u0065\u006e\u0064\u0020\u0072o\u0077");
};_bgdb ._gcfeb =true ;_bgdb ._cdafe =startRow ;_bgdb ._bcfb =endRow ;return nil ;};

// SetHeight sets the height of the rectangle.
func (_dadedc *Rectangle )SetHeight (height float64 ){_dadedc ._cfedb =height };

// FitMode returns the fit mode of the ellipse.
func (_geef *Ellipse )FitMode ()FitMode {return _geef ._dbcd };

// EnableWordWrap sets the paragraph word wrap flag.
func (_fgga *StyledParagraph )EnableWordWrap (val bool ){_fgga ._fbfd =val };

// SetWidthTop sets border width for top.
func (_agb *border )SetWidthTop (bw float64 ){_agb ._cfb =bw };

// SetDashPattern sets the dash pattern of the line.
// NOTE: the dash pattern is taken into account only if the style of the
// line is set to dashed.
func (_cecef *Line )SetDashPattern (dashArray []int64 ,dashPhase int64 ){_cecef ._feda =dashArray ;_cecef ._fafgc =dashPhase ;};func (_ccdg *templateProcessor )parsePageBreak (_ebfbf *templateNode )(interface{},error ){return _aegb (),nil ;};

// FillOpacity returns the fill opacity of the ellipse (0-1).
func (_ffca *Ellipse )FillOpacity ()float64 {return _ffca ._cebc };func (_ceddd *Grid )cloneRow (_baac int )*GridRow {_fbdef :=&GridRow {_deege :_ceddd ._gfeb ,_bacb :_baac +1,_dfdbg :_ceddd };for _ ,_gebd :=range _ceddd ._bgcb [_baac ]._fgfe {_ccab ,_ :=_fbdef .NewMultiCell (_gebd ._caffg ,1);
if _ccab !=nil {_ccab ._ddgeb =_gebd ._ddgeb ;_ccab ._cbcd =_gebd ._cbcd ;_ccab ._bdcg =_gebd ._bdcg ;_ccab ._gegc =_gebd ._gegc ;_ccab ._edee =_gebd ._edee ;_ccab ._ccc =_gebd ._ccc ;_ccab ._eaeb =_gebd ._eaeb ;_ccab ._bddf =_gebd ._bddf ;_ccab ._eefb =_gebd ._eefb ;
_ccab ._egca =_gebd ._egca ;_ccab ._acbe =_gebd ._acbe ;_ccab ._gffc =_gebd ._gffc ;_ccab ._feee =_gebd ._feee ;_ccab ._bfcc =_gebd ._bfcc ;_ccab ._gcfa =_gebd ._gcfa ;_ccab ._efab =_gebd ._efab ;_ccab ._ddca =_gebd ._ddca ;_ccab ._ffgf =_gebd ._ffgf ;
_ccab ._fcfg =_baac +1;_ccab ._fbbd =_gebd ._fbbd ;};};return _fbdef ;};

// ColorCMYKFromArithmetic creates a Color from arithmetic color values (0-1).
// Example:
//
//	green := ColorCMYKFromArithmetic(1.0, 0.0, 1.0, 0.0)
func ColorCMYKFromArithmetic (c ,m ,y ,k float64 )Color {return cmykColor {_dgfd :_fa .Max (_fa .Min (c ,1.0),0.0),_dcb :_fa .Max (_fa .Min (m ,1.0),0.0),_eead :_fa .Max (_fa .Min (y ,1.0),0.0),_edb :_fa .Max (_fa .Min (k ,1.0),0.0)};};func (_abef *Table )resetColumnWidths (){_abef ._bdggd =[]float64 {};
_bcdee :=float64 (1.0)/float64 (_abef ._fcegg );for _fcbb :=0;_fcbb < _abef ._fcegg ;_fcbb ++{_abef ._bdggd =append (_abef ._bdggd ,_bcdee );};};var (ErrContentNotFit =_ee .New ("\u0043\u0061\u006e\u006e\u006ft\u0020\u0066\u0069\u0074\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020i\u006e\u0074\u006f\u0020\u0061\u006e\u0020\u0065\u0078\u0069\u0073\u0074\u0069\u006e\u0067\u0020\u0073\u0070\u0061\u0063\u0065");
);

// EnablePageWrap controls whether the division is wrapped across pages.
// If disabled, the division is moved in its entirety on a new page, if it
// does not fit in the available height. By default, page wrapping is enabled.
// If the height of the division is larger than an entire page, wrapping is
// enabled automatically in order to avoid unwanted behavior.
// Currently, page wrapping can only be disabled for vertical divisions.
func (_agcg *Division )EnablePageWrap (enable bool ){_agcg ._gbfd =enable };

// TotalLines returns all the rows in the invoice totals table as
// description-value cell pairs.
func (_feeec *Invoice )TotalLines ()[][2]*InvoiceCell {_bgdf :=[][2]*InvoiceCell {_feeec ._ageb };_bgdf =append (_bgdf ,_feeec ._efgb ...);return append (_bgdf ,_feeec ._adgc );};func (_ebcff *templateProcessor )parseLinearGradientAttr (creator *Creator ,_cfbcb string )Color {_cefb :=ColorBlack ;
if _cfbcb ==""{return _cefb ;};_egage :=creator .NewLinearGradientColor ([]*ColorPoint {});_egage .SetExtends (true ,true );var (_gdebeb =_fc .Split (_cfbcb [16:len (_cfbcb )-1],"\u002c");_ceaf =_fc .TrimSpace (_gdebeb [0]););if _fc .HasSuffix (_ceaf ,"\u0064\u0065\u0067"){_afbfde ,_dbgfg :=_fbf .ParseFloat (_ceaf [:len (_ceaf )-3],64);
if _dbgfg !=nil {_a .Log .Debug ("\u0046\u0061\u0069\u006c\u0065\u0064 \u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0067\u0072\u0061\u0064\u0069e\u006e\u0074\u0020\u0061\u006e\u0067\u006ce\u003a\u0020\u0025\u0076",_dbgfg );}else {_egage .SetAngle (_afbfde );
};_gdebeb =_gdebeb [1:];};_badfb ,_ccgfg :=_ebcff .processGradientColorPair (_gdebeb );if _badfb ==nil ||_ccgfg ==nil {return _cefb ;};for _cgbea :=0;_cgbea < len (_badfb );_cgbea ++{_egage .AddColorStop (_badfb [_cgbea ],_ccgfg [_cgbea ]);};return _egage ;
};

// SetTotal sets the total of the invoice.
func (_bcgc *Invoice )SetTotal (value string ){_bcgc ._adgc [1].Value =value };

// ScaleToHeight scales the ellipse to the specified height. The width of
// the ellipse is scaled so that the aspect ratio is maintained.
func (_fdbf *Ellipse )ScaleToHeight (h float64 ){_gfag :=_fdbf ._fdgf /_fdbf ._gaabd ;_fdbf ._gaabd =h ;_fdbf ._fdgf =h *_gfag ;};

// SetShowNumbering sets a flag to indicate whether or not to show chapter numbers as part of title.
func (_cecc *Chapter )SetShowNumbering (show bool ){_cecc ._ebfb =show ;_cecc ._dgca .SetText (_cecc .headingText ());};

// TOCLine represents a line in a table of contents.
// The component can be used both in the context of a
// table of contents component and as a standalone component.
// The representation of a table of contents line is as follows:
/*
         [number] [title]      [separator] [page]
   e.g.: Chapter1 Introduction ........... 1
*/
type TOCLine struct{_geffaf *StyledParagraph ;

// Holds the text and style of the number part of the TOC line.
Number TextChunk ;

// Holds the text and style of the title part of the TOC line.
Title TextChunk ;

// Holds the text and style of the separator part of the TOC line.
Separator TextChunk ;

// Holds the text and style of the page part of the TOC line.
Page TextChunk ;_dacdb float64 ;_aeaad uint ;_acade float64 ;_cgfgd Positioning ;_fbcga float64 ;_ebafe float64 ;_ageffa int64 ;};

// SetBorderWidth sets the border width of the ellipse.
func (_acbf *Ellipse )SetBorderWidth (bw float64 ){_acbf ._daa =bw };func _gcbe (_agfg *GraphicSVGElement )(*GraphicSVG ,error ){return &GraphicSVG {_bdca :_agfg ,_cgd :PositionRelative ,_edeb :Margins {Top :10,Bottom :10}},nil ;};

// GeneratePageBlocks generates a page break block.
func (_ffde *PageBreak )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_degde :=[]*Block {NewBlock (ctx .PageWidth ,ctx .PageHeight -ctx .Y ),NewBlock (ctx .PageWidth ,ctx .PageHeight )};ctx .Page ++;_ddfc :=ctx ;_ddfc .Y =ctx .Margins .Top ;
_ddfc .X =ctx .Margins .Left ;_ddfc .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom ;_ddfc .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right ;ctx =_ddfc ;return _degde ,ctx ,nil ;};func (_agad *StyledParagraph )split (_fccce DrawContext )(_dfag ,_ddcfd *StyledParagraph ,_gdfd error ){if _gdfd =_agad .wrapChunks (false );
_gdfd !=nil {return nil ,nil ,_gdfd ;};if len (_agad ._cbba )==1&&_agad ._abbd > _fccce .Height {return _agad ,nil ,nil ;};_adef :=func (_dabdb []*TextChunk ,_dacge []*TextChunk )[]*TextChunk {if len (_dacge )==0{return _dabdb ;};_abeeb :=len (_dabdb );
if _abeeb ==0{return append (_dabdb ,_dacge ...);};if _dabdb [_abeeb -1].Style ==_dacge [0].Style {_dabdb [_abeeb -1].Text +=_dacge [0].Text ;}else {_dabdb =append (_dabdb ,_dacge [0]);};return append (_dabdb ,_dacge [1:]...);};_adae :=func (_gegee *StyledParagraph ,_cfce []*TextChunk )*StyledParagraph {if len (_cfce )==0{return nil ;
};_bdea :=*_gegee ;_bdea ._dgaca =_cfce ;return &_bdea ;};var (_fffge float64 ;_aabf []*TextChunk ;_abeca []*TextChunk ;);for _ ,_fddda :=range _agad ._cbba {var _cfcf float64 ;_ffede :=make ([]*TextChunk ,0,len (_fddda ));for _ ,_bdedd :=range _fddda {if _dfee :=_bdedd .Style .FontSize ;
_dfee > _cfcf {_cfcf =_dfee ;};_ffede =append (_ffede ,_bdedd .clone ());};_cfcf *=_agad ._abbd ;if _agad ._dfedd .IsRelative (){if _fffge +_cfcf > _fccce .Height {_abeca =_adef (_abeca ,_ffede );}else {_aabf =_adef (_aabf ,_ffede );};};_fffge +=_cfcf ;
};_agad ._cbba =nil ;if len (_abeca )==0{return _agad ,nil ,nil ;};return _adae (_agad ,_aabf ),_adae (_agad ,_abeca ),nil ;};func (_eabbd *GraphicSVGElement )toContentStream (_eecg *_eg .ContentCreator ,_ggfeg *_db .PdfPageResources ){_eage ,_eee :=_bacba (_eabbd .Attributes ,_eabbd ._aece );
if _eee !=nil {_a .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0073\u0074\u0079\u006c\u0065\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_eee );};_eabbd .Style =_eage ;switch _eabbd .Name {case "\u0070\u0061\u0074\u0068":_eabbd .drawPath (_eecg ,_ggfeg );
for _ ,_cadc :=range _eabbd .Children {_cadc .toContentStream (_eecg ,_ggfeg );};case "\u0072\u0065\u0063\u0074":_eabbd .drawRect (_eecg ,_ggfeg );for _ ,_bgggg :=range _eabbd .Children {_bgggg .toContentStream (_eecg ,_ggfeg );};case "\u0063\u0069\u0072\u0063\u006c\u0065":_eabbd .drawCircle (_eecg ,_ggfeg );
for _ ,_baffa :=range _eabbd .Children {_baffa .toContentStream (_eecg ,_ggfeg );};case "\u0065l\u006c\u0069\u0070\u0073\u0065":_eabbd .drawEllipse (_eecg ,_ggfeg );for _ ,_daded :=range _eabbd .Children {_daded .toContentStream (_eecg ,_ggfeg );};case "\u0070\u006f\u006c\u0079\u006c\u0069\u006e\u0065":_eabbd .drawPolyline (_eecg ,_ggfeg );
for _ ,_cgac :=range _eabbd .Children {_cgac .toContentStream (_eecg ,_ggfeg );};case "\u0070o\u006c\u0079\u0067\u006f\u006e":_eabbd .drawPolygon (_eecg ,_ggfeg );for _ ,_defg :=range _eabbd .Children {_defg .toContentStream (_eecg ,_ggfeg );};case "\u006c\u0069\u006e\u0065":_eabbd .drawLine (_eecg ,_ggfeg );
for _ ,_ddcd :=range _eabbd .Children {_ddcd .toContentStream (_eecg ,_ggfeg );};case "\u0074\u0065\u0078\u0074":_eabbd .drawText (_eecg ,_ggfeg );for _ ,_bgaea :=range _eabbd .Children {_bgaea .toContentStream (_eecg ,_ggfeg );};case "\u0067":_bggfg ,_dcbc :=_eabbd .Attributes ["\u0066\u0069\u006c\u006c"];
_babd ,_aaadb :=_eabbd .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"];_ddbeb ,_ggcfc :=_eabbd .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];_begg ,_bbdf :=_eabbd .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];
for _ ,_efd :=range _eabbd .Children {if _ ,_ggcfb :=_efd .Attributes ["\u0066\u0069\u006c\u006c"];!_ggcfb &&_dcbc {_efd .Attributes ["\u0066\u0069\u006c\u006c"]=_bggfg ;};if _ ,_dgeg :=_efd .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"];!_dgeg &&_aaadb {_efd .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"]=_babd ;
};if _ ,_dcbg :=_efd .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];!_dcbg &&_ggcfc {_efd .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"]=_ddbeb ;};if _ ,_dacbb :=_efd .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];
!_dacbb &&_bbdf {_efd .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"]=_begg ;};_efd .toContentStream (_eecg ,_ggfeg );};};};func _ecgg (_ddgd string )[]token {var (_aegac []token ;_ceeg string ;);for _ ,_fdfbc :=range _ddgd {_edcdf :=string (_fdfbc );
switch {case _gfdbca .isCommand (_edcdf ):_aegac ,_ceeg =_gbgee (_aegac ,_ceeg );_aegac =append (_aegac ,token {_edcdf ,true });case _edcdf =="\u002e":if _ceeg ==""{_ceeg ="\u0030";};if _fc .Contains (_ceeg ,_edcdf ){_aegac =append (_aegac ,token {_ceeg ,false });
_ceeg ="\u0030";};fallthrough;case _edcdf >="\u0030"&&_edcdf <="\u0039"||_edcdf =="\u0065":_ceeg +=_edcdf ;case _edcdf =="\u002d":if _fc .HasSuffix (_ceeg ,"\u0065"){_ceeg +=_edcdf ;}else {_aegac ,_ =_gbgee (_aegac ,_ceeg );_ceeg =_edcdf ;};default:_aegac ,_ceeg =_gbgee (_aegac ,_ceeg );
};};_aegac ,_ =_gbgee (_aegac ,_ceeg );return _aegac ;};

// Invoice represents a configurable invoice template.
type Invoice struct{_gfegb string ;_aeae *Image ;_egde *InvoiceAddress ;_eeddc *InvoiceAddress ;_agae string ;_eddg [2]*InvoiceCell ;_fgeaf [2]*InvoiceCell ;_fbbc [2]*InvoiceCell ;_febf [][2]*InvoiceCell ;_egfdb []*InvoiceCell ;_gbeaa [][]*InvoiceCell ;
_ageb [2]*InvoiceCell ;_adgc [2]*InvoiceCell ;_efgb [][2]*InvoiceCell ;_egfgg [2]string ;_ddgeg [2]string ;_edgb [][2]string ;_bcag TextStyle ;_fcfb TextStyle ;_gecfa TextStyle ;_eefg TextStyle ;_adbac TextStyle ;_afafg TextStyle ;_bgcc TextStyle ;_eeeg InvoiceCellProps ;
_ebac InvoiceCellProps ;_cbdg InvoiceCellProps ;_gdbce InvoiceCellProps ;_efcbb Positioning ;};

// SetIncludeInTOC sets a flag to indicate whether or not to include in tOC.
func (_dgaf *Chapter )SetIncludeInTOC (includeInTOC bool ){_dgaf ._cadag =includeInTOC };

// Rectangle defines a rectangle with upper left corner at (x,y) and a specified width and height.  The rectangle
// can have a colored fill and/or border with a specified width.
// Implements the Drawable interface and can be drawn on PDF using the Creator.
type Rectangle struct{_eeadac float64 ;_abegd float64 ;_cdcgg float64 ;_cfedb float64 ;_fbbdf Positioning ;_ggac Color ;_dfecg float64 ;_gdea Color ;_dfbcd float64 ;_adaf float64 ;_edbfa float64 ;_efgaa float64 ;_bbef float64 ;_fcdc float64 ;_abeec Margins ;
_dfced FitMode ;_bgbc *_db .StructureTagInfo ;};func (_ecf *Chapter )headingText ()string {_cdea :=_ecf ._bfag ;if _cbe :=_ecf .headingNumber ();_cbe !=""{_cdea =_g .Sprintf ("\u0025\u0073\u0020%\u0073",_cbe ,_cdea );};return _cdea ;};

// SetMarkedContentID sets the marked content ID for the text chunk.
func (_abcgd *TextChunk )SetMarkedContentID (mcid int64 ){if _abcgd ._gefgc ==nil {_abcgd ._gefgc =_db .NewStructureTagInfo ();_abcgd ._gefgc .StructureType =_db .StructureTypeSpan ;};_abcgd ._gefgc .Mcid =mcid ;};func _agbb (_efefe *templateProcessor ,_cbcdd *templateNode )(interface{},error ){return _efefe .parseChapter (_cbcdd );
};func (_bgegg *Table )sortCells (){_gg .Slice (_bgegg ._ccgf ,func (_dacaf ,_eefga int )bool {_begae :=_bgegg ._ccgf [_dacaf ]._efage ;_eggg :=_bgegg ._ccgf [_eefga ]._efage ;if _begae < _eggg {return true ;};if _begae > _eggg {return false ;};return _bgegg ._ccgf [_dacaf ]._afdac < _bgegg ._ccgf [_eefga ]._afdac ;
});};

// TemplateOptions contains options and resources to use when rendering
// a template with a Creator instance.
// All the resources in the map fields can be referenced by their
// name/key in the template which is rendered using the options instance.
type TemplateOptions struct{

// HelperFuncMap is used to define functions which can be accessed
// inside the rendered templates by their assigned names.
HelperFuncMap _b .FuncMap ;

// SubtemplateMap contains templates which can be rendered alongside
// the main template. They can be accessed using their assigned names
// in the main template or in the other subtemplates.
// Subtemplates defined inside the subtemplates specified in the map
// can be accessed directly.
// All resources available to the main template are also available
// to the subtemplates.
SubtemplateMap map[string ]_cc .Reader ;

// FontMap contains pre-loaded fonts which can be accessed
// inside the rendered templates by their assigned names.
FontMap map[string ]*_db .PdfFont ;

// ImageMap contains pre-loaded images which can be accessed
// inside the rendered templates by their assigned names.
ImageMap map[string ]*_db .Image ;

// ColorMap contains colors which can be accessed
// inside the rendered templates by their assigned names.
ColorMap map[string ]Color ;

// ChartMap contains charts which can be accessed
// inside the rendered templates by their assigned names.
ChartMap map[string ]_fed .ChartRenderable ;};func (_edca *Image )rotatedSize ()(float64 ,float64 ){_cfac :=_edca ._bbg ;_gbdd :=_edca ._fcbd ;_aace :=_edca ._edebf ;if _aace ==0{return _cfac ,_gbdd ;};_gedfg :=_gc .Path {Points :[]_gc .Point {_gc .NewPoint (0,0).Rotate (_aace ),_gc .NewPoint (_cfac ,0).Rotate (_aace ),_gc .NewPoint (0,_gbdd ).Rotate (_aace ),_gc .NewPoint (_cfac ,_gbdd ).Rotate (_aace )}}.GetBoundingBox ();
return _gedfg .Width ,_gedfg .Height ;};func (_abgge *templateProcessor )parseChart (_afge *templateNode )(interface{},error ){var _cdga string ;for _ ,_ccceb :=range _afge ._ccge .Attr {_bdcf :=_ccceb .Value ;switch _fedgg :=_ccceb .Name .Local ;_fedgg {case "\u0073\u0072\u0063":_cdga =_bdcf ;
};};if _cdga ==""{_abgge .nodeLogError (_afge ,"\u0043\u0068\u0061\u0072\u0074\u0020\u0060\u0073\u0072\u0063\u0060\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0063\u0061n\u006e\u006f\u0074\u0020\u0062e\u0020\u0065m\u0070\u0074\u0079\u002e");
return nil ,_cbabd ;};_feedg ,_agdfd :=_abgge ._bgggf .ChartMap [_cdga ];if !_agdfd {_abgge .nodeLogError (_afge ,"\u0043\u006ful\u0064\u0020\u006eo\u0074\u0020\u0066\u0069nd \u0063ha\u0072\u0074\u0020\u0072\u0065\u0073\u006fur\u0063\u0065\u003a\u0020\u0060\u0025\u0073`\u002e",_cdga );
return nil ,_cbabd ;};_ddced :=NewChart (_feedg );for _ ,_fdag :=range _afge ._ccge .Attr {_aggd :=_fdag .Value ;switch _agac :=_fdag .Name .Local ;_agac {case "\u0078":_ddced .SetPos (_abgge .parseFloatAttr (_agac ,_aggd ),_ddced ._cda );case "\u0079":_ddced .SetPos (_ddced ._dfcb ,_abgge .parseFloatAttr (_agac ,_aggd ));
case "\u006d\u0061\u0072\u0067\u0069\u006e":_dfeca :=_abgge .parseMarginAttr (_agac ,_aggd );_ddced .SetMargins (_dfeca .Left ,_dfeca .Right ,_dfeca .Top ,_dfeca .Bottom );case "\u0077\u0069\u0064t\u0068":_ddced ._cegb .SetWidth (int (_abgge .parseFloatAttr (_agac ,_aggd )));
case "\u0068\u0065\u0069\u0067\u0068\u0074":_ddced ._cegb .SetHeight (int (_abgge .parseFloatAttr (_agac ,_aggd )));case "\u0073\u0072\u0063":break ;default:_abgge .nodeLogDebug (_afge ,"\u0055n\u0073\u0075p\u0070\u006f\u0072\u0074e\u0064\u0020\u0063h\u0061\u0072\u0074\u0020\u0061\u0074\u0074\u0072\u0069bu\u0074\u0065\u003a \u0060\u0025s\u0060\u002e\u0020\u0053\u006b\u0069p\u0070\u0069n\u0067\u002e",_agac );
};};return _ddced ,nil ;};

// SetSideBorderColor sets the cell's side border color.
func (_cbfce *TableCell )SetSideBorderColor (side CellBorderSide ,col Color ){switch side {case CellBorderSideAll :_cbfce ._dgdf =col ;_cbfce ._cebb =col ;_cbfce ._fdffg =col ;_cbfce ._effdc =col ;case CellBorderSideTop :_cbfce ._dgdf =col ;case CellBorderSideBottom :_cbfce ._cebb =col ;
case CellBorderSideLeft :_cbfce ._fdffg =col ;case CellBorderSideRight :_cbfce ._effdc =col ;};};

// FilledCurve represents a closed path of Bezier curves with a border and fill.
type FilledCurve struct{_bec []_gc .CubicBezierCurve ;FillEnabled bool ;_fcef Color ;BorderEnabled bool ;BorderWidth float64 ;_ccde Color ;_eae *_db .StructureTagInfo ;};

// BorderColor returns the border color of the ellipse.
func (_gfgf *Ellipse )BorderColor ()Color {return _gfgf ._bcgd };func _fcegf ()commands {var _cfaeg =map[string ]int {"\u006d":2,"\u007a":0,"\u006c":2,"\u0068":1,"\u0076":1,"\u0063":6,"\u0073":4,"\u0071":4,"\u0074":2,"\u0061":7};var _cfgac []string ;for _ageeg :=range _cfaeg {_cfgac =append (_cfgac ,_ageeg );
};return commands {_cfgac ,_cfaeg ,"\u006d","\u007a"};};func (_ecfgc *Ellipse )applyFitMode (_efcd float64 ){_efcd -=_ecfgc ._dafc .Left +_ecfgc ._dafc .Right ;switch _ecfgc ._dbcd {case FitModeFillWidth :_ecfgc .ScaleToWidth (_efcd );};};

// SetMargins sets the Block's left, right, top, bottom, margins.
func (_abc *Block )SetMargins (left ,right ,top ,bottom float64 ){_abc ._fg .Left =left ;_abc ._fg .Right =right ;_abc ._fg .Top =top ;_abc ._fg .Bottom =bottom ;};

// RotatedSize returns the width and height of the rotated block.
func (_egf *Block )RotatedSize ()(float64 ,float64 ){_ ,_ ,_dag ,_be :=_adeg (_egf ._fedd ,_egf ._dbe ,_egf ._da );return _dag ,_be ;};func _abcff (_eccag []*ColorPoint )*LinearShading {return &LinearShading {_afdb :&shading {_cgbf :ColorWhite ,_efbd :false ,_edga :[]bool {false ,false },_bacgg :_eccag },_effcg :&_db .PdfRectangle {}};
};

// SetBorderColor sets the cell's border color.
func (_ebecb *TableCell )SetBorderColor (col Color ){_ebecb ._fdffg =col ;_ebecb ._cebb =col ;_ebecb ._effdc =col ;_ebecb ._dgdf =col ;};

// Length calculates and returns the length of the line.
func (_bbge *Line )Length ()float64 {return _fa .Sqrt (_fa .Pow (_bbge ._cacg -_bbge ._ggdbc ,2.0)+_fa .Pow (_bbge ._degba -_bbge ._geega ,2.0));};func (_feec *pageTransformations )applyFlip (_ecfg *_db .PdfPage )error {_dab ,_agcb :=_feec ._egeb ,_feec ._aeg ;
if !_dab &&!_agcb {return nil ;};if _ecfg ==nil {return _ee .New ("\u006e\u006f\u0020\u0070\u0061\u0067\u0065\u0020\u0061c\u0074\u0069\u0076\u0065");};_bcdf ,_ecfd :=_ecfg .GetMediaBox ();if _ecfd !=nil {return _ecfd ;};_cceg ,_ebc :=_bcdf .Width (),_bcdf .Height ();
_afag ,_ecfd :=_ecfg .GetRotate ();if _ecfd !=nil {_a .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0025\u0073\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0061\u006e\u0064\u0020\u0061\u0073\u0073\u0075\u006d\u0069\u006e\u0067\u0020\u006e\u006f\u0020\u0072\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u000a",_ecfd .Error ());
};if _befd :=_afag %360!=0&&_afag %90==0;_befd {if _fgge :=(360+_afag %360)%360;_fgge ==90||_fgge ==270{_dab ,_agcb =_agcb ,_dab ;};};_abe ,_abcb :=1.0,0.0;if _dab {_abe ,_abcb =-1.0,-_cceg ;};_ddba ,_eegc :=1.0,0.0;if _agcb {_ddba ,_eegc =-1.0,-_ebc ;
};_fff :=_eg .NewContentCreator ().Scale (_abe ,_ddba ).Translate (_abcb ,_eegc );_bgae ,_ecfd :=_dd .MakeStream (_fff .Bytes (),_dd .NewFlateEncoder ());if _ecfd !=nil {return _ecfd ;};_fcffe :=_dd .MakeArray (_bgae );_fcffe .Append (_ecfg .GetContentStreamObjs ()...);
_ecfg .Contents =_fcffe ;return nil ;};

// GenerateKDict generates a K dictionary for the chart component.
func (_dgcac *Chart )GenerateKDict ()(*_db .KDict ,error ){if _dgcac ._aaf ==nil {return nil ,_g .Errorf ("c\u0068\u0061\u0072\u0074\u0020\u0073t\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0074\u0061\u0067\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _dgcac ._aaf .GenerateKDict (),nil ;};func (_eccd *LinearShading )shadingModel ()*_db .PdfShadingType2 {_ggba :=_gc .NewPoint (_eccd ._effcg .Llx +_eccd ._effcg .Width ()/2,_eccd ._effcg .Lly +_eccd ._effcg .Height ()/2);_acfe :=_gc .NewPoint (_eccd ._effcg .Llx ,_eccd ._effcg .Lly +_eccd ._effcg .Height ()/2).Add (-_ggba .X ,-_ggba .Y ).Rotate (_eccd ._bfdd ).Add (_ggba .X ,_ggba .Y );
_acfe =_gc .NewPoint (_fa .Max (_fa .Min (_acfe .X ,_eccd ._effcg .Urx ),_eccd ._effcg .Llx ),_fa .Max (_fa .Min (_acfe .Y ,_eccd ._effcg .Ury ),_eccd ._effcg .Lly ));_abeeg :=_gc .NewPoint (_eccd ._effcg .Urx ,_eccd ._effcg .Lly +_eccd ._effcg .Height ()/2).Add (-_ggba .X ,-_ggba .Y ).Rotate (_eccd ._bfdd ).Add (_ggba .X ,_ggba .Y );
_abeeg =_gc .NewPoint (_fa .Min (_fa .Max (_abeeg .X ,_eccd ._effcg .Llx ),_eccd ._effcg .Urx ),_fa .Min (_fa .Max (_abeeg .Y ,_eccd ._effcg .Lly ),_eccd ._effcg .Ury ));_gcefg :=_db .NewPdfShadingType2 ();_gcefg .PdfShading .ShadingType =_dd .MakeInteger (2);
_gcefg .PdfShading .ColorSpace =_db .NewPdfColorspaceDeviceRGB ();_gcefg .PdfShading .AntiAlias =_dd .MakeBool (_eccd ._afdb ._efbd );_gcefg .Coords =_dd .MakeArrayFromFloats ([]float64 {_acfe .X ,_acfe .Y ,_abeeg .X ,_abeeg .Y });_gcefg .Extend =_dd .MakeArray (_dd .MakeBool (_eccd ._afdb ._edga [0]),_dd .MakeBool (_eccd ._afdb ._edga [1]));
_gcefg .Function =_eccd ._afdb .generatePdfFunctions ();return _gcefg ;};func (_gbdcf *templateProcessor )parseColor (_aded string )Color {if _aded ==""{return nil ;};_gdegf ,_bdcgg :=_gbdcf ._bgggf .ColorMap [_aded ];if _bdcgg {return _gdegf ;};if _aded [0]=='#'{return ColorRGBFromHex (_aded );
};return nil ;};

// Width returns the Block's width.
func (_egg *Block )Width ()float64 {return _egg ._fedd };func _afbb (_cbcae []*_db .PdfAnnotation )[]*_db .PdfAnnotation {var _egcac []*_db .PdfAnnotation ;for _ ,_ddacb :=range _cbcae {_egcac =append (_egcac ,_ggfbg (_ddacb ));};return _egcac ;};func _cafg (_bdfg *Table ,_agdg DrawContext )([]*Block ,DrawContext ,error ){var _daca []*Block ;
_fbddc :=NewBlock (_agdg .PageWidth ,_agdg .PageHeight );_bdfg .updateRowHeights (_agdg .Width -_bdfg ._acbac .Left -_bdfg ._acbac .Right );_aacff :=_bdfg ._acbac .Top ;if _bdfg ._bafbg .IsRelative ()&&!_bdfg ._aecdf {_ageeb :=_bdfg .Height ();if _ageeb > _agdg .Height -_bdfg ._acbac .Top &&_ageeb <=_agdg .PageHeight -_agdg .Margins .Top -_agdg .Margins .Bottom {_daca =[]*Block {NewBlock (_agdg .PageWidth ,_agdg .PageHeight -_agdg .Y )};
var _bcae error ;if _ ,_agdg ,_bcae =_aegb ().GeneratePageBlocks (_agdg );_bcae !=nil {return nil ,_agdg ,_bcae ;};_aacff =0;};};_acbacd :=_agdg ;if _bdfg ._bafbg .IsAbsolute (){_agdg .X =_bdfg ._cafd ;_agdg .Y =_bdfg ._efcce ;}else {_agdg .X +=_bdfg ._acbac .Left ;
_agdg .Y +=_aacff ;_agdg .Width -=_bdfg ._acbac .Left +_bdfg ._acbac .Right ;_agdg .Height -=_aacff ;};_adgdc :=_agdg .Width ;_fefdg :=_agdg .X ;_bcbgb :=_agdg .Y ;_babe :=_agdg .Height ;_gdccf :=0;_fdce ,_bgcga :=-1,-1;if _bdfg ._gcfeb {for _aadeg ,_cdeee :=range _bdfg ._ccgf {if _cdeee ._efage < _bdfg ._cdafe {continue ;
};if _cdeee ._efage > _bdfg ._bcfb {break ;};if _fdce < 0{_fdce =_aadeg ;};_bgcga =_aadeg ;};};if _agecf :=_bdfg .wrapContent (_agdg );_agecf !=nil {return nil ,_agdg ,_agecf ;};_bdfg .updateRowHeights (_agdg .Width -_bdfg ._acbac .Left -_bdfg ._acbac .Right );
var (_ebcfc bool ;_bgbfa int ;_fedeg int ;_fdcba bool ;_gebbf int ;_adfb error ;_gcff int64 ;_gdaea *_db .KDict ;);if _bdfg ._ebea {_bdfg .openTag (_fbddc ,_bdfg ._fefdd );_gcff =_bdfg ._fefdd .Mcid +1;};for _adffb :=0;_adffb < len (_bdfg ._ccgf );_adffb ++{_ddcc :=_bdfg ._ccgf [_adffb ];
if _bdfg ._ebea &&_ddcc ._efage > _gebbf {_bdfg .openTag (_fbddc ,&_db .StructureTagInfo {Mcid :_gcff ,StructureType :_db .StructureTypeTableRow });_gdaea =_bdfg .addRowTag ();_gebbf =_ddcc ._efage ;_gcff ++;};if _afefb ,_dbcgf :=_bdfg .getLastCellFromCol (_ddcc ._afdac );
_afefb ==_adffb {if (_dbcgf ._efage +_dbcgf ._cgedc -1)< _bdfg ._fgdc {for _cbgf :=_ddcc ._efage ;_cbgf < _bdfg ._fgdc ;_cbgf ++{_gcccb :=&TableCell {};_gcccb ._efage =_cbgf +1;_gcccb ._cgedc =1;_gcccb ._afdac =_ddcc ._afdac ;_bdfg ._ccgf =append (_bdfg ._ccgf ,_gcccb );
};};};_aadff :=_ddcc .width (_bdfg ._bdggd ,_adgdc );_abcee :=float64 (0.0);for _abge :=0;_abge < _ddcc ._afdac -1;_abge ++{_abcee +=_bdfg ._bdggd [_abge ]*_adgdc ;};_dbggb :=float64 (0.0);for _dbeg :=_gdccf ;_dbeg < _ddcc ._efage -1;_dbeg ++{_dbggb +=_bdfg ._gdbgf [_dbeg ];
};_agdg .Height =_babe -_dbggb ;_cfeee :=float64 (0.0);for _ebebc :=0;_ebebc < _ddcc ._cgedc ;_ebebc ++{_cfeee +=_bdfg ._gdbgf [_ddcc ._efage +_ebebc -1];};_fbfbfe :=_fdcba &&_ddcc ._efage !=_gebbf ;_gebbf =_ddcc ._efage ;if _fbfbfe ||_cfeee > _agdg .Height {if _bdfg ._dffga &&!_fdcba {_fdcba ,_adfb =_bdfg .wrapRow (_adffb ,_agdg ,_adgdc );
if _adfb !=nil {return nil ,_agdg ,_adfb ;};if _fdcba {_adffb --;continue ;};};_daca =append (_daca ,_fbddc );_fbddc =NewBlock (_agdg .PageWidth ,_agdg .PageHeight );_fefdg =_agdg .Margins .Left +_bdfg ._acbac .Left ;_bcbgb =_agdg .Margins .Top ;_agdg .Height =_agdg .PageHeight -_agdg .Margins .Top -_agdg .Margins .Bottom ;
_agdg .Page ++;_babe =_agdg .Height ;_gdccf =_ddcc ._efage -1;_dbggb =0;_fdcba =false ;if _bdfg ._gcfeb &&_fdce >=0{_bgbfa =_adffb ;_adffb =_fdce -1;_fedeg =_gdccf ;_gdccf =_bdfg ._cdafe -1;_ebcfc =true ;if _ddcc ._cgedc > (_bdfg ._fgdc -_gebbf )||(_ddcc ._cgedc > 1&&_adffb < 0){_a .Log .Debug ("\u0054a\u0062\u006ce\u0020\u0068\u0065a\u0064\u0065\u0072\u0020\u0072\u006f\u0077s\u0070\u0061\u006e\u0020\u0065\u0078c\u0065\u0065\u0064\u0073\u0020\u0061\u0076\u0061\u0069\u006c\u0061b\u006c\u0065\u0020\u0073\u0070\u0061\u0063\u0065\u002e");
_ebcfc =false ;_fdce ,_bgcga =-1,-1;};continue ;};if _fbfbfe {_adffb --;continue ;};};_agdg .Width =_aadff ;_agdg .X =_fefdg +_abcee ;_agdg .Y =_bcbgb +_dbggb ;if _cfeee > _agdg .PageHeight -_agdg .Margins .Top -_agdg .Margins .Bottom {_cfeee =_agdg .PageHeight -_agdg .Margins .Top -_agdg .Margins .Bottom ;
};_cadff :=_fddd (_agdg .X ,_agdg .Y ,_aadff ,_cfeee );if _ddcc ._bfcef !=nil {_cadff .SetFillColor (_ddcc ._bfcef );};_cadff .SetOpacity (_ddcc ._ebbg );_cadff .LineStyle =_ddcc ._cdadf ;_cadff ._cdd =_ddcc ._cabbe ;_cadff ._egc =_ddcc ._bfaea ;_cadff ._fdd =_ddcc ._eafce ;
_cadff ._cgbc =_ddcc ._gecgee ;if _ddcc ._fdffg !=nil {_cadff .SetColorLeft (_ddcc ._fdffg );};if _ddcc ._cebb !=nil {_cadff .SetColorBottom (_ddcc ._cebb );};if _ddcc ._effdc !=nil {_cadff .SetColorRight (_ddcc ._effdc );};if _ddcc ._dgdf !=nil {_cadff .SetColorTop (_ddcc ._dgdf );
};_cadff .SetWidthBottom (_ddcc ._cfcab );_cadff .SetWidthLeft (_ddcc ._gbgfg );_cadff .SetWidthRight (_ddcc ._dccbf );_cadff .SetWidthTop (_ddcc ._ebde );_cgged :=NewBlock (_fbddc ._fedd ,_fbddc ._dbe );_cfeb :=_fbddc .Draw (_cadff );if _cfeb !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_cfeb );
};if _ddcc ._fbaee !=nil {var _bbec *_db .KDict ;if _bdfg ._ebea {_bdfg .openTag (_fbddc ,&_db .StructureTagInfo {Mcid :_gcff ,StructureType :_db .StructureTypeTableData });_ddcc .SetMarkedContentID (_gcff );_ddcc .SetStructureType (_db .StructureTypeTableData );
_bbec ,_cfeb =_ddcc .GenerateKDict ();if _cfeb !=nil {return nil ,_agdg ,_g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074\u0065 \u004b\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0074\u0061b\u006c\u0065\u0020\u0063\u0065\u006c\u006c\u003a\u0020\u0025\u0077",_cfeb );
};_gdaea .AddKChild (_bbec );_gcff ++;};_gbbae :=_ddcc ._fbaee .Width ();_aaaag :=_ddcc ._fbaee .Height ();_bfgbe :=0.0;switch _bcbfd :=_ddcc ._fbaee .(type ){case *Paragraph :if _bcbfd ._gcdc {_gbbae =_bcbfd .getMaxLineWidth ()/1000.0;};_bgada ,_bcaed ,_ :=_bcbfd .getTextMetrics ();
_fgagb ,_dfgg :=_bgada *_bcbfd ._gafdf ,_bcaed *_bcbfd ._gafdf ;_aaaag =_aaaag -_dfgg +_fgagb ;_bfgbe +=_fgagb -_dfgg ;_deefe :=0.5;if _bdfg ._cbge {_deefe =0.3;};switch _ddcc ._adfcfg {case CellVerticalAlignmentTop :_bfgbe +=_fgagb *_deefe ;case CellVerticalAlignmentBottom :_bfgbe -=_fgagb *_deefe ;
};_gbbae +=_bcbfd ._fbgdf .Left +_bcbfd ._fbgdf .Right ;_aaaag +=_bcbfd ._fbgdf .Top +_bcbfd ._fbgdf .Bottom ;if _bbec !=nil {_bcbfd .SetMarkedContentID (_gcff );_bcbfd .SetStructureType (_db .StructureTypeParagraph );_afad ,_afcfa :=_bcbfd .GenerateKDict ();
if _afcfa !=nil {return nil ,_agdg ,_g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064 \u0074\u006f\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074\u0065\u0020\u004b\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0070\u0061\u0072\u0061\u0067\u0072a\u0070h\u003a\u0020\u0025\u0077",_afcfa );
};_bbec .AddKChild (_afad );_gcff ++;};case *StyledParagraph :if _bcbfd ._gfce {_gbbae =_bcbfd .getMaxLineWidth ()/1000.0;};_eebcb ,_bagb ,_gbcde :=_bcbfd .getLineMetrics (0);_agcde ,_cafff :=_eebcb *_bcbfd ._abbd ,_bagb *_bcbfd ._abbd ;if _bcbfd ._bded ==TextVerticalAlignmentCenter {_bfgbe =_cafff -(_bagb +(_eebcb +_gbcde -_bagb )/2+(_cafff -_bagb )/2);
};if len (_bcbfd ._cbba )==1{_aaaag =_agcde ;}else {_aaaag =_aaaag -_cafff +_agcde ;};_bfgbe +=_agcde -_cafff ;switch _ddcc ._adfcfg {case CellVerticalAlignmentTop :_bfgbe +=_agcde *0.5;case CellVerticalAlignmentBottom :_bfgbe -=_agcde *0.5;};_gbbae +=_bcbfd ._feeea .Left +_bcbfd ._feeea .Right ;
_aaaag +=_bcbfd ._feeea .Top +_bcbfd ._feeea .Bottom ;if _bbec !=nil {_bcbfd .SetMarkedContentID (_gcff );_bcbfd .SetStructureType (_db .StructureTypeParagraph );_befaga ,_ddcgc :=_bcbfd .GenerateKDict ();if _ddcgc !=nil {return nil ,_agdg ,_g .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064 \u0074\u006f\u0020g\u0065\u006e\u0065r\u0061\u0074\u0065\u0020\u004b\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079 f\u006f\u0072\u0020\u0073\u0074\u0079\u006c\u0065\u0064\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u003a\u0020\u0025\u0077",_ddcgc );
};_bbec .AddKChild (_befaga );_gcff ++;};case *Table :_gbbae =_aadff ;case *List :_gbbae =_aadff ;case *Division :_gbbae =_aadff ;case *Chart :_gbbae =_aadff ;case *Line :_aaaag +=_bcbfd ._fffd .Top +_bcbfd ._fffd .Bottom ;_bfgbe -=_bcbfd .Height ()/2;
case *Image :_gbbae +=_bcbfd ._efde .Left +_bcbfd ._efde .Right ;_aaaag +=_bcbfd ._efde .Top +_bcbfd ._efde .Bottom ;};switch _ddcc ._dfdda {case CellHorizontalAlignmentLeft :_agdg .X +=_ddcc ._eggag ;_agdg .Width -=_ddcc ._eggag ;case CellHorizontalAlignmentCenter :if _gdeg :=_aadff -_gbbae ;
_gdeg > 0{_agdg .X +=_gdeg /2;_agdg .Width -=_gdeg /2;};case CellHorizontalAlignmentRight :if _aadff > _gbbae {_agdg .X =_agdg .X +_aadff -_gbbae -_ddcc ._eggag ;_agdg .Width -=_ddcc ._eggag ;};};_dfeb :=_agdg .Y ;_dbbcg :=_agdg .Height ;_agdg .Y +=_bfgbe ;
switch _ddcc ._adfcfg {case CellVerticalAlignmentTop :case CellVerticalAlignmentMiddle :if _bbfff :=_cfeee -_aaaag ;_bbfff > 0{_agdg .Y +=_bbfff /2;_agdg .Height -=_bbfff /2;};case CellVerticalAlignmentBottom :if _cfeee > _aaaag {_agdg .Y =_agdg .Y +_cfeee -_aaaag ;
_agdg .Height =_cfeee ;};};_cgffc :=_fbddc .DrawWithContext (_ddcc ._fbaee ,_agdg );if _cgffc !=nil {if _ee .Is (_cgffc ,ErrContentNotFit )&&!_fbfbfe {_fbddc =_cgged ;_fbfbfe =true ;_adffb --;continue ;};_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_cgffc );
};_agdg .Y =_dfeb ;_agdg .Height =_dbbcg ;};_agdg .Y +=_cfeee ;_agdg .Height -=_cfeee ;if _ebcfc &&_adffb +1> _bgcga {_bcbgb +=_dbggb +_cfeee ;_babe -=_cfeee +_dbggb ;_gdccf =_fedeg ;_adffb =_bgbfa -1;_ebcfc =false ;};if _bdfg ._ebea {_bdfg .closeTag (_fbddc );
if _adffb +1==len (_bdfg ._ccgf )||_bdfg ._ccgf [_adffb +1]._efage > _gebbf {_bdfg .closeTag (_fbddc );};};};if _bdfg ._ebea {_bdfg .closeTag (_fbddc );};_daca =append (_daca ,_fbddc );if _bdfg ._bafbg .IsAbsolute (){return _daca ,_acbacd ,nil ;};_agdg .X =_acbacd .X ;
_agdg .Width =_acbacd .Width ;_agdg .Y +=_bdfg ._acbac .Bottom ;_agdg .Height -=_bdfg ._acbac .Bottom ;return _daca ,_agdg ,nil ;};

// NewRectangle creates a new rectangle with the left corner at (`x`, `y`),
// having the specified width and height.
// NOTE: In relative positioning mode, `x` and `y` are calculated using the
// current context. Furthermore, when the fit mode is set to fill the available
// space, the rectangle is scaled so that it occupies the entire context width
// while maintaining the original aspect ratio.
func (_cedc *Creator )NewRectangle (x ,y ,width ,height float64 )*Rectangle {return _cgaag (x ,y ,width ,height );};

// SetStyle sets the style for all the line components: number, title,
// separator, page.
func (_ebgeee *TOCLine )SetStyle (style TextStyle ){_ebgeee .Number .Style =style ;_ebgeee .Title .Style =style ;_ebgeee .Separator .Style =style ;_ebgeee .Page .Style =style ;};func _caefg (_facbd *templateProcessor ,_abebe *templateNode )(interface{},error ){return _facbd .parseTable (_abebe );
};func (_abdea *templateProcessor )parseFitModeAttr (_gdcb ,_agdac string )FitMode {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0066\u0069\u0074\u0020\u006do\u0064\u0065\u0020\u0061\u0074\u0074r\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060\u0025\u0073\u0060\u002c \u0025\u0073\u0029\u002e",_gdcb ,_agdac );
_fabdf :=map[string ]FitMode {"\u006e\u006f\u006e\u0065":FitModeNone ,"\u0066\u0069\u006c\u006c\u002d\u0077\u0069\u0064\u0074\u0068":FitModeFillWidth }[_agdac ];return _fabdf ;};

// NewTOC creates a new table of contents.
func (_gedg *Creator )NewTOC (title string )*TOC {_eefc :=_gedg .NewTextStyle ();_eefc .Font =_gedg ._edeg ;return _efcbd (title ,_gedg .NewTextStyle (),_eefc );};func (_ffeag *TableCell )height (_baeg float64 )float64 {var _ageaf float64 ;switch _gfge :=_ffeag ._fbaee .(type ){case *Paragraph :if _gfge ._gcdc {_gfge .SetWidth (_baeg -_ffeag ._eggag -_gfge ._fbgdf .Left -_gfge ._fbgdf .Right );
};_ageaf =_gfge .Height ()+_gfge ._fbgdf .Top +_gfge ._fbgdf .Bottom ;if !_ffeag ._dcdcf ._cbge {_ageaf +=(0.5*_gfge ._febee *_gfge ._gafdf );};case *StyledParagraph :if _gfge ._gfce {_gfge .SetWidth (_baeg -_ffeag ._eggag -_gfge ._feeea .Left -_gfge ._feeea .Right );
};_ageaf =_gfge .Height ()+_gfge ._feeea .Top +_gfge ._feeea .Bottom ;if !_ffeag ._dcdcf ._cbge {_ageaf +=(0.5*_gfge .getTextHeight ());};case *Image :_gfge .applyFitMode (_baeg -_ffeag ._eggag );_ageaf =_gfge .Height ()+_gfge ._efde .Top +_gfge ._efde .Bottom ;
case *Table :_gfge .updateRowHeights (_baeg -_ffeag ._eggag -_gfge ._acbac .Left -_gfge ._acbac .Right );_ageaf =_gfge .Height ()+_gfge ._acbac .Top +_gfge ._acbac .Bottom ;case *List :_ageaf =_gfge .ctxHeight (_baeg -_ffeag ._eggag )+_gfge ._ddggd .Top +_gfge ._ddggd .Bottom ;
case *Division :_ageaf =_gfge .ctxHeight (_baeg -_ffeag ._eggag )+_gfge ._fccaf .Top +_gfge ._fccaf .Bottom +_gfge ._ddcg .Top +_gfge ._ddcg .Bottom ;case *Chart :_ageaf =_gfge .Height ()+_gfge ._ace .Top +_gfge ._ace .Bottom ;case *Rectangle :_gfge .applyFitMode (_baeg -_ffeag ._eggag );
_ageaf =_gfge .Height ()+_gfge ._abeec .Top +_gfge ._abeec .Bottom +_gfge ._dfbcd ;case *Ellipse :_gfge .applyFitMode (_baeg -_ffeag ._eggag );_ageaf =_gfge .Height ()+_gfge ._dafc .Top +_gfge ._dafc .Bottom ;case *Line :_ageaf =_gfge .Height ()+_gfge ._fffd .Top +_gfge ._fffd .Bottom ;
};return _ageaf ;};

// SetMarkedContentID sets the marked content ID for the image.
func (_cgece *Image )SetMarkedContentID (mcid int64 ){if _cgece ._fecd ==nil {_cgece ._fecd =_db .NewStructureTagInfo ();_cgece ._fecd .StructureType =_db .StructureTypeFigure ;};_cgece ._fecd .Mcid =mcid ;};func (_geddd *templateProcessor )parseFontAttr (_dddga ,_aacbe string )*_db .PdfFont {_a .Log .Debug ("P\u0061\u0072\u0073\u0069\u006e\u0067 \u0066\u006f\u006e\u0074\u0020\u0061t\u0074\u0072\u0069\u0062\u0075\u0074\u0065:\u0020\u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073)\u002e",_dddga ,_aacbe );
_cbfcag :=_geddd .creator ._bae ;if _aacbe ==""{return _cbfcag ;};_adfcec :=_fc .Split (_aacbe ,"\u002c");for _ ,_bdae :=range _adfcec {_bdae =_fc .TrimSpace (_bdae );if _bdae ==""{continue ;};_abefc ,_cdge :=_geddd ._bgggf .FontMap [_aacbe ];if _cdge {return _abefc ;
};_eeggg ,_cdge :=map[string ]_db .StdFontName {"\u0063o\u0075\u0072\u0069\u0065\u0072":_db .CourierName ,"\u0063\u006f\u0075r\u0069\u0065\u0072\u002d\u0062\u006f\u006c\u0064":_db .CourierBoldName ,"\u0063o\u0075r\u0069\u0065\u0072\u002d\u006f\u0062\u006c\u0069\u0071\u0075\u0065":_db .CourierObliqueName ,"c\u006fu\u0072\u0069\u0065\u0072\u002d\u0062\u006f\u006cd\u002d\u006f\u0062\u006ciq\u0075\u0065":_db .CourierBoldObliqueName ,"\u0068e\u006c\u0076\u0065\u0074\u0069\u0063a":_db .HelveticaName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0062\u006f\u006c\u0064":_db .HelveticaBoldName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u006f\u0062l\u0069\u0071\u0075\u0065":_db .HelveticaObliqueName ,"\u0068\u0065\u006c\u0076et\u0069\u0063\u0061\u002d\u0062\u006f\u006c\u0064\u002d\u006f\u0062\u006c\u0069\u0071u\u0065":_db .HelveticaBoldObliqueName ,"\u0073\u0079\u006d\u0062\u006f\u006c":_db .SymbolName ,"\u007a\u0061\u0070\u0066\u002d\u0064\u0069\u006e\u0067\u0062\u0061\u0074\u0073":_db .ZapfDingbatsName ,"\u0074\u0069\u006de\u0073":_db .TimesRomanName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064":_db .TimesBoldName ,"\u0074\u0069\u006de\u0073\u002d\u0069\u0074\u0061\u006c\u0069\u0063":_db .TimesItalicName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064\u002d\u0069t\u0061\u006c\u0069\u0063":_db .TimesBoldItalicName }[_aacbe ];
if _cdge {if _faega ,_egaf :=_db .NewStandard14Font (_eeggg );_egaf ==nil {return _faega ;};};if _dgdee :=_geddd .parseAttrPropList (_bdae );len (_dgdee )> 0{if _bebcb ,_edbafd :=_dgdee ["\u0070\u0061\u0074\u0068"];_edbafd {_cfbbf :=_db .NewPdfFontFromTTFFile ;
if _gbdeg ,_egbbd :=_dgdee ["\u0074\u0079\u0070\u0065"];_egbbd &&_gbdeg =="\u0063o\u006d\u0070\u006f\u0073\u0069\u0074e"{_cfbbf =_db .NewCompositePdfFontFromTTFFile ;};if _eedfe ,_ccdbf :=_cfbbf (_bebcb );_ccdbf !=nil {_a .Log .Debug ("\u0043\u006fu\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u0060\u0025\u0073\u0060\u003a %\u0076\u002e",_bebcb ,_ccdbf );
}else {return _eedfe ;};};};};return _cbfcag ;};type border struct{_fbac float64 ;_gba float64 ;_geb float64 ;_agd float64 ;_ffc Color ;_decd Color ;_edfb float64 ;_dcg Color ;_eaag float64 ;_aff Color ;_cbfa float64 ;_geaa Color ;_cfb float64 ;LineStyle _gc .LineStyle ;
_cdd CellBorderStyle ;_egc CellBorderStyle ;_fdd CellBorderStyle ;_cgbc CellBorderStyle ;_afec float64 ;};

// AddHighlightedText adds a new highlighted text to the paragraph.
func (_efggg *StyledParagraph )AddHighlightedText (text string ,color Color ,alpha float64 )*TextChunk {_egba :=NewTextChunk (text ,_efggg ._acfg );_egba .Highlight (color ,alpha );return _efggg .appendChunk (_egba );};func (_gggg *Invoice )SetStructureType (structureType _db .StructureType ){};
func _fabba (_acaaa ,_bcdbg ,_aedae TextChunk ,_cfde uint ,_gfgbcb TextStyle )*TOCLine {_bebbd :=_gcefe (_gfgbcb );_bebbd .SetEnableWrap (true );_bebbd .SetTextAlignment (TextAlignmentLeft );_bebbd .SetMargins (0,0,2,2);_ecbc :=&TOCLine {_geffaf :_bebbd ,Number :_acaaa ,Title :_bcdbg ,Page :_aedae ,Separator :TextChunk {Text :"\u002e",Style :_gfgbcb },_dacdb :0,_aeaad :_cfde ,_acade :10,_cgfgd :PositionRelative };
_bebbd ._feeea .Left =_ecbc ._dacdb +float64 (_ecbc ._aeaad -1)*_ecbc ._acade ;_bebbd ._gcggg =_ecbc .prepareParagraph ;return _ecbc ;};

// GeneratePageBlocks generates the page blocks. Multiple blocks are generated
// if the contents wrap over multiple pages. Implements the Drawable interface.
func (_eebfa *StyledParagraph )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_cagea :=ctx ;var _bfecdf []*Block ;_fafcf :=NewBlock (ctx .PageWidth ,ctx .PageHeight );if _eebfa ._dfedd .IsRelative (){ctx .X +=_eebfa ._feeea .Left ;
ctx .Y +=_eebfa ._feeea .Top ;ctx .Width -=_eebfa ._feeea .Left +_eebfa ._feeea .Right ;ctx .Height -=_eebfa ._feeea .Top ;_eebfa .SetWidth (ctx .Width );}else {if int (_eebfa ._gfba )<=0{_eebfa .SetWidth (_eebfa .getTextWidth ()/1000.0);};ctx .X =_eebfa ._ecgb ;
ctx .Y =_eebfa ._gbdad ;};if _eebfa ._gcggg !=nil {_eebfa ._gcggg (_eebfa ,ctx );};if _cbedf :=_eebfa .wrapText ();_cbedf !=nil {return nil ,ctx ,_cbedf ;};_bdbaa :=_eebfa ._cbba ;_fbddb :=0;for {_gacdb ,_bgbcd ,_dabda :=_faafc (_fafcf ,_eebfa ,_bdbaa ,ctx );
if _dabda !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_dabda );return nil ,ctx ,_dabda ;};ctx =_gacdb ;_bfecdf =append (_bfecdf ,_fafcf );if _bdbaa =_bgbcd ;len (_bgbcd )==0{break ;};if len (_bgbcd )==_fbddb {return nil ,ctx ,_ee .New ("\u006e\u006f\u0074\u0020\u0065\u006e\u006f\u0075\u0067\u0068 \u0073\u0070\u0061\u0063\u0065\u0020\u0066o\u0072\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068");
};_fafcf =NewBlock (ctx .PageWidth ,ctx .PageHeight );ctx .Page ++;_gacdb =ctx ;_gacdb .Y =ctx .Margins .Top ;_gacdb .X =ctx .Margins .Left +_eebfa ._feeea .Left ;_gacdb .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom ;_gacdb .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_eebfa ._feeea .Left -_eebfa ._feeea .Right ;
ctx =_gacdb ;_fbddb =len (_bgbcd );};if _eebfa ._dfedd .IsRelative (){ctx .Y +=_eebfa ._feeea .Bottom ;ctx .Height -=_eebfa ._feeea .Bottom ;if !ctx .Inline {ctx .X =_cagea .X ;ctx .Width =_cagea .Width ;};return _bfecdf ,ctx ,nil ;};return _bfecdf ,_cagea ,nil ;
};

// NewTOCLine creates a new table of contents line with the default style.
func (_dfea *Creator )NewTOCLine (number ,title ,page string ,level uint )*TOCLine {return _eadag (number ,title ,page ,level ,_dfea .NewTextStyle ());};

// NewPolygon creates a new polygon.
func (_feag *Creator )NewPolygon (points [][]_gc .Point )*Polygon {return _efga (points )};

// SetHorizontalAlignment sets the cell's horizontal alignment of content.
// Can be one of:
// - CellHorizontalAlignmentLeft
// - CellHorizontalAlignmentCenter
// - CellHorizontalAlignmentRight
func (_ceggb *GridCell )SetHorizontalAlignment (halign CellHorizontalAlignment ){_ceggb ._efab =halign };

// GetMargins returns the Paragraph's margins: left, right, top, bottom.
func (_eacdg *StyledParagraph )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _eacdg ._feeea .Left ,_eacdg ._feeea .Right ,_eacdg ._feeea .Top ,_eacdg ._feeea .Bottom ;};

// Highlight adds a highlight annotation to the text chunk with the specified color and alpha value.
func (_eaba *TextChunk )Highlight (color Color ,alpha float64 )*_db .PdfAnnotation {_gdade ,_acgaf ,_fffa :=color .ToRGB ();_dgfdb :=_db .NewPdfAnnotationHighlight ();_dgfdb .C =_dd .MakeArrayFromFloats ([]float64 {_gdade ,_acgaf ,_fffa });_dgfdb .CA =_dd .MakeFloat (alpha );
_eaba .AddAnnotation (_dgfdb .PdfAnnotation );return _dgfdb .PdfAnnotation ;};

// Paragraph represents text drawn with a specified font and can wrap across lines and pages.
// By default, it occupies the available width in the drawing context.
//
// Deprecated: This object is deprecated and will be removed in future versions.
//
// Use StyledParagraph instead as it provides more features and is more flexible.
type Paragraph struct{_cegc string ;_aad *_db .PdfFont ;_febee float64 ;_gafdf float64 ;_ffgfg Color ;_aegda TextAlignment ;_gcdc bool ;_becad float64 ;_fegae int ;_gcfc bool ;_cagf float64 ;_fbgdf Margins ;_aecc Positioning ;_eedf float64 ;_cgdg float64 ;
_cgbge ,_cgdc float64 ;_gbde []string ;_dfec *_db .StructureTagInfo ;_fedb string ;};

// GenerateKDict generates a K dictionary for the paragraph component.
func (_bbcac *Paragraph )GenerateKDict ()(*_db .KDict ,error ){if _bbcac ._dfec ==nil {return nil ,_g .Errorf ("p\u0061\u0072\u0061\u0067\u0072\u0061p\u0068\u0020\u0073\u0074\u0072\u0075c\u0074\u0075\u0072\u0065\u0020\u0069\u006ef\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073e\u0074");
};return _bbcac ._dfec .GenerateKDict (),nil ;};

// Width returns the width of the Paragraph.
func (_aecag *Paragraph )Width ()float64 {if _aecag ._gcdc &&int (_aecag ._becad )> 0{return _aecag ._becad ;};return _aecag .getTextWidth ()/1000.0;};

// SetRowHeight sets the height for a specified row.
func (_dadfd *Table )SetRowHeight (row int ,h float64 )error {if row < 1||row > len (_dadfd ._gdbgf ){return _ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");};_dadfd ._gdbgf [row -1]=h ;return nil ;
};

// SetStyle sets paragraph style for all chunks.
func (_dbfe *StyledParagraph )SetStyle (style TextStyle ){_dbfe ._acfg =style ;for _ ,_cfeea :=range _dbfe ._dgaca {_cfeea .Style =style ;};};

// SetStructureType sets the structure type for the list.
func (_dfbb *List )SetStructureType (structureType _db .StructureType ){};

// SetFitMode sets the fit mode of the ellipse.
// NOTE: The fit mode is only applied if relative positioning is used.
func (_efaa *Ellipse )SetFitMode (fitMode FitMode ){_efaa ._dbcd =fitMode };type token struct{_bcdfe string ;_ecccc bool ;};

// SetMargins sets the margins TOC line.
func (_ceggfg *TOCLine )SetMargins (left ,right ,top ,bottom float64 ){_ceggfg ._dacdb =left ;_abfge :=&_ceggfg ._geffaf ._feeea ;_abfge .Left =_ceggfg ._dacdb +float64 (_ceggfg ._aeaad -1)*_ceggfg ._acade ;_abfge .Right =right ;_abfge .Top =top ;_abfge .Bottom =bottom ;
};func (_cbfd *templateProcessor )nodeLogDebug (_eefbd *templateNode ,_gdgf string ,_ebgb ...interface{}){_a .Log .Debug (_cbfd .getNodeErrorLocation (_eefbd ,_gdgf ,_ebgb ...));};

// CurvePolygon represents a curve polygon shape.
// Implements the Drawable interface and can be drawn on PDF using the Creator.
type CurvePolygon struct{_accc *_gc .CurvePolygon ;_feece float64 ;_gafd float64 ;_gbabf Color ;_ceada *_db .StructureTagInfo ;};

// IsAbsolute checks if the positioning is absolute.
func (_adfa Positioning )IsAbsolute ()bool {return _adfa ==PositionAbsolute };

// AddAnnotation adds an annotation on a TextChunk.
func (_edbcgf *TextChunk )AddAnnotation (annotation *_db .PdfAnnotation ){if annotation ==nil {return ;};_edbcgf ._aecg =append (_edbcgf ._aecg ,annotation );};func _dedcd (_cdfgc *templateProcessor ,_acagf *templateNode )(interface{},error ){return _cdfgc .parseLine (_acagf );
};

// AddLine appends a new line to the invoice line items table.
func (_fcbdc *Invoice )AddLine (values ...string )[]*InvoiceCell {_fbggf :=len (_fcbdc ._egfdb );var _bccec []*InvoiceCell ;for _ebbb ,_efgf :=range values {_fbca :=_fcbdc .newCell (_efgf ,_fcbdc ._cbdg );if _ebbb < _fbggf {_fbca .Alignment =_fcbdc ._egfdb [_ebbb ].Alignment ;
};_bccec =append (_bccec ,_fbca );};_fcbdc ._gbeaa =append (_fcbdc ._gbeaa ,_bccec );return _bccec ;};

// SetAddressHeadingStyle sets the style properties used to render the
// heading of the invoice address sections.
func (_acda *Invoice )SetAddressHeadingStyle (style TextStyle ){_acda ._adbac =style };

// PageFinalizeFunctionArgs holds the input arguments provided to the page
// finalize callback function which can be set using Creator.PageFinalize.
type PageFinalizeFunctionArgs struct{PageNum int ;PageWidth float64 ;PageHeight float64 ;TOCPages int ;TotalPages int ;};

// SetLineSeparator sets the separator for all new lines of the table of contents.
func (_ceegb *TOC )SetLineSeparator (separator string ){_ceegb ._fedgf =separator };

// Finalize renders all blocks to the creator pages. In addition, it takes care
// of adding headers and footers, as well as generating the front page,
// table of contents and outlines.
// Finalize is automatically called before writing the document out. Calling the
// method manually can be useful when adding external pages to the creator,
// using the AddPage method, as it renders all creator blocks to the added
// pages, without having to write the document out.
// NOTE: TOC and outlines are generated only if the AddTOC and AddOutlines
// fields of the creator are set to true (enabled by default). Furthermore, TOCs
// and outlines without content are skipped. TOC and outline content is
// added automatically when using the chapter component. TOCs and outlines can
// also be set externally, using the SetTOC and SetOutlineTree methods.
// Finalize should only be called once, after all draw calls have taken place,
// as it will return immediately if the creator instance has been finalized.
func (_dadf *Creator )Finalize ()error {if _dadf ._abf {return nil ;};_gbbf :=len (_dadf ._bcbe );_dcfbg :=0;if _dadf ._cga !=nil {_fgb :=*_dadf ;_dadf ._bcbe =nil ;_dadf ._abcd =nil ;_dadf .initContext ();_bbag :=FrontpageFunctionArgs {PageNum :1,TotalPages :_gbbf };
_dadf ._cga (_bbag );_dcfbg +=len (_dadf ._bcbe );_dadf ._bcbe =_fgb ._bcbe ;_dadf ._abcd =_fgb ._abcd ;};if _dadf .AddTOC {_dadf .initContext ();_dadf ._fcga .Page =_dcfbg +1;if _dadf .CustomTOC &&_dadf ._bba !=nil {_abg :=*_dadf ;_dadf ._bcbe =nil ;_dadf ._abcd =nil ;
if _ffdc :=_dadf ._bba (_dadf ._dbaf );_ffdc !=nil {return _ffdc ;};_dcfbg +=len (_dadf ._bcbe );_dadf ._bcbe =_abg ._bcbe ;_dadf ._abcd =_abg ._abcd ;}else {if _dadf ._bba !=nil {if _bfecb :=_dadf ._bba (_dadf ._dbaf );_bfecb !=nil {return _bfecb ;};};
_dfbg ,_ ,_dddd :=_dadf ._dbaf .GeneratePageBlocks (_dadf ._fcga );if _dddd !=nil {_a .Log .Debug ("\u0046\u0061i\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074\u0065\u0020\u0062\u006c\u006f\u0063\u006b\u0073: \u0025\u0076",_dddd );
return _dddd ;};_dcfbg +=len (_dfbg );};_eebe :=_dadf ._dbaf .Lines ();for _ ,_bagg :=range _eebe {_bgc ,_ddce :=_fbf .Atoi (_bagg .Page .Text );if _ddce !=nil {continue ;};_bagg .Page .Text =_fbf .Itoa (_bgc +_dcfbg );_bagg ._ageffa +=int64 (_dcfbg );
};};_efgd :=false ;var _cbce []*_db .PdfPage ;if _dadf ._cga !=nil {_degf :=*_dadf ;_dadf ._bcbe =nil ;_dadf ._abcd =nil ;_gefc :=FrontpageFunctionArgs {PageNum :1,TotalPages :_gbbf };_dadf ._cga (_gefc );_gbbf +=len (_dadf ._bcbe );_cbce =_dadf ._bcbe ;
_dadf ._bcbe =append (_dadf ._bcbe ,_degf ._bcbe ...);_dadf ._abcd =_degf ._abcd ;_efgd =true ;};var _ccad []*_db .PdfPage ;if _dadf .AddTOC {_dadf .initContext ();if _dadf .CustomTOC &&_dadf ._bba !=nil {_bgcd :=*_dadf ;_dadf ._bcbe =nil ;_dadf ._abcd =nil ;
if _edab :=_dadf ._bba (_dadf ._dbaf );_edab !=nil {_a .Log .Debug ("\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074i\u006e\u0067\u0020\u0054\u004f\u0043\u003a\u0020\u0025\u0076",_edab );return _edab ;};_ccad =_dadf ._bcbe ;_gbbf +=len (_ccad );
_dadf ._bcbe =_bgcd ._bcbe ;_dadf ._abcd =_bgcd ._abcd ;}else {if _dadf ._bba !=nil {if _adcf :=_dadf ._bba (_dadf ._dbaf );_adcf !=nil {_a .Log .Debug ("\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006e\u0065\u0072\u0061\u0074i\u006e\u0067\u0020\u0054\u004f\u0043\u003a\u0020\u0025\u0076",_adcf );
return _adcf ;};};_acc ,_ ,_ :=_dadf ._dbaf .GeneratePageBlocks (_dadf ._fcga );for _ ,_adddf :=range _acc {_adddf .SetPos (0,0);_gbbf ++;_dadfa :=_dadf .newPage ();_ccad =append (_ccad ,_dadfa );_dadf .setActivePage (_dadfa );_dadf .Draw (_adddf );};};
if _efgd {_fefe :=_cbce ;_ddgc :=_dadf ._bcbe [len (_cbce ):];_dadf ._bcbe =append ([]*_db .PdfPage {},_fefe ...);_dadf ._bcbe =append (_dadf ._bcbe ,_ccad ...);_dadf ._bcbe =append (_dadf ._bcbe ,_ddgc ...);}else {_dadf ._bcbe =append (_ccad ,_dadf ._bcbe ...);
};};if _dadf ._gcbc !=nil &&_dadf .AddOutlines {var _afcb func (_aead *_db .OutlineItem );_afcb =func (_fage *_db .OutlineItem ){_fage .Dest .Page +=int64 (_dcfbg );if _cggf :=int (_fage .Dest .Page );_cggf >=0&&_cggf < len (_dadf ._bcbe ){_fage .Dest .PageObj =_dadf ._bcbe [_cggf ].GetPageAsIndirectObject ();
}else {_a .Log .Debug ("\u0057\u0041R\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0070\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0065\u0072\u0020\u0066\u006f\u0072\u0020\u0070\u0061\u0067\u0065\u0020\u0025\u0064",_cggf );
};_fage .Dest .Y =_cg .RoundDefault (_dadf ._adde -_fage .Dest .Y );_fafe :=_fage .Items ();for _ ,_addb :=range _fafe {_afcb (_addb );};};_acad :=_dadf ._gcbc .Items ();for _ ,_cdcg :=range _acad {_afcb (_cdcg );};if _dadf .AddTOC {var _aga int ;if _efgd {_aga =len (_cbce );
};_gcc :=_db .NewOutlineDest (int64 (_aga ),0,_dadf ._adde );if _aga >=0&&_aga < len (_dadf ._bcbe ){_gcc .PageObj =_dadf ._bcbe [_aga ].GetPageAsIndirectObject ();}else {_a .Log .Debug ("\u0057\u0041R\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0070\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0065\u0072\u0020\u0066\u006f\u0072\u0020\u0070\u0061\u0067\u0065\u0020\u0025\u0064",_aga );
};_dadf ._gcbc .Insert (0,_db .NewOutlineItem ("\u0054\u0061\u0062\u006c\u0065\u0020\u006f\u0066\u0020\u0043\u006f\u006et\u0065\u006e\u0074\u0073",_gcc ));};};for _eeaf ,_cfgb :=range _dadf ._bcbe {_dadf .setActivePage (_cfgb );if _dadf ._aebd !=nil {_ddcf ,_fbfc ,_dgfef :=_cfgb .Size ();
if _dgfef !=nil {return _dgfef ;};_daeda :=PageFinalizeFunctionArgs {PageNum :_eeaf +1,PageWidth :_ddcf ,PageHeight :_fbfc ,TOCPages :len (_ccad ),TotalPages :_gbbf };if _gecg :=_dadf ._aebd (_daeda );_gecg !=nil {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0070\u0061\u0067\u0065\u0020\u0066\u0069\u006e\u0061\u006c\u0069\u007a\u0065 \u0063\u0061\u006c\u006c\u0062\u0061\u0063k\u003a\u0020\u0025\u0076",_gecg );
return _gecg ;};};if _dadf ._cgeg !=nil {_dgac :=NewBlock (_dadf ._cfgg ,_dadf ._gecf .Top );_baae :=HeaderFunctionArgs {PageNum :_eeaf +1,TotalPages :_gbbf };_dadf ._cgeg (_dgac ,_baae );_dgac .SetPos (0,0);if _aabaa :=_dadf .Draw (_dgac );_aabaa !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0064\u0072\u0061\u0077\u0069n\u0067 \u0068e\u0061\u0064\u0065\u0072\u003a\u0020\u0025v",_aabaa );
return _aabaa ;};};if _dadf ._dfe !=nil {_ebgf :=NewBlock (_dadf ._cfgg ,_dadf ._gecf .Bottom );_bgd :=FooterFunctionArgs {PageNum :_eeaf +1,TotalPages :_gbbf };_dadf ._dfe (_ebgf ,_bgd );_ebgf .SetPos (0,_dadf ._adde -_ebgf ._dbe );if _fggd :=_dadf .Draw (_ebgf );
_fggd !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0064\u0072\u0061\u0077\u0069n\u0067 \u0066o\u006f\u0074\u0065\u0072\u003a\u0020\u0025v",_fggd );return _fggd ;};};_bdee ,_eaggc :=_dadf ._fbgd [_cfgb ];if _gfdc ,_dedb :=_dadf ._gade [_cfgb ];
_dedb {if _eaggc {_bdee .transformBlock (_gfdc );};if _aeeac :=_gfdc .drawToPage (_cfgb );_aeeac !=nil {_a .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0064\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0070\u0061\u0067\u0065\u0020%\u0064\u0020\u0062\u006c\u006f\u0063\u006bs\u003a\u0020\u0025\u0076",_eeaf +1,_aeeac );
return _aeeac ;};};if _eaggc {if _dce :=_bdee .transformPage (_cfgb );_dce !=nil {_a .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0074\u0072\u0061\u006e\u0073f\u006f\u0072\u006d\u0020\u0070\u0061\u0067\u0065\u003a\u0020%\u0076",_dce );
return _dce ;};};};_dadf ._abf =true ;return nil ;};

// SetBorderWidth sets the border width.
func (_feac *CurvePolygon )SetBorderWidth (borderWidth float64 ){_feac ._accc .BorderWidth =borderWidth };var PPI float64 =72;

// The Image type is used to draw an image onto PDF.
type Image struct{_agge *_db .XObjectImage ;_fbebf *_db .Image ;_fdfeb string ;_edebf float64 ;_bbg ,_fcbd float64 ;_gddb ,_gaga float64 ;_dfbc Positioning ;_aagg HorizontalAlignment ;_fabc float64 ;_dff float64 ;_efee float64 ;_efde Margins ;_aeda ,_bbfeb float64 ;
_fagc _dd .StreamEncoder ;_dddb FitMode ;_gbed bool ;_fecd *_db .StructureTagInfo ;};

// SetColorTop sets border color for top.
func (_egce *border )SetColorTop (col Color ){_egce ._geaa =col };

// BorderWidth returns the border width of the ellipse.
func (_bgad *Ellipse )BorderWidth ()float64 {return _bgad ._daa };func (_gggc *TOCLine )prepareParagraph (_eccda *StyledParagraph ,_bgdbf DrawContext ){_dbbae :=_gggc .Title .Text ;if _gggc .Number .Text !=""{_dbbae ="\u0020"+_dbbae ;};_dbbae +="\u0020";
_addcc :=_gggc .Page .Text ;if _addcc !=""{_addcc ="\u0020"+_addcc ;};_eefea :=[]*_db .PdfAnnotation {};_faagb :=_gggc .getLineLink ();if _faagb !=nil {_eefea =append (_eefea ,_faagb );};_eccda ._dgaca =[]*TextChunk {{Text :_gggc .Number .Text ,Style :_gggc .Number .Style ,_aecg :_eefea },{Text :_dbbae ,Style :_gggc .Title .Style ,_aecg :_eefea },{Text :_addcc ,Style :_gggc .Page .Style ,_aecg :_eefea }};
_eccda .wrapText ();_dedf :=len (_eccda ._cbba );if _dedf ==0{return ;};_dgffa :=_bgdbf .Width *1000-_eccda .getTextLineWidth (_eccda ._cbba [_dedf -1]);_egcc :=_eccda .getTextLineWidth ([]*TextChunk {&_gggc .Separator });_bdaf :=int (_dgffa /_egcc );_bfde :=_fc .Repeat (_gggc .Separator .Text ,_bdaf );
_dgfb :=_gggc .Separator .Style ;_acccf :=_eccda .Insert (2,_bfde );_acccf .Style =_dgfb ;_acccf .SetAnnotation (_gggc .getLineLink ());_dgffa =_dgffa -float64 (_bdaf )*_egcc ;if _dgffa > 500{_eeabb ,_fgedf :=_dgfb .Font .GetRuneMetrics (' ');if _fgedf &&_dgffa > _eeabb .Wx {_fcab :=int (_dgffa /_eeabb .Wx );
if _fcab > 0{_fbgaf :=_dgfb ;_fbgaf .FontSize =1;_acccf =_eccda .Insert (2,_fc .Repeat ("\u0020",_fcab ));_acccf .Style =_fbgaf ;_acccf .SetAnnotation (_gggc .getLineLink ());};};};};

// GridRow defines a row which can contain cells.
type GridRow struct{_fgfe []*GridCell ;_deege float64 ;_fega float64 ;_bacb int ;_dfdbg *Grid ;_adcb *_db .StructureTagInfo ;};

// Fit fits the chunk into the specified bounding box, cropping off the
// remainder in a new chunk, if it exceeds the specified dimensions.
// NOTE: The method assumes a line height of 1.0. In order to account for other
// line height values, the passed in height must be divided by the line height:
// height = height / lineHeight
func (_aecagc *TextChunk )Fit (width ,height float64 )(*TextChunk ,error ){_abace ,_gfgde :=_aecagc .Wrap (width );if _gfgde !=nil {return nil ,_gfgde ;};_cefc :=int (height /_aecagc .Style .FontSize );if _cefc >=len (_abace ){return nil ,nil ;};_eedfg :="\u000a";
_aecagc .Text =_fc .Replace (_fc .Join (_abace [:_cefc ],"\u0020"),_eedfg +"\u0020",_eedfg ,-1);_cegfa :=_fc .Replace (_fc .Join (_abace [_cefc :],"\u0020"),_eedfg +"\u0020",_eedfg ,-1);return NewTextChunk (_cegfa ,_aecagc .Style ),nil ;};func _acfa (_cbcg Color ,_dgcd float64 )*ColorPoint {return &ColorPoint {_efgba :_cbcg ,_acbfb :_dgcd }};


// GetMargins returns the margins of the TOC line: left, right, top, bottom.
func (_afgff *TOCLine )GetMargins ()(float64 ,float64 ,float64 ,float64 ){_fcdcc :=&_afgff ._geffaf ._feeea ;return _afgff ._dacdb ,_fcdcc .Right ,_fcdcc .Top ,_fcdcc .Bottom ;};

// SetMargins sets the Paragraph's margins.
func (_bfagg *Paragraph )SetMargins (left ,right ,top ,bottom float64 ){_bfagg ._fbgdf .Left =left ;_bfagg ._fbgdf .Right =right ;_bfagg ._fbgdf .Top =top ;_bfagg ._fbgdf .Bottom =bottom ;};

// TOC represents a table of contents component.
// It consists of a paragraph heading and a collection of
// table of contents lines.
// The representation of a table of contents line is as follows:
//
//	[number] [title]      [separator] [page]
//
// e.g.: Chapter1 Introduction ........... 1
type TOC struct{_bcbeb *StyledParagraph ;_bebaa []*TOCLine ;_fabcf TextStyle ;_cdaad TextStyle ;_fbgcd TextStyle ;_dfbae TextStyle ;_fedgf string ;_bfffc float64 ;_dfcfb Margins ;_bdcca Positioning ;_dgbc TextStyle ;_dace bool ;};

// AddInternalLinkWithTag adds a new internal link to the paragraph with proper tagging for accessibility.
// The text parameter represents the text that is displayed.
// The user is taken to the specified page, at the specified x and y coordinates.
// Position 0, 0 is at the top left of the page.
// The zoom of the destination page is controlled with the zoom parameter (0 keeps current zoom).
// The options parameter contains accessibility properties like tooltip, altText, and mcid.
func (_fbcd *StyledParagraph )AddInternalLinkWithTag (text string ,page int64 ,x ,y ,zoom float64 ,options LinkTagOptions )(*TextChunk ,*_db .KDict ){_daeb :=_bagfe (page -1,x ,y ,zoom ,options .Tooltip );_gbag ,_ceac ,_dbcgd :=_fbcd .createAccessibleLinkChunk (text ,_daeb ,options );
if _dbcgd !=nil {_a .Log .Error ("F\u0061\u0069\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u0069\u006e\u0074\u0065\u0072n\u0061\u006c\u0020\u006c\u0069\u006e\u006b\u0020\u0077\u0069th\u0020\u0074\u0061g\u003a \u0025\u0076",_dbcgd );
return nil ,nil ;};return _gbag ,_ceac ;};

// NewTextChunk returns a new text chunk instance.
func NewTextChunk (text string ,style TextStyle )*TextChunk {return &TextChunk {Text :text ,Style :style ,VerticalAlignment :TextVerticalAlignmentBaseline ,_aecg :[]*_db .PdfAnnotation {},_eeddd :[]bool {}};};

// SetMargins sets the margins of the graphic svg component.
func (_ggagf *GraphicSVG )SetMargins (left ,right ,top ,bottom float64 ){_ggagf ._edeb .Left =left ;_ggagf ._edeb .Right =right ;_ggagf ._edeb .Top =top ;_ggagf ._edeb .Bottom =bottom ;};

// SetStructureType sets the structure type for the paragraph.
func (_feaaf *StyledParagraph )SetStructureType (structureType _db .StructureType ){if _feaaf ._gbfc ==nil {_feaaf ._gbfc =_db .NewStructureTagInfo ();};_feaaf ._gbfc .StructureType =structureType ;};

// This method is not supported by Division component and exists solely to satisfy the Drawable interface.
func (_egdc *Division )SetMarkedContentID (id int64 ){};

// Width returns the width of the Paragraph.
func (_fbbgf *StyledParagraph )Width ()float64 {if _fbbgf ._gfce &&int (_fbbgf ._gfba )> 0{return _fbbgf ._gfba ;};return _fbbgf .getTextWidth ()/1000.0;};

// SetRowPosition sets cell row position.
func (_dcggf *TableCell )SetRowPosition (row int ){_dcggf ._efage =row };

// PageSize represents the page size as a 2 element array representing the width and height in PDF document units (points).
type PageSize [2]float64 ;

// GetMargins returns the Block's margins: left, right, top, bottom.
func (_bdb *Block )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _bdb ._fg .Left ,_bdb ._fg .Right ,_bdb ._fg .Top ,_bdb ._fg .Bottom ;};

// GetMargins returns the left, right, top, bottom Margins.
func (_cfgad *Table )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _cfgad ._acbac .Left ,_cfgad ._acbac .Right ,_cfgad ._acbac .Top ,_cfgad ._acbac .Bottom ;};

// FitMode defines resizing options of an object inside a container.
type FitMode int ;func (_fbcb *Table )closeTag (_bgdc *Block ){_edced :=_eg .NewContentCreator ();_edced .Add_EMC ();_bgdc .addContents (_edced .Operations ());};

// SetWidth set the Image's document width to specified w. This does not change the raw image data, i.e.
// no actual scaling of data is performed. That is handled by the PDF viewer.
func (_bccg *Image )SetWidth (w float64 ){_bccg ._bbg =w };

// SetTextOverflow controls the behavior of paragraph text which
// does not fit in the available space.
func (_daab *StyledParagraph )SetTextOverflow (textOverflow TextOverflow ){_daab ._gbafb =textOverflow };func (_cafcec *StyledParagraph )wrapText ()error {return _cafcec .wrapChunks (true )};

// SetHeading sets the text and the style of the heading of the TOC component.
func (_acdcb *TOC )SetHeading (text string ,style TextStyle ){_ecgce :=_acdcb .Heading ();_ecgce .Reset ();_gafgg :=_ecgce .Append (text );_gafgg .Style =style ;};

// GetIndent get the cell's left indent.
func (_dbcc *GridCell )GetIndent ()float64 {return _dbcc ._ddca };func (_ebfbd *Paragraph )getTextMetrics ()(_cdbdc ,_efba ,_bffb float64 ){_gaea :=_dfdcc (_ebfbd ._aad ,_ebfbd ._febee );if _gaea ._eggd > _cdbdc {_cdbdc =_gaea ._eggd ;};if _gaea ._agcc < _bffb {_bffb =_gaea ._agcc ;
};if _egbf :=_ebfbd ._febee ;_egbf > _efba {_efba =_egbf ;};return _cdbdc ,_efba ,_bffb ;};

// SetFillColor sets the fill color.
func (_fbeg *Polygon )SetFillColor (color Color ){_fbeg ._fbef =color ;_fbeg ._addde .FillColor =_cdfe (color );};

// SetFontColor sets the font color for the paragraph.
func (_fbce *StyledParagraph )SetFontColor (color Color ){_fbce ._acfg .Color =color ;for _ ,_dafbg :=range _fbce ._dgaca {_dafbg .Style .Color =color ;};};

// SetBorderRadius sets the radius of the rectangle corners.
func (_bgbf *Rectangle )SetBorderRadius (topLeft ,topRight ,bottomLeft ,bottomRight float64 ){_bgbf ._edbfa =topLeft ;_bgbf ._efgaa =topRight ;_bgbf ._bbef =bottomLeft ;_bgbf ._fcdc =bottomRight ;};func (_cacd *Invoice )generateInformationBlocks (_afef DrawContext )([]*Block ,DrawContext ,error ){_edgce :=_gcefe (_cacd ._bcag );
_edgce .SetMargins (0,0,0,20);_eebd :=_cacd .drawAddress (_cacd ._eeddc );_eebd =append (_eebd ,_edgce );_eebd =append (_eebd ,_cacd .drawAddress (_cacd ._egde )...);_bcagc :=_dfge ();for _ ,_bbce :=range _eebd {_bcagc .Add (_bbce );};_dfbgf :=_cacd .drawInformation ();
_dafe :=_cgdd (2);_dafe .SetMargins (0,0,25,0);_eebdg :=_dafe .NewCell ();_eebdg .SetIndent (0);_eebdg .SetContent (_bcagc );_eebdg =_dafe .NewCell ();_eebdg .SetContent (_dfbgf );return _dafe .GeneratePageBlocks (_afef );};func (_bggef *templateProcessor )loadImageFromSrc (_fcbg string )(*Image ,error ){if _fcbg ==""{_a .Log .Error ("\u0049\u006d\u0061\u0067\u0065\u0020\u0060\u0073\u0072\u0063\u0060\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0063\u0061n\u006e\u006f\u0074\u0020\u0062e\u0020\u0065m\u0070\u0074\u0079\u002e");
return nil ,_cbabd ;};_befbf :=_fc .Split (_fcbg ,"\u002c");for _ ,_dgcec :=range _befbf {_dgcec =_fc .TrimSpace (_dgcec );if _dgcec ==""{continue ;};_ddcdf ,_bgdgb :=_bggef ._bgggf .ImageMap [_dgcec ];if _bgdgb {return _cefg (_ddcdf );};if _abbgg :=_bggef .parseAttrPropList (_dgcec );
len (_abbgg )> 0{if _acaag ,_ffdbg :=_abbgg ["\u0070\u0061\u0074\u0068"];_ffdbg {if _fcfab ,_eecga :=_egcgc (_acaag );_eecga !=nil {_a .Log .Debug ("\u0043\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020l\u006f\u0061\u0064\u0020\u0069\u006d\u0061g\u0065\u0020\u0060\u0025\u0073\u0060\u003a\u0020\u0025\u0076\u002e",_acaag ,_eecga );
}else {return _fcfab ,nil ;};};};};_a .Log .Error ("\u0043\u006ful\u0064\u0020\u006eo\u0074\u0020\u0066\u0069nd \u0069ma\u0067\u0065\u0020\u0072\u0065\u0073\u006fur\u0063\u0065\u003a\u0020\u0060\u0025\u0073`\u002e",_fcbg );return nil ,_cbabd ;};

// This method is not supported by PageBreak component and exists solely to satisfy the Drawable interface.
func (_fgeg *PageBreak )SetMarkedContentID (id int64 ){};

// This method is not supported by Border component and exists solely to satisfy the Drawable interface.
func (_dfg *border )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };

// SetWidth sets the the Paragraph width. This is essentially the wrapping width,
// i.e. the width the text can extend to prior to wrapping over to next line.
func (_dfdff *StyledParagraph )SetWidth (width float64 ){_dfdff ._gfba =width ;_dfdff .wrapText ()};

// GeneratePageBlocks draws the composite Bezier curve on a new block
// representing the page. Implements the Drawable interface.
func (_gbga *PolyBezierCurve )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_gagcb :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_ggbdc ,_bgde :=_gagcb .setOpacity (_gbga ._dfdf ,_gbga ._efcf );if _bgde !=nil {return nil ,ctx ,_bgde ;
};_bebea :=_gbga ._gefa ;_bebea .FillEnabled =_bebea .FillColor !=nil ;var (_bfagb =ctx .PageHeight ;_eedfd =_bebea .Curves ;_addf =make ([]_gc .CubicBezierCurve ,0,len (_bebea .Curves )););_gdbab :=_db .PdfRectangle {};for _gdae :=range _bebea .Curves {_dabdd :=_eedfd [_gdae ];
_dabdd .P0 .Y =_bfagb -_dabdd .P0 .Y ;_dabdd .P1 .Y =_bfagb -_dabdd .P1 .Y ;_dabdd .P2 .Y =_bfagb -_dabdd .P2 .Y ;_dabdd .P3 .Y =_bfagb -_dabdd .P3 .Y ;_addf =append (_addf ,_dabdd );_cdbbb :=_dabdd .GetBounds ();if _gdae ==0{_gdbab =_cdbbb ;}else {_gdbab .Llx =_fa .Min (_gdbab .Llx ,_cdbbb .Llx );
_gdbab .Lly =_fa .Min (_gdbab .Lly ,_cdbbb .Lly );_gdbab .Urx =_fa .Max (_gdbab .Urx ,_cdbbb .Urx );_gdbab .Ury =_fa .Max (_gdbab .Ury ,_cdbbb .Ury );};};_bebea .Curves =_addf ;defer func (){_bebea .Curves =_eedfd }();if _bebea .FillEnabled {_fdgbc :=_gfae (_gagcb ,_gbga ._gefa .FillColor ,_gbga ._eagb ,func ()Rectangle {return Rectangle {_eeadac :_gdbab .Llx ,_abegd :_gdbab .Lly ,_cdcgg :_gdbab .Width (),_cfedb :_gdbab .Height ()};
});if _fdgbc !=nil {return nil ,ctx ,_fdgbc ;};};_abec ,_ ,_bgde :=_bebea .MarkedDraw (_ggbdc ,_gbga ._ceefc );if _bgde !=nil {return nil ,ctx ,_bgde ;};if _bgde =_gagcb .addContentsByString (string (_abec ));_bgde !=nil {return nil ,ctx ,_bgde ;};return []*Block {_gagcb },ctx ,nil ;
};

// Width returns Image's document width.
func (_dead *Image )Width ()float64 {return _dead ._bbg };func (_edda *Image )makeXObject ()error {_aeefg ,_effb :=_db .NewXObjectImageFromImageLazy (_edda ._fbebf ,nil ,_edda ._fagc ,_edda ._gbed );if _effb !=nil {_a .Log .Error ("\u0046\u0061\u0069le\u0064\u0020\u0074\u006f\u0020\u0063\u0072\u0065\u0061t\u0065 \u0078o\u0062j\u0065\u0063\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_effb );
return _effb ;};_edda ._agge =_aeefg ;return nil ;};

// NewList creates a new list.
func (_ecc *Creator )NewList ()*List {return _dfafd (_ecc .NewTextStyle ())};

// Height returns the Block's height.
func (_cfc *Block )Height ()float64 {return _cfc ._dbe };

// NewCell makes a new cell and inserts it into the table at the current position.
func (_ggcc *Table )NewCell ()*TableCell {return _ggcc .MultiCell (1,1)};

// SetLineWidth sets the line width.
func (_cadeb *Line )SetLineWidth (width float64 ){_cadeb ._gbgbb =width };

// TextRenderingMode determines whether showing text shall cause glyph
// outlines to be stroked, filled, used as a clipping boundary, or some
// combination of the three.
// See section 9.3 "Text State Parameters and Operators" and
// Table 106 (pp. 254-255 PDF32000_2008).
type TextRenderingMode int ;

// SetLevel sets the indentation level of the TOC line.
func (_fgbad *TOCLine )SetLevel (level uint ){_fgbad ._aeaad =level ;_fgbad ._geffaf ._feeea .Left =_fgbad ._dacdb +float64 (_fgbad ._aeaad -1)*_fgbad ._acade ;};var (PageSizeA3 =PageSize {297*PPMM ,420*PPMM };PageSizeA4 =PageSize {210*PPMM ,297*PPMM };
PageSizeA5 =PageSize {148*PPMM ,210*PPMM };PageSizeLetter =PageSize {8.5*PPI ,11*PPI };PageSizeLegal =PageSize {8.5*PPI ,14*PPI };);

// Text sets the text content of the Paragraph.
func (_ceaae *Paragraph )Text ()string {return _ceaae ._cegc };func (_adf *Block )drawToPage (_cb *_db .PdfPage )error {_bffc :=&_eg .ContentStreamOperations {};if _cb .Resources ==nil {_cb .Resources =_db .NewPdfPageResources ();};_bde :=_bdf (_bffc ,_cb .Resources ,_adf ._fd ,_adf ._faa );
if _bde !=nil {return _bde ;};if _bde =_eacd (_adf ._faa ,_cb .Resources );_bde !=nil {return _bde ;};if _bde =_cb .AppendContentBytes (_bffc .Bytes (),true );_bde !=nil {return _bde ;};for _ ,_ae :=range _adf ._ecd {_cb .AddAnnotation (_ae );};return nil ;
};

// SetMargins sets the margins of the component. The margins are applied
// around the division.
func (_acbc *Division )SetMargins (left ,right ,top ,bottom float64 ){_acbc ._fccaf .Left =left ;_acbc ._fccaf .Right =right ;_acbc ._fccaf .Top =top ;_acbc ._fccaf .Bottom =bottom ;};

// Notes returns the notes section of the invoice as a title-content pair.
func (_cbfba *Invoice )Notes ()(string ,string ){return _cbfba ._egfgg [0],_cbfba ._egfgg [1]};

// UnsupportedRuneError is an error that occurs when there is unsupported glyph being used.
type UnsupportedRuneError struct{Message string ;Rune rune ;};

// Horizontal returns total horizontal (left + right) margin.
func (_fdbb *Margins )Horizontal ()float64 {return _fdbb .Left +_fdbb .Right };

// SetPdfWriterAccessFunc sets a PdfWriter access function/hook.
// Exposes the PdfWriter just prior to writing the PDF.  Can be used to encrypt the output PDF, etc.
//
// Example of encrypting with a user/owner password "password"
// Prior to calling c.WriteFile():
//
//	c.SetPdfWriterAccessFunc(func(w *model.PdfWriter) error {
//		userPass := []byte("password")
//		ownerPass := []byte("password")
//		err := w.Encrypt(userPass, ownerPass, nil)
//		return err
//	})
func (_baggg *Creator )SetPdfWriterAccessFunc (pdfWriterAccessFunc func (_febg *_db .PdfWriter )error ){_baggg ._cecda =pdfWriterAccessFunc ;};

// SetWidth sets the width of the ellipse.
func (_ddbe *Ellipse )SetWidth (width float64 ){_ddbe ._fdgf =width };

// GetOptimizer returns current PDF optimizer.
func (_gcbf *Creator )GetOptimizer ()_db .Optimizer {return _gcbf ._dfaf };func _ccdff (_cbfc *Block ,_fecdd *Paragraph ,_gbfa DrawContext )(DrawContext ,error ){_egebg :=1;_bacg :=_dd .PdfObjectName ("\u0046\u006f\u006e\u0074"+_fbf .Itoa (_egebg ));for _cbfc ._faa .HasFontByName (_bacg ){_egebg ++;
_bacg =_dd .PdfObjectName ("\u0046\u006f\u006e\u0074"+_fbf .Itoa (_egebg ));};_ecbge :=_cbfc ._faa .SetFontByName (_bacg ,_fecdd ._aad .ToPdfObject ());if _ecbge !=nil {return _gbfa ,_ecbge ;};_fecdd .wrapText ();_fecfe :=_eg .NewContentCreator ();_fecfe .Add_q ();
_ffcf :=_cg .RoundDefault (_gbfa .PageHeight -_gbfa .Y -_fecdd ._febee *_fecdd ._gafdf );_fecfe .Translate (_gbfa .X ,_ffcf );if _fecdd ._cagf !=0{_fecfe .RotateDeg (_fecdd ._cagf );};_bgdfc :=_cdfe (_fecdd ._ffgfg );_ecbge =_gfae (_cbfc ,_bgdfc ,_fecdd ._ffgfg ,func ()Rectangle {return Rectangle {_eeadac :_gbfa .X ,_abegd :_ffcf ,_cdcgg :_fecdd .getMaxLineWidth ()/1000.0,_cfedb :_fecdd .Height ()};
});if _ecbge !=nil {return _gbfa ,_ecbge ;};_fecfe .Add_BT ();_gdebb :=map[string ]_dd .PdfObject {};if _fecdd ._dfec !=nil {_gdebb ["\u004d\u0043\u0049\u0044"]=_dd .MakeInteger (_fecdd ._dfec .Mcid );};if _fecdd ._fedb !=""{_gdebb ["\u004c\u0061\u006e\u0067"]=_dd .MakeString (_fecdd ._fedb );
};if len (_gdebb )> 0{_fecfe .Add_BDC (*_dd .MakeName (string (_fecdd ._dfec .StructureType )),_gdebb );};_fecfe .SetNonStrokingColor (_bgdfc ).Add_Tf (_bacg ,_fecdd ._febee ).Add_TL (_fecdd ._febee *_fecdd ._gafdf );for _ccdae ,_adbc :=range _fecdd ._gbde {if _ccdae !=0{_fecfe .Add_Tstar ();
};_bagccf :=[]rune (_adbc );_aecd :=0.0;_bfebc :=0;for _cbfaa ,_gfbf :=range _bagccf {if _gfbf ==' '{_bfebc ++;continue ;};if _gfbf =='\u000A'{continue ;};_eddec ,_bccfc :=_fecdd ._aad .GetRuneMetrics (_gfbf );if !_bccfc {_a .Log .Debug ("\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0072\u0075\u006e\u0065\u0020\u0069=\u0025\u0064\u0020\u0072\u0075\u006e\u0065=\u0030\u0078\u0025\u0030\u0034\u0078\u003d\u0025\u0063\u0020\u0069n\u0020\u0066\u006f\u006e\u0074\u0020\u0025\u0073\u0020\u0025\u0073",_cbfaa ,_gfbf ,_gfbf ,_fecdd ._aad .BaseFont (),_fecdd ._aad .Subtype ());
return _gbfa ,_ee .New ("\u0075\u006e\u0073\u0075pp\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0065\u0078\u0074\u0020\u0067\u006c\u0079p\u0068");};_aecd +=_fecdd ._febee *_eddec .Wx ;};var _dfcba []_dd .PdfObject ;_bcgad ,_dbdfg :=_fecdd ._aad .GetRuneMetrics (' ');
if !_dbdfg {return _gbfa ,_ee .New ("\u0074\u0068e \u0066\u006f\u006et\u0020\u0064\u006f\u0065s n\u006ft \u0068\u0061\u0076\u0065\u0020\u0061\u0020sp\u0061\u0063\u0065\u0020\u0067\u006c\u0079p\u0068");};_fbbb :=_bcgad .Wx ;switch _fecdd ._aegda {case TextAlignmentJustify :if _bfebc > 0&&_ccdae < len (_fecdd ._gbde )-1{_fbbb =(_fecdd ._becad *1000.0-_aecd )/float64 (_bfebc )/_fecdd ._febee ;
};case TextAlignmentCenter :_agaeg :=_aecd +float64 (_bfebc )*_fbbb *_fecdd ._febee ;_aeefgg :=_cg .RoundDefault ((_fecdd ._becad *1000.0-_agaeg )/2/_fecdd ._febee );_dfcba =append (_dfcba ,_dd .MakeFloat (-_aeefgg ));case TextAlignmentRight :_gadeb :=_aecd +float64 (_bfebc )*_fbbb *_fecdd ._febee ;
_fedf :=_cg .RoundDefault ((_fecdd ._becad *1000.0-_gadeb )/_fecdd ._febee );_dfcba =append (_dfcba ,_dd .MakeFloat (-_fedf ));};_egdbb :=_fecdd ._aad .Encoder ();var _gaffg []byte ;for _ ,_bbbf :=range _bagccf {if _bbbf =='\u000A'{continue ;};if _bbbf ==' '{if len (_gaffg )> 0{_dfcba =append (_dfcba ,_dd .MakeStringFromBytes (_gaffg ));
_gaffg =nil ;};_dfcba =append (_dfcba ,_dd .MakeFloat (-_fbbb ));}else {if _ ,_bfce :=_egdbb .RuneToCharcode (_bbbf );!_bfce {_ecbge =UnsupportedRuneError {Message :_g .Sprintf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u0072\u0075\u006e\u0065 \u0069\u006e\u0020\u0074\u0065\u0078\u0074\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u003a\u0020\u0025\u0023\u0078\u0020\u0028\u0025\u0063\u0029",_bbbf ,_bbbf ),Rune :_bbbf };
_gbfa ._aeag =append (_gbfa ._aeag ,_ecbge );_a .Log .Debug (_ecbge .Error ());if _gbfa ._bcbgg <=0{continue ;};_bbbf =_gbfa ._bcbgg ;};_gaffg =append (_gaffg ,_egdbb .Encode (string (_bbbf ))...);};};if len (_gaffg )> 0{_dfcba =append (_dfcba ,_dd .MakeStringFromBytes (_gaffg ));
};_fecfe .Add_TJ (_dfcba ...);};if len (_gdebb )> 0{_fecfe .Add_EMC ();};_fecfe .Add_ET ();_fecfe .Add_Q ();_bgdfca :=_fecfe .Operations ();_bgdfca .WrapIfNeeded ();_cbfc .addWrappedContents (_bgdfca );if _fecdd ._aecc .IsRelative (){_aeadd :=_fecdd .Height ();
_gbfa .Y =_cg .RoundDefault (_gbfa .Y +_aeadd );_gbfa .Height =_cg .RoundDefault (_gbfa .Height -_aeadd );if _gbfa .Inline {_gbfa .X =_cg .RoundDefault (_gbfa .X +_fecdd .Width ()+_fecdd ._fbgdf .Right );};};return _gbfa ,nil ;};

// NewMultiCell makes a new cell with given colspan and rowspan and inserts it into the row at the current position.
func (_aaebg *GridRow )NewMultiCell (colspan ,rowspan int )(*GridCell ,error ){_dfae :=&GridCell {_gcfa :len (_aaebg ._fgfe ),_fcfg :_aaebg ._bacb ,_caffg :colspan ,_fcbe :rowspan };_dfae ._fcfg =_aaebg ._bacb ;_dfae ._gcfa =0;for _ ,_gbcf :=range _aaebg ._fgfe {_dfae ._gcfa +=_gbcf ._caffg ;
};for _beca ,_bcdfb :=range _aaebg ._dfdbg ._bgcb {if _beca ==_aaebg ._bacb +1{break ;};for _ ,_dbede :=range _bcdfb ._fgfe {if _dbede ._fcfg +_dbede ._fcbe > _dfae ._fcfg {if _dbede ._gcfa +_dbede ._caffg > _dfae ._gcfa {_dfae ._gcfa =_dbede ._gcfa +_dbede ._caffg ;
};};};};if _dfae ._gcfa >=_aaebg ._dfdbg ._cecb {return nil ,_ee .New ("\u0063\u0061n'\u0074\u0020\u0061d\u0064\u0020\u0061\u006ey m\u006fre\u0020\u0063\u0065\u006c\u006c\u0073\u0020to\u0020\u0074\u0068\u0069\u0073\u0020\u0072o\u0077");};_dfae ._ddca =5;
_dfae ._bddf =CellBorderStyleNone ;_dfae ._ccc =_gc .LineStyleSolid ;_dfae ._efab =CellHorizontalAlignmentLeft ;_dfae ._fbbd =CellVerticalAlignmentTop ;_dfae ._gffc =0;_dfae ._acbe =0;_dfae ._feee =0;_dfae ._bfcc =0;_gfee :=ColorBlack ;_dfae ._bdcg =_gfee ;
_dfae ._cbcd =_gfee ;_dfae ._gegc =_gfee ;_dfae ._edee =_gfee ;_dfae ._ffgf =1.0;_aaebg ._fgfe =append (_aaebg ._fgfe ,_dfae );return _dfae ,nil ;};

// SetTextAlignment sets the horizontal alignment of the text within the space provided.
func (_efdfa *StyledParagraph )SetTextAlignment (align TextAlignment ){_efdfa ._deace =align };

// AddAnnotation adds an annotation to the current block.
// The annotation will be added to the page the block will be rendered on.
func (_eec *Block )AddAnnotation (annotation *_db .PdfAnnotation ){for _ ,_ad :=range _eec ._ecd {if _ad ==annotation {return ;};};_eec ._ecd =append (_eec ._ecd ,annotation );};

// ColorRGBFromArithmetic creates a Color from arithmetic color values (0-1).
// Example:
//
//	green := ColorRGBFromArithmetic(0.0, 1.0, 0.0)
func ColorRGBFromArithmetic (r ,g ,b float64 )Color {return rgbColor {_dbbe :_fa .Max (_fa .Min (r ,1.0),0.0),_bggf :_fa .Max (_fa .Min (g ,1.0),0.0),_aec :_fa .Max (_fa .Min (b ,1.0),0.0)};};func (_eafd *Creator )newPage ()*_db .PdfPage {_beb :=_db .NewPdfPage ();
_cdff :=_eafd ._bggg [0];_acb :=_eafd ._bggg [1];_fce :=_db .PdfRectangle {Llx :0,Lly :0,Urx :_cdff ,Ury :_acb };_beb .MediaBox =&_fce ;_eafd ._cfgg =_cdff ;_eafd ._adde =_acb ;_eafd .initContext ();return _beb ;};

// FrontpageFunctionArgs holds the input arguments to a front page drawing function.
// It is designed as a struct, so additional parameters can be added in the future with backwards
// compatibility.
type FrontpageFunctionArgs struct{PageNum int ;TotalPages int ;};

// InvoiceAddress contains contact information that can be displayed
// in an invoice. It is used for the seller and buyer information in the
// invoice template.
type InvoiceAddress struct{Heading string ;Name string ;Street string ;Street2 string ;Zip string ;City string ;State string ;Country string ;Phone string ;Email string ;

// Separator defines the separator between different address components,
// such as the city, state and zip code. It defaults to ", " when the
// field is an empty string.
Separator string ;

// If enabled, the Phone field label (`Phone: `) is not displayed.
HidePhoneLabel bool ;

// If enabled, the Email field label (`Email: `) is not displayed.
HideEmailLabel bool ;};

// GetMargins returns the Image's margins: left, right, top, bottom.
func (_fdcb *Image )GetMargins ()(float64 ,float64 ,float64 ,float64 ){return _fdcb ._efde .Left ,_fdcb ._efde .Right ,_fdcb ._efde .Top ,_fdcb ._efde .Bottom ;};

// SetTitleStyle sets the style properties of the invoice title.
func (_cdbb *Invoice )SetTitleStyle (style TextStyle ){_cdbb ._gecfa =style };

// SetIndent sets the left offset of the list when nested into another list.
func (_deadb *List )SetIndent (indent float64 ){_deadb ._gagc =indent ;_deadb ._dgbd =false };

// NoteHeadingStyle returns the style properties used to render the heading of
// the invoice note sections.
func (_cecag *Invoice )NoteHeadingStyle ()TextStyle {return _cecag ._bgcc };

// GeneratePageBlocks generate the Page blocks. Draws the Image on a block, implementing the Drawable interface.
func (_dcfa *Image )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){if _dcfa ._agge ==nil {if _abbgd :=_dcfa .makeXObject ();_abbgd !=nil {return nil ,ctx ,_abbgd ;};};var _dabd []*Block ;_fgbe :=ctx ;_ffadf :=NewBlock (ctx .PageWidth ,ctx .PageHeight );
if _dcfa ._dfbc .IsRelative (){_dcfa .applyFitMode (ctx .Width );ctx .X +=_dcfa ._efde .Left ;ctx .Y +=_dcfa ._efde .Top ;ctx .Width -=_dcfa ._efde .Left +_dcfa ._efde .Right ;ctx .Height -=_dcfa ._efde .Top +_dcfa ._efde .Bottom ;if _dcfa ._fcbd > ctx .Height {_dabd =append (_dabd ,_ffadf );
_ffadf =NewBlock (ctx .PageWidth ,ctx .PageHeight );ctx .Page ++;_ddgb :=ctx ;_ddgb .Y =ctx .Margins .Top +_dcfa ._efde .Top ;_ddgb .X =ctx .Margins .Left +_dcfa ._efde .Left ;_ddgb .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom -_dcfa ._efde .Top -_dcfa ._efde .Bottom ;
_ddgb .Width =ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_dcfa ._efde .Left -_dcfa ._efde .Right ;ctx =_ddgb ;};}else {ctx .X =_dcfa ._fabc ;ctx .Y =_dcfa ._dff ;};ctx ,_eaef :=_dcgdc (_ffadf ,_dcfa ,ctx );if _eaef !=nil {return nil ,ctx ,_eaef ;
};_dabd =append (_dabd ,_ffadf );if _dcfa ._dfbc .IsAbsolute (){ctx =_fgbe ;}else {ctx .X =_fgbe .X ;ctx .Width =_fgbe .Width ;ctx .Y +=_dcfa ._efde .Bottom ;};return _dabd ,ctx ,nil ;};const (TextAlignmentLeft TextAlignment =iota ;TextAlignmentRight ;
TextAlignmentCenter ;TextAlignmentJustify ;);

// SetExtends specifies whether ot extend the shading beyond the starting and ending points.
//
// Text extends is set to `[]bool{false, false}` by default.
func (_afga *RadialShading )SetExtends (start bool ,end bool ){_afga ._aada .SetExtends (start ,end )};

// TextChunk represents a chunk of text along with a particular style.
type TextChunk struct{

// The text that is being rendered in the PDF.
Text string ;

// The style of the text being rendered.
Style TextStyle ;_aecg []*_db .PdfAnnotation ;_eeddd []bool ;

// The vertical alignment of the text chunk.
VerticalAlignment TextVerticalAlignment ;_aabc *string ;_cbea *string ;_fcgd *string ;_gefgc *_db .StructureTagInfo ;};func (_cacgf *Table )updateRowHeights (_bfgg float64 ){for _ ,_fcfeg :=range _cacgf ._ccgf {_bfaeg :=_fcfeg .width (_cacgf ._bdggd ,_bfgg );
_ffeca :=_fcfeg .height (_bfaeg );_febb :=_cacgf ._gdbgf [_fcfeg ._efage +_fcfeg ._cgedc -2];if _fcfeg ._cgedc > 1{_dcea :=0.0;_cccg :=_cacgf ._gdbgf [_fcfeg ._efage -1:(_fcfeg ._efage +_fcfeg ._cgedc -1)];for _ ,_aafe :=range _cccg {_dcea +=_aafe ;};if _ffeca <=_dcea {continue ;
};};if _ffeca > _febb {_egead :=_ffeca /float64 (_fcfeg ._cgedc );if _egead > _febb {for _dcfbgg :=1;_dcfbgg <=_fcfeg ._cgedc ;_dcfbgg ++{if _egead > _cacgf ._gdbgf [_fcfeg ._efage +_dcfbgg -2]{_cacgf ._gdbgf [_fcfeg ._efage +_dcfbgg -2]=_egead ;};};};
};};};

// MultiCell makes a new cell with the specified row span and col span
// and inserts it into the table at the current position.
func (_eaddg *Table )MultiCell (rowspan ,colspan int )*TableCell {_eaddg ._gfcb ++;_fafdc :=(_eaddg .moveToNextAvailableCell ()-1)%(_eaddg ._fcegg )+1;_acge :=(_eaddg ._gfcb -1)/_eaddg ._fcegg +1;for _acge > _eaddg ._fgdc {_eaddg ._fgdc ++;_eaddg ._gdbgf =append (_eaddg ._gdbgf ,_eaddg ._gfafc );
};_fbgca :=&TableCell {};_fbgca ._efage =_acge ;_fbgca ._afdac =_fafdc ;_fbgca ._cdffb =_db .NewStructureTagInfo ();_fbgca ._cdffb .StructureType =_db .StructureTypeTableData ;_fbgca ._eggag =5;_fbgca ._cabbe =CellBorderStyleNone ;_fbgca ._cdadf =_gc .LineStyleSolid ;
_fbgca ._dfdda =CellHorizontalAlignmentLeft ;_fbgca ._adfcfg =CellVerticalAlignmentTop ;_fbgca ._gbgfg =0;_fbgca ._cfcab =0;_fbgca ._dccbf =0;_fbgca ._ebde =0;_dfddc :=ColorBlack ;_fbgca ._fdffg =_dfddc ;_fbgca ._cebb =_dfddc ;_fbgca ._effdc =_dfddc ;_fbgca ._dgdf =_dfddc ;
_fbgca ._ebbg =1.0;if rowspan < 1{_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065\u003a\u0020\u0063\u0065\u006c\u006c\u0020\u0072\u006f\u0077\u0073\u0070a\u006e\u0020\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0061t\u0020\u0031\u0020\u0028\u0025\u0064\u0029\u002e\u0020\u0053\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0063e\u006c\u006c\u0020\u0072\u006f\u0077s\u0070\u0061n\u0020\u0074o\u00201\u002e",rowspan );
rowspan =1;};_dfeba :=_eaddg ._fgdc -(_fbgca ._efage -1);if rowspan > _dfeba {_a .Log .Debug ("\u0054\u0061b\u006c\u0065\u003a\u0020\u0063\u0065\u006c\u006c\u0020\u0072\u006f\u0077\u0073\u0070\u0061\u006e\u0020\u0028\u0025d\u0029\u0020\u0065\u0078\u0063\u0065e\u0064\u0073\u0020\u0072\u0065\u006d\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0072o\u0077\u0073 \u0028\u0025\u0064\u0029.\u0020\u0041\u0064\u0064\u0069n\u0067\u0020\u0072\u006f\u0077\u0073\u002e",rowspan ,_dfeba );
_eaddg ._fgdc +=rowspan -1;for _edge :=0;_edge <=rowspan -_dfeba ;_edge ++{_eaddg ._gdbgf =append (_eaddg ._gdbgf ,_eaddg ._gfafc );};};for _accff :=0;_accff < colspan &&_fafdc +_accff -1< len (_eaddg ._ggee );_accff ++{_eaddg ._ggee [_fafdc +_accff -1]=rowspan -1;
};_fbgca ._cgedc =rowspan ;if colspan < 1{_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065\u003a\u0020\u0063\u0065\u006c\u006c\u0020\u0063\u006f\u006c\u0073\u0070a\u006e\u0020\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0061n\u0020\u0031\u0020\u0028\u0025\u0064\u0029\u002e\u0020\u0053\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0063e\u006c\u006c\u0020\u0063\u006f\u006cs\u0070\u0061n\u0020\u0074o\u00201\u002e",colspan );
colspan =1;};_fadag :=_eaddg ._fcegg -(_fbgca ._afdac -1);if colspan > _fadag {_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065:\u0020\u0063\u0065\u006c\u006c\u0020\u0063o\u006c\u0073\u0070\u0061\u006e\u0020\u0028\u0025\u0064\u0029\u0020\u0065\u0078\u0063\u0065\u0065\u0064\u0073\u0020\u0072\u0065\u006d\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0072\u006f\u0077\u0020\u0063\u006f\u006c\u0073\u0020\u0028\u0025d\u0029\u002e\u0020\u0041\u0064\u006a\u0075\u0073\u0074\u0069\u006e\u0067 \u0063\u006f\u006c\u0073\u0070\u0061n\u002e",colspan ,_fadag );
colspan =_fadag ;};_fbgca ._edece =colspan ;_eaddg ._gfcb +=colspan -1;_eaddg ._ccgf =append (_eaddg ._ccgf ,_fbgca );_fbgca ._dcdcf =_eaddg ;return _fbgca ;};

// CellBorderStyle defines the table cell's border style.
type CellBorderStyle int ;func _cebab (_geeed interface{})(interface{},error ){switch _gbgg :=_geeed .(type ){case uint8 :return int64 (_gbgg ),nil ;case int8 :return int64 (_gbgg ),nil ;case uint16 :return int64 (_gbgg ),nil ;case int16 :return int64 (_gbgg ),nil ;
case uint32 :return int64 (_gbgg ),nil ;case int32 :return int64 (_gbgg ),nil ;case uint64 :return int64 (_gbgg ),nil ;case int64 :return _gbgg ,nil ;case int :return int64 (_gbgg ),nil ;case float32 :return float64 (_gbgg ),nil ;case float64 :return _gbgg ,nil ;
};return nil ,_g .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069d\u0020\u0076\u0061\u006c\u0075\u0065\u002c\u0020\u0025\u0076\u0020\u0069\u0073 \u006e\u006f\u0074\u0020\u0061\u0020\u006eu\u006d\u0062\u0065\u0072",_geeed );};

// ParseFromSVGStream creates a GraphicSVG instance from SVG stream input.
func ParseFromSVGStream (source _cc .Reader )(*GraphicSVGElement ,error ){_ceceg :=_e .NewDecoder (source );_ceceg .CharsetReader =_d .NewReaderLabel ;_acba ,_aacc :=_fafdd (_ceceg );if _aacc !=nil {return nil ,_aacc ;};if _fcaaf :=_acba .Decode (_ceceg );
_fcaaf !=nil &&_fcaaf !=_cc .EOF {return nil ,_fcaaf ;};return _acba ,nil ;};

// EnablePageWrap controls whether the table is wrapped across pages.
// If disabled, the table is moved in its entirety on a new page, if it
// does not fit in the available height. By default, page wrapping is enabled.
// If the height of the table is larger than an entire page, wrapping is
// enabled automatically in order to avoid unwanted behavior.
func (_bedfc *Table )EnablePageWrap (enable bool ){_bedfc ._aecdf =enable };

// Height returns the height of the ellipse.
func (_adfc *Ellipse )Height ()float64 {return _adfc ._gaabd };

// SetStructureType sets the structure type for the curve component.
func (_edea *Curve )SetStructureType (structureType _db .StructureType ){if _edea ._fefg ==nil {_edea ._fefg =_db .NewStructureTagInfo ();};_edea ._fefg .StructureType =structureType ;};

// LinkTagOptions represents optional parameters for tagged link annotations,
// providing flexibility for adding accessibility features.
type LinkTagOptions struct{

// MCID is the marked content identifier for structure tree association
MCID int64 ;

// AltText is alternative text for screen readers (only used if different from visible text)
AltText string ;

// Tooltip sets the annotation's Contents field (tooltip on hover)
Tooltip string ;};

// TextDecorationLineStyle represents the style of lines used to decorate
// a text chunk (e.g. underline).
type TextDecorationLineStyle struct{

// Color represents the color of the line (default: the color of the text).
Color Color ;

// Offset represents the vertical offset of the line (default: 1).
Offset float64 ;

// Thickness represents the thickness of the line (default: 1).
Thickness float64 ;};

// FillColor returns the fill color of the rectangle.
func (_agdf *Rectangle )FillColor ()Color {return _agdf ._ggac };

// SetSideBorderColor sets the cell's side border color.
func (_gecde *GridCell )SetSideBorderColor (side CellBorderSide ,col Color ){switch side {case CellBorderSideAll :_gecde ._edee =col ;_gecde ._cbcd =col ;_gecde ._bdcg =col ;_gecde ._gegc =col ;case CellBorderSideTop :_gecde ._edee =col ;case CellBorderSideBottom :_gecde ._cbcd =col ;
case CellBorderSideLeft :_gecde ._bdcg =col ;case CellBorderSideRight :_gecde ._gegc =col ;};};

// SetMarkedContentID sets the marked content ID for the paragraph.
func (_cbef *StyledParagraph )SetMarkedContentID (mcid int64 ){if _cbef ._gbfc ==nil {_cbef ._gbfc =_db .NewStructureTagInfo ();_cbef ._gbfc .StructureType =_db .StructureTypeParagraph ;};_cbef ._gbfc .Mcid =mcid ;};func (_edcd *Grid )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };
func _egefc (_dcege *templateProcessor ,_cedea *templateNode )(interface{},error ){return _dcege .parseChart (_cedea );};

// SetTerms sets the terms and conditions section of the invoice.
func (_bdgb *Invoice )SetTerms (title ,content string ){_bdgb ._ddgeg =[2]string {title ,content }};

// MoveDown moves the drawing context down by relative displacement dy (negative goes up).
func (_ecaa *Creator )MoveDown (dy float64 ){_ecaa ._fcga .Y +=dy };

// RotateDeg rotates the current active page by angle degrees.  An error is returned on failure,
// which can be if there is no currently active page, or the angleDeg is not a multiple of 90 degrees.
func (_aaba *Creator )RotateDeg (angleDeg int64 )error {_egdd :=_aaba .getActivePage ();if _egdd ==nil {_a .Log .Debug ("F\u0061\u0069\u006c\u0020\u0074\u006f\u0020\u0072\u006f\u0074\u0061\u0074\u0065\u003a\u0020\u006e\u006f\u0020p\u0061\u0067\u0065\u0020\u0063\u0075\u0072\u0072\u0065\u006etl\u0079\u0020\u0061c\u0074i\u0076\u0065");
return _ee .New ("\u006e\u006f\u0020\u0070\u0061\u0067\u0065\u0020\u0061c\u0074\u0069\u0076\u0065");};if angleDeg %90!=0{_a .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0050\u0061\u0067e\u0020\u0072\u006f\u0074\u0061\u0074\u0069on\u0020\u0061\u006e\u0067l\u0065\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u006dul\u0074\u0069p\u006c\u0065\u0020\u006f\u0066\u0020\u0039\u0030");
return _ee .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");};var _gbbd int64 ;if _egdd .Rotate !=nil {_gbbd =*(_egdd .Rotate );};_gbbd +=angleDeg ;_egdd .Rotate =&_gbbd ;return nil ;};func (_acadg *TemplateOptions )init (){if _acadg .SubtemplateMap ==nil {_acadg .SubtemplateMap =map[string ]_cc .Reader {};
};if _acadg .FontMap ==nil {_acadg .FontMap =map[string ]*_db .PdfFont {};};if _acadg .ImageMap ==nil {_acadg .ImageMap =map[string ]*_db .Image {};};if _acadg .ColorMap ==nil {_acadg .ColorMap =map[string ]Color {};};if _acadg .ChartMap ==nil {_acadg .ChartMap =map[string ]_fed .ChartRenderable {};
};};

// Creator is a wrapper around functionality for creating PDF reports and/or adding new
// content onto imported PDF pages, etc.
type Creator struct{

// Errors keeps error messages that should not interrupt pdf processing and to be checked later.
Errors []error ;

// UnsupportedCharacterReplacement is character that will be used to replace unsupported glyph.
// The value will be passed to drawing context.
UnsupportedCharacterReplacement rune ;_bcbe []*_db .PdfPage ;_gade map[*_db .PdfPage ]*Block ;_fbgd map[*_db .PdfPage ]*pageTransformations ;_abcd *_db .PdfPage ;_bggg PageSize ;_fcga DrawContext ;_gecf Margins ;_cfgg ,_adde float64 ;_egfd int ;_cga func (_fgg FrontpageFunctionArgs );
_bba func (_cbab *TOC )error ;_cgeg func (_bbac *Block ,_ebb HeaderFunctionArgs );_dfe func (_ggbd *Block ,_acagb FooterFunctionArgs );_aebd func (_cgf PageFinalizeFunctionArgs )error ;_cecda func (_cbfg *_db .PdfWriter )error ;_abf bool ;

// Controls whether a table of contents will be generated.
AddTOC bool ;

// CustomTOC specifies if the TOC is rendered by the user.
// When the `CustomTOC` field is set to `true`, the default TOC component is not rendered.
// Instead the TOC is drawn by the user, in the callback provided to
// the `Creator.CreateTableOfContents` method.
// If `CustomTOC` is set to `false`, the callback provided to
// `Creator.CreateTableOfContents` customizes the style of the automatically generated TOC component.
CustomTOC bool ;_dbaf *TOC ;

// Controls whether outlines will be generated.
AddOutlines bool ;_gcbc *_db .Outline ;_gdc *_db .PdfOutlineTreeNode ;_edbf *_db .PdfAcroForm ;_gfed _dd .PdfObject ;_dfaf _db .Optimizer ;_feca []*_db .PdfFont ;_bae *_db .PdfFont ;_edeg *_db .PdfFont ;_aceb *_db .StructTreeRoot ;_gbf *_db .ViewerPreferences ;
_bge string ;};

// ScaleToWidth scales the rectangle to the specified width. The height of
// the rectangle is scaled so that the aspect ratio is maintained.
func (_fbbfd *Rectangle )ScaleToWidth (w float64 ){_efdcc :=_fbbfd ._cfedb /_fbbfd ._cdcgg ;_fbbfd ._cdcgg =w ;_fbbfd ._cfedb =w *_efdcc ;};

// SetFillColor sets background color for border.
func (_fdb *border )SetFillColor (col Color ){_fdb ._ffc =col };func (_cee *Block )setOpacity (_ed float64 ,_fdg float64 )(string ,error ){if (_ed < 0||_ed >=1.0)&&(_fdg < 0||_fdg >=1.0){return "",nil ;};_dcf :=0;_df :=_g .Sprintf ("\u0047\u0053\u0025\u0064",_dcf );
for _cee ._faa .HasExtGState (_dd .PdfObjectName (_df )){_dcf ++;_df =_g .Sprintf ("\u0047\u0053\u0025\u0064",_dcf );};_ga :=_dd .MakeDict ();if _ed >=0&&_ed < 1.0{_ga .Set ("\u0063\u0061",_dd .MakeFloat (_ed ));};if _fdg >=0&&_fdg < 1.0{_ga .Set ("\u0043\u0041",_dd .MakeFloat (_fdg ));
};_ega :=_cee ._faa .AddExtGState (_dd .PdfObjectName (_df ),_ga );if _ega !=nil {return "",_ega ;};return _df ,nil ;};

// GenerateKDict generates a K dictionary for the image.
func (_egagf *Image )GenerateKDict ()(*_db .KDict ,error ){if _egagf ._fecd ==nil {return nil ,_g .Errorf ("\u0069\u006d\u0061g\u0065\u0020\u0073\u0074r\u0075\u0063\u0074\u0075\u0072\u0065\u0020i\u006e\u0066\u006f\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};return _egagf ._fecd .GenerateKDict (),nil ;};func _fag (_bed *Chapter ,_bcdb *TOC ,_bfec *_db .Outline ,_aeb string ,_dgcf int ,_ddge TextStyle )*Chapter {var _affa uint =1;if _bed !=nil {_affa =_bed ._bafa +1;};_bdec :=&Chapter {_eab :_dgcf ,_bfag :_aeb ,_ebfb :true ,_cadag :true ,_age :_bed ,_fbg :_bcdb ,_eabb :_bfec ,_bade :[]Drawable {},_bafa :_affa };
_bggb :=_gcefe (_ddge );_bdd :=_bggb .SetText (_bdec .headingText ());_bdd .Style =_ddge ;_bdec ._dgca =_bggb ;return _bdec ;};func (_daceg *TOC )GenerateKDict ()(*_db .KDict ,error ){return nil ,nil };func (_befcb *templateProcessor )parseTextOverflowAttr (_adab ,_eecc string )TextOverflow {_a .Log .Debug ("\u0050a\u0072\u0073i\u006e\u0067\u0020\u0074e\u0078\u0074\u0020o\u0076\u0065\u0072\u0066\u006c\u006f\u0077\u0020\u0061tt\u0072\u0069\u0062u\u0074\u0065:\u0020\u0028\u0060\u0025\u0073\u0060,\u0020\u0025s\u0029\u002e",_adab ,_eecc );
_ebcee :=map[string ]TextOverflow {"\u0076i\u0073\u0069\u0062\u006c\u0065":TextOverflowVisible ,"\u0068\u0069\u0064\u0064\u0065\u006e":TextOverflowHidden }[_eecc ];return _ebcee ;};func _ecag (_ecda ,_dbcfc ,_fabce ,_efbf float64 )*Line {return &Line {_ggdbc :_ecda ,_geega :_dbcfc ,_cacg :_fabce ,_degba :_efbf ,_feedb :ColorBlack ,_ffade :1.0,_gbgbb :1.0,_feda :[]int64 {1,1},_eddaa :PositionAbsolute };
};

// SetPositioning sets the positioning of the line (absolute or relative).
func (_feebf *Line )SetPositioning (positioning Positioning ){_feebf ._eddaa =positioning };

// SetStructureType sets the structure type for the rectangle.
func (_edcgg *Rectangle )SetStructureType (structureType _db .StructureType ){if _edcgg ._bgbc ==nil {_edcgg ._bgbc =_db .NewStructureTagInfo ();};_edcgg ._bgbc .StructureType =structureType ;};func (_bdgg *StyledParagraph )wrapWordChunks (){if !_bdgg ._fbfd {return ;
};var (_eaac []*TextChunk ;_eaadf *_db .PdfFont ;);for _ ,_ccac :=range _bdgg ._dgaca {_eceg :=[]rune (_ccac .Text );if _eaadf ==nil {_eaadf =_ccac .Style .Font ;};_gacd :=_ccac ._aecg ;_cfcdc :=_ccac .VerticalAlignment ;if len (_eaac )> 0{if len (_eceg )==1&&_ec .IsPunct (_eceg [0])&&_ccac .Style .Font ==_eaadf {_ffgbd :=[]rune (_eaac [len (_eaac )-1].Text );
_eaac [len (_eaac )-1].Text =string (append (_ffgbd ,_eceg [0]));continue ;}else {_ ,_agab :=_fbf .Atoi (_ccac .Text );if _agab ==nil {_adff :=[]rune (_eaac [len (_eaac )-1].Text );_fffc :=len (_adff );if _fffc >=2{_ ,_eeegf :=_fbf .Atoi (string (_adff [_fffc -2]));
if _eeegf ==nil &&_ec .IsPunct (_adff [_fffc -1]){_eaac [len (_eaac )-1].Text =string (append (_adff ,_eceg ...));continue ;};};};};};_ddbag ,_fdgda :=_gbdaa (_ccac .Text );if _fdgda !=nil {_a .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0062\u0072\u0065\u0061\u006b\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0074\u006f\u0020w\u006f\u0072\u0064\u0073\u003a\u0020\u0025\u0076",_fdgda );
_ddbag =[]string {_ccac .Text };};for _ ,_fbceb :=range _ddbag {_efaeb :=NewTextChunk (_fbceb ,_ccac .Style );_efaeb ._aecg =_afbb (_gacd );_efaeb .VerticalAlignment =_cfcdc ;_eaac =append (_eaac ,_efaeb );};_eaadf =_ccac .Style .Font ;};if len (_eaac )> 0{_bdgg ._dgaca =_eaac ;
};};func (_bfd *Block )mergeBlocks (_dcc *Block )error {_feg :=_bdf (_bfd ._fd ,_bfd ._faa ,_dcc ._fd ,_dcc ._faa );if _feg !=nil {return _feg ;};for _ ,_beg :=range _dcc ._ecd {_bfd .AddAnnotation (_beg );};return nil ;};func _gdfba (_gcfgg ,_afdcc string )*_db .PdfAnnotation {_feebgc :=_db .NewPdfAnnotationLink ();
_febeed :=_db .NewBorderStyle ();_febeed .SetBorderWidth (0);_feebgc .BS =_febeed .ToPdfObject ();_dbafe :=_db .NewPdfActionURI ();_dbafe .URI =_dd .MakeString (_gcfgg );_feebgc .SetAction (_dbafe .PdfAction );if _afdcc !=""{_feebgc .Contents =_dd .MakeString (_afdcc );
};return _feebgc .PdfAnnotation ;};

// SetMargins sets the Table's left, right, top, bottom margins.
func (_fdaff *Table )SetMargins (left ,right ,top ,bottom float64 ){_fdaff ._acbac .Left =left ;_fdaff ._acbac .Right =right ;_fdaff ._acbac .Top =top ;_fdaff ._acbac .Bottom =bottom ;};

// SetPositioning sets the positioning of the ellipse (absolute or relative).
func (_ecafc *Ellipse )SetPositioning (position Positioning ){_ecafc ._cddg =position };

// ParseFromSVGFile creates a GraphicSVG instance from file.
func ParseFromSVGFile (path string )(*GraphicSVGElement ,error ){_egea ,_defdg :=_eb .Open (path );if _defdg !=nil {return nil ,_defdg ;};defer _egea .Close ();return ParseFromSVGStream (_egea );};

// GeneratePageBlocks draws the block contents on a template Page block.
// Implements the Drawable interface.
func (_ac *Block )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_ge :=_bce .IdentityMatrix ();_ebg ,_fcca :=_ac .Width (),_ac .Height ();if _ac ._fbb .IsRelative (){_ge =_ge .Translate (ctx .X ,ctx .PageHeight -ctx .Y -_fcca );}else {_ge =_ge .Translate (_ac ._ced ,ctx .PageHeight -_ac ._fda -_fcca );
};_gec :=_fcca ;if _ac ._da !=0{_ge =_ge .Translate (_ebg /2,_fcca /2).Rotate (_ac ._da *_fa .Pi /180.0).Translate (-_ebg /2,-_fcca /2);_ ,_gec =_ac .RotatedSize ();};if _ac ._fbb .IsRelative (){ctx .Y +=_gec ;};_dde :=_eg .NewContentCreator ();_dde .Add_cm (_ge [0],_ge [1],_ge [3],_ge [4],_ge [6],_ge [7]);
_ef :=_ac .duplicate ();_cec :=append (*_dde .Operations (),*_ef ._fd ...);_cec .WrapIfNeeded ();_ef ._fd =&_cec ;for _ ,_egd :=range _ac ._ecd {_bd ,_aca :=_dd .GetArray (_egd .Rect );if !_aca ||_bd .Len ()!=4{_a .Log .Debug ("\u0057\u0041\u0052\u004e\u003a \u0069\u006e\u0076\u0061\u006ci\u0064 \u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0052\u0065\u0063\u0074\u0020\u0066\u0069\u0065l\u0064\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_egd .Rect );
continue ;};_ceg ,_faf :=_db .NewPdfRectangle (*_bd );if _faf !=nil {_a .Log .Debug ("\u0057A\u0052N\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074 \u0070\u0061\u0072\u0073e\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020\u0052\u0065\u0063\u0074\u0020\u0066\u0069\u0065\u006c\u0064\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061y\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002e",_faf );
continue ;};_ceg .Transform (_ge );_egd .Rect =_ceg .ToPdfObject ();};return []*Block {_ef },ctx ,nil ;};

// SetOptimizer sets the optimizer to optimize PDF before writing.
func (_agba *Creator )SetOptimizer (optimizer _db .Optimizer ){_agba ._dfaf =optimizer };

// CurCol returns the currently active cell's column number.
func (_bdbf *Table )CurCol ()int {_feaf :=(_bdbf ._gfcb -1)%(_bdbf ._fcegg )+1;return _feaf };

// SetSubtotal sets the subtotal of the invoice.
func (_cfaf *Invoice )SetSubtotal (value string ){_cfaf ._ageb [1].Value =value };func (_ffdg *GridRow )updateRowHeight (_dgfg float64 ){_ffdg ._fega =_ffdg ._deege ;for _ ,_gada :=range _ffdg ._fgfe {_begga :=_gada .width (_ffdg ._dfdbg ._daad ,_dgfg );
_bfccf :=_gada .height (_begga );if _bfccf > _ffdg ._fega {_ffdg ._fega =_bfccf ;};};};func _bagfe (_cgade int64 ,_caee ,_deafc ,_aadca float64 ,_edcgf string )*_db .PdfAnnotation {_febff :=_db .NewPdfAnnotationLink ();_gacaa :=_db .NewBorderStyle ();_gacaa .SetBorderWidth (0);
_febff .BS =_gacaa .ToPdfObject ();if _cgade < 0{_cgade =0;};_febff .Dest =_dd .MakeArray (_dd .MakeInteger (_cgade ),_dd .MakeName ("\u0058\u0059\u005a"),_dd .MakeFloat (_caee ),_dd .MakeFloat (_deafc ),_dd .MakeFloat (_aadca ));if _edcgf !=""{_febff .Contents =_dd .MakeString (_edcgf );
};return _febff .PdfAnnotation ;};const (CellBorderSideLeft CellBorderSide =iota ;CellBorderSideRight ;CellBorderSideTop ;CellBorderSideBottom ;CellBorderSideAll ;);func (_dfgd *Table )addRowTag ()*_db .KDict {_fefed :=_db .NewKDictionary ();_fefed .S =_dd .MakeName (string (_db .StructureTypeTableRow ));
_dfgd ._bcbfe .AddKChild (_fefed );return _fefed ;};func (_adfge *templateProcessor )parseTextAlignmentAttr (_fbdf ,_gfgc string )TextAlignment {_a .Log .Debug ("\u0050a\u0072\u0073i\u006e\u0067\u0020t\u0065\u0078\u0074\u0020\u0061\u006c\u0069g\u006e\u006d\u0065\u006e\u0074\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028`\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_fbdf ,_gfgc );
_ddaed :=map[string ]TextAlignment {"\u006c\u0065\u0066\u0074":TextAlignmentLeft ,"\u0072\u0069\u0067h\u0074":TextAlignmentRight ,"\u0063\u0065\u006e\u0074\u0065\u0072":TextAlignmentCenter ,"\u006au\u0073\u0074\u0069\u0066\u0079":TextAlignmentJustify }[_gfgc ];
return _ddaed ;};

// SetLazy sets the lazy mode for the image.
func (_bfae *Image )SetLazy (lazy bool ){_bfae ._gbed =lazy };func (_fbfcg *RadialShading )shadingModel ()*_db .PdfShadingType3 {_gedc ,_gdbf ,_efae :=_fbfcg ._aada ._cgbf .ToRGB ();var _efdeg _gc .Point ;switch _fbfcg ._edfg {case AnchorBottomLeft :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Llx ,Y :_fbfcg ._gcdbd .Lly };
case AnchorBottomRight :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Urx ,Y :_fbfcg ._gcdbd .Ury -_fbfcg ._gcdbd .Height ()};case AnchorTopLeft :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Llx ,Y :_fbfcg ._gcdbd .Lly +_fbfcg ._gcdbd .Height ()};case AnchorTopRight :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Urx ,Y :_fbfcg ._gcdbd .Ury };
case AnchorLeft :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Llx ,Y :_fbfcg ._gcdbd .Lly +_fbfcg ._gcdbd .Height ()/2};case AnchorTop :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Llx +_fbfcg ._gcdbd .Width ()/2,Y :_fbfcg ._gcdbd .Ury };case AnchorRight :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Urx ,Y :_fbfcg ._gcdbd .Lly +_fbfcg ._gcdbd .Height ()/2};
case AnchorBottom :_efdeg =_gc .Point {X :_fbfcg ._gcdbd .Urx +_fbfcg ._gcdbd .Width ()/2,Y :_fbfcg ._gcdbd .Lly };default:_efdeg =_gc .NewPoint (_fbfcg ._gcdbd .Llx +_fbfcg ._gcdbd .Width ()/2,_fbfcg ._gcdbd .Lly +_fbfcg ._gcdbd .Height ()/2);};_beba :=_fbfcg ._begab ;
_baddb :=_fbfcg ._fceg ;_bfbfe :=_efdeg .X +_fbfcg ._ccdef ;_gaadc :=_efdeg .Y +_fbfcg ._agafb ;if _beba ==-1.0{_beba =0.0;};if _baddb ==-1.0{var _gdbfc []float64 ;_gbede :=_fa .Pow (_bfbfe -_fbfcg ._gcdbd .Llx ,2)+_fa .Pow (_gaadc -_fbfcg ._gcdbd .Lly ,2);
_gdbfc =append (_gdbfc ,_fa .Abs (_gbede ));_adfcg :=_fa .Pow (_bfbfe -_fbfcg ._gcdbd .Llx ,2)+_fa .Pow (_fbfcg ._gcdbd .Lly +_fbfcg ._gcdbd .Height ()-_gaadc ,2);_gdbfc =append (_gdbfc ,_fa .Abs (_adfcg ));_agda :=_fa .Pow (_fbfcg ._gcdbd .Urx -_bfbfe ,2)+_fa .Pow (_gaadc -_fbfcg ._gcdbd .Ury -_fbfcg ._gcdbd .Height (),2);
_gdbfc =append (_gdbfc ,_fa .Abs (_agda ));_deage :=_fa .Pow (_fbfcg ._gcdbd .Urx -_bfbfe ,2)+_fa .Pow (_fbfcg ._gcdbd .Ury -_gaadc ,2);_gdbfc =append (_gdbfc ,_fa .Abs (_deage ));_gg .Slice (_gdbfc ,func (_egga ,_bdcge int )bool {return _egga > _bdcge });
_baddb =_fa .Sqrt (_gdbfc [0]);};_dbcb :=&_db .PdfRectangle {Llx :_bfbfe -_baddb ,Lly :_gaadc -_baddb ,Urx :_bfbfe +_baddb ,Ury :_gaadc +_baddb };_dbdfe :=_db .NewPdfShadingType3 ();_dbdfe .PdfShading .ShadingType =_dd .MakeInteger (3);_dbdfe .PdfShading .ColorSpace =_db .NewPdfColorspaceDeviceRGB ();
_dbdfe .PdfShading .Background =_dd .MakeArrayFromFloats ([]float64 {_gedc ,_gdbf ,_efae });_dbdfe .PdfShading .BBox =_dbcb ;_dbdfe .PdfShading .AntiAlias =_dd .MakeBool (_fbfcg ._aada ._efbd );_dbdfe .Coords =_dd .MakeArrayFromFloats ([]float64 {_bfbfe ,_gaadc ,_beba ,_bfbfe ,_gaadc ,_baddb });
_dbdfe .Domain =_dd .MakeArrayFromFloats ([]float64 {0.0,1.0});_dbdfe .Extend =_dd .MakeArray (_dd .MakeBool (_fbfcg ._aada ._edga [0]),_dd .MakeBool (_fbfcg ._aada ._edga [1]));_dbdfe .Function =_fbfcg ._aada .generatePdfFunctions ();return _dbdfe ;};


// SetMarkedContentID sets marked content ID.
func (_dfef *Curve )SetMarkedContentID (mcid int64 ){if _dfef ._fefg ==nil {_dfef ._fefg =_db .NewStructureTagInfo ();};_dfef ._fefg .Mcid =mcid ;};func (_gfdgc *Division )split (_cfdg DrawContext )(_fggg ,_ddac *Division ){var (_fefb float64 ;_dgad ,_gebg []VectorDrawable ;
);_dfbe :=_cfdg .Width -_gfdgc ._fccaf .Left -_gfdgc ._fccaf .Right -_gfdgc ._ddcg .Left -_gfdgc ._ddcg .Right ;for _cffgf ,_ebgg :=range _gfdgc ._cadaa {_fefb +=_abcfe (_ebgg ,_dfbe );if _fefb < _cfdg .Height {_dgad =append (_dgad ,_ebgg );}else {_gebg =_gfdgc ._cadaa [_cffgf :];
break ;};};if len (_dgad )> 0{_fggg =_dfge ();*_fggg =*_gfdgc ;_fggg ._cadaa =_dgad ;if _gfdgc ._gfef !=nil {_fggg ._gfef =&Background {};*_fggg ._gfef =*_gfdgc ._gfef ;};};if len (_gebg )> 0{_ddac =_dfge ();*_ddac =*_gfdgc ;_ddac ._cadaa =_gebg ;if _gfdgc ._gfef !=nil {_ddac ._gfef =&Background {};
*_ddac ._gfef =*_gfdgc ._gfef ;};};return _fggg ,_ddac ;};

// SetColorRight sets border color for right.
func (_ada *border )SetColorRight (col Color ){_ada ._aff =col };

// SetOpacity sets opacity for border in range 0-1.
func (_abdb *border )SetOpacity (opacity float64 ){_abdb ._afec =opacity };

// Inline returns whether the inline mode of the division is active.
func (_ggfe *Division )Inline ()bool {return _ggfe ._ffeb };func (_aefc *StyledParagraph )getTextLineWidth (_adaac []*TextChunk )float64 {var _gadaa float64 ;_aega :=len (_adaac );for _adged ,_ecebb :=range _adaac {_aagga :=&_ecebb .Style ;_bbgaf :=len (_ecebb .Text );
for _egeeg ,_faadb :=range _ecebb .Text {if _faadb =='\u000A'{continue ;};_fada ,_cbced :=_aagga .Font .GetRuneMetrics (_faadb );if !_cbced {_a .Log .Debug ("\u0052\u0075\u006e\u0065\u0020\u0063\u0068\u0061\u0072\u0020\u006d\u0065\u0074\u0072\u0069c\u0073 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0021\u0020\u0025\u0076\u000a",_faadb );
return -1;};_gadaa +=_aagga .FontSize *_fada .Wx *_aagga .horizontalScale ();if _faadb !=' '&&(_adged !=_aega -1||_egeeg !=_bbgaf -1){_gadaa +=_aagga .CharSpacing *1000.0;};};};return _gadaa ;};func _aadc (_gdfbd ,_fbagf ,_bbfc int )[]int {_bcedc :=[]int {};
for _ggccb :=_gdfbd ;_ggccb <=_bbfc ;_ggccb +=_fbagf {_bcedc =append (_bcedc ,_ggccb );};return _bcedc ;};

// NewSubchapter creates a new child chapter with the specified title.
func (_fae *Chapter )NewSubchapter (title string )*Chapter {_bccb :=_bffba (_fae ._dgca ._dgaca [0].Style .Font );_bccb .FontSize =14;_fae ._eed ++;_ddcb :=_fag (_fae ,_fae ._fbg ,_fae ._eabb ,title ,_fae ._eed ,_bccb );_fae .Add (_ddcb );return _ddcb ;
};

// SetBackgroundColor set background color of the shading area.
//
// By default the background color is set to white.
func (_bdab *LinearShading )SetBackgroundColor (backgroundColor Color ){_bdab ._afdb .SetBackgroundColor (backgroundColor );};

// SetMargins sets the margins for the Image (in relative mode): left, right, top, bottom.
func (_deaca *Image )SetMargins (left ,right ,top ,bottom float64 ){_deaca ._efde .Left =left ;_deaca ._efde .Right =right ;_deaca ._efde .Top =top ;_deaca ._efde .Bottom =bottom ;};func _fcde (_eega string )(float64 ,error ){_eega =_fc .TrimSpace (_eega );
var _dbafd float64 ;if _fc .HasSuffix (_eega ,"\u0025"){_fagdf ,_gbfg :=_fbf .ParseFloat (_fc .TrimSuffix (_eega ,"\u0025"),64);if _gbfg !=nil {return 0,_gbfg ;};_dbafd =(_fagdf *255.0)/100.0;}else {_acebg ,_ebff :=_fbf .Atoi (_eega );if _ebff !=nil {return 0,_ebff ;
};_dbafd =float64 (_acebg );};return _dbafd ,nil ;};

// FitMode returns the fit mode of the image.
func (_dfed *Image )FitMode ()FitMode {return _dfed ._dddb };func _baca (_bbeag *templateProcessor ,_fbbcc *templateNode )(interface{},error ){return _bbeag .parseImage (_fbbcc );};

// SetBorder sets the cell's border style.
func (_bcfa *TableCell )SetBorder (side CellBorderSide ,style CellBorderStyle ,width float64 ){if style ==CellBorderStyleSingle &&side ==CellBorderSideAll {_bcfa ._cabbe =CellBorderStyleSingle ;_bcfa ._gbgfg =width ;_bcfa ._gecgee =CellBorderStyleSingle ;
_bcfa ._cfcab =width ;_bcfa ._bfaea =CellBorderStyleSingle ;_bcfa ._dccbf =width ;_bcfa ._eafce =CellBorderStyleSingle ;_bcfa ._ebde =width ;}else if style ==CellBorderStyleDouble &&side ==CellBorderSideAll {_bcfa ._cabbe =CellBorderStyleDouble ;_bcfa ._gbgfg =width ;
_bcfa ._gecgee =CellBorderStyleDouble ;_bcfa ._cfcab =width ;_bcfa ._bfaea =CellBorderStyleDouble ;_bcfa ._dccbf =width ;_bcfa ._eafce =CellBorderStyleDouble ;_bcfa ._ebde =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideLeft {_bcfa ._cabbe =style ;
_bcfa ._gbgfg =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideBottom {_bcfa ._gecgee =style ;_bcfa ._cfcab =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideRight {_bcfa ._bfaea =style ;
_bcfa ._dccbf =width ;}else if (style ==CellBorderStyleSingle ||style ==CellBorderStyleDouble )&&side ==CellBorderSideTop {_bcfa ._eafce =style ;_bcfa ._ebde =width ;};};

// SetAlternateText sets the alternate text for the image.
func (_afebd *Image )SetAlternateText (text string ){_afebd ._fdfeb =text };

// SetWidthRight sets border width for right.
func (_aaa *border )SetWidthRight (bw float64 ){_aaa ._cbfa =bw };

// Add adds a new line with the default style to the table of contents.
func (_bgbe *TOC )Add (number ,title ,page string ,level uint )*TOCLine {_cgcaa :=_bgbe .AddLine (_fabba (TextChunk {Text :number ,Style :_bgbe ._fabcf },TextChunk {Text :title ,Style :_bgbe ._cdaad },TextChunk {Text :page ,Style :_bgbe ._dfbae },level ,_bgbe ._dgbc ));
if _cgcaa ==nil {return nil ;};_acbfbd :=&_bgbe ._dfcfb ;_cgcaa .SetMargins (_acbfbd .Left ,_acbfbd .Right ,_acbfbd .Top ,_acbfbd .Bottom );_cgcaa .SetLevelOffset (_bgbe ._bfffc );_cgcaa .Separator .Text =_bgbe ._fedgf ;_cgcaa .Separator .Style =_bgbe ._fbgcd ;
return _cgcaa ;};func (_dbddg *StyledParagraph )addLine (_dafff []*TextChunk )bool {if _dbddg ._ffeg > 0&&len (_dbddg ._cbba )+1>=_dbddg ._ffeg {if len (_dafff )> 0{_dbddg ._cbba =append (_dbddg ._cbba ,_dafff );};return false ;};_dbddg ._cbba =append (_dbddg ._cbba ,_dafff );
return true ;};

// SetBackgroundColor set background color of the shading area.
//
// By default the background color is set to white.
func (_bcea *RadialShading )SetBackgroundColor (backgroundColor Color ){_bcea ._aada .SetBackgroundColor (backgroundColor );};

// SetFillColor sets the fill color.
func (_gbc *CurvePolygon )SetFillColor (color Color ){_gbc ._gbabf =color ;_gbc ._accc .FillColor =_cdfe (color );};

// Level returns the indentation level of the TOC line.
func (_abgb *TOCLine )Level ()uint {return _abgb ._aeaad };

// Color returns the color of the line.
func (_aagd *Line )Color ()Color {return _aagd ._feedb };

// AddInternalLink adds a new internal link to the paragraph.
// The text parameter represents the text that is displayed.
// The user is taken to the specified page, at the specified x and y
// coordinates. Position 0, 0 is at the top left of the page.
// The zoom of the destination page is controlled with the zoom
// parameter. Pass in 0 to keep the current zoom value.
func (_ggbg *StyledParagraph )AddInternalLink (text string ,page int64 ,x ,y ,zoom float64 )*TextChunk {_effbg :=NewTextChunk (text ,_ggbg ._ecff );_effbg .AddAnnotation (_bagfe (page -1,x ,y ,zoom ,""));return _ggbg .appendChunk (_effbg );};func _ccdc (_acadb map[string ]interface{},_cafe ...interface{})(map[string ]interface{},error ){_fedae :=len (_cafe );
if _fedae %2!=0{return nil ,_dd .ErrRangeError ;};for _ecgf :=0;_ecgf < _fedae ;_ecgf +=2{_gdbde ,_dfbcda :=_cafe [_ecgf ].(string );if !_dfbcda {return nil ,_dd .ErrTypeError ;};_acadb [_gdbde ]=_cafe [_ecgf +1];};return _acadb ,nil ;};

// NewTable create a new Table with a specified number of columns.
func (_gddc *Creator )NewTable (cols int )*Table {return _cgdd (cols )};

// Subtotal returns the invoice subtotal description and value cells.
// The returned values can be used to customize the styles of the cells.
func (_cagc *Invoice )Subtotal ()(*InvoiceCell ,*InvoiceCell ){return _cagc ._ageb [0],_cagc ._ageb [1]};

// Table allows organizing content in an rows X columns matrix, which can spawn across multiple pages.
type Table struct{_fgdc int ;_fcegg int ;_gfcb int ;_bdggd []float64 ;_gdbgf []float64 ;_gfafc float64 ;_ccgf []*TableCell ;_ggee []int ;_bafbg Positioning ;_cafd ,_efcce float64 ;_acbac Margins ;_gcfeb bool ;_cdafe int ;_bcfb int ;_dffga bool ;_aecdf bool ;
_cbge bool ;_fefdd *_db .StructureTagInfo ;_ebea bool ;_bcbfe *_db .KDict ;};

// SetExtends specifies whether to extend the shading beyond the starting and ending points.
//
// Text extends is set to `[]bool{false, false}` by default.
func (_acdc *shading )SetExtends (start bool ,end bool ){_acdc ._edga =[]bool {start ,end }};

// SetOpacity sets opacity for Image.
func (_geab *Image )SetOpacity (opacity float64 ){_geab ._efee =opacity };

// SetActualText sets the actual text for the text chunk.
func (_fgaf *TextChunk )SetActualText (text string ){_fgaf ._cbea =&text };

// VectorDrawable is a Drawable with a specified width and height.
type VectorDrawable interface{Drawable ;

// Width returns the width of the Drawable.
Width ()float64 ;

// Height returns the height of the Drawable.
Height ()float64 ;};func (_cfe *Block )addContentsByString (_ege string )error {_gcdg :=_eg .NewContentStreamParser (_ege );_de ,_ggb :=_gcdg .Parse ();if _ggb !=nil {return _ggb ;};_cfe ._fd .WrapIfNeeded ();_de .WrapIfNeeded ();*_cfe ._fd =append (*_cfe ._fd ,*_de ...);
return nil ;};func (_afba *templateProcessor )parseCellBorderStyleAttr (_abegc ,_bbgce string )CellBorderStyle {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020c\u0065\u006c\u006c b\u006f\u0072\u0064\u0065\u0072\u0020s\u0074\u0079\u006c\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a \u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025s\u0029\u002e",_abegc ,_bbgce );
_ccada :=map[string ]CellBorderStyle {"\u006e\u006f\u006e\u0065":CellBorderStyleNone ,"\u0073\u0069\u006e\u0067\u006c\u0065":CellBorderStyleSingle ,"\u0064\u006f\u0075\u0062\u006c\u0065":CellBorderStyleDouble }[_bbgce ];return _ccada ;};func (_fgcb *templateProcessor )parseImageEncoder (_gfgda ,_fffgg string )_dd .StreamEncoder {_a .Log .Debug ("\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074e\u003a\u0020\u0028\u0060\u0025s\u0060\u002c \u0025\u0073\u0029\u002e",_gfgda ,_fffgg );
if _fffgg =="\u0066\u006c\u0061t\u0065"{return _dd .NewFlateEncoder ();}else if _fffgg =="\u0064\u0063\u0074"{return _dd .NewDCTEncoder ();};_a .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u0076\u0061\u006cu\u0065\u0020\u006f\u0066 \u0065\u006e\u0063\u006f\u0064\u0065\u0072\u003a\u0020\u0028\u0060\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u0020\u0066\u006f\u0075\u006e\u0064\u002e",_gfgda ,_fffgg );
return nil ;};

// Width returns the width of the ellipse.
func (_ecaf *Ellipse )Width ()float64 {return _ecaf ._fdgf };func (_bdggg *templateProcessor )parseTable (_afcc *templateNode )(interface{},error ){var _bbbce int64 ;for _ ,_facdg :=range _afcc ._ccge .Attr {_ggfeb :=_facdg .Value ;switch _gacf :=_facdg .Name .Local ;
_gacf {case "\u0063o\u006c\u0075\u006d\u006e\u0073":_bbbce =_bdggg .parseInt64Attr (_gacf ,_ggfeb );};};if _bbbce <=0{_bdggg .nodeLogDebug (_afcc ,"\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006eu\u006d\u0062e\u0072\u0020\u006f\u0066\u0020\u0074\u0061\u0062\u006ce\u0020\u0063\u006f\u006cu\u006d\u006e\u0073\u003a\u0020\u0025\u0064\u002e\u0020\u0053\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0031\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020m\u0061\u0079\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e",_bbbce );
_bbbce =1;};_eaca :=_bdggg .creator .NewTable (int (_bbbce ));for _ ,_aeeff :=range _afcc ._ccge .Attr {_ggbf :=_aeeff .Value ;switch _fbbga :=_aeeff .Name .Local ;_fbbga {case "\u0063\u006f\u006c\u0075\u006d\u006e\u002d\u0077\u0069\u0064\u0074\u0068\u0073":_eaca .SetColumnWidths (_bdggg .parseFloatArray (_fbbga ,_ggbf )...);
case "\u006d\u0061\u0072\u0067\u0069\u006e":_dfdgb :=_bdggg .parseMarginAttr (_fbbga ,_ggbf );_eaca .SetMargins (_dfdgb .Left ,_dfdgb .Right ,_dfdgb .Top ,_dfdgb .Bottom );case "\u0078":_eaca .SetPos (_bdggg .parseFloatAttr (_fbbga ,_ggbf ),_eaca ._efcce );
case "\u0079":_eaca .SetPos (_eaca ._cafd ,_bdggg .parseFloatAttr (_fbbga ,_ggbf ));case "\u0068\u0065a\u0064\u0065\u0072-\u0073\u0074\u0061\u0072\u0074\u002d\u0072\u006f\u0077":_eaca ._cdafe =int (_bdggg .parseInt64Attr (_fbbga ,_ggbf ));case "\u0068\u0065\u0061\u0064\u0065\u0072\u002d\u0065\u006ed\u002d\u0072\u006f\u0077":_eaca ._bcfb =int (_bdggg .parseInt64Attr (_fbbga ,_ggbf ));
case "\u0065n\u0061b\u006c\u0065\u002d\u0072\u006f\u0077\u002d\u0077\u0072\u0061\u0070":_eaca .EnableRowWrap (_bdggg .parseBoolAttr (_fbbga ,_ggbf ));case "\u0065\u006ea\u0062\u006c\u0065-\u0070\u0061\u0067\u0065\u002d\u0077\u0072\u0061\u0070":_eaca .EnablePageWrap (_bdggg .parseBoolAttr (_fbbga ,_ggbf ));
case "\u0063o\u006c\u0075\u006d\u006e\u0073":break ;default:_bdggg .nodeLogDebug (_afcc ,"\u0055n\u0073\u0075p\u0070\u006f\u0072\u0074e\u0064\u0020\u0074a\u0062\u006c\u0065\u0020\u0061\u0074\u0074\u0072\u0069bu\u0074\u0065\u003a \u0060\u0025s\u0060\u002e\u0020\u0053\u006b\u0069p\u0070\u0069n\u0067\u002e",_fbbga );
};};if _eaca ._cdafe !=0&&_eaca ._bcfb !=0{_ecdfc :=_eaca .SetHeaderRows (_eaca ._cdafe ,_eaca ._bcfb );if _ecdfc !=nil {_bdggg .nodeLogDebug (_afcc ,"\u0043\u006ful\u0064\u0020\u006eo\u0074\u0020\u0073\u0065t t\u0061bl\u0065\u0020\u0068\u0065\u0061\u0064\u0065r \u0072\u006f\u0077\u0073\u003a\u0020\u0025v\u002e",_ecdfc );
};}else {_eaca ._cdafe =0;_eaca ._bcfb =0;};return _eaca ,nil ;};

// GetCoords returns coordinates of border.
func (_beef *border )GetCoords ()(float64 ,float64 ){return _beef ._fbac ,_beef ._gba };

// BorderColor returns the border color of the rectangle.
func (_abedg *Rectangle )BorderColor ()Color {return _abedg ._gdea };type cmykColor struct{_dgfd ,_dcb ,_eead ,_edb float64 };

// SetMargins sets the margins of the paragraph.
func (_dbbcb *List )SetMargins (left ,right ,top ,bottom float64 ){_dbbcb ._ddggd .Left =left ;_dbbcb ._ddggd .Right =right ;_dbbcb ._ddggd .Top =top ;_dbbcb ._ddggd .Bottom =bottom ;};

// Context returns the current drawing context.
func (_gdd *Creator )Context ()DrawContext {return _gdd ._fcga };

// Division is a container component which can wrap across multiple pages.
// Currently supported drawable components:
// - *Paragraph
// - *StyledParagraph
// - *Image
// - *Chart
//
// The component stacking behavior is vertical, where the drawables are drawn
// on top of each other.
type Division struct{_cadaa []VectorDrawable ;_ebd Positioning ;_fccaf Margins ;_ddcg Margins ;_ffeb bool ;_gbfd bool ;_gfef *Background ;_aag *_db .StructureTagInfo ;};

// SetMargins sets the Grid's left, right, top, bottom margins.
func (_ddebe *Grid )SetMargins (left ,right ,top ,bottom float64 ){_ddebe ._cbdfcg .Left =left ;_ddebe ._cbdfcg .Right =right ;_ddebe ._cbdfcg .Top =top ;_ddebe ._cbdfcg .Bottom =bottom ;};

// Crop crops the Image to the specified bounds.
func (_feaaa *Image )Crop (x0 ,y0 ,x1 ,y1 int ){_egac ,_eagc :=_feaaa ._fbebf .ToGoImage ();if _eagc !=nil {_ggd .Fatalf ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0074o\u0020\u0047\u006f\u0020\u0049m\u0061\u0067e\u003a\u0020\u0025\u0076",_eagc );
};var _gabd _ea .Image ;_dabde :=_ea .Rect (x0 ,y0 ,x1 ,y1 );if _cdbe :=_dabde .Intersect (_egac .Bounds ());!_dabde .Empty (){_fdbcb :=_ea .NewRGBA (_ea .Rect (0,0,_dabde .Dx (),_dabde .Dy ()));for _ecga :=_cdbe .Min .Y ;_ecga < _cdbe .Max .Y ;_ecga ++{for _cgafe :=_cdbe .Min .X ;
_cgafe < _cdbe .Max .X ;_cgafe ++{_fdbcb .Set (_cgafe -_cdbe .Min .X ,_ecga -_cdbe .Min .Y ,_egac .At (_cgafe ,_ecga ));};};_gabd =_fdbcb ;}else {_gabd =&_ea .RGBA {};};_ebgga ,_eagc :=_db .ImageHandling .NewImageFromGoImage (_gabd );if _eagc !=nil {_ggd .Fatalf ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u0072\u0065\u0061\u0074\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0066\u0072\u006fm\u0020\u0047\u006f\u0020\u0049m\u0061\u0067e\u003a\u0020\u0025\u0076",_eagc );
};_acgf :=float64 (_ebgga .Width );_cabb :=float64 (_ebgga .Height );_feaaa ._fbebf =_ebgga ;_feaaa ._gddb =_acgf ;_feaaa ._gaga =_cabb ;_feaaa ._bbg =_acgf ;_feaaa ._fcbd =_cabb ;};func (_ebaace *templateProcessor )parseListMarker (_beedac *templateNode )(interface{},error ){if _beedac ._gafge ==nil {_ebaace .nodeLogError (_beedac ,"\u004c\u0069\u0073\u0074\u0020\u006da\u0072\u006b\u0065\u0072\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u0063a\u006e\u006e\u006f\u0074\u0020\u0062\u0065 \u006e\u0069\u006c\u002e");
return nil ,_bbcfc ;};var _cdcgd *TextChunk ;switch _aebcd :=_beedac ._gafge ._aeceb .(type ){case *List :_cdcgd =&_aebcd ._debc ;case *listItem :_cdcgd =&_aebcd ._daedg ;default:_ebaace .nodeLogError (_beedac ,"\u0025\u0076 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0076\u0061\u006c\u0069\u0064\u0020\u0070\u0061\u0072\u0065\u006e\u0074\u0020\u006e\u006f\u0064\u0065\u0020\u0066\u006f\u0072\u0020\u006c\u0069\u0073\u0074\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u002e",_aebcd );
return nil ,_bbcfc ;};if _ ,_eefdc :=_ebaace .parseTextChunk (_beedac ,_cdcgd );_eefdc !=nil {_ebaace .nodeLogError (_beedac ,"\u0043\u006f\u0075ld\u0020\u006e\u006f\u0074\u0020\u0070\u0061\u0072\u0073e\u0020l\u0069s\u0074 \u006d\u0061\u0072\u006b\u0065\u0072\u003a\u0020\u0060\u0025\u0076\u0060\u002e",_eefdc );
return nil ,nil ;};return _cdcgd ,nil ;};

// DrawTemplate renders the template provided through the specified reader,
// using the specified `data` and `options`.
// Creator templates are first executed as text/template *Template instances,
// so the specified `data` is inserted within the template.
// The second phase of processing is actually parsing the template, translating
// it into creator components and rendering them using the provided options.
// Both the `data` and `options` parameters can be nil.
func (_bfc *Block )DrawTemplate (c *Creator ,r _cc .Reader ,data interface{},options *TemplateOptions )error {return _gcba (c ,r ,data ,options ,_bfc );};

// NewPolyline creates a new polyline.
func (_gcad *Creator )NewPolyline (points []_gc .Point )*Polyline {return _cdfd (points )};

// SetColumns overwrites any columns in the line items table. This should be
// called before AddLine.
func (_addc *Invoice )SetColumns (cols []*InvoiceCell ){_addc ._egfdb =cols };

// Width is not used. Not used as a Table element is designed to fill into
// available width depending on the context. Returns 0.
func (_ggge *Table )Width ()float64 {return 0};func (_gfgfc *templateProcessor )parseChapterHeading (_feafb *templateNode )(interface{},error ){if _feafb ._gafge ==nil {_gfgfc .nodeLogError (_feafb ,"\u0043\u0068a\u0070\u0074\u0065\u0072 \u0068\u0065a\u0064\u0069\u006e\u0067\u0020\u0070\u0061\u0072e\u006e\u0074\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065 \u006e\u0069\u006c\u002e");
return nil ,_bbcfc ;};_fabac ,_fcebb :=_feafb ._gafge ._aeceb .(*Chapter );if !_fcebb {_gfgfc .nodeLogError (_feafb ,"\u0043h\u0061\u0070t\u0065\u0072\u0020h\u0065\u0061\u0064\u0069\u006e\u0067\u0020p\u0061\u0072\u0065\u006e\u0074\u0020(\u0025\u0054\u0029\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020a\u0020\u0063\u0068\u0061\u0070\u0074\u0065\u0072\u002e",_feafb ._gafge ._aeceb );
return nil ,_bbcfc ;};_ccbdg :=_fabac .GetHeading ();if _ ,_fbdb :=_gfgfc .parseParagraph (_feafb ,_ccbdg );_fbdb !=nil {return nil ,_fbdb ;};return _ccbdg ,nil ;};func (_edbfg *templateProcessor )parseInt64Array (_fcda ,_gcgga string )[]int64 {_a .Log .Debug ("\u0050\u0061\u0072s\u0069\u006e\u0067\u0020\u0069\u006e\u0074\u0036\u0034\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060%\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_fcda ,_gcgga );
_gfac :=_fc .Fields (_gcgga );_dgade :=make ([]int64 ,0,len (_gfac ));for _ ,_ddbee :=range _gfac {_gdaga ,_ :=_fbf .ParseInt (_ddbee ,10,64);_dgade =append (_dgade ,_gdaga );};return _dgade ;};

// NewImageFromFile creates an Image from a file.
func (_cfda *Creator )NewImageFromFile (path string )(*Image ,error ){return _egcgc (path )};func _gbdaa (_effbb string )([]string ,error ){if !_fe .ValidString (_effbb ){return []string {_effbb },_ee .New ("\u0069n\u0070\u0075\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020v\u0061\u006c\u0069\u0064\u0020\u0055\u0054\u0046\u002d\u0038");
};var (_ecgd []string ;_aadb []rune ;);for _ ,_ageecc :=range _effbb {if _ageecc =='\u000A'{if len (_aadb )> 0{_ecgd =append (_ecgd ,string (_aadb ));};_ecgd =append (_ecgd ,string (_ageecc ));_aadb =nil ;continue ;};_aadb =append (_aadb ,_ageecc );};if len (_aadb )> 0{_ecgd =append (_ecgd ,string (_aadb ));
};var _badg []string ;for _ ,_cabde :=range _ecgd {_ddbdb :=[]rune (_cabde );_aface :=_ce .NewScanner (_ddbdb );var _ggbec []rune ;for _gadbd :=0;_gadbd < len (_ddbdb );_gadbd ++{_ ,_affbf ,_bedce :=_aface .Next ();if _bedce !=nil {return nil ,_bedce ;
};if _affbf ==_ce .BreakProhibited ||_ec .IsSpace (_ddbdb [_gadbd ]){_ggbec =append (_ggbec ,_ddbdb [_gadbd ]);if _ec .IsSpace (_ddbdb [_gadbd ]){_badg =append (_badg ,string (_ggbec ));_ggbec =[]rune {};};continue ;}else {if len (_ggbec )> 0{_badg =append (_badg ,string (_ggbec ));
};_ggbec =[]rune {_ddbdb [_gadbd ]};};};if len (_ggbec )> 0{_badg =append (_badg ,string (_ggbec ));};};return _badg ,nil ;};

// SetStructureType sets the structure type for the PolyBezierCurve.
func (_gffd *PolyBezierCurve )SetStructureType (structureType _db .StructureType ){if _gffd ._ceefc ==nil {_gffd ._ceefc =_db .NewStructureTagInfo ();};_gffd ._ceefc .StructureType =structureType ;};

// SetAntiAlias enables anti alias config.
//
// Anti alias is disabled by default.
func (_daee *shading )SetAntiAlias (enable bool ){_daee ._efbd =enable };func (_abeb *Paragraph )wrapText ()error {if !_abeb ._gcdc ||int (_abeb ._becad )<=0{_abeb ._gbde =[]string {_abeb ._cegc };return nil ;};_bcdd :=NewTextChunk (_abeb ._cegc ,TextStyle {Font :_abeb ._aad ,FontSize :_abeb ._febee });
_dfab ,_aedc :=_bcdd .Wrap (_abeb ._becad );if _aedc !=nil {return _aedc ;};if _abeb ._fegae > 0&&len (_dfab )> _abeb ._fegae {_dfab =_dfab [:_abeb ._fegae ];};_abeb ._gbde =_dfab ;return nil ;};func _aaad (_aafa string )(*GraphicSVG ,error ){_edcf ,_deea :=ParseFromSVGString (_aafa );
if _deea !=nil {return nil ,_deea ;};return _gcbe (_edcf );};

// HorizontalAlignment represents the horizontal alignment of components
// within a page.
type HorizontalAlignment int ;

// Curve represents a cubic Bezier curve with a control point.
type Curve struct{_cfcbc float64 ;_fafc float64 ;_gebb float64 ;_feaa float64 ;_cbdb float64 ;_ddad float64 ;_gaab Color ;_aged float64 ;_fefg *_db .StructureTagInfo ;};

// SetLink makes the line an internal link.
// The text parameter represents the text that is displayed.
// The user is taken to the specified page, at the specified x and y
// coordinates. Position 0, 0 is at the top left of the page.
func (_feab *TOCLine )SetLink (page int64 ,x ,y float64 ){_feab ._fbcga =x ;_feab ._ebafe =y ;_feab ._ageffa =page ;_bacbec :=_feab ._geffaf ._ecff .Color ;_feab .Number .Style .Color =_bacbec ;_feab .Title .Style .Color =_bacbec ;_feab .Separator .Style .Color =_bacbec ;
_feab .Page .Style .Color =_bacbec ;};

// NewPage adds a new Page to the Creator and sets as the active Page.
func (_gcfd *Creator )NewPage ()*_db .PdfPage {_bbbd :=_gcfd .newPage ();_gcfd ._bcbe =append (_gcfd ._bcbe ,_bbbd );_gcfd ._fcga .Page ++;return _bbbd ;};

// AddTotalLine adds a new line in the invoice totals table.
func (_fafb *Invoice )AddTotalLine (desc ,value string )(*InvoiceCell ,*InvoiceCell ){_bgb :=&InvoiceCell {_fafb ._gdbce ,desc };_gebdb :=&InvoiceCell {_fafb ._gdbce ,value };_fafb ._efgb =append (_fafb ._efgb ,[2]*InvoiceCell {_bgb ,_gebdb });return _bgb ,_gebdb ;
};

// GeneratePageBlocks draws the chart onto a block.
func (_cce *Chart )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_eaf :=ctx ;_dad :=_cce ._gcac .IsRelative ();var _dege []*Block ;if _dad {_dcad :=1.0;_gef :=_cce ._ace .Top ;if float64 (_cce ._cegb .Height ())> ctx .Height -_cce ._ace .Top {_dege =[]*Block {NewBlock (ctx .PageWidth ,ctx .PageHeight -ctx .Y )};
var _cdef error ;if _ ,ctx ,_cdef =_aegb ().GeneratePageBlocks (ctx );_cdef !=nil {return nil ,ctx ,_cdef ;};_gef =0;};ctx .X +=_cce ._ace .Left +_dcad ;ctx .Y +=_gef ;ctx .Width -=_cce ._ace .Left +_cce ._ace .Right +2*_dcad ;ctx .Height -=_gef ;_cce ._cegb .SetWidth (int (ctx .Width ));
}else {ctx .X =_cce ._dfcb ;ctx .Y =_cce ._cda ;};_fef :=_eg .NewContentCreator ();if _cce ._aaf !=nil {_fef .Add_BDC (*_dd .MakeName (string (_cce ._aaf .StructureType )),map[string ]_dd .PdfObject {"\u004d\u0043\u0049\u0044":_dd .MakeInteger (_cce ._aaf .Mcid )});
};_fef .Translate (0,ctx .PageHeight );_fef .Scale (1,-1);_fef .Translate (ctx .X ,ctx .Y );_adaa :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_cce ._cegb .Render (_fcf .NewRenderer (_fef ,_adaa ._faa ),nil );if _cce ._aaf !=nil {_fef .Add_EMC ();};if _ebee :=_adaa .addContentsByString (_fef .String ());
_ebee !=nil {return nil ,ctx ,_ebee ;};if _dad {_gcded :=_cce .Height ()+_cce ._ace .Bottom ;ctx .Y +=_gcded ;ctx .Height -=_gcded ;}else {ctx =_eaf ;};_dege =append (_dege ,_adaa );return _dege ,ctx ,nil ;};

// CreateFrontPage sets a function to generate a front Page.
func (_afb *Creator )CreateFrontPage (genFrontPageFunc func (_cced FrontpageFunctionArgs )){_afb ._cga =genFrontPageFunc ;};

// SetNotes sets the notes section of the invoice.
func (_ccef *Invoice )SetNotes (title ,content string ){_ccef ._egfgg =[2]string {title ,content }};func (_fgefe *TOC )SetStructureType (structureType _db .StructureType ){};

// TableCell defines a table cell which can contain a Drawable as content.
type TableCell struct{_bfcef Color ;_ebbg float64 ;_cdadf _gc .LineStyle ;_cabbe CellBorderStyle ;_fdffg Color ;_gbgfg float64 ;_gecgee CellBorderStyle ;_cebb Color ;_cfcab float64 ;_bfaea CellBorderStyle ;_effdc Color ;_dccbf float64 ;_eafce CellBorderStyle ;
_dgdf Color ;_ebde float64 ;_efage ,_afdac int ;_cgedc int ;_edece int ;_fbaee VectorDrawable ;_dfdda CellHorizontalAlignment ;_adfcfg CellVerticalAlignment ;_eggag float64 ;_dcdcf *Table ;_cdffb *_db .StructureTagInfo ;};func (_daggd *templateProcessor )parseCellAlignmentAttr (_fcebc ,_cccc string )CellHorizontalAlignment {_a .Log .Debug ("\u0050a\u0072\u0073i\u006e\u0067\u0020c\u0065\u006c\u006c\u0020\u0061\u006c\u0069g\u006e\u006d\u0065\u006e\u0074\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0028`\u0025\u0073\u0060\u002c\u0020\u0025\u0073\u0029\u002e",_fcebc ,_cccc );
_fbgbf :=map[string ]CellHorizontalAlignment {"\u006c\u0065\u0066\u0074":CellHorizontalAlignmentLeft ,"\u0063\u0065\u006e\u0074\u0065\u0072":CellHorizontalAlignmentCenter ,"\u0072\u0069\u0067h\u0074":CellHorizontalAlignmentRight }[_cccc ];return _fbgbf ;
};

// Heading returns the heading component of the table of contents.
func (_afcde *TOC )Heading ()*StyledParagraph {return _afcde ._bcbeb };

// SetOpacity sets the cell's opacity in the range 0-1.
func (_dgbe *GridCell )SetOpacity (opacity float64 ){_dgbe ._ffgf =opacity };

// Lines returns all the rows of the invoice line items table.
func (_fgfc *Invoice )Lines ()[][]*InvoiceCell {return _fgfc ._gbeaa };func (_bcbb *Grid )insertRowAfter (_aebdg int ,_cace *GridRow ){_bcbb ._bgcb =append (_bcbb ._bgcb ,&GridRow {});copy (_bcbb ._bgcb [_aebdg +2:],_bcbb ._bgcb [_aebdg +1:]);_bcbb ._bgcb [_aebdg +1]=_cace ;
for _bcede ,_aeed :=range _bcbb ._bgcb {if _bcede > _aebdg +1{_aeed ._bacb +=1;for _ ,_beeb :=range _aeed ._fgfe {_beeb ._fcfg +=1;};};};};func (_agbffe *templateProcessor )parseMarginAttr (_fbffe ,_adbed string )Margins {_a .Log .Debug ("\u0050\u0061r\u0073\u0069\u006e\u0067 \u006d\u0061r\u0067\u0069\u006e\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0028\u0060\u0025\u0073\u0060\u002c \u0025\u0073\u0029\u002e",_fbffe ,_adbed );
_ceae :=Margins {};switch _bbfea :=_fc .Fields (_adbed );len (_bbfea ){case 1:_ceae .Top ,_ =_fbf .ParseFloat (_bbfea [0],64);_ceae .Bottom =_ceae .Top ;_ceae .Left =_ceae .Top ;_ceae .Right =_ceae .Top ;case 2:_ceae .Top ,_ =_fbf .ParseFloat (_bbfea [0],64);
_ceae .Bottom =_ceae .Top ;_ceae .Left ,_ =_fbf .ParseFloat (_bbfea [1],64);_ceae .Right =_ceae .Left ;case 3:_ceae .Top ,_ =_fbf .ParseFloat (_bbfea [0],64);_ceae .Left ,_ =_fbf .ParseFloat (_bbfea [1],64);_ceae .Right =_ceae .Left ;_ceae .Bottom ,_ =_fbf .ParseFloat (_bbfea [2],64);
case 4:_ceae .Top ,_ =_fbf .ParseFloat (_bbfea [0],64);_ceae .Right ,_ =_fbf .ParseFloat (_bbfea [1],64);_ceae .Bottom ,_ =_fbf .ParseFloat (_bbfea [2],64);_ceae .Left ,_ =_fbf .ParseFloat (_bbfea [3],64);};return _ceae ;};

// SetBorderColor sets the cell's border color.
func (_gccg *GridCell )SetBorderColor (col Color ){_gccg ._bdcg =col ;_gccg ._cbcd =col ;_gccg ._gegc =col ;_gccg ._edee =col ;};

// SetWidth sets the Paragraph width. This is essentially the wrapping width, i.e. the width the
// text can extend to prior to wrapping over to next line.
func (_eddfe *Paragraph )SetWidth (width float64 ){_eddfe ._becad =width ;_eddfe .wrapText ()};

// ToContentCreator convert SVG and add elements contentstream then returns `contentstream.ContentCreator`.
func (_adfeb *GraphicSVGElement )ToContentCreator (cc *_eg .ContentCreator ,res *_db .PdfPageResources ,scaleX ,scaleY ,translateX ,translateY float64 )*_eg .ContentCreator {if _adfeb .Name =="\u0073\u0076\u0067"{_adfeb .SetScaling (scaleX ,scaleY );cc .Add_cm (1,0,0,1,translateX ,translateY );
_adfeb .setDefaultScaling (_adfeb ._aece );cc .Add_q ();_gbeb :=_fa .Max (scaleX ,scaleY );cc .Add_re (_adfeb .ViewBox .X *_gbeb ,_adfeb .ViewBox .Y *_gbeb ,_adfeb .ViewBox .W *_gbeb ,_adfeb .ViewBox .H *_gbeb );cc .Add_W ();cc .Add_n ();_adfeb .processDefs ();
for _ ,_dgdc :=range _adfeb .Children {_dgdc .ViewBox =_adfeb .ViewBox ;_dgdc ._ccfaaf =_adfeb ._ccfaaf ;_dgdc ._gcdge =_adfeb ._gcdge ;_dgdc .toContentStream (cc ,res );};cc .Add_Q ();return cc ;};return nil ;};

// SetAngle sets the rotation angle of the text.
func (_fdbg *StyledParagraph )SetAngle (angle float64 ){_fdbg ._bbacbd =angle };func (_debff *templateProcessor )getNodeErrorLocation (_ecge *templateNode ,_cbcgg string ,_ffee ...interface{})string {_cbfac :=_g .Sprintf (_cbcgg ,_ffee ...);_begdb :=_g .Sprintf ("\u0025\u0064",_ecge ._abbc );
if _ecge ._eagge !=0{_begdb =_g .Sprintf ("\u0025\u0064\u003a%\u0064",_ecge ._eagge ,_ecge ._adafb );};if _debff ._edcc !=""{return _g .Sprintf ("\u0025\u0073\u0020\u005b\u0025\u0073\u003a\u0025\u0073\u005d",_cbfac ,_debff ._edcc ,_begdb );};return _g .Sprintf ("\u0025s\u0020\u005b\u0025\u0073\u005d",_cbfac ,_begdb );
};

// SetFillOpacity sets the fill opacity.
func (_fab *CurvePolygon )SetFillOpacity (opacity float64 ){_fab ._feece =opacity };

// SetStructureType sets the structure type for the polyline.
func (_bfbd *Polyline )SetStructureType (structureType _db .StructureType ){if _bfbd ._bafaf ==nil {_bfbd ._bafaf =_db .NewStructureTagInfo ();};_bfbd ._bafaf .StructureType =structureType ;};

// GeneratePageBlocks generates the page blocks.  Multiple blocks are generated if the contents wrap
// over multiple pages. Implements the Drawable interface.
func (_geegad *Paragraph )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_baec :=ctx ;var _dcgc []*Block ;_ffbd :=NewBlock (ctx .PageWidth ,ctx .PageHeight );if _geegad ._aecc .IsRelative (){ctx .X +=_cg .RoundDefault (_geegad ._fbgdf .Left );
ctx .Y +=_cg .RoundDefault (_geegad ._fbgdf .Top );ctx .Width -=_cg .RoundDefault (_geegad ._fbgdf .Left +_geegad ._fbgdf .Right );ctx .Height -=_cg .RoundDefault (_geegad ._fbgdf .Top );_geegad .SetWidth (ctx .Width );if _geegad .Height ()> ctx .Height {_dcgc =append (_dcgc ,_ffbd );
_ffbd =NewBlock (ctx .PageWidth ,ctx .PageHeight );ctx .Page ++;_becb :=ctx ;_becb .Y =_cg .RoundDefault (ctx .Margins .Top );_becb .X =_cg .RoundDefault (ctx .Margins .Left +_geegad ._fbgdf .Left );_becb .Height =_cg .RoundDefault (ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom );
_becb .Width =_cg .RoundDefault (ctx .PageWidth -ctx .Margins .Left -ctx .Margins .Right -_geegad ._fbgdf .Left -_geegad ._fbgdf .Right );ctx =_becb ;};}else {if int (_geegad ._becad )<=0{_geegad .SetWidth (_geegad .getTextWidth ());};ctx .X =_geegad ._eedf ;
ctx .Y =_geegad ._cgdg ;};ctx ,_ecdb :=_ccdff (_ffbd ,_geegad ,ctx );if _ecdb !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_ecdb );return nil ,ctx ,_ecdb ;};_dcgc =append (_dcgc ,_ffbd );if _geegad ._aecc .IsRelative (){ctx .Y +=_cg .RoundDefault (_geegad ._fbgdf .Bottom );
ctx .Height -=_cg .RoundDefault (_geegad ._fbgdf .Bottom );if !ctx .Inline {ctx .X =_baec .X ;ctx .Width =_baec .Width ;};return _dcgc ,ctx ,nil ;};return _dcgc ,_baec ,nil ;};

// Path is a collection of all the subpaths in 'd' attribute.
type Path struct{Subpaths []*Subpath ;};

// Subpath is a collection of Commands, beginning with moveto command and
// usually ending with closepath command.
type Subpath struct{Commands []*Command ;};

// Width returns the width of the graphic svg.
func (_edcg *GraphicSVG )Width ()float64 {return _edcg ._bdca .Width };func (_ebfbb *Grid )SetStructureType (structureType _db .StructureType ){};

// DashPattern returns the dash pattern of the line.
func (_bgbd *Line )DashPattern ()(_deegf []int64 ,_gggf int64 ){return _bgbd ._feda ,_bgbd ._fafgc };

// SetAntiAlias enables anti alias config.
//
// Anti alias is disabled by default.
func (_acgdb *RadialShading )SetAntiAlias (enable bool ){_acgdb ._aada .SetAntiAlias (enable )};type pageTransformations struct{_ddae *_bce .Matrix ;_egeb bool ;_aeg bool ;};func (_babcg *templateProcessor )parseChapter (_debca *templateNode )(interface{},error ){_eabbf :=_babcg .creator .NewChapter ;
if _debca ._gafge !=nil {if _caaa ,_ceeb :=_debca ._gafge ._aeceb .(*Chapter );_ceeb {_eabbf =_caaa .NewSubchapter ;};};_bcgbd :=_eabbf ("");for _ ,_gffa :=range _debca ._ccge .Attr {_bdacc :=_gffa .Value ;switch _fbea :=_gffa .Name .Local ;_fbea {case "\u0073\u0068\u006f\u0077\u002d\u006e\u0075\u006d\u0062e\u0072\u0069\u006e\u0067":_bcgbd .SetShowNumbering (_babcg .parseBoolAttr (_fbea ,_bdacc ));
case "\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u002d\u0069n\u002d\u0074\u006f\u0063":_bcgbd .SetIncludeInTOC (_babcg .parseBoolAttr (_fbea ,_bdacc ));case "\u006d\u0061\u0072\u0067\u0069\u006e":_fbeda :=_babcg .parseMarginAttr (_fbea ,_bdacc );_bcgbd .SetMargins (_fbeda .Left ,_fbeda .Right ,_fbeda .Top ,_fbeda .Bottom );
default:_babcg .nodeLogDebug (_debca ,"\u0055\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0063\u0068\u0061\u0070\u0074\u0065\u0072\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020\u0060\u0025\u0073\u0060\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u002e",_fbea );
};};return _bcgbd ,nil ;};

// NewStyledParagraph creates a new styled paragraph.
// Default attributes:
// Font: Helvetica,
// Font size: 10
// Encoding: WinAnsiEncoding
// Wrap: enabled
// Text color: black
func (_agbaa *Creator )NewStyledParagraph ()*StyledParagraph {return _gcefe (_agbaa .NewTextStyle ())};

// SetStructureType sets the structure type for the Polygon.
func (_cfcbd *Polygon )SetStructureType (structureType _db .StructureType ){if _cfcbd ._daega ==nil {_cfcbd ._daega =_db .NewStructureTagInfo ();};_cfcbd ._daega .StructureType =structureType ;};

// TextOverflow determines the behavior of paragraph text which does
// not fit in the available space.
type TextOverflow int ;

// SetHeight sets the height of the ellipse.
func (_aeca *Ellipse )SetHeight (height float64 ){_aeca ._gaabd =height };const (TextVerticalAlignmentBaseline TextVerticalAlignment =iota ;TextVerticalAlignmentCenter ;TextVerticalAlignmentBottom ;TextVerticalAlignmentTop ;);

// SetFontSize sets the font size in document units (points).
func (_dabb *Paragraph )SetFontSize (fontSize float64 ){_dabb ._febee =fontSize };

// AddShadingResource adds shading dictionary inside the resources dictionary.
func (_ecbda *RadialShading )AddShadingResource (block *Block )(_acgc _dd .PdfObjectName ,_dfac error ){_fadf :=1;_acgc =_dd .PdfObjectName ("\u0053\u0068"+_fbf .Itoa (_fadf ));for block ._faa .HasShadingByName (_acgc ){_fadf ++;_acgc =_dd .PdfObjectName ("\u0053\u0068"+_fbf .Itoa (_fadf ));
};if _fbfg :=block ._faa .SetShadingByName (_acgc ,_ecbda .shadingModel ().ToPdfObject ());_fbfg !=nil {return "",_fbfg ;};return _acgc ,nil ;};

// SetStructureType sets the structure type for the image.
func (_cdbd *Image )SetStructureType (structureType _db .StructureType ){if _cdbd ._fecd ==nil {_cdbd ._fecd =_db .NewStructureTagInfo ();};_cdbd ._fecd .StructureType =structureType ;};func (_dbac *Invoice )newCell (_aaea string ,_gbaaf InvoiceCellProps )*InvoiceCell {return &InvoiceCell {_gbaaf ,_aaea };
};

// Height returns the total height of all rows.
func (_gbaea *Grid )Height ()float64 {_dbbgb :=float64 (0.0);for _ ,_gacc :=range _gbaea ._bgcb {_dbbgb +=_gacc ._deege ;};return _dbbgb ;};

// GeneratePageBlocks draw graphic svg into block.
func (_gffe *GraphicSVG )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_fbcg :=ctx ;_ebcf :=_gffe ._cgd .IsRelative ();var _bfac []*Block ;if _ebcf {_ecada :=1.0;_eeff :=_gffe ._edeb .Top ;if _gffe ._bdca .Height > ctx .Height -_gffe ._edeb .Top {_bfac =[]*Block {NewBlock (ctx .PageWidth ,ctx .PageHeight -ctx .Y )};
var _bffg error ;if _ ,ctx ,_bffg =_aegb ().GeneratePageBlocks (ctx );_bffg !=nil {return nil ,ctx ,_bffg ;};_eeff =0;};ctx .X +=_gffe ._edeb .Left +_ecada ;ctx .Y +=_eeff ;ctx .Width -=_gffe ._edeb .Left +_gffe ._edeb .Right +2*_ecada ;ctx .Height -=_eeff ;
}else {ctx .X =_gffe ._edbc ;ctx .Y =_gffe ._cbdfc ;};_dgdd :=_eg .NewContentCreator ();_dgdd .Translate (0,ctx .PageHeight );_dgdd .Scale (1,-1);_dgdd .Translate (ctx .X ,ctx .Y );_eeacc :=_gffe ._bdca .Width /_gffe ._bdca .ViewBox .W ;_acee :=_gffe ._bdca .Height /_gffe ._bdca .ViewBox .H ;
_eacg :=0.0;_edbcd :=0.0;if _ebcf {_eacg =_gffe ._edbc -(_gffe ._bdca .ViewBox .X *_fa .Max (_eeacc ,_acee ));_edbcd =_gffe ._cbdfc -(_gffe ._bdca .ViewBox .Y *_fa .Max (_eeacc ,_acee ));};_fadg :=NewBlock (ctx .PageWidth ,ctx .PageHeight );if _gffe ._fdcf !=nil {_dgdd .Add_BDC (*_dd .MakeName (string (_gffe ._fdcf .StructureType )),map[string ]_dd .PdfObject {"\u004d\u0043\u0049\u0044":_dd .MakeInteger (_gffe ._fdcf .Mcid )});
};_gffe ._bdca .SetPos (ctx .X ,ctx .Y );_gffe ._bdca .ToContentCreator (_dgdd ,_fadg ._faa ,_eeacc ,_acee ,_eacg ,_edbcd );if _gffe ._fdcf !=nil {_dgdd .Add_EMC ();};if _befa :=_fadg .addContentsByString (_dgdd .String ());_befa !=nil {return nil ,ctx ,_befa ;
};if _ebcf {_bcf :=_gffe .Height ()+_gffe ._edeb .Bottom ;ctx .Y +=_bcf ;ctx .Height -=_bcf ;}else {ctx =_fbcg ;};_bfac =append (_bfac ,_fadg );return _bfac ,ctx ,nil ;};

// NewChapter creates a new chapter with the specified title as the heading.
func (_gadc *Creator )NewChapter (title string )*Chapter {_gadc ._egfd ++;_fege :=_gadc .NewTextStyle ();_fege .FontSize =16;return _fag (nil ,_gadc ._dbaf ,_gadc ._gcbc ,title ,_gadc ._egfd ,_fege );};func (_deeea *Paragraph )getMaxLineWidth ()float64 {if _deeea ._gbde ==nil ||(_deeea ._gbde !=nil &&len (_deeea ._gbde )==0){_deeea .wrapText ();
};var _ddcea float64 ;for _ ,_efbcd :=range _deeea ._gbde {_fgabb :=_deeea .getTextLineWidth (_efbcd );if _fgabb > _ddcea {_ddcea =_fgabb ;};};return _ddcea ;};func (_geda *GraphicSVGElement )parseColorPoints ()[]*ColorPoint {var _gaac []*ColorPoint ;var _eefee error ;
for _ ,_baafb :=range _geda .Children {if _baafb .Name =="\u0073\u0074\u006f\u0070"{_begb :=ColorBlack ;_fggf :=0.0;for _bceb ,_adcc :=range _baafb .Attributes {if _bceb =="\u006f\u0066\u0066\u0073\u0065\u0074"{if _fc .HasSuffix (_adcc ,"\u0025"){_abae ,_aeef :=_fbf .ParseFloat (_fc .TrimSuffix (_adcc ,"\u0025"),64);
if _aeef !=nil {continue ;};_fggf =_abae /100;}else {_fggf ,_eefee =_fbf .ParseFloat (_adcc ,64);if _eefee !=nil {continue ;};};}else if _bceb =="\u0073\u0074\u006f\u0070\u002d\u0063\u006f\u006c\u006f\u0072"{if _adcc [0]=='#'{_begb =ColorRGBFromHex (_adcc );
}else {_fecf ,_deac :=_ba .ColorMap [_adcc ];if _deac {_begb =ColorRGBFrom8bit (_fecf .R ,_fecf .G ,_fecf .B );};};};};_gaac =append (_gaac ,NewColorPoint (_begb ,_fggf ));};};return _gaac ;};

// SetBoundingBox set gradient color bounding box where the gradient would be rendered.
func (_feecg *LinearShading )SetBoundingBox (x ,y ,width ,height float64 ){_feecg ._effcg =&_db .PdfRectangle {Llx :x ,Lly :y ,Urx :x +width ,Ury :y +height };};

// SetLineColor sets the line color.
func (_gccd *Polyline )SetLineColor (color Color ){_gccd ._dabba .LineColor =_cdfe (color )};

// SetMaxLines sets the maximum number of lines before the paragraph
// text is truncated.
func (_fagf *Paragraph )SetMaxLines (maxLines int ){_fagf ._fegae =maxLines ;_fagf .wrapText ()};

// TextVerticalAlignment controls the vertical position of the text
// in a styled paragraph.
type TextVerticalAlignment int ;

// SetLevelOffset sets the amount of space an indentation level occupies.
func (_bbgcd *TOCLine )SetLevelOffset (levelOffset float64 ){_bbgcd ._acade =levelOffset ;_bbgcd ._geffaf ._feeea .Left =_bbgcd ._dacdb +float64 (_bbgcd ._aeaad -1)*_bbgcd ._acade ;};

// SetFillColor sets the fill color of the rectangle.
func (_fbee *Rectangle )SetFillColor (col Color ){_fbee ._ggac =col };

// Opacity returns the opacity of the line.
func (_ffceg *Line )Opacity ()float64 {return _ffceg ._ffade };

// Flip flips the active page on the specified axes.
// If `flipH` is true, the page is flipped horizontally. Similarly, if `flipV`
// is true, the page is flipped vertically. If both are true, the page is
// flipped both horizontally and vertically.
// NOTE: the flip transformations are applied when the creator is finalized,
// which is at write time in most cases.
func (_fdee *Creator )Flip (flipH ,flipV bool )error {_agbg :=_fdee .getActivePage ();if _agbg ==nil {return _ee .New ("\u006e\u006f\u0020\u0070\u0061\u0067\u0065\u0020\u0061c\u0074\u0069\u0076\u0065");};_bac ,_adfe :=_fdee ._fbgd [_agbg ];if !_adfe {_bac =&pageTransformations {};
_fdee ._fbgd [_agbg ]=_bac ;};_bac ._egeb =flipH ;_bac ._aeg =flipV ;return nil ;};

// SetHorizontalAlignment sets the cell's horizontal alignment of content.
// Can be one of:
// - CellHorizontalAlignmentLeft
// - CellHorizontalAlignmentCenter
// - CellHorizontalAlignmentRight
func (_cegde *TableCell )SetHorizontalAlignment (halign CellHorizontalAlignment ){_cegde ._dfdda =halign ;};func _bgcca (_bfcd []token )([]*Command ,error ){var (_edegf []*Command ;_gfedd []float64 ;);for _edbce :=len (_bfcd )-1;_edbce >=0;_edbce --{_ceccb :=_bfcd [_edbce ];
if _ceccb ._ecccc {_ceea :=_gfdbca ._ddgaa [_fc .ToLower (_ceccb ._bcdfe )];_begd :=len (_gfedd );if _ceea ==0&&_begd ==0{_abbef :=&Command {Symbol :_ceccb ._bcdfe };_edegf =append ([]*Command {_abbef },_edegf ...);}else if _ceea !=0&&_begd %_ceea ==0{_aafd :=_begd /_ceea ;
for _gddg :=0;_gddg < _aafd ;_gddg ++{_geggb :=_ceccb ._bcdfe ;if _geggb =="\u006d"&&_gddg < _aafd -1{_geggb ="\u006c";};if _geggb =="\u004d"&&_gddg < _aafd -1{_geggb ="\u004c";};_bcaa :=&Command {_geggb ,_egfe (_gfedd [:_ceea ])};_edegf =append ([]*Command {_bcaa },_edegf ...);
_gfedd =_gfedd [_ceea :];};}else {_ebggf :=pathParserError {"I\u006e\u0063\u006f\u0072\u0072\u0065c\u0074\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u006f\u0066\u0020\u0070\u0061r\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006fr\u0020"+_ceccb ._bcdfe };
return nil ,_ebggf ;};}else {_eaadaa ,_gedee :=_cacac (_ceccb ._bcdfe ,64);if _gedee !=nil {return nil ,_gedee ;};_gfedd =append (_gfedd ,_eaadaa );};};return _edegf ,nil ;};

// ScaleToWidth sets the graphic svg scaling factor with the given width.
func (_dgdb *GraphicSVG )ScaleToWidth (w float64 ){_abcc :=_dgdb ._bdca .Height /_dgdb ._bdca .Width ;_dgdb ._bdca .Width =w ;_dgdb ._bdca .Height =w *_abcc ;_dgdb ._bdca .SetScaling (_abcc ,_abcc );};

// GetHorizontalAlignment returns the horizontal alignment of the image.
func (_bbacb *Image )GetHorizontalAlignment ()HorizontalAlignment {return _bbacb ._aagg };

// Margins returns the margins of the list: left, right, top, bottom.
func (_bdag *List )Margins ()(float64 ,float64 ,float64 ,float64 ){return _bdag ._ddggd .Left ,_bdag ._ddggd .Right ,_bdag ._ddggd .Top ,_bdag ._ddggd .Bottom ;};func _eafbb (_bcbf int )*Grid {_adcag :=&Grid {_cecb :_bcbf ,_gfeb :10.0,_daad :[]float64 {}};
_adcag .resetColumnWidths ();return _adcag ;};

// SetLanguageIdentifier sets the language identifier for the paragraph.
func (_ddfef *StyledParagraph )SetLanguageIdentifier (id string ){if _ddfef ._gbfc ==nil {_ddfef ._gbfc =_db .NewStructureTagInfo ();_ddfef ._gbfc .StructureType =_db .StructureTypeParagraph ;};_ddfef ._eafc =id ;};func _gcdfe (_adaea *_eb .File )([]*_db .PdfPage ,error ){_cbbe ,_ddccg :=_db .NewPdfReader (_adaea );
if _ddccg !=nil {return nil ,_ddccg ;};_bcbge ,_ddccg :=_cbbe .GetNumPages ();if _ddccg !=nil {return nil ,_ddccg ;};var _deebb []*_db .PdfPage ;for _defb :=0;_defb < _bcbge ;_defb ++{_cdag ,_ccdcb :=_cbbe .GetPage (_defb +1);if _ccdcb !=nil {return nil ,_ccdcb ;
};_deebb =append (_deebb ,_cdag );};return _deebb ,nil ;};func (_addd *Creator )wrapPageIfNeeded (_cfbc *_db .PdfPage )(*_db .PdfPage ,error ){_ebegg ,_ece :=_cfbc .GetAllContentStreams ();if _ece !=nil {return nil ,_ece ;};_cdeb :=_eg .NewContentStreamParser (_ebegg );
_dfdd ,_ece :=_cdeb .Parse ();if _ece !=nil {return nil ,_ece ;};if !_dfdd .HasUnclosedQ (){return nil ,nil ;};_dfdd .WrapIfNeeded ();_cff ,_ece :=_dd .MakeStream (_dfdd .Bytes (),_dd .NewFlateEncoder ());if _ece !=nil {return nil ,_ece ;};_cfbc .Contents =_dd .MakeArray (_cff );
return _cfbc ,nil ;};func _gbecf (_degc *templateProcessor ,_cecga *templateNode )(interface{},error ){return _degc .parseDivision (_cecga );};

// SetWidth sets the width of the rectangle.
func (_ceadec *Rectangle )SetWidth (width float64 ){_ceadec ._cdcgg =width };const (TextRenderingModeFill TextRenderingMode =iota ;TextRenderingModeStroke ;TextRenderingModeFillStroke ;TextRenderingModeInvisible ;TextRenderingModeFillClip ;TextRenderingModeStrokeClip ;
TextRenderingModeFillStrokeClip ;TextRenderingModeClip ;);

// RadialShading holds information that will be used to render a radial shading.
type RadialShading struct{_aada *shading ;_gcdbd *_db .PdfRectangle ;_edfg AnchorPoint ;_ccdef float64 ;_agafb float64 ;_begab float64 ;_fceg float64 ;};func _aegb ()*PageBreak {return &PageBreak {}};

// GeneratePageBlocks generates the grid page blocks. Multiple blocks are
// generated if the contents wrap over multiple pages.
// Implements the Drawable interface.
func (_gfde *Grid )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){var _cfbe []*Block ;_efb :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_gfde .updateRowHeights (ctx .Width -_gfde ._cbdfcg .Left -_gfde ._cbdfcg .Right );_gcabe :=_gfde ._cbdfcg .Top ;
_bfeb :=ctx ;if _gfde ._cgaf .IsAbsolute (){ctx .X =_gfde ._edfdg ;ctx .Y =_gfde ._dbbgd ;}else {ctx .X +=_gfde ._cbdfcg .Left ;ctx .Y +=_gcabe ;ctx .Width -=_gfde ._cbdfcg .Left +_gfde ._cbdfcg .Right ;ctx .Height -=_gcabe ;};_eaagb :=ctx .Width ;_fdgg :=ctx .X ;
_dcaa :=ctx .Y ;_dcdc :=ctx .Height ;_dbbb :=0;_effd :=false ;for _eada :=0;_eada < len (_gfde ._bgcb );_eada ++{_gceg :=_gfde ._bgcb [_eada ];_fccdd :=float64 (0.0);for _cdeg :=_dbbb ;_cdeg < _gceg ._bacb ;_cdeg ++{_fccdd +=_gfde ._bgcb [_cdeg ]._fega ;
};ctx .Height =_dcdc -_fccdd ;if _effd {_cfbe =append (_cfbe ,_efb );_efb =NewBlock (ctx .PageWidth ,ctx .PageHeight );_fdgg =ctx .Margins .Left +_gfde ._cbdfcg .Left ;_dcaa =ctx .Margins .Top ;ctx .Height =ctx .PageHeight -ctx .Margins .Top -ctx .Margins .Bottom ;
ctx .Page ++;_dcdc =ctx .Height ;_dbbb =_eada ;_fccdd =0;_effd =false ;};if _gceg ._fega > ctx .Height {_ccag :=_gfde .cloneRow (_gceg ._bacb );_abbg :=false ;for _cfaaa ,_efaac :=range _gceg ._fgfe {switch _dbeeg :=_efaac ._cdaac .(type ){case *StyledParagraph :_abaf :=ctx ;
_abaf .Height =_fa .Floor (ctx .Height -_dbeeg ._feeea .Top -_dbeeg ._feeea .Bottom -0.5*_dbeeg .getTextHeight ());_gggd ,_dgcaf ,_fabd :=_dbeeg .split (_abaf );if _fabd !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_fabd );};if _gggd !=nil &&_dgcaf !=nil &&_ccag !=nil {_efaac .SetContent (_gggd );
_ccag ._fgfe [_cfaaa ].SetContent (_dgcaf );_abbg =true ;};case *Division :_dgfcb :=ctx ;_dgfcb .Height =_fa .Floor (ctx .Height -_dbeeg ._fccaf .Top -_dbeeg ._fccaf .Bottom );_bddbd ,_cadcg :=_dbeeg .split (_dgfcb );if _bddbd !=nil &&_cadcg !=nil &&_ccag !=nil {_efaac .SetContent (_bddbd );
_ccag ._fgfe [_cfaaa ].SetContent (_cadcg );_abbg =true ;};case *List :_gbdac :=ctx ;_gbdac .Height =_fa .Floor (ctx .Height -_dbeeg ._ddggd .Vertical ());_gdad ,_gfaf :=_dbeeg .split (_gbdac );if _gdad !=nil &&_gfaf !=nil &&_ccag !=nil {_efaac .SetContent (_gdad );
_ccag ._fgfe [_cfaaa ].SetContent (_gfaf );_abbg =true ;};};};_effd =true ;if _abbg {_gfde .insertRowAfter (_gceg ._bacb ,_ccag );_gceg .updateRowHeight (_eaagb );_ccag .updateRowHeight (_eaagb );}else {_eada -=1;continue ;};};for _ ,_cfcdd :=range _gceg ._fgfe {_fcaa :=_cfcdd .width (_gfde ._daad ,_eaagb );
_ade :=float64 (0.0);for _dfaa :=0;_dfaa < _cfcdd ._gcfa ;_dfaa ++{_ade +=_gfde ._daad [_dfaa ]*_eaagb ;};_ddebd :=float64 (0.0);for _edeea :=0;_edeea < _cfcdd ._fcbe ;_edeea ++{_ddebd +=_gfde ._bgcb [_cfcdd ._fcfg +_edeea ]._fega ;};ctx .Width =_fcaa ;
ctx .X =_fdgg +_ade ;ctx .Y =_dcaa +_fccdd ;_bgcf :=_fddd (ctx .X ,ctx .Y ,_fcaa ,_ddebd );if _cfcdd ._ddgeb !=nil {_bgcf .SetFillColor (_cfcdd ._ddgeb );};_bgcf .SetOpacity (_cfcdd ._ffgf );_bgcf .LineStyle =_cfcdd ._ccc ;_bgcf ._cdd =_cfcdd ._bddf ;_bgcf ._egc =_cfcdd ._eefb ;
_bgcf ._fdd =_cfcdd ._egca ;_bgcf ._cgbc =_cfcdd ._eaeb ;if _cfcdd ._bdcg !=nil {_bgcf .SetColorLeft (_cfcdd ._bdcg );};if _cfcdd ._cbcd !=nil {_bgcf .SetColorBottom (_cfcdd ._cbcd );};if _cfcdd ._gegc !=nil {_bgcf .SetColorRight (_cfcdd ._gegc );};if _cfcdd ._edee !=nil {_bgcf .SetColorTop (_cfcdd ._edee );
};_bgcf .SetWidthBottom (_cfcdd ._acbe );_bgcf .SetWidthLeft (_cfcdd ._gffc );_bgcf .SetWidthRight (_cfcdd ._feee );_bgcf .SetWidthTop (_cfcdd ._bfcc );_gcegf :=_efb .Draw (_bgcf );if _gcegf !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_gcegf );
};if _cfcdd ._cdaac !=nil {_ceda :=_cfcdd ._cdaac .Width ();_faef :=_cfcdd ._cdaac .Height ();_afg :=0.0;switch _ffga :=_cfcdd ._cdaac .(type ){case *Paragraph :if _ffga ._gcdc {_ceda =_ffga .getMaxLineWidth ()/1000.0;};_edad ,_eddca ,_ :=_ffga .getTextMetrics ();
_fdfc ,_fafda :=_edad *_ffga ._gafdf ,_eddca *_ffga ._gafdf ;_faef =_faef -_fafda +_fdfc ;_afg +=_fdfc -_fafda ;_ebge :=0.5;switch _cfcdd ._fbbd {case CellVerticalAlignmentTop :_afg +=_fdfc *_ebge ;case CellVerticalAlignmentBottom :_afg -=_fdfc *_ebge ;
};_ceda +=_ffga ._fbgdf .Left +_ffga ._fbgdf .Right ;_faef +=_ffga ._fbgdf .Top +_ffga ._fbgdf .Bottom ;case *StyledParagraph :if _ffga ._gfce {_ceda =_ffga .getMaxLineWidth ()/1000.0;};_efca ,_egdde ,_fbab :=_ffga .getLineMetrics (0);_cgda ,_acgd :=_efca *_ffga ._abbd ,_egdde *_ffga ._abbd ;
if _ffga ._bded ==TextVerticalAlignmentCenter {_afg =_acgd -(_egdde +(_efca +_fbab -_egdde )/2+(_acgd -_egdde )/2);};if len (_ffga ._cbba )==1{_faef =_cgda ;}else {_faef =_faef -_acgd +_cgda ;};_afg +=_cgda -_acgd ;switch _cfcdd ._fbbd {case CellVerticalAlignmentTop :_afg +=_cgda *0.5;
case CellVerticalAlignmentBottom :_afg -=_cgda *0.5;};_ceda +=_ffga ._feeea .Left +_ffga ._feeea .Right ;_faef +=_ffga ._feeea .Top +_ffga ._feeea .Bottom ;case *Table :_ceda =_fcaa ;case *List :_ceda =_fcaa ;case *Division :_ceda =_fcaa ;case *Chart :_ceda =_fcaa ;
case *Line :_faef +=_ffga ._fffd .Top +_ffga ._fffd .Bottom ;_afg -=_ffga .Height ()/2;case *Image :_ceda +=_ffga ._efde .Left +_ffga ._efde .Right ;_faef +=_ffga ._efde .Top +_ffga ._efde .Bottom ;};switch _cfcdd ._efab {case CellHorizontalAlignmentLeft :ctx .X +=_cfcdd ._ddca ;
ctx .Width -=_cfcdd ._ddca ;case CellHorizontalAlignmentCenter :if _bebc :=_fcaa -_ceda ;_bebc > 0{ctx .X +=_bebc /2;ctx .Width -=_bebc /2;};case CellHorizontalAlignmentRight :if _fcaa > _ceda {ctx .X =ctx .X +_fcaa -_ceda -_cfcdd ._ddca ;ctx .Width -=_cfcdd ._ddca ;
};};_abfe :=ctx .Y ;_bcde :=ctx .Height ;ctx .Y +=_afg ;switch _cfcdd ._fbbd {case CellVerticalAlignmentTop :case CellVerticalAlignmentMiddle :if _aebgb :=_ddebd -_faef ;_aebgb > 0{ctx .Y +=_aebgb /2;ctx .Height -=_aebgb /2;};case CellVerticalAlignmentBottom :if _ddebd > _faef {ctx .Y =ctx .Y +_ddebd -_faef ;
ctx .Height =_ddebd ;};};_dgff :=_efb .DrawWithContext (_cfcdd ._cdaac ,ctx );if _dgff !=nil {_a .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_dgff );};ctx .Y =_abfe ;ctx .Height =_bcde ;};};ctx .Y +=_gceg ._fega ;};_cfbe =append (_cfbe ,_efb );
if _gfde ._cgaf .IsAbsolute (){return _cfbe ,_bfeb ,nil ;};ctx .X =_bfeb .X ;ctx .Width =_bfeb .Width ;ctx .Y +=_gfde ._cbdfcg .Bottom ;ctx .Height -=_gfde ._cbdfcg .Bottom ;return _cfbe ,ctx ,nil ;};

// SetBorderColor sets the border color for the path.
func (_bgdg *FilledCurve )SetBorderColor (color Color ){_bgdg ._ccde =color };

// SetFitMode sets the fit mode of the image.
// NOTE: The fit mode is only applied if relative positioning is used.
func (_afcg *Image )SetFitMode (fitMode FitMode ){_afcg ._dddb =fitMode };func (_gafe rgbColor )ToRGB ()(float64 ,float64 ,float64 ){return _gafe ._dbbe ,_gafe ._bggf ,_gafe ._aec ;};

// Positioning returns the type of positioning the rectangle is set to use.
func (_dddc *Rectangle )Positioning ()Positioning {return _dddc ._fbbdf };

// GeneratePageBlocks draws the composite curve polygon on a new block
// representing the page. Implements the Drawable interface.
func (_gbba *CurvePolygon )GeneratePageBlocks (ctx DrawContext )([]*Block ,DrawContext ,error ){_fdbbg :=NewBlock (ctx .PageWidth ,ctx .PageHeight );_baad ,_gdff :=_fdbbg .setOpacity (_gbba ._feece ,_gbba ._gafd );if _gdff !=nil {return nil ,ctx ,_gdff ;
};_eebf :=_gbba ._accc ;_eebf .FillEnabled =_eebf .FillColor !=nil ;_eebf .BorderEnabled =_eebf .BorderColor !=nil &&_eebf .BorderWidth > 0;var (_ddbg =ctx .PageHeight ;_faee =_eebf .Rings ;_acebb =make ([][]_gc .CubicBezierCurve ,0,len (_eebf .Rings ));
);_abcg :=_db .PdfRectangle {};if len (_faee )> 0&&len (_faee [0])> 0{_bbff :=_faee [0][0];_bbff .P0 .Y =_ddbg -_bbff .P0 .Y ;_bbff .P1 .Y =_ddbg -_bbff .P1 .Y ;_bbff .P2 .Y =_ddbg -_bbff .P2 .Y ;_bbff .P3 .Y =_ddbg -_bbff .P3 .Y ;_abcg =_bbff .GetBounds ();
};for _ ,_dcged :=range _faee {_dbba :=make ([]_gc .CubicBezierCurve ,0,len (_dcged ));for _ ,_gdbe :=range _dcged {_ggece :=_gdbe ;_ggece .P0 .Y =_ddbg -_ggece .P0 .Y ;_ggece .P1 .Y =_ddbg -_ggece .P1 .Y ;_ggece .P2 .Y =_ddbg -_ggece .P2 .Y ;_ggece .P3 .Y =_ddbg -_ggece .P3 .Y ;
_dbba =append (_dbba ,_ggece );_deed :=_ggece .GetBounds ();_abcg .Llx =_fa .Min (_abcg .Llx ,_deed .Llx );_abcg .Lly =_fa .Min (_abcg .Lly ,_deed .Lly );_abcg .Urx =_fa .Max (_abcg .Urx ,_deed .Urx );_abcg .Ury =_fa .Max (_abcg .Ury ,_deed .Ury );};_acebb =append (_acebb ,_dbba );
};_eebf .Rings =_acebb ;defer func (){_eebf .Rings =_faee }();if _eebf .FillEnabled {_geeaf :=_gfae (_fdbbg ,_gbba ._accc .FillColor ,_gbba ._gbabf ,func ()Rectangle {return Rectangle {_eeadac :_abcg .Llx ,_abegd :_abcg .Lly ,_cdcgg :_abcg .Width (),_cfedb :_abcg .Height ()};
});if _geeaf !=nil {return nil ,ctx ,_geeaf ;};};_decf ,_ ,_gdff :=_eebf .MarkedDraw (_baad ,_gbba ._ceada );if _gdff !=nil {return nil ,ctx ,_gdff ;};if _gdff =_fdbbg .addContentsByString (string (_decf ));_gdff !=nil {return nil ,ctx ,_gdff ;};return []*Block {_fdbbg },ctx ,nil ;
};func (_agfbb *Rectangle )applyFitMode (_edagc float64 ){_edagc -=_agfbb ._abeec .Left +_agfbb ._abeec .Right +_agfbb ._dfbcd ;switch _agfbb ._dfced {case FitModeFillWidth :_agfbb .ScaleToWidth (_edagc );};};

// NewParagraph creates a new text paragraph.
// Default attributes:
// Font: Helvetica,
// Font size: 10
// Encoding: WinAnsiEncoding
// Wrap: enabled
// Text color: black
//
// Deprecated: Use NewStyledParagraph instead for better styling options.
func (_egfg *Creator )NewParagraph (text string )*Paragraph {return _feaag (text ,_egfg .NewTextStyle ())};

// SkipRows skips over a specified number of rows in the table.
func (_afbf *Table )SkipRows (num int ){_dfgfb :=num *_afbf ._fcegg -1;if _dfgfb < 0{_a .Log .Debug ("\u0054\u0061\u0062\u006c\u0065:\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0073\u006b\u0069\u0070\u0020b\u0061\u0063\u006b\u0020\u0074\u006f\u0020\u0070\u0072\u0065\u0076\u0069\u006f\u0075\u0073\u0020\u0063\u0065\u006c\u006c\u0073");
return ;};for _edfe :=0;_edfe < _dfgfb ;_edfe ++{_afbf .NewCell ();};};