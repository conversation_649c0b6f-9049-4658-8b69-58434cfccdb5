//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package docutil ;import (_f "errors";_e "fmt";_c "github.com/unidoc/unipdf/v4/common";_a "github.com/unidoc/unipdf/v4/core";);func (_aa *Document )GetPages ()([]Page ,bool ){_bca ,_bgc :=_aa .FindCatalog ();if !_bgc {return nil ,false ;};return _bca .GetPages ();
};func _edf (_bcg _a .PdfObject )(_a .PdfObjectName ,error ){var _gae *_a .PdfObjectName ;var _cfc *_a .PdfObjectArray ;if _ded ,_dee :=_bcg .(*_a .PdfIndirectObject );_dee {if _dega ,_fbd :=_ded .PdfObject .(*_a .PdfObjectArray );_fbd {_cfc =_dega ;}else if _gc ,_ee :=_ded .PdfObject .(*_a .PdfObjectName );
_ee {_gae =_gc ;};}else if _ebag ,_adc :=_bcg .(*_a .PdfObjectArray );_adc {_cfc =_ebag ;}else if _bce ,_gac :=_bcg .(*_a .PdfObjectName );_gac {_gae =_bce ;};if _gae !=nil {switch *_gae {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":return *_gae ,nil ;
case "\u0050a\u0074\u0074\u0065\u0072\u006e":return *_gae ,nil ;};};if _cfc !=nil &&_cfc .Len ()> 0{if _ega ,_decf :=_cfc .Get (0).(*_a .PdfObjectName );_decf {switch *_ega {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":if _cfc .Len ()==1{return *_ega ,nil ;
};case "\u0043a\u006c\u0047\u0072\u0061\u0079","\u0043\u0061\u006c\u0052\u0047\u0042","\u004c\u0061\u0062":return *_ega ,nil ;case "\u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064","\u0050a\u0074\u0074\u0065\u0072\u006e","\u0049n\u0064\u0065\u0078\u0065\u0064":return *_ega ,nil ;
case "\u0053\u0065\u0070\u0061\u0072\u0061\u0074\u0069\u006f\u006e","\u0044e\u0076\u0069\u0063\u0065\u004e":return *_ega ,nil ;};};};return "",nil ;};func (_gec *Document )AddStream (stream *_a .PdfObjectStream ){for _ ,_dgg :=range _gec .Objects {if _dgg ==stream {return ;
};};_gec .Objects =append (_gec .Objects ,stream );};func (_ddg *Catalog )SetMetadata (data []byte )error {_bda ,_fg :=_a .MakeStream (data ,nil );if _fg !=nil {return _fg ;};_bda .Set ("\u0054\u0079\u0070\u0065",_a .MakeName ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061"));
_bda .Set ("\u0053u\u0062\u0074\u0079\u0070\u0065",_a .MakeName ("\u0058\u004d\u004c"));_ddg .Object .Set ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061",_bda );_ddg ._dd .Objects =append (_ddg ._dd .Objects ,_bda );return nil ;};func (_feb *Catalog )SetMarkInfo (mi _a .PdfObject ){if mi ==nil {_feb .Object .Remove ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f");
return ;};_ad :=_a .MakeIndirectObject (mi );_feb .Object .Set ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f",_ad );_feb ._dd .Objects =append (_feb ._dd .Objects ,_ad );};func (_efb Page )FindXObjectImages ()([]*Image ,error ){_degd ,_ffb :=_efb .GetResourcesXObject ();
if !_ffb {return nil ,nil ;};var _cda []*Image ;var _cce error ;_egg :=map[*_a .PdfObjectStream ]int {};_fd :=map[*_a .PdfObjectStream ]struct{}{};var _fac int ;for _ ,_eeb :=range _degd .Keys (){_acba ,_ffe :=_a .GetStream (_degd .Get (_eeb ));if !_ffe {continue ;
};if _ ,_ebae :=_egg [_acba ];_ebae {continue ;};_aee ,_aeg :=_a .GetName (_acba .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if !_aeg ||_aee .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};_acd :=Image {BitsPerComponent :8,Stream :_acba ,Name :string (_eeb )};
if _acd .Colorspace ,_cce =_edf (_acba .PdfObjectDictionary .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));_cce !=nil {_c .Log .Error ("\u0045\u0072\u0072\u006f\u0072\u0020\u0064\u0065\u0074\u0065r\u006d\u0069\u006e\u0065\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0025\u0073",_cce );
continue ;};if _ebc ,_aac :=_a .GetIntVal (_acba .PdfObjectDictionary .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));_aac {_acd .BitsPerComponent =_ebc ;};if _cfd ,_gcc :=_a .GetIntVal (_acba .PdfObjectDictionary .Get ("\u0057\u0069\u0064t\u0068"));
_gcc {_acd .Width =_cfd ;};if _fdb ,_gca :=_a .GetIntVal (_acba .PdfObjectDictionary .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));_gca {_acd .Height =_fdb ;};if _gef ,_ceg :=_a .GetStream (_acba .Get ("\u0053\u004d\u0061s\u006b"));_ceg {_acd .SMask =&ImageSMask {Image :&_acd ,Stream :_gef };
_fd [_gef ]=struct{}{};};switch _acd .Colorspace {case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_acd .ColorComponents =3;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_acd .ColorComponents =1;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_acd .ColorComponents =4;
default:_acd .ColorComponents =-1;};_egg [_acba ]=_fac ;_cda =append (_cda ,&_acd );_fac ++;};var _bdc []int ;for _ ,_agc :=range _cda {if _agc .SMask !=nil {_gfb ,_ecf :=_egg [_agc .SMask .Stream ];if _ecf {_bdc =append (_bdc ,_gfb );};};};_eed :=make ([]*Image ,len (_cda )-len (_bdc ));
_fac =0;_eeg :for _gaca ,_dgdb :=range _cda {for _ ,_aga :=range _bdc {if _gaca ==_aga {continue _eeg ;};};_eed [_fac ]=_dgdb ;_fac ++;};return _cda ,nil ;};type Document struct{ID [2]string ;Version _a .Version ;Objects []_a .PdfObject ;Info _a .PdfObject ;
Crypt *_a .PdfCrypt ;UseHashBasedID bool ;};func (_ecg *OutputIntents )Len ()int {return _ecg ._ffg .Len ()};func (_bd *Catalog )HasMetadata ()bool {_g :=_bd .Object .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");return _g !=nil ;};func (_gfe *Document )FindCatalog ()(*Catalog ,bool ){var _ca *_a .PdfObjectDictionary ;
for _ ,_cae :=range _gfe .Objects {_ddf ,_eb :=_a .GetDict (_cae );if !_eb {continue ;};if _baa ,_fff :=_a .GetName (_ddf .Get ("\u0054\u0079\u0070\u0065"));_fff &&*_baa =="\u0043a\u0074\u0061\u006c\u006f\u0067"{_ca =_ddf ;break ;};};if _ca ==nil {return nil ,false ;
};return &Catalog {Object :_ca ,_dd :_gfe },true ;};func (_aeb *Catalog )NewOutputIntents ()*OutputIntents {return &OutputIntents {_fb :_aeb ._dd }};type Content struct{Stream *_a .PdfObjectStream ;_eac int ;_deff Page ;};func (_egc *Catalog )GetMarkInfo ()(*_a .PdfObjectDictionary ,bool ){_ffc ,_cb :=_a .GetDict (_egc .Object .Get ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f"));
return _ffc ,_cb ;};func (_dcb Page )GetResources ()(*_a .PdfObjectDictionary ,bool ){return _a .GetDict (_dcb .Object .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));};type Catalog struct{Object *_a .PdfObjectDictionary ;_dd *Document ;};func (_agf Content )GetData ()([]byte ,error ){_afe ,_geg :=_a .NewEncoderFromStream (_agf .Stream );
if _geg !=nil {return nil ,_geg ;};_aed ,_geg :=_afe .DecodeStream (_agf .Stream );if _geg !=nil {return nil ,_geg ;};return _aed ,nil ;};func (_de *Catalog )SetVersion (){_de .Object .Set ("\u0056e\u0072\u0073\u0069\u006f\u006e",_a .MakeName (_e .Sprintf ("\u0025\u0064\u002e%\u0064",_de ._dd .Version .Major ,_de ._dd .Version .Minor )));
};type OutputIntents struct{_ffg *_a .PdfObjectArray ;_fb *Document ;_fgf *_a .PdfIndirectObject ;};func (_dcd Page )GetResourcesXObject ()(*_a .PdfObjectDictionary ,bool ){_ea ,_bdaf :=_dcd .GetResources ();if !_bdaf {return nil ,false ;};return _a .GetDict (_ea .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));
};func (_edb Page )GetContents ()([]Content ,bool ){_gb ,_def :=_a .GetArray (_edb .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));if !_def {_gfeb ,_gg :=_a .GetStream (_edb .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_gg {return nil ,false ;};return []Content {{Stream :_gfeb ,_deff :_edb ,_eac :0}},true ;};_fcf :=make ([]Content ,_gb .Len ());for _cdd ,_gab :=range _gb .Elements (){_gcg ,_bfe :=_a .GetStream (_gab );if !_bfe {continue ;};_fcf [_cdd ]=Content {Stream :_gcg ,_deff :_edb ,_eac :_cdd };
};return _fcf ,true ;};type OutputIntent struct{Object *_a .PdfObjectDictionary ;};func (_fe *Catalog )GetStructTreeRoot ()(*_a .PdfObjectDictionary ,bool ){return _a .GetDict (_fe .Object .Get ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074"));
};func (_dedd *Page )Number ()int {return _dedd ._afb };func (_dbc *Catalog )GetOutputIntents ()(*OutputIntents ,bool ){_fgc :=_dbc .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073");if _fgc ==nil {return nil ,false ;
};_bc ,_ed :=_a .GetIndirect (_fgc );if !_ed {return nil ,false ;};_bf ,_fa :=_a .GetArray (_bc .PdfObject );if !_fa {return nil ,false ;};return &OutputIntents {_fgf :_bc ,_ffg :_bf ,_fb :_dbc ._dd },true ;};type Page struct{_afb int ;Object *_a .PdfObjectDictionary ;
_gd *Document ;};func (_cf *OutputIntents )Get (i int )(OutputIntent ,bool ){if _cf ._ffg ==nil {return OutputIntent {},false ;};if i >=_cf ._ffg .Len (){return OutputIntent {},false ;};_ga :=_cf ._ffg .Get (i );_da ,_gfc :=_a .GetIndirect (_ga );if !_gfc {_cfg ,_ge :=_a .GetDict (_ga );
return OutputIntent {Object :_cfg },_ge ;};_dga ,_ef :=_a .GetDict (_da .PdfObject );return OutputIntent {Object :_dga },_ef ;};func (_ceb *Catalog )SetStructTreeRoot (structTreeRoot _a .PdfObject ){if structTreeRoot ==nil {_ceb .Object .Remove ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074");
return ;};_ag :=_a .MakeIndirectObject (structTreeRoot );_ceb .Object .Set ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074",_ag );_ceb ._dd .Objects =append (_ceb ._dd .Objects ,_ag );};func (_fc *Catalog )SetOutputIntents (outputIntents *OutputIntents ){if _dg :=_fc .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073");
_dg !=nil {for _add ,_acb :=range _fc ._dd .Objects {if _acb ==_dg {if outputIntents ._fgf ==_dg {return ;};_fc ._dd .Objects =append (_fc ._dd .Objects [:_add ],_fc ._dd .Objects [_add +1:]...);break ;};};};_db :=outputIntents ._fgf ;if _db ==nil {_db =_a .MakeIndirectObject (outputIntents ._ffg );
};_fc .Object .Set ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073",_db );_fc ._dd .Objects =append (_fc ._dd .Objects ,_db );};func (_cd *OutputIntents )Add (oi _a .PdfObject )error {_cc ,_dgd :=oi .(*_a .PdfObjectDictionary );
if !_dgd {return _f .New ("\u0069\u006e\u0070\u0075\u0074\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020\u0069\u006e\u0074\u0065\u006et\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0061\u006e\u0020\u006f\u0062\u006a\u0065\u0063t\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};if _bfb ,_gf :=_a .GetStream (_cc .Get ("\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072o\u0066\u0069\u006c\u0065"));_gf {_cd ._fb .Objects =append (_cd ._fb .Objects ,_bfb );};_aca ,_fgd :=oi .(*_a .PdfIndirectObject );if !_fgd {_aca =_a .MakeIndirectObject (oi );
};if _cd ._ffg ==nil {_cd ._ffg =_a .MakeArray (_aca );}else {_cd ._ffg .Append (_aca );};_cd ._fb .Objects =append (_cd ._fb .Objects ,_aca );return nil ;};func (_dbb *Content )SetData (data []byte )error {_gdg ,_cee :=_a .MakeStream (data ,_a .NewFlateEncoder ());
if _cee !=nil {return _cee ;};_be ,_ddga :=_a .GetArray (_dbb ._deff .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));if !_ddga &&_dbb ._eac ==0{_dbb ._deff .Object .Set ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073",_gdg );}else {if _cee =_be .Set (_dbb ._eac ,_gdg );
_cee !=nil {return _cee ;};};_dbb ._deff ._gd .Objects =append (_dbb ._deff ._gd .Objects ,_gdg );return nil ;};func (_aeea Page )FindXObjectForms ()[]*_a .PdfObjectStream {_gdd ,_age :=_aeea .GetResourcesXObject ();if !_age {return nil ;};_gcb :=map[*_a .PdfObjectStream ]struct{}{};
var _cfdd func (_gacac *_a .PdfObjectDictionary ,_ggb map[*_a .PdfObjectStream ]struct{});_cfdd =func (_dcbd *_a .PdfObjectDictionary ,_egcg map[*_a .PdfObjectStream ]struct{}){for _ ,_fgdf :=range _dcbd .Keys (){_gce ,_aebg :=_a .GetStream (_dcbd .Get (_fgdf ));
if !_aebg {continue ;};if _ ,_aae :=_egcg [_gce ];_aae {continue ;};_fad ,_cg :=_a .GetName (_gce .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if !_cg ||_fad .String ()!="\u0046\u006f\u0072\u006d"{continue ;};_egcg [_gce ]=struct{}{};_cfa ,_cg :=_a .GetDict (_gce .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_cg {continue ;};_aec ,_efc :=_a .GetDict (_cfa .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));if _efc {_cfdd (_aec ,_egcg );};};};_cfdd (_gdd ,_gcb );var _dab []*_a .PdfObjectStream ;for _deb :=range _gcb {_dab =append (_dab ,_deb );};return _dab ;
};type ImageSMask struct{Image *Image ;Stream *_a .PdfObjectStream ;};func (_ec *Catalog )GetPages ()([]Page ,bool ){_dec ,_b :=_a .GetDict (_ec .Object .Get ("\u0050\u0061\u0067e\u0073"));if !_b {return nil ,false ;};_bg ,_ff :=_a .GetArray (_dec .Get ("\u004b\u0069\u0064\u0073"));
if !_ff {return nil ,false ;};_ac :=make ([]Page ,_bg .Len ());for _ae ,_dc :=range _bg .Elements (){_ba ,_ce :=_a .GetDict (_dc );if !_ce {continue ;};_ac [_ae ]=Page {Object :_ba ,_afb :_ae +1,_gd :_ec ._dd };};return _ac ,true ;};type Image struct{Name string ;
Width int ;Height int ;Colorspace _a .PdfObjectName ;ColorComponents int ;BitsPerComponent int ;SMask *ImageSMask ;Stream *_a .PdfObjectStream ;};func (_eba *Document )AddIndirectObject (indirect *_a .PdfIndirectObject ){for _ ,_dgaf :=range _eba .Objects {if _dgaf ==indirect {return ;
};};_eba .Objects =append (_eba .Objects ,indirect );};func (_eg *Catalog )GetMetadata ()(*_a .PdfObjectStream ,bool ){return _a .GetStream (_eg .Object .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061"));};