//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package fonts ;import (_fa "bytes";_e "encoding/binary";_g "errors";_f "fmt";_ga "github.com/unidoc/unipdf/v4/common";_gb "github.com/unidoc/unipdf/v4/core";_ca "github.com/unidoc/unipdf/v4/internal/cmap";_be "github.com/unidoc/unipdf/v4/internal/textencoding";
_b "io";_ae "os";_cg "regexp";_c "sort";_d "strings";_aeb "sync";);var _agb *RuneCharSafeMap ;func (_ce *RuneCharSafeMap )Range (f func (_gaa rune ,_gae CharMetrics )(_bec bool )){_ce ._gdf .RLock ();defer _ce ._gdf .RUnlock ();for _aa ,_fcc :=range _ce ._gd {if f (_aa ,_fcc ){break ;
};};};func _faff ()StdFont {_cgb .Do (_cca );_fbf :=Descriptor {Name :HelveticaBoldObliqueName ,Family :string (HelveticaName ),Weight :FontWeightBold ,Flags :0x0060,BBox :[4]float64 {-174,-228,1114,962},ItalicAngle :-12,Ascent :718,Descent :-207,CapHeight :718,XHeight :532,StemV :140,StemH :118};
return NewStdFont (_fbf ,_agb );};func TtfParseFile (fileStr string )(TtfType ,error ){_baca ,_eed :=_ae .Open (fileStr );if _eed !=nil {return TtfType {},_eed ;};defer _baca .Close ();return TtfParse (_baca );};const (HelveticaName =StdFontName ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");
HelveticaBoldName =StdFontName ("\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0042\u006f\u006c\u0064");HelveticaObliqueName =StdFontName ("\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u004f\u0062l\u0069\u0071\u0075\u0065");HelveticaBoldObliqueName =StdFontName ("H\u0065\u006c\u0076\u0065ti\u0063a\u002d\u0042\u006f\u006c\u0064O\u0062\u006c\u0069\u0071\u0075\u0065");
);func (_caed *ttfParser )readByte ()(_ccd uint8 ){_e .Read (_caed ._cdc ,_e .BigEndian ,&_ccd );return _ccd ;};func (_cae *ttfParser )ParseHead ()error {if _eca :=_cae .Seek ("\u0068\u0065\u0061\u0064");_eca !=nil {return _eca ;};_cae .Skip (3*4);_adde :=_cae .ReadULong ();
if _adde !=0x5F0F3CF5{_ga .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0049\u006e\u0063\u006fr\u0072e\u0063\u0074\u0020\u006d\u0061\u0067\u0069\u0063\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u002e\u0020\u0046\u006fn\u0074\u0020\u006d\u0061\u0079\u0020\u006e\u006f\u0074\u0020\u0064\u0069\u0073\u0070\u006c\u0061\u0079\u0020\u0063\u006f\u0072\u0072\u0065\u0063t\u006c\u0079\u002e\u0020\u0025\u0073",_cae );
};_cae .Skip (2);_cae ._def .UnitsPerEm =_cae .ReadUShort ();_cae .Skip (2*8);_cae ._def .Xmin =_cae .ReadShort ();_cae ._def .Ymin =_cae .ReadShort ();_cae ._def .Xmax =_cae .ReadShort ();_cae ._def .Ymax =_cae .ReadShort ();return nil ;};var _ Font =StdFont {};
var _df _aeb .Once ;func _ffe ()StdFont {_df .Do (_dae );_cgbb :=Descriptor {Name :TimesRomanName ,Family :_ed ,Weight :FontWeightRoman ,Flags :0x0020,BBox :[4]float64 {-168,-218,1000,898},ItalicAngle :0,Ascent :683,Descent :-217,CapHeight :662,XHeight :450,StemV :84,StemH :28};
return NewStdFont (_cgbb ,_beb );};var _ad =&fontMap {_dec :make (map[StdFontName ]func ()StdFont )};func _gcc ()StdFont {_gfa :=_be .NewSymbolEncoder ();_fec :=Descriptor {Name :SymbolName ,Family :string (SymbolName ),Weight :FontWeightMedium ,Flags :0x0004,BBox :[4]float64 {-180,-293,1090,1010},ItalicAngle :0,Ascent :0,Descent :0,CapHeight :0,XHeight :0,StemV :85,StemH :92};
return NewStdFontWithEncoding (_fec ,_dabg ,_gfa );};var _fce =[]int16 {667,944,667,667,667,667,667,667,667,667,667,667,667,667,667,667,722,722,722,612,667,667,667,667,667,667,667,667,667,722,500,667,722,722,722,778,389,389,389,389,389,389,389,389,500,667,667,611,611,611,611,611,889,722,722,722,722,722,722,944,722,722,722,722,722,722,722,722,611,722,667,667,667,667,556,556,556,556,556,611,611,611,611,722,722,722,722,722,722,722,722,722,667,889,667,611,611,611,611,611,611,611,500,500,500,500,333,500,722,500,500,778,500,500,570,570,500,832,500,500,278,220,348,348,333,333,333,220,350,444,444,333,444,444,333,500,333,333,250,250,747,500,500,500,500,608,500,400,333,570,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,570,500,389,389,333,556,500,556,500,500,167,500,500,500,500,333,570,549,500,500,333,333,556,333,333,278,278,278,278,278,278,278,278,500,500,278,278,382,278,570,549,606,494,278,778,333,606,576,570,556,556,556,556,500,549,556,500,500,500,500,500,722,333,500,500,500,500,750,750,300,266,300,500,500,500,500,333,333,494,833,250,250,1000,570,570,500,500,500,555,500,500,500,333,333,333,278,389,389,549,389,389,747,333,389,389,389,389,389,500,333,500,500,278,250,500,600,278,366,278,500,500,750,300,333,1000,500,300,556,556,556,556,556,556,556,500,556,556,444,667,500,444,444,444,500,389,389,389,389,500};
func _bbdg ()StdFont {_gdga :=_be .NewZapfDingbatsEncoder ();_bbc :=Descriptor {Name :ZapfDingbatsName ,Family :string (ZapfDingbatsName ),Weight :FontWeightMedium ,Flags :0x0004,BBox :[4]float64 {-1,-143,981,820},ItalicAngle :0,Ascent :0,Descent :0,CapHeight :0,XHeight :0,StemV :90,StemH :28};
return NewStdFontWithEncoding (_bbc ,_bf ,_gdga );};func _cge ()StdFont {_df .Do (_dae );_eg :=Descriptor {Name :TimesItalicName ,Family :_ed ,Weight :FontWeightMedium ,Flags :0x0060,BBox :[4]float64 {-169,-217,1010,883},ItalicAngle :-15.5,Ascent :683,Descent :-217,CapHeight :653,XHeight :441,StemV :76,StemH :32};
return NewStdFont (_eg ,_dge );};var _dbfe *RuneCharSafeMap ;const (_ed ="\u0054\u0069\u006de\u0073";TimesRomanName =StdFontName ("T\u0069\u006d\u0065\u0073\u002d\u0052\u006f\u006d\u0061\u006e");TimesBoldName =StdFontName ("\u0054\u0069\u006d\u0065\u0073\u002d\u0042\u006f\u006c\u0064");
TimesItalicName =StdFontName ("\u0054\u0069\u006de\u0073\u002d\u0049\u0074\u0061\u006c\u0069\u0063");TimesBoldItalicName =StdFontName ("\u0054\u0069m\u0065\u0073\u002dB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063"););func _aabb ()StdFont {_dbf .Do (_gfe );
_dc :=Descriptor {Name :CourierName ,Family :string (CourierName ),Weight :FontWeightMedium ,Flags :0x0021,BBox :[4]float64 {-23,-250,715,805},ItalicAngle :0,Ascent :629,Descent :-157,CapHeight :562,XHeight :426,StemV :51,StemH :51};return NewStdFont (_dc ,_fac );
};func (_gee *ttfParser )ParseName ()error {if _eeec :=_gee .Seek ("\u006e\u0061\u006d\u0065");_eeec !=nil {return _eeec ;};_cfd ,_ :=_gee ._cdc .Seek (0,_b .SeekCurrent );_gee ._def .PostScriptName ="";_gee .Skip (2);_gcf :=_gee .ReadUShort ();_fagb :=_gee .ReadUShort ();
for _fabg :=uint16 (0);_fabg < _gcf &&_gee ._def .PostScriptName =="";_fabg ++{_gee .Skip (3*2);_efcg :=_gee .ReadUShort ();_cbc :=_gee .ReadUShort ();_abbe :=_gee .ReadUShort ();if _efcg ==6{_gee ._cdc .Seek (_cfd +int64 (_fagb )+int64 (_abbe ),_b .SeekStart );
_cdd ,_cgdc :=_gee .ReadStr (int (_cbc ));if _cgdc !=nil {return _cgdc ;};_cdd =_d .Replace (_cdd ,"\u0000","",-1);_gbfa ,_cgdc :=_cg .Compile ("\u005b\u0028\u0029\u007b\u007d\u003c\u003e\u0020\u002f%\u005b\u005c\u005d\u005d");if _cgdc !=nil {return _cgdc ;
};_gee ._def .PostScriptName =_gbfa .ReplaceAllString (_cdd ,"");};};if _gee ._def .PostScriptName ==""{_ga .Log .Debug ("\u0050a\u0072\u0073e\u004e\u0061\u006de\u003a\u0020\u0054\u0068\u0065\u0020\u006ea\u006d\u0065\u0020\u0050\u006f\u0073t\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0077\u0061\u0073\u0020n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e");
};return nil ;};func (_ab *RuneCharSafeMap )Write (b rune ,r CharMetrics ){_ab ._gdf .Lock ();defer _ab ._gdf .Unlock ();_ab ._gd [b ]=r ;};var _ade =[]GlyphName {"\u002en\u006f\u0074\u0064\u0065\u0066","\u002e\u006e\u0075l\u006c","\u006e\u006fn\u006d\u0061\u0072k\u0069\u006e\u0067\u0072\u0065\u0074\u0075\u0072\u006e","\u0073\u0070\u0061c\u0065","\u0065\u0078\u0063\u006c\u0061\u006d","\u0071\u0075\u006f\u0074\u0065\u0064\u0062\u006c","\u006e\u0075\u006d\u0062\u0065\u0072\u0073\u0069\u0067\u006e","\u0064\u006f\u006c\u006c\u0061\u0072","\u0070e\u0072\u0063\u0065\u006e\u0074","\u0061m\u0070\u0065\u0072\u0073\u0061\u006ed","q\u0075\u006f\u0074\u0065\u0073\u0069\u006e\u0067\u006c\u0065","\u0070a\u0072\u0065\u006e\u006c\u0065\u0066t","\u0070\u0061\u0072\u0065\u006e\u0072\u0069\u0067\u0068\u0074","\u0061\u0073\u0074\u0065\u0072\u0069\u0073\u006b","\u0070\u006c\u0075\u0073","\u0063\u006f\u006dm\u0061","\u0068\u0079\u0070\u0068\u0065\u006e","\u0070\u0065\u0072\u0069\u006f\u0064","\u0073\u006c\u0061s\u0068","\u007a\u0065\u0072\u006f","\u006f\u006e\u0065","\u0074\u0077\u006f","\u0074\u0068\u0072e\u0065","\u0066\u006f\u0075\u0072","\u0066\u0069\u0076\u0065","\u0073\u0069\u0078","\u0073\u0065\u0076e\u006e","\u0065\u0069\u0067h\u0074","\u006e\u0069\u006e\u0065","\u0063\u006f\u006co\u006e","\u0073e\u006d\u0069\u0063\u006f\u006c\u006fn","\u006c\u0065\u0073\u0073","\u0065\u0071\u0075a\u006c","\u0067r\u0065\u0061\u0074\u0065\u0072","\u0071\u0075\u0065\u0073\u0074\u0069\u006f\u006e","\u0061\u0074","\u0041","\u0042","\u0043","\u0044","\u0045","\u0046","\u0047","\u0048","\u0049","\u004a","\u004b","\u004c","\u004d","\u004e","\u004f","\u0050","\u0051","\u0052","\u0053","\u0054","\u0055","\u0056","\u0057","\u0058","\u0059","\u005a","b\u0072\u0061\u0063\u006b\u0065\u0074\u006c\u0065\u0066\u0074","\u0062a\u0063\u006b\u0073\u006c\u0061\u0073h","\u0062\u0072\u0061c\u006b\u0065\u0074\u0072\u0069\u0067\u0068\u0074","a\u0073\u0063\u0069\u0069\u0063\u0069\u0072\u0063\u0075\u006d","\u0075\u006e\u0064\u0065\u0072\u0073\u0063\u006f\u0072\u0065","\u0067\u0072\u0061v\u0065","\u0061","\u0062","\u0063","\u0064","\u0065","\u0066","\u0067","\u0068","\u0069","\u006a","\u006b","\u006c","\u006d","\u006e","\u006f","\u0070","\u0071","\u0072","\u0073","\u0074","\u0075","\u0076","\u0077","\u0078","\u0079","\u007a","\u0062r\u0061\u0063\u0065\u006c\u0065\u0066t","\u0062\u0061\u0072","\u0062\u0072\u0061\u0063\u0065\u0072\u0069\u0067\u0068\u0074","\u0061\u0073\u0063\u0069\u0069\u0074\u0069\u006c\u0064\u0065","\u0041d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0041\u0072\u0069n\u0067","\u0043\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0045\u0061\u0063\u0075\u0074\u0065","\u004e\u0074\u0069\u006c\u0064\u0065","\u004fd\u0069\u0065\u0072\u0065\u0073\u0069s","\u0055d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0061\u0061\u0063\u0075\u0074\u0065","\u0061\u0067\u0072\u0061\u0076\u0065","a\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0061d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0061\u0074\u0069\u006c\u0064\u0065","\u0061\u0072\u0069n\u0067","\u0063\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0065\u0061\u0063\u0075\u0074\u0065","\u0065\u0067\u0072\u0061\u0076\u0065","e\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0065d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0069\u0061\u0063\u0075\u0074\u0065","\u0069\u0067\u0072\u0061\u0076\u0065","i\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0069d\u0069\u0065\u0072\u0065\u0073\u0069s","\u006e\u0074\u0069\u006c\u0064\u0065","\u006f\u0061\u0063\u0075\u0074\u0065","\u006f\u0067\u0072\u0061\u0076\u0065","o\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u006fd\u0069\u0065\u0072\u0065\u0073\u0069s","\u006f\u0074\u0069\u006c\u0064\u0065","\u0075\u0061\u0063\u0075\u0074\u0065","\u0075\u0067\u0072\u0061\u0076\u0065","u\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0075d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0064\u0061\u0067\u0067\u0065\u0072","\u0064\u0065\u0067\u0072\u0065\u0065","\u0063\u0065\u006e\u0074","\u0073\u0074\u0065\u0072\u006c\u0069\u006e\u0067","\u0073e\u0063\u0074\u0069\u006f\u006e","\u0062\u0075\u006c\u006c\u0065\u0074","\u0070a\u0072\u0061\u0067\u0072\u0061\u0070h","\u0067\u0065\u0072\u006d\u0061\u006e\u0064\u0062\u006c\u0073","\u0072\u0065\u0067\u0069\u0073\u0074\u0065\u0072\u0065\u0064","\u0063o\u0070\u0079\u0072\u0069\u0067\u0068t","\u0074r\u0061\u0064\u0065\u006d\u0061\u0072k","\u0061\u0063\u0075t\u0065","\u0064\u0069\u0065\u0072\u0065\u0073\u0069\u0073","\u006e\u006f\u0074\u0065\u0071\u0075\u0061\u006c","\u0041\u0045","\u004f\u0073\u006c\u0061\u0073\u0068","\u0069\u006e\u0066\u0069\u006e\u0069\u0074\u0079","\u0070l\u0075\u0073\u006d\u0069\u006e\u0075s","\u006ce\u0073\u0073\u0065\u0071\u0075\u0061l","\u0067\u0072\u0065a\u0074\u0065\u0072\u0065\u0071\u0075\u0061\u006c","\u0079\u0065\u006e","\u006d\u0075","p\u0061\u0072\u0074\u0069\u0061\u006c\u0064\u0069\u0066\u0066","\u0073u\u006d\u006d\u0061\u0074\u0069\u006fn","\u0070r\u006f\u0064\u0075\u0063\u0074","\u0070\u0069","\u0069\u006e\u0074\u0065\u0067\u0072\u0061\u006c","o\u0072\u0064\u0066\u0065\u006d\u0069\u006e\u0069\u006e\u0065","\u006f\u0072\u0064m\u0061\u0073\u0063\u0075\u006c\u0069\u006e\u0065","\u004f\u006d\u0065g\u0061","\u0061\u0065","\u006f\u0073\u006c\u0061\u0073\u0068","\u0071\u0075\u0065s\u0074\u0069\u006f\u006e\u0064\u006f\u0077\u006e","\u0065\u0078\u0063\u006c\u0061\u006d\u0064\u006f\u0077\u006e","\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u006e\u006f\u0074","\u0072a\u0064\u0069\u0063\u0061\u006c","\u0066\u006c\u006f\u0072\u0069\u006e","a\u0070\u0070\u0072\u006f\u0078\u0065\u0071\u0075\u0061\u006c","\u0044\u0065\u006ct\u0061","\u0067\u0075\u0069\u006c\u006c\u0065\u006d\u006f\u0074\u006c\u0065\u0066\u0074","\u0067\u0075\u0069\u006c\u006c\u0065\u006d\u006f\u0074r\u0069\u0067\u0068\u0074","\u0065\u006c\u006c\u0069\u0070\u0073\u0069\u0073","\u006e\u006fn\u0062\u0072\u0065a\u006b\u0069\u006e\u0067\u0073\u0070\u0061\u0063\u0065","\u0041\u0067\u0072\u0061\u0076\u0065","\u0041\u0074\u0069\u006c\u0064\u0065","\u004f\u0074\u0069\u006c\u0064\u0065","\u004f\u0045","\u006f\u0065","\u0065\u006e\u0064\u0061\u0073\u0068","\u0065\u006d\u0064\u0061\u0073\u0068","\u0071\u0075\u006ft\u0065\u0064\u0062\u006c\u006c\u0065\u0066\u0074","\u0071\u0075\u006f\u0074\u0065\u0064\u0062\u006c\u0072\u0069\u0067\u0068\u0074","\u0071u\u006f\u0074\u0065\u006c\u0065\u0066t","\u0071\u0075\u006f\u0074\u0065\u0072\u0069\u0067\u0068\u0074","\u0064\u0069\u0076\u0069\u0064\u0065","\u006co\u007a\u0065\u006e\u0067\u0065","\u0079d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0059d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0066\u0072\u0061\u0063\u0074\u0069\u006f\u006e","\u0063\u0075\u0072\u0072\u0065\u006e\u0063\u0079","\u0067\u0075\u0069\u006c\u0073\u0069\u006e\u0067\u006c\u006c\u0065\u0066\u0074","\u0067\u0075\u0069\u006c\u0073\u0069\u006e\u0067\u006cr\u0069\u0067\u0068\u0074","\u0066\u0069","\u0066\u006c","\u0064a\u0067\u0067\u0065\u0072\u0064\u0062l","\u0070\u0065\u0072\u0069\u006f\u0064\u0063\u0065\u006et\u0065\u0072\u0065\u0064","\u0071\u0075\u006f\u0074\u0065\u0073\u0069\u006e\u0067l\u0062\u0061\u0073\u0065","\u0071\u0075\u006ft\u0065\u0064\u0062\u006c\u0062\u0061\u0073\u0065","p\u0065\u0072\u0074\u0068\u006f\u0075\u0073\u0061\u006e\u0064","A\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","E\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0041\u0061\u0063\u0075\u0074\u0065","\u0045d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0045\u0067\u0072\u0061\u0076\u0065","\u0049\u0061\u0063\u0075\u0074\u0065","I\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0049d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0049\u0067\u0072\u0061\u0076\u0065","\u004f\u0061\u0063\u0075\u0074\u0065","O\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0061\u0070\u0070l\u0065","\u004f\u0067\u0072\u0061\u0076\u0065","\u0055\u0061\u0063\u0075\u0074\u0065","U\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0055\u0067\u0072\u0061\u0076\u0065","\u0064\u006f\u0074\u006c\u0065\u0073\u0073\u0069","\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0074\u0069\u006cd\u0065","\u006d\u0061\u0063\u0072\u006f\u006e","\u0062\u0072\u0065v\u0065","\u0064o\u0074\u0061\u0063\u0063\u0065\u006et","\u0072\u0069\u006e\u0067","\u0063e\u0064\u0069\u006c\u006c\u0061","\u0068\u0075\u006eg\u0061\u0072\u0075\u006d\u006c\u0061\u0075\u0074","\u006f\u0067\u006f\u006e\u0065\u006b","\u0063\u0061\u0072o\u006e","\u004c\u0073\u006c\u0061\u0073\u0068","\u006c\u0073\u006c\u0061\u0073\u0068","\u0053\u0063\u0061\u0072\u006f\u006e","\u0073\u0063\u0061\u0072\u006f\u006e","\u005a\u0063\u0061\u0072\u006f\u006e","\u007a\u0063\u0061\u0072\u006f\u006e","\u0062r\u006f\u006b\u0065\u006e\u0062\u0061r","\u0045\u0074\u0068","\u0065\u0074\u0068","\u0059\u0061\u0063\u0075\u0074\u0065","\u0079\u0061\u0063\u0075\u0074\u0065","\u0054\u0068\u006fr\u006e","\u0074\u0068\u006fr\u006e","\u006d\u0069\u006eu\u0073","\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","o\u006e\u0065\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","t\u0077\u006f\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","\u0074\u0068\u0072\u0065\u0065\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","\u006fn\u0065\u0068\u0061\u006c\u0066","\u006f\u006e\u0065\u0071\u0075\u0061\u0072\u0074\u0065\u0072","\u0074\u0068\u0072\u0065\u0065\u0071\u0075\u0061\u0072\u0074\u0065\u0072\u0073","\u0066\u0072\u0061n\u0063","\u0047\u0062\u0072\u0065\u0076\u0065","\u0067\u0062\u0072\u0065\u0076\u0065","\u0049\u0064\u006f\u0074\u0061\u0063\u0063\u0065\u006e\u0074","\u0053\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0073\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0043\u0061\u0063\u0075\u0074\u0065","\u0063\u0061\u0063\u0075\u0074\u0065","\u0043\u0063\u0061\u0072\u006f\u006e","\u0063\u0063\u0061\u0072\u006f\u006e","\u0064\u0063\u0072\u006f\u0061\u0074"};
const (CourierName =StdFontName ("\u0043o\u0075\u0072\u0069\u0065\u0072");CourierBoldName =StdFontName ("\u0043\u006f\u0075r\u0069\u0065\u0072\u002d\u0042\u006f\u006c\u0064");CourierObliqueName =StdFontName ("\u0043o\u0075r\u0069\u0065\u0072\u002d\u004f\u0062\u006c\u0069\u0071\u0075\u0065");
CourierBoldObliqueName =StdFontName ("\u0043\u006f\u0075\u0072ie\u0072\u002d\u0042\u006f\u006c\u0064\u004f\u0062\u006c\u0069\u0071\u0075\u0065"););func (_ac StdFont )Name ()string {return string (_ac ._bge .Name )};func (_aec StdFont )Descriptor ()Descriptor {return _aec ._bge };
func TtfParse (r _b .ReadSeeker )(TtfType ,error ){_aca :=&ttfParser {_cdc :r };return _aca .Parse ()};var _fga *RuneCharSafeMap ;type StdFont struct{_bge Descriptor ;_fef *RuneCharSafeMap ;_agd _be .TextEncoder ;};func (_agbf *ttfParser )ParseMaxp ()error {if _bfe :=_agbf .Seek ("\u006d\u0061\u0078\u0070");
_bfe !=nil {return _bfe ;};_agbf .Skip (4);_agbf ._gcce =_agbf .ReadUShort ();return nil ;};var _fae *RuneCharSafeMap ;func IsStdFont (name StdFontName )bool {_ ,_bbg :=_ad .read (name );return _bbg };var _ef =[]int16 {611,889,611,611,611,611,611,611,611,611,611,611,667,667,667,667,722,722,722,612,611,611,611,611,611,611,611,611,611,722,500,611,722,722,722,722,333,333,333,333,333,333,333,333,444,667,667,556,556,611,556,556,833,667,667,667,667,667,722,944,722,722,722,722,722,722,722,722,611,722,611,611,611,611,500,500,500,500,500,556,556,556,611,722,722,722,722,722,722,722,722,722,611,833,611,556,556,556,556,556,556,556,500,500,500,500,333,500,667,500,500,778,500,500,422,541,500,920,500,500,278,275,400,400,389,389,333,275,350,444,444,333,444,444,333,500,333,333,250,250,760,500,500,500,500,544,500,400,333,675,500,333,278,444,444,444,444,444,444,444,500,889,444,889,500,444,675,500,333,389,278,500,500,500,500,500,167,500,500,500,500,333,675,549,500,500,333,333,500,333,333,278,278,278,278,278,278,278,278,444,444,278,278,300,278,675,549,675,471,278,722,333,675,500,675,500,500,500,500,500,549,500,500,500,500,500,500,667,333,500,500,500,500,750,750,300,276,310,500,500,500,523,333,333,476,833,250,250,1000,675,675,500,500,500,420,556,556,556,333,333,333,214,389,389,453,389,389,760,333,389,389,389,389,389,500,333,500,500,278,250,500,600,278,300,278,500,500,750,300,333,980,500,300,500,500,500,500,500,500,500,500,500,500,444,667,444,444,444,444,500,389,389,389,389,500};
func _aebe (_fbde map[string ]uint32 )string {var _bbe []string ;for _fgb :=range _fbde {_bbe =append (_bbe ,_fgb );};_c .Slice (_bbe ,func (_aaa ,_fed int )bool {return _fbde [_bbe [_aaa ]]< _fbde [_bbe [_fed ]]});_aaf :=[]string {_f .Sprintf ("\u0054\u0072\u0075\u0065Ty\u0070\u0065\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u003a\u0020\u0025\u0064",len (_fbde ))};
for _ ,_acf :=range _bbe {_aaf =append (_aaf ,_f .Sprintf ("\u0009%\u0071\u0020\u0025\u0035\u0064",_acf ,_fbde [_acf ]));};return _d .Join (_aaf ,"\u000a");};func init (){RegisterStdFont (CourierName ,_aabb ,"\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u0043\u006f\u0075\u0072\u0069e\u0072\u004e\u0065\u0077","\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u004e\u0065\u0077");
RegisterStdFont (CourierBoldName ,_cga ,"\u0043o\u0075r\u0069\u0065\u0072\u004e\u0065\u0077\u002c\u0042\u006f\u006c\u0064");RegisterStdFont (CourierObliqueName ,_aea ,"\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u004e\u0065\u0077\u002c\u0049t\u0061\u006c\u0069\u0063");
RegisterStdFont (CourierBoldObliqueName ,_dab ,"C\u006f\u0075\u0072\u0069er\u004ee\u0077\u002c\u0042\u006f\u006cd\u0049\u0074\u0061\u006c\u0069\u0063");};type FontWeight int ;func NewFontFile2FromPdfObject (obj _gb .PdfObject )(TtfType ,error ){obj =_gb .TraceToDirectObject (obj );
_fbdf ,_cgea :=obj .(*_gb .PdfObjectStream );if !_cgea {_ga .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u006f\u006e\u0074\u0046\u0069\u006c\u0065\u0032\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0073\u0074\u0072e\u0061\u006d \u0028\u0025\u0054\u0029",obj );
return TtfType {},_gb .ErrTypeError ;};_cfa ,_aeg :=_gb .DecodeStream (_fbdf );if _aeg !=nil {return TtfType {},_aeg ;};_fecd :=ttfParser {_cdc :_fa .NewReader (_cfa )};return _fecd .Parse ();};var _ffb *RuneCharSafeMap ;var _bf =&RuneCharSafeMap {_gd :map[rune ]CharMetrics {' ':{Wx :278},'→':{Wx :838},'↔':{Wx :1016},'↕':{Wx :458},'①':{Wx :788},'②':{Wx :788},'③':{Wx :788},'④':{Wx :788},'⑤':{Wx :788},'⑥':{Wx :788},'⑦':{Wx :788},'⑧':{Wx :788},'⑨':{Wx :788},'⑩':{Wx :788},'■':{Wx :761},'▲':{Wx :892},'▼':{Wx :892},'◆':{Wx :788},'●':{Wx :791},'◗':{Wx :438},'★':{Wx :816},'☎':{Wx :719},'☛':{Wx :960},'☞':{Wx :939},'♠':{Wx :626},'♣':{Wx :776},'♥':{Wx :694},'♦':{Wx :595},'✁':{Wx :974},'✂':{Wx :961},'✃':{Wx :974},'✄':{Wx :980},'✆':{Wx :789},'✇':{Wx :790},'✈':{Wx :791},'✉':{Wx :690},'✌':{Wx :549},'✍':{Wx :855},'✎':{Wx :911},'✏':{Wx :933},'✐':{Wx :911},'✑':{Wx :945},'✒':{Wx :974},'✓':{Wx :755},'✔':{Wx :846},'✕':{Wx :762},'✖':{Wx :761},'✗':{Wx :571},'✘':{Wx :677},'✙':{Wx :763},'✚':{Wx :760},'✛':{Wx :759},'✜':{Wx :754},'✝':{Wx :494},'✞':{Wx :552},'✟':{Wx :537},'✠':{Wx :577},'✡':{Wx :692},'✢':{Wx :786},'✣':{Wx :788},'✤':{Wx :788},'✥':{Wx :790},'✦':{Wx :793},'✧':{Wx :794},'✩':{Wx :823},'✪':{Wx :789},'✫':{Wx :841},'✬':{Wx :823},'✭':{Wx :833},'✮':{Wx :816},'✯':{Wx :831},'✰':{Wx :923},'✱':{Wx :744},'✲':{Wx :723},'✳':{Wx :749},'✴':{Wx :790},'✵':{Wx :792},'✶':{Wx :695},'✷':{Wx :776},'✸':{Wx :768},'✹':{Wx :792},'✺':{Wx :759},'✻':{Wx :707},'✼':{Wx :708},'✽':{Wx :682},'✾':{Wx :701},'✿':{Wx :826},'❀':{Wx :815},'❁':{Wx :789},'❂':{Wx :789},'❃':{Wx :707},'❄':{Wx :687},'❅':{Wx :696},'❆':{Wx :689},'❇':{Wx :786},'❈':{Wx :787},'❉':{Wx :713},'❊':{Wx :791},'❋':{Wx :785},'❍':{Wx :873},'❏':{Wx :762},'❐':{Wx :762},'❑':{Wx :759},'❒':{Wx :759},'❖':{Wx :784},'❘':{Wx :138},'❙':{Wx :277},'❚':{Wx :415},'❛':{Wx :392},'❜':{Wx :392},'❝':{Wx :668},'❞':{Wx :668},'❡':{Wx :732},'❢':{Wx :544},'❣':{Wx :544},'❤':{Wx :910},'❥':{Wx :667},'❦':{Wx :760},'❧':{Wx :760},'❶':{Wx :788},'❷':{Wx :788},'❸':{Wx :788},'❹':{Wx :788},'❺':{Wx :788},'❻':{Wx :788},'❼':{Wx :788},'❽':{Wx :788},'❾':{Wx :788},'❿':{Wx :788},'➀':{Wx :788},'➁':{Wx :788},'➂':{Wx :788},'➃':{Wx :788},'➄':{Wx :788},'➅':{Wx :788},'➆':{Wx :788},'➇':{Wx :788},'➈':{Wx :788},'➉':{Wx :788},'➊':{Wx :788},'➋':{Wx :788},'➌':{Wx :788},'➍':{Wx :788},'➎':{Wx :788},'➏':{Wx :788},'➐':{Wx :788},'➑':{Wx :788},'➒':{Wx :788},'➓':{Wx :788},'➔':{Wx :894},'➘':{Wx :748},'➙':{Wx :924},'➚':{Wx :748},'➛':{Wx :918},'➜':{Wx :927},'➝':{Wx :928},'➞':{Wx :928},'➟':{Wx :834},'➠':{Wx :873},'➡':{Wx :828},'➢':{Wx :924},'➣':{Wx :924},'➤':{Wx :917},'➥':{Wx :930},'➦':{Wx :931},'➧':{Wx :463},'➨':{Wx :883},'➩':{Wx :836},'➪':{Wx :836},'➫':{Wx :867},'➬':{Wx :867},'➭':{Wx :696},'➮':{Wx :696},'➯':{Wx :874},'➱':{Wx :874},'➲':{Wx :760},'➳':{Wx :946},'➴':{Wx :771},'➵':{Wx :865},'➶':{Wx :771},'➷':{Wx :888},'➸':{Wx :967},'➹':{Wx :888},'➺':{Wx :831},'➻':{Wx :873},'➼':{Wx :927},'➽':{Wx :970},'➾':{Wx :918},'\uf8d7':{Wx :390},'\uf8d8':{Wx :390},'\uf8d9':{Wx :317},'\uf8da':{Wx :317},'\uf8db':{Wx :276},'\uf8dc':{Wx :276},'\uf8dd':{Wx :509},'\uf8de':{Wx :509},'\uf8df':{Wx :410},'\uf8e0':{Wx :410},'\uf8e1':{Wx :234},'\uf8e2':{Wx :234},'\uf8e3':{Wx :334},'\uf8e4':{Wx :334}}};
var _ecf =[]int16 {722,1000,722,722,722,722,722,722,722,722,722,667,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,500,611,778,778,778,778,389,389,389,389,389,389,389,389,500,778,778,667,667,667,667,667,944,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,611,778,722,722,722,722,556,556,556,556,556,667,667,667,611,722,722,722,722,722,722,722,722,722,722,1000,722,722,722,722,667,667,667,667,500,500,500,500,333,500,722,500,500,833,500,500,581,520,500,930,500,556,278,220,394,394,333,333,333,220,350,444,444,333,444,444,333,500,333,333,250,250,747,500,556,500,500,672,556,400,333,570,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,570,500,333,333,333,556,500,556,500,500,167,500,500,500,556,333,570,549,500,500,333,333,556,333,333,278,278,278,278,278,278,278,333,556,556,278,278,394,278,570,549,570,494,278,833,333,570,556,570,556,556,556,556,500,549,556,500,500,500,500,500,722,333,500,500,500,500,750,750,300,300,330,500,500,556,540,333,333,494,1000,250,250,1000,570,570,556,500,500,555,500,500,500,333,333,333,278,444,444,549,444,444,747,333,389,389,389,389,389,500,333,500,500,278,250,500,600,333,416,333,556,500,750,300,333,1000,500,300,556,556,556,556,556,556,556,500,556,556,500,722,500,500,500,500,500,444,444,444,444,500};
var _bae =[]int16 {722,1000,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,556,611,778,778,778,722,278,278,278,278,278,278,278,278,556,722,722,611,611,611,611,611,833,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,667,778,722,722,722,722,667,667,667,667,667,611,611,611,667,722,722,722,722,722,722,722,722,722,667,944,667,667,667,667,611,611,611,611,556,556,556,556,333,556,889,556,556,722,556,556,584,584,389,975,556,611,278,280,389,389,333,333,333,280,350,556,556,333,556,556,333,556,333,333,278,250,737,556,611,556,556,743,611,400,333,584,556,333,278,556,556,556,556,556,556,556,556,1000,556,1000,556,556,584,611,333,333,333,611,556,611,556,556,167,611,611,611,611,333,584,549,556,556,333,333,611,333,333,278,278,278,278,278,278,278,278,556,556,278,278,400,278,584,549,584,494,278,889,333,584,611,584,611,611,611,611,556,549,611,556,611,611,611,611,944,333,611,611,611,556,834,834,333,370,365,611,611,611,556,333,333,494,889,278,278,1000,584,584,611,611,611,474,500,500,500,278,278,278,238,389,389,549,389,389,737,333,556,556,556,556,556,556,333,556,556,278,278,556,600,333,389,333,611,556,834,333,333,1000,556,333,611,611,611,611,611,611,611,556,611,611,556,778,556,556,556,556,556,500,500,500,500,556};
var _dce =[]int16 {667,1000,667,667,667,667,667,667,667,667,667,667,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,556,611,778,778,778,722,278,278,278,278,278,278,278,278,500,667,667,556,556,556,556,556,833,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,667,778,722,722,722,722,667,667,667,667,667,611,611,611,667,722,722,722,722,722,722,722,722,722,667,944,667,667,667,667,611,611,611,611,556,556,556,556,333,556,889,556,556,667,556,556,469,584,389,1015,556,556,278,260,334,334,278,278,333,260,350,500,500,333,500,500,333,556,333,278,278,250,737,556,556,556,556,643,556,400,333,584,556,333,278,556,556,556,556,556,556,556,556,1000,556,1000,556,556,584,556,278,333,278,500,556,500,556,556,167,556,556,556,611,333,584,549,556,556,333,333,556,333,333,222,278,278,278,278,278,222,222,500,500,222,222,299,222,584,549,584,471,222,833,333,584,556,584,556,556,556,556,556,549,556,556,556,556,556,556,944,333,556,556,556,556,834,834,333,370,365,611,556,556,537,333,333,476,889,278,278,1000,584,584,556,556,611,355,333,333,333,222,222,222,191,333,333,453,333,333,737,333,500,500,500,500,500,556,278,556,556,278,278,556,600,278,317,278,556,556,834,333,333,1000,556,333,556,556,556,556,556,556,556,556,556,556,500,722,500,500,500,500,556,500,500,500,500,556};
var _aae *RuneCharSafeMap ;func (_efa *ttfParser )ParseHmtx ()error {if _dfa :=_efa .Seek ("\u0068\u006d\u0074\u0078");_dfa !=nil {return _dfa ;};_efa ._def .Widths =make ([]uint16 ,0,8);for _agc :=uint16 (0);_agc < _efa ._age ;_agc ++{_efa ._def .Widths =append (_efa ._def .Widths ,_efa .ReadUShort ());
_efa .Skip (2);};if _efa ._age < _efa ._gcce &&_efa ._age > 0{_ede :=_efa ._def .Widths [_efa ._age -1];for _gbdc :=_efa ._age ;_gbdc < _efa ._gcce ;_gbdc ++{_efa ._def .Widths =append (_efa ._def .Widths ,_ede );};};return nil ;};type CharMetrics struct{Wx float64 ;
Wy float64 ;};func _cga ()StdFont {_dbf .Do (_gfe );_ba :=Descriptor {Name :CourierBoldName ,Family :string (CourierName ),Weight :FontWeightBold ,Flags :0x0021,BBox :[4]float64 {-113,-250,749,801},ItalicAngle :0,Ascent :629,Descent :-157,CapHeight :562,XHeight :439,StemV :106,StemH :84};
return NewStdFont (_ba ,_fga );};func init (){RegisterStdFont (HelveticaName ,_gdd ,"\u0041\u0072\u0069a\u006c");RegisterStdFont (HelveticaBoldName ,_dca ,"\u0041\u0072\u0069\u0061\u006c\u002c\u0042\u006f\u006c\u0064");RegisterStdFont (HelveticaObliqueName ,_fccb ,"\u0041\u0072\u0069a\u006c\u002c\u0049\u0074\u0061\u006c\u0069\u0063");
RegisterStdFont (HelveticaBoldObliqueName ,_faff ,"\u0041\u0072i\u0061\u006c\u002cB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063");};func _dgc ()StdFont {_df .Do (_dae );_gdda :=Descriptor {Name :TimesBoldName ,Family :_ed ,Weight :FontWeightBold ,Flags :0x0020,BBox :[4]float64 {-168,-218,1000,935},ItalicAngle :0,Ascent :683,Descent :-217,CapHeight :676,XHeight :461,StemV :139,StemH :44};
return NewStdFont (_gdda ,_eee );};func (_feg *ttfParser )parseCmapFormat6 ()error {_bcca :=int (_feg .ReadUShort ());_eddb :=int (_feg .ReadUShort ());_ga .Log .Trace ("p\u0061\u0072\u0073\u0065\u0043\u006d\u0061\u0070\u0046o\u0072\u006d\u0061\u0074\u0036\u003a\u0020%s\u0020\u0066\u0069\u0072s\u0074\u0043\u006f\u0064\u0065\u003d\u0025\u0064\u0020en\u0074\u0072y\u0043\u006f\u0075\u006e\u0074\u003d\u0025\u0064",_feg ._def .String (),_bcca ,_eddb );
for _eba :=0;_eba < _eddb ;_eba ++{_gaaf :=GID (_feg .ReadUShort ());_feg ._def .Chars [rune (_eba +_bcca )]=_gaaf ;};return nil ;};func (_ggd *ttfParser )parseCmapVersion (_bdd int64 )error {_ga .Log .Trace ("p\u0061\u0072\u0073\u0065\u0043\u006da\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e\u003a \u006f\u0066\u0066s\u0065t\u003d\u0025\u0064",_bdd );
if _ggd ._def .Chars ==nil {_ggd ._def .Chars =make (map[rune ]GID );};_ggd ._cdc .Seek (int64 (_ggd ._add ["\u0063\u006d\u0061\u0070"])+_bdd ,_b .SeekStart );var _aaec ,_cea uint32 ;_fdd :=_ggd .ReadUShort ();if _fdd < 8{_aaec =uint32 (_ggd .ReadUShort ());
_cea =uint32 (_ggd .ReadUShort ());}else {_ggd .ReadUShort ();_aaec =_ggd .ReadULong ();_cea =_ggd .ReadULong ();};_ga .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u0043m\u0061\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e\u003a\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u003d\u0025\u0064 \u006c\u0065\u006e\u0067\u0074\u0068\u003d\u0025\u0064\u0020\u006c\u0061\u006e\u0067u\u0061g\u0065\u003d\u0025\u0064",_fdd ,_aaec ,_cea );
switch _fdd {case 0:return _ggd .parseCmapFormat0 ();case 6:return _ggd .parseCmapFormat6 ();case 12:return _ggd .parseCmapFormat12 ();default:_ga .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0063m\u0061\u0070\u0020\u0066\u006f\u0072\u006da\u0074\u003d\u0025\u0064",_fdd );
return nil ;};};var _cgb _aeb .Once ;func NewStdFont (desc Descriptor ,metrics *RuneCharSafeMap )StdFont {return NewStdFontWithEncoding (desc ,metrics ,_be .NewStandardEncoder ());};func _aea ()StdFont {_dbf .Do (_gfe );_afe :=Descriptor {Name :CourierObliqueName ,Family :string (CourierName ),Weight :FontWeightMedium ,Flags :0x0061,BBox :[4]float64 {-27,-250,849,805},ItalicAngle :-12,Ascent :629,Descent :-157,CapHeight :562,XHeight :426,StemV :51,StemH :51};
return NewStdFont (_afe ,_dbfe );};func (_gc *RuneCharSafeMap )Length ()int {_gc ._gdf .RLock ();defer _gc ._gdf .RUnlock ();return len (_gc ._gd );};func (_cb StdFont )GetRuneMetrics (r rune )(CharMetrics ,bool ){_af ,_afd :=_cb ._fef .Read (r );return _af ,_afd ;
};type RuneCharSafeMap struct{_gd map[rune ]CharMetrics ;_gdf _aeb .RWMutex ;};func _dab ()StdFont {_dbf .Do (_gfe );_deb :=Descriptor {Name :CourierBoldObliqueName ,Family :string (CourierName ),Weight :FontWeightBold ,Flags :0x0061,BBox :[4]float64 {-57,-250,869,801},ItalicAngle :-12,Ascent :629,Descent :-157,CapHeight :562,XHeight :439,StemV :106,StemH :84};
return NewStdFont (_deb ,_dgd );};func (_dcd *ttfParser )ParsePost ()error {if _ccaa :=_dcd .Seek ("\u0070\u006f\u0073\u0074");_ccaa !=nil {return _ccaa ;};_efd :=_dcd .Read32Fixed ();_dcd ._def .ItalicAngle =_dcd .Read32Fixed ();_dcd ._def .UnderlinePosition =_dcd .ReadShort ();
_dcd ._def .UnderlineThickness =_dcd .ReadShort ();_dcd ._def .IsFixedPitch =_dcd .ReadULong ()!=0;_dcd .ReadULong ();_dcd .ReadULong ();_dcd .ReadULong ();_dcd .ReadULong ();_ga .Log .Trace ("\u0050a\u0072\u0073\u0065\u0050\u006f\u0073\u0074\u003a\u0020\u0066\u006fr\u006d\u0061\u0074\u0054\u0079\u0070\u0065\u003d\u0025\u0066",_efd );
switch _efd {case 1.0:_dcd ._def .GlyphNames =_ade ;case 2.0:_daf :=int (_dcd .ReadUShort ());_dcdg :=make ([]int ,_daf );_dcd ._def .GlyphNames =make ([]GlyphName ,_daf );_fafa :=-1;for _dba :=0;_dba < _daf ;_dba ++{_bda :=int (_dcd .ReadUShort ());_dcdg [_dba ]=_bda ;
if _bda <=0x7fff&&_bda > _fafa {_fafa =_bda ;};};var _ace []GlyphName ;if _fafa >=len (_ade ){_ace =make ([]GlyphName ,_fafa -len (_ade )+1);for _eab :=0;_eab < _fafa -len (_ade )+1;_eab ++{_fad :=int (_dcd .readByte ());_adc ,_fgc :=_dcd .ReadStr (_fad );
if _fgc !=nil {return _fgc ;};_ace [_eab ]=GlyphName (_adc );};};for _fba :=0;_fba < _daf ;_fba ++{_bdg :=_dcdg [_fba ];if _bdg < len (_ade ){_dcd ._def .GlyphNames [_fba ]=_ade [_bdg ];}else if _bdg >=len (_ade )&&_bdg <=32767{_dcd ._def .GlyphNames [_fba ]=_ace [_bdg -len (_ade )];
}else {_dcd ._def .GlyphNames [_fba ]="\u002e\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064";};};case 2.5:_gabg :=make ([]int ,_dcd ._gcce );for _efcd :=0;_efcd < len (_gabg );_efcd ++{_dcca :=int (_dcd .ReadSByte ());_gabg [_efcd ]=_efcd +1+_dcca ;
};_dcd ._def .GlyphNames =make ([]GlyphName ,len (_gabg ));for _bcg :=0;_bcg < len (_dcd ._def .GlyphNames );_bcg ++{_afde :=_ade [_gabg [_bcg ]];_dcd ._def .GlyphNames [_bcg ]=_afde ;};case 3.0:_ga .Log .Debug ("\u004e\u006f\u0020\u0050\u006f\u0073t\u0053\u0063\u0072i\u0070\u0074\u0020n\u0061\u006d\u0065\u0020\u0069\u006e\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020is\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e");
default:_ga .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0055\u006e\u006b\u006e\u006f\u0077\u006e\u0020f\u006fr\u006d\u0061\u0074\u0054\u0079\u0070\u0065=\u0025\u0066",_efd );};return nil ;};var _dabg =&RuneCharSafeMap {_gd :map[rune ]CharMetrics {' ':{Wx :250},'!':{Wx :333},'#':{Wx :500},'%':{Wx :833},'&':{Wx :778},'(':{Wx :333},')':{Wx :333},'+':{Wx :549},',':{Wx :250},'.':{Wx :250},'/':{Wx :278},'0':{Wx :500},'1':{Wx :500},'2':{Wx :500},'3':{Wx :500},'4':{Wx :500},'5':{Wx :500},'6':{Wx :500},'7':{Wx :500},'8':{Wx :500},'9':{Wx :500},':':{Wx :278},';':{Wx :278},'<':{Wx :549},'=':{Wx :549},'>':{Wx :549},'?':{Wx :444},'[':{Wx :333},']':{Wx :333},'_':{Wx :500},'{':{Wx :480},'|':{Wx :200},'}':{Wx :480},'¬':{Wx :713},'°':{Wx :400},'±':{Wx :549},'µ':{Wx :576},'×':{Wx :549},'÷':{Wx :549},'ƒ':{Wx :500},'Α':{Wx :722},'Β':{Wx :667},'Γ':{Wx :603},'Ε':{Wx :611},'Ζ':{Wx :611},'Η':{Wx :722},'Θ':{Wx :741},'Ι':{Wx :333},'Κ':{Wx :722},'Λ':{Wx :686},'Μ':{Wx :889},'Ν':{Wx :722},'Ξ':{Wx :645},'Ο':{Wx :722},'Π':{Wx :768},'Ρ':{Wx :556},'Σ':{Wx :592},'Τ':{Wx :611},'Υ':{Wx :690},'Φ':{Wx :763},'Χ':{Wx :722},'Ψ':{Wx :795},'α':{Wx :631},'β':{Wx :549},'γ':{Wx :411},'δ':{Wx :494},'ε':{Wx :439},'ζ':{Wx :494},'η':{Wx :603},'θ':{Wx :521},'ι':{Wx :329},'κ':{Wx :549},'λ':{Wx :549},'ν':{Wx :521},'ξ':{Wx :493},'ο':{Wx :549},'π':{Wx :549},'ρ':{Wx :549},'ς':{Wx :439},'σ':{Wx :603},'τ':{Wx :439},'υ':{Wx :576},'φ':{Wx :521},'χ':{Wx :549},'ψ':{Wx :686},'ω':{Wx :686},'ϑ':{Wx :631},'ϒ':{Wx :620},'ϕ':{Wx :603},'ϖ':{Wx :713},'•':{Wx :460},'…':{Wx :1000},'′':{Wx :247},'″':{Wx :411},'⁄':{Wx :167},'€':{Wx :750},'ℑ':{Wx :686},'℘':{Wx :987},'ℜ':{Wx :795},'Ω':{Wx :768},'ℵ':{Wx :823},'←':{Wx :987},'↑':{Wx :603},'→':{Wx :987},'↓':{Wx :603},'↔':{Wx :1042},'↵':{Wx :658},'⇐':{Wx :987},'⇑':{Wx :603},'⇒':{Wx :987},'⇓':{Wx :603},'⇔':{Wx :1042},'∀':{Wx :713},'∂':{Wx :494},'∃':{Wx :549},'∅':{Wx :823},'∆':{Wx :612},'∇':{Wx :713},'∈':{Wx :713},'∉':{Wx :713},'∋':{Wx :439},'∏':{Wx :823},'∑':{Wx :713},'−':{Wx :549},'∗':{Wx :500},'√':{Wx :549},'∝':{Wx :713},'∞':{Wx :713},'∠':{Wx :768},'∧':{Wx :603},'∨':{Wx :603},'∩':{Wx :768},'∪':{Wx :768},'∫':{Wx :274},'∴':{Wx :863},'∼':{Wx :549},'≅':{Wx :549},'≈':{Wx :549},'≠':{Wx :549},'≡':{Wx :549},'≤':{Wx :549},'≥':{Wx :549},'⊂':{Wx :713},'⊃':{Wx :713},'⊄':{Wx :713},'⊆':{Wx :713},'⊇':{Wx :713},'⊕':{Wx :768},'⊗':{Wx :768},'⊥':{Wx :658},'⋅':{Wx :250},'⌠':{Wx :686},'⌡':{Wx :686},'〈':{Wx :329},'〉':{Wx :329},'◊':{Wx :494},'♠':{Wx :753},'♣':{Wx :753},'♥':{Wx :753},'♦':{Wx :753},'\uf6d9':{Wx :790},'\uf6da':{Wx :790},'\uf6db':{Wx :890},'\uf8e5':{Wx :500},'\uf8e6':{Wx :603},'\uf8e7':{Wx :1000},'\uf8e8':{Wx :790},'\uf8e9':{Wx :790},'\uf8ea':{Wx :786},'\uf8eb':{Wx :384},'\uf8ec':{Wx :384},'\uf8ed':{Wx :384},'\uf8ee':{Wx :384},'\uf8ef':{Wx :384},'\uf8f0':{Wx :384},'\uf8f1':{Wx :494},'\uf8f2':{Wx :494},'\uf8f3':{Wx :494},'\uf8f4':{Wx :494},'\uf8f5':{Wx :686},'\uf8f6':{Wx :384},'\uf8f7':{Wx :384},'\uf8f8':{Wx :384},'\uf8f9':{Wx :384},'\uf8fa':{Wx :384},'\uf8fb':{Wx :384},'\uf8fc':{Wx :494},'\uf8fd':{Wx :494},'\uf8fe':{Wx :494},'\uf8ff':{Wx :790}}};
var _bac =[]int16 {722,889,722,722,722,722,722,722,722,722,722,667,667,667,667,667,722,722,722,612,611,611,611,611,611,611,611,611,611,722,500,556,722,722,722,722,333,333,333,333,333,333,333,333,389,722,722,611,611,611,611,611,889,722,722,722,722,722,722,889,722,722,722,722,722,722,722,722,556,722,667,667,667,667,556,556,556,556,556,611,611,611,556,722,722,722,722,722,722,722,722,722,722,944,722,722,722,722,611,611,611,611,444,444,444,444,333,444,667,444,444,778,444,444,469,541,500,921,444,500,278,200,480,480,333,333,333,200,350,444,444,333,444,444,333,500,333,278,250,250,760,500,500,500,500,588,500,400,333,564,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,564,500,333,333,333,556,500,556,500,500,167,500,500,500,500,333,564,549,500,500,333,333,500,333,333,278,278,278,278,278,278,278,278,500,500,278,278,344,278,564,549,564,471,278,778,333,564,500,564,500,500,500,500,500,549,500,500,500,500,500,500,722,333,500,500,500,500,750,750,300,276,310,500,500,500,453,333,333,476,833,250,250,1000,564,564,500,444,444,408,444,444,444,333,333,333,180,333,333,453,333,333,760,333,389,389,389,389,389,500,278,500,500,278,250,500,600,278,326,278,500,500,750,300,333,980,500,300,500,500,500,500,500,500,500,500,500,500,500,722,500,500,500,500,500,444,444,444,444,500};
func init (){RegisterStdFont (SymbolName ,_gcc ,"\u0053\u0079\u006d\u0062\u006f\u006c\u002c\u0049\u0074\u0061\u006c\u0069\u0063","S\u0079\u006d\u0062\u006f\u006c\u002c\u0042\u006f\u006c\u0064","\u0053\u0079\u006d\u0062\u006f\u006c\u002c\u0042\u006f\u006c\u0064\u0049t\u0061\u006c\u0069\u0063");
RegisterStdFont (ZapfDingbatsName ,_bbdg );};type fontMap struct{_aeb .Mutex ;_dec map[StdFontName ]func ()StdFont ;};func (_eaf StdFont )ToPdfObject ()_gb .PdfObject {_eeb :=_gb .MakeDict ();_eeb .Set ("\u0054\u0079\u0070\u0065",_gb .MakeName ("\u0046\u006f\u006e\u0074"));
_eeb .Set ("\u0053u\u0062\u0074\u0079\u0070\u0065",_gb .MakeName ("\u0054\u0079\u0070e\u0031"));_eeb .Set ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074",_gb .MakeName (_eaf .Name ()));_eeb .Set ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067",_eaf ._agd .ToPdfObject ());
return _gb .MakeIndirectObject (_eeb );};func NewStdFontWithEncoding (desc Descriptor ,metrics *RuneCharSafeMap ,encoder _be .TextEncoder )StdFont {var _ec rune =0xA0;if _ ,_fee :=metrics .Read (_ec );!_fee {_da ,_ :=metrics .Read (0x20);metrics .Write (_ec ,_da );
};return StdFont {_bge :desc ,_fef :metrics ,_agd :encoder };};func NewStdFontByName (name StdFontName )(StdFont ,bool ){_bd ,_cgd :=_ad .read (name );if !_cgd {return StdFont {},false ;};return _bd (),true ;};func (_fb *fontMap )write (_bc StdFontName ,_bbd func ()StdFont ){_fb .Lock ();
defer _fb .Unlock ();_fb ._dec [_bc ]=_bbd ;};func init (){RegisterStdFont (TimesRomanName ,_ffe ,"\u0054\u0069\u006d\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006d\u0061\u006e","\u0054\u0069\u006de\u0073");RegisterStdFont (TimesBoldName ,_dgc ,"\u0054i\u006de\u0073\u004e\u0065\u0077\u0052o\u006d\u0061n\u002c\u0042\u006f\u006c\u0064","\u0054\u0069\u006d\u0065\u0073\u002c\u0042\u006f\u006c\u0064");
RegisterStdFont (TimesItalicName ,_cge ,"T\u0069m\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006da\u006e\u002c\u0049\u0074al\u0069\u0063","\u0054\u0069\u006de\u0073\u002c\u0049\u0074\u0061\u006c\u0069\u0063");RegisterStdFont (TimesBoldItalicName ,_bbb ,"\u0054i\u006d\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006d\u0061\u006e,\u0042\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063","\u0054\u0069m\u0065\u0073\u002cB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063");
};func (_bcfe StdFont )Encoder ()_be .TextEncoder {return _bcfe ._agd };func (_aed *RuneCharSafeMap )Copy ()*RuneCharSafeMap {_bea :=MakeRuneCharSafeMap (_aed .Length ());_aed .Range (func (_fc rune ,_ag CharMetrics )(_ea bool ){_bea ._gd [_fc ]=_ag ;return false });
return _bea ;};func _gdd ()StdFont {_cgb .Do (_cca );_dcb :=Descriptor {Name :HelveticaName ,Family :string (HelveticaName ),Weight :FontWeightMedium ,Flags :0x0020,BBox :[4]float64 {-166,-225,1000,931},ItalicAngle :0,Ascent :718,Descent :-207,CapHeight :718,XHeight :523,StemV :88,StemH :76};
return NewStdFont (_dcb ,_aae );};var _dgd *RuneCharSafeMap ;func (_cbb *ttfParser )ParseOS2 ()error {if _fbce :=_cbb .Seek ("\u004f\u0053\u002f\u0032");_fbce !=nil {return _fbce ;};_bfb :=_cbb .ReadUShort ();_cbb .Skip (4*2);_cbb .Skip (11*2+10+4*4+4);
_dbfb :=_cbb .ReadUShort ();_cbb ._def .Bold =(_dbfb &32)!=0;_cbb .Skip (2*2);_cbb ._def .TypoAscender =_cbb .ReadShort ();_cbb ._def .TypoDescender =_cbb .ReadShort ();if _bfb >=2{_cbb .Skip (3*2+2*4+2);_cbb ._def .CapHeight =_cbb .ReadShort ();}else {_cbb ._def .CapHeight =0;
};return nil ;};var _beb *RuneCharSafeMap ;type GlyphName =_be .GlyphName ;func RegisterStdFont (name StdFontName ,fnc func ()StdFont ,aliases ...StdFontName ){if _ ,_bcf :=_ad .read (name );_bcf {panic ("\u0066o\u006e\u0074\u0020\u0061l\u0072\u0065\u0061\u0064\u0079 \u0072e\u0067i\u0073\u0074\u0065\u0072\u0065\u0064\u003a "+string (name ));
};_ad .write (name ,fnc );for _ ,_gdgg :=range aliases {RegisterStdFont (_gdgg ,fnc );};};const (FontWeightMedium FontWeight =iota ;FontWeightBold ;FontWeightRoman ;);type GID =_be .GID ;func (_aabgd *ttfParser )Skip (n int ){_aabgd ._cdc .Seek (int64 (n ),_b .SeekCurrent )};
func (_fcca *ttfParser )ReadUShort ()(_dea uint16 ){_e .Read (_fcca ._cdc ,_e .BigEndian ,&_dea );return _dea ;};type TtfType struct{UnitsPerEm uint16 ;PostScriptName string ;Bold bool ;ItalicAngle float64 ;IsFixedPitch bool ;TypoAscender int16 ;TypoDescender int16 ;
UnderlinePosition int16 ;UnderlineThickness int16 ;Xmin ,Ymin ,Xmax ,Ymax int16 ;CapHeight int16 ;Widths []uint16 ;Chars map[rune ]GID ;GlyphNames []GlyphName ;};func (_bee *ttfParser )parseTTC ()(TtfType ,error ){_bee .Skip (2*2);_ead :=_bee .ReadULong ();
if _ead < 1{return TtfType {},_g .New ("N\u006f \u0066\u006f\u006e\u0074\u0073\u0020\u0069\u006e \u0054\u0054\u0043\u0020fi\u006c\u0065");};_eeg :=_bee .ReadULong ();_ ,_eea :=_bee ._cdc .Seek (int64 (_eeg ),_b .SeekStart );if _eea !=nil {return TtfType {},_eea ;
};return _bee .Parse ();};type ttfParser struct{_def TtfType ;_cdc _b .ReadSeeker ;_add map[string ]uint32 ;_age uint16 ;_gcce uint16 ;};func (_bgeb *ttfParser )Parse ()(TtfType ,error ){_bbcb ,_cce :=_bgeb .ReadStr (4);if _cce !=nil {return TtfType {},_cce ;
};if _bbcb =="\u0074\u0074\u0063\u0066"{return _bgeb .parseTTC ();}else if _bbcb !="\u0000\u0001\u0000\u0000"&&_bbcb !="\u0074\u0072\u0075\u0065"{_ga .Log .Debug ("\u0055n\u0072\u0065c\u006f\u0067\u006ei\u007a\u0065\u0064\u0020\u0054\u0072\u0075e\u0054\u0079\u0070\u0065\u0020\u0066i\u006c\u0065\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u002e\u0020v\u0065\u0072\u0073\u0069\u006f\u006e\u003d\u0025\u0071",_bbcb );
};_eff :=int (_bgeb .ReadUShort ());_bgeb .Skip (3*2);_bgeb ._add =make (map[string ]uint32 );var _bab string ;for _ccf :=0;_ccf < _eff ;_ccf ++{_bab ,_cce =_bgeb .ReadStr (4);if _cce !=nil {return TtfType {},_cce ;};_bgeb .Skip (4);_afg :=_bgeb .ReadULong ();
_bgeb .Skip (4);_bgeb ._add [_bab ]=_afg ;};_ga .Log .Trace (_aebe (_bgeb ._add ));if _cce =_bgeb .ParseComponents ();_cce !=nil {return TtfType {},_cce ;};return _bgeb ._def ,nil ;};func (_fe *fontMap )read (_aab StdFontName )(func ()StdFont ,bool ){_fe .Lock ();
defer _fe .Unlock ();_bb ,_dg :=_fe ._dec [_aab ];return _bb ,_dg ;};var _dbf _aeb .Once ;func _dca ()StdFont {_cgb .Do (_cca );_ced :=Descriptor {Name :HelveticaBoldName ,Family :string (HelveticaName ),Weight :FontWeightBold ,Flags :0x0020,BBox :[4]float64 {-170,-228,1003,962},ItalicAngle :0,Ascent :718,Descent :-207,CapHeight :718,XHeight :532,StemV :140,StemH :118};
return NewStdFont (_ced ,_bece );};func (_abb *TtfType )String ()string {return _f .Sprintf ("\u0046\u004fN\u0054\u005f\u0046\u0049\u004cE\u0032\u007b\u0025\u0023\u0071 \u0055\u006e\u0069\u0074\u0073\u0050\u0065\u0072\u0045\u006d\u003d\u0025\u0064\u0020\u0042\u006f\u006c\u0064\u003d\u0025\u0074\u0020\u0049\u0074\u0061\u006c\u0069\u0063\u0041\u006e\u0067\u006c\u0065\u003d\u0025\u0066\u0020"+"\u0043\u0061pH\u0065\u0069\u0067h\u0074\u003d\u0025\u0064 Ch\u0061rs\u003d\u0025\u0064\u0020\u0047\u006c\u0079ph\u004e\u0061\u006d\u0065\u0073\u003d\u0025d\u007d",_abb .PostScriptName ,_abb .UnitsPerEm ,_abb .Bold ,_abb .ItalicAngle ,_abb .CapHeight ,len (_abb .Chars ),len (_abb .GlyphNames ));
};type Font interface{Encoder ()_be .TextEncoder ;GetRuneMetrics (_ee rune )(CharMetrics ,bool );};func _fccb ()StdFont {_cgb .Do (_cca );_bef :=Descriptor {Name :HelveticaObliqueName ,Family :string (HelveticaName ),Weight :FontWeightMedium ,Flags :0x0060,BBox :[4]float64 {-170,-225,1116,931},ItalicAngle :-12,Ascent :718,Descent :-207,CapHeight :718,XHeight :523,StemV :88,StemH :76};
return NewStdFont (_bef ,_fae );};var _aede =[]rune {'A','Æ','Á','Ă','Â','Ä','À','Ā','Ą','Å','Ã','B','C','Ć','Č','Ç','D','Ď','Đ','∆','E','É','Ě','Ê','Ë','Ė','È','Ē','Ę','Ð','€','F','G','Ğ','Ģ','H','I','Í','Î','Ï','İ','Ì','Ī','Į','J','K','Ķ','L','Ĺ','Ľ','Ļ','Ł','M','N','Ń','Ň','Ņ','Ñ','O','Œ','Ó','Ô','Ö','Ò','Ő','Ō','Ø','Õ','P','Q','R','Ŕ','Ř','Ŗ','S','Ś','Š','Ş','Ș','T','Ť','Ţ','Þ','U','Ú','Û','Ü','Ù','Ű','Ū','Ų','Ů','V','W','X','Y','Ý','Ÿ','Z','Ź','Ž','Ż','a','á','ă','â','´','ä','æ','à','ā','&','ą','å','^','~','*','@','ã','b','\\','|','{','}','[',']','˘','¦','•','c','ć','ˇ','č','ç','¸','¢','ˆ',':',',','\uf6c3','©','¤','d','†','‡','ď','đ','°','¨','÷','$','˙','ı','e','é','ě','ê','ë','ė','è','8','…','ē','—','–','ę','=','ð','!','¡','f','ﬁ','5','ﬂ','ƒ','4','⁄','g','ğ','ģ','ß','`','>','≥','«','»','‹','›','h','˝','-','i','í','î','ï','ì','ī','į','j','k','ķ','l','ĺ','ľ','ļ','<','≤','¬','◊','ł','m','¯','−','µ','×','n','ń','ň','ņ','9','≠','ñ','#','o','ó','ô','ö','œ','˛','ò','ő','ō','1','½','¼','¹','ª','º','ø','õ','p','¶','(',')','∂','%','.','·','‰','+','±','q','?','¿','"','„','“','”','‘','’','‚','\'','r','ŕ','√','ř','ŗ','®','˚','s','ś','š','ş','ș','§',';','7','6','/',' ','£','∑','t','ť','ţ','þ','3','¾','³','˜','™','2','²','u','ú','û','ü','ù','ű','ū','_','ų','ů','v','w','x','y','ý','ÿ','¥','z','ź','ž','ż','0'};
func (_gab *ttfParser )ParseComponents ()error {if _dde :=_gab .ParseHead ();_dde !=nil {return _dde ;};if _cac :=_gab .ParseHhea ();_cac !=nil {return _cac ;};if _bcc :=_gab .ParseMaxp ();_bcc !=nil {return _bcc ;};if _eebd :=_gab .ParseHmtx ();_eebd !=nil {return _eebd ;
};if _ ,_fbec :=_gab ._add ["\u006e\u0061\u006d\u0065"];_fbec {if _gbd :=_gab .ParseName ();_gbd !=nil {return _gbd ;};};if _ ,_faaf :=_gab ._add ["\u004f\u0053\u002f\u0032"];_faaf {if _cgc :=_gab .ParseOS2 ();_cgc !=nil {return _cgc ;};};if _ ,_eb :=_gab ._add ["\u0070\u006f\u0073\u0074"];
_eb {if _fde :=_gab .ParsePost ();_fde !=nil {return _fde ;};};if _ ,_faed :=_gab ._add ["\u0063\u006d\u0061\u0070"];_faed {if _eaae :=_gab .ParseCmap ();_eaae !=nil {return _eaae ;};};return nil ;};func (_fgfc *ttfParser )ParseCmap ()error {var _gaeg int64 ;
if _ggc :=_fgfc .Seek ("\u0063\u006d\u0061\u0070");_ggc !=nil {return _ggc ;};_fgfc .ReadUShort ();_febe :=int (_fgfc .ReadUShort ());_bgea :=int64 (0);_dcc :=int64 (0);_gac :=int64 (0);for _egdf :=0;_egdf < _febe ;_egdf ++{_bacf :=_fgfc .ReadUShort ();
_ggb :=_fgfc .ReadUShort ();_gaeg =int64 (_fgfc .ReadULong ());if _bacf ==3&&_ggb ==1{_dcc =_gaeg ;}else if _bacf ==3&&_ggb ==10{_gac =_gaeg ;}else if _bacf ==1&&_ggb ==0{_bgea =_gaeg ;};};if _bgea !=0{if _gcca :=_fgfc .parseCmapVersion (_bgea );_gcca !=nil {return _gcca ;
};};if _dcc !=0{if _gbdd :=_fgfc .parseCmapSubtable31 (_dcc );_gbdd !=nil {return _gbdd ;};};if _gac !=0{if _eef :=_fgfc .parseCmapVersion (_gac );_eef !=nil {return _eef ;};};if _dcc ==0&&_bgea ==0&&_gac ==0{_ga .Log .Debug ("\u0074\u0074\u0066P\u0061\u0072\u0073\u0065\u0072\u002e\u0050\u0061\u0072\u0073\u0065\u0043\u006d\u0061\u0070\u002e\u0020\u004e\u006f\u0020\u0033\u0031\u002c\u0020\u0031\u0030\u002c\u0020\u00331\u0030\u0020\u0074\u0061\u0062\u006c\u0065\u002e");
};return nil ;};func _gfe (){const _faf =600;_fac =MakeRuneCharSafeMap (len (_aede ));for _ ,_cfe :=range _aede {_fac .Write (_cfe ,CharMetrics {Wx :_faf });};_fga =_fac .Copy ();_dgd =_fac .Copy ();_dbfe =_fac .Copy ();};var _bece *RuneCharSafeMap ;func MakeRuneCharSafeMap (length int )*RuneCharSafeMap {return &RuneCharSafeMap {_gd :make (map[rune ]CharMetrics ,length )};
};func (_abbea *ttfParser )Read32Fixed ()float64 {_bgebb :=float64 (_abbea .ReadShort ());_gdba :=float64 (_abbea .ReadUShort ())/65536.0;return _bgebb +_gdba ;};func (_aef *ttfParser )ReadStr (length int )(string ,error ){_fedg :=make ([]byte ,length );
_bca ,_ffd :=_aef ._cdc .Read (_fedg );if _ffd !=nil {return "",_ffd ;}else if _bca !=length {return "",_f .Errorf ("\u0075\u006e\u0061bl\u0065\u0020\u0074\u006f\u0020\u0072\u0065\u0061\u0064\u0020\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0073",length );
};return string (_fedg ),nil ;};func (_bbda *ttfParser )ReadShort ()(_cec int16 ){_e .Read (_bbda ._cdc ,_e .BigEndian ,&_cec );return _cec ;};func (_edf *TtfType )MakeToUnicode ()*_ca .CMap {_ecd :=make (map[_ca .CharCode ]rune );if len (_edf .GlyphNames )==0{for _fbea :=range _edf .Chars {_ecd [_ca .CharCode (_fbea )]=_fbea ;
};return _ca .NewToUnicodeCMap (_ecd );};for _gfc ,_bebe :=range _edf .Chars {_bgee :=_ca .CharCode (_gfc );_gcb :=_edf .GlyphNames [_bebe ];_bgf ,_efc :=_be .GlyphToRune (_gcb );if !_efc {_ga .Log .Debug ("\u004e\u006f \u0072\u0075\u006e\u0065\u002e\u0020\u0063\u006f\u0064\u0065\u003d\u0030\u0078\u0025\u0030\u0034\u0078\u0020\u0067\u006c\u0079\u0070h=\u0025\u0071",_gfc ,_gcb );
_bgf =_be .MissingCodeRune ;};_ecd [_bgee ]=_bgf ;};return _ca .NewToUnicodeCMap (_ecd );};func _dae (){_beb =MakeRuneCharSafeMap (len (_aede ));_eee =MakeRuneCharSafeMap (len (_aede ));_ffb =MakeRuneCharSafeMap (len (_aede ));_dge =MakeRuneCharSafeMap (len (_aede ));
for _gad ,_dd :=range _aede {_beb .Write (_dd ,CharMetrics {Wx :float64 (_bac [_gad ])});_eee .Write (_dd ,CharMetrics {Wx :float64 (_ecf [_gad ])});_ffb .Write (_dd ,CharMetrics {Wx :float64 (_fce [_gad ])});_dge .Write (_dd ,CharMetrics {Wx :float64 (_ef [_gad ])});
};};func (_bbab *ttfParser )parseCmapSubtable10 (_afb int64 )error {if _bbab ._def .Chars ==nil {_bbab ._def .Chars =make (map[rune ]GID );};_bbab ._cdc .Seek (int64 (_bbab ._add ["\u0063\u006d\u0061\u0070"])+_afb ,_b .SeekStart );var _gef ,_dcba uint32 ;
_ddb :=_bbab .ReadUShort ();if _ddb < 8{_gef =uint32 (_bbab .ReadUShort ());_dcba =uint32 (_bbab .ReadUShort ());}else {_bbab .ReadUShort ();_gef =_bbab .ReadULong ();_dcba =_bbab .ReadULong ();};_ga .Log .Trace ("\u0070\u0061r\u0073\u0065\u0043\u006d\u0061p\u0053\u0075\u0062\u0074\u0061b\u006c\u0065\u0031\u0030\u003a\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u003d\u0025\u0064\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003d\u0025\u0064\u0020\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065\u003d\u0025\u0064",_ddb ,_gef ,_dcba );
if _ddb !=0{return _g .New ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0063\u006d\u0061p\u0020s\u0075\u0062\u0074\u0061\u0062\u006c\u0065\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_ecab ,_dag :=_bbab .ReadStr (256);if _dag !=nil {return _dag ;
};_dgg :=[]byte (_ecab );for _ecdf ,_eag :=range _dgg {_bbab ._def .Chars [rune (_ecdf )]=GID (_eag );if _eag !=0{_f .Printf ("\u0009\u0030\u0078\u002502\u0078\u0020\u279e\u0020\u0030\u0078\u0025\u0030\u0032\u0078\u003d\u0025\u0063\u000a",_ecdf ,_eag ,rune (_eag ));
};};return nil ;};func (_agda *ttfParser )ParseHhea ()error {if _ebe :=_agda .Seek ("\u0068\u0068\u0065\u0061");_ebe !=nil {return _ebe ;};_agda .Skip (4+15*2);_agda ._age =_agda .ReadUShort ();return nil ;};type Descriptor struct{Name StdFontName ;Family string ;
Weight FontWeight ;Flags uint ;BBox [4]float64 ;ItalicAngle float64 ;Ascent float64 ;Descent float64 ;CapHeight float64 ;XHeight float64 ;StemV float64 ;StemH float64 ;};func (_de *RuneCharSafeMap )Read (b rune )(CharMetrics ,bool ){_de ._gdf .RLock ();
defer _de ._gdf .RUnlock ();_cc ,_bg :=_de ._gd [b ];return _cc ,_bg ;};func (_fecdd *ttfParser )parseCmapFormat0 ()error {_debc ,_aee :=_fecdd .ReadStr (256);if _aee !=nil {return _aee ;};_cba :=[]byte (_debc );_ga .Log .Trace ("\u0070a\u0072\u0073e\u0043\u006d\u0061p\u0046\u006f\u0072\u006d\u0061\u0074\u0030:\u0020\u0025\u0073\u000a\u0064\u0061t\u0061\u0053\u0074\u0072\u003d\u0025\u002b\u0071\u000a\u0064\u0061t\u0061\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d",_fecdd ._def .String (),_debc ,_cba );
for _aabg ,_fge :=range _cba {_fecdd ._def .Chars [rune (_aabg )]=GID (_fge );};return nil ;};var _eee *RuneCharSafeMap ;func _bbb ()StdFont {_df .Do (_dae );_aaeb :=Descriptor {Name :TimesBoldItalicName ,Family :_ed ,Weight :FontWeightBold ,Flags :0x0060,BBox :[4]float64 {-200,-218,996,921},ItalicAngle :-15,Ascent :683,Descent :-217,CapHeight :669,XHeight :462,StemV :121,StemH :42};
return NewStdFont (_aaeb ,_ffb );};func (_gfd *ttfParser )Seek (tag string )error {_eegc ,_egeb :=_gfd ._add [tag ];if !_egeb {return _f .Errorf ("\u0074\u0061\u0062\u006ce \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0073",tag );
};_gfd ._cdc .Seek (int64 (_eegc ),_b .SeekStart );return nil ;};func _cca (){_aae =MakeRuneCharSafeMap (len (_aede ));_bece =MakeRuneCharSafeMap (len (_aede ));for _ge ,_eaa :=range _aede {_aae .Write (_eaa ,CharMetrics {Wx :float64 (_dce [_ge ])});_bece .Write (_eaa ,CharMetrics {Wx :float64 (_bae [_ge ])});
};_fae =_aae .Copy ();_agb =_bece .Copy ();};type StdFontName string ;func (_fab StdFont )GetMetricsTable ()*RuneCharSafeMap {return _fab ._fef };func (_gbg *ttfParser )ReadULong ()(_fege uint32 ){_e .Read (_gbg ._cdc ,_e .BigEndian ,&_fege );return _fege ;
};func (_gcd *ttfParser )parseCmapSubtable31 (_dgb int64 )error {_aga :=make ([]rune ,0,8);_ddf :=make ([]rune ,0,8);_cbf :=make ([]int16 ,0,8);_fgf :=make ([]uint16 ,0,8);_gcd ._def .Chars =make (map[rune ]GID );_gcd ._cdc .Seek (int64 (_gcd ._add ["\u0063\u006d\u0061\u0070"])+_dgb ,_b .SeekStart );
_agea :=_gcd .ReadUShort ();if _agea !=4{_ga .Log .Debug ("u\u006e\u0065\u0078\u0070\u0065\u0063t\u0065\u0064\u0020\u0073\u0075\u0062t\u0061\u0062\u006c\u0065\u0020\u0066\u006fr\u006d\u0061\u0074\u003a\u0020\u0025\u0064\u0020\u0028\u0025w\u0029",_agea );
return nil ;};_gcd .Skip (2*2);_aad :=int (_gcd .ReadUShort ()/2);_gcd .Skip (3*2);for _egd :=0;_egd < _aad ;_egd ++{_ddf =append (_ddf ,rune (_gcd .ReadUShort ()));};_gcd .Skip (2);for _fgg :=0;_fgg < _aad ;_fgg ++{_aga =append (_aga ,rune (_gcd .ReadUShort ()));
};for _cgba :=0;_cgba < _aad ;_cgba ++{_cbf =append (_cbf ,_gcd .ReadShort ());};_edd ,_ :=_gcd ._cdc .Seek (int64 (0),_b .SeekCurrent );for _gbc :=0;_gbc < _aad ;_gbc ++{_fgf =append (_fgf ,_gcd .ReadUShort ());};for _beda :=0;_beda < _aad ;_beda ++{_ceg :=_aga [_beda ];
_bccd :=_ddf [_beda ];_ege :=_cbf [_beda ];_eddd :=_fgf [_beda ];if _eddd > 0{_gcd ._cdc .Seek (_edd +2*int64 (_beda )+int64 (_eddd ),_b .SeekStart );};for _fbc :=_ceg ;_fbc <=_bccd ;_fbc ++{if _fbc ==0xFFFF{break ;};var _cda int32 ;if _eddd > 0{_cda =int32 (_gcd .ReadUShort ());
if _cda > 0{_cda +=int32 (_ege );};}else {_cda =_fbc +int32 (_ege );};if _cda >=65536{_cda -=65536;};if _cda > 0{_gcd ._def .Chars [_fbc ]=GID (_cda );};};};return nil ;};const (SymbolName =StdFontName ("\u0053\u0079\u006d\u0062\u006f\u006c");ZapfDingbatsName =StdFontName ("\u005a\u0061\u0070f\u0044\u0069\u006e\u0067\u0062\u0061\u0074\u0073");
);func (_bebd *ttfParser )ReadSByte ()(_gcba int8 ){_e .Read (_bebd ._cdc ,_e .BigEndian ,&_gcba );return _gcba ;};func (_gdb *TtfType )MakeEncoder ()(_be .SimpleEncoder ,error ){_dgdc :=make (map[_be .CharCode ]GlyphName );for _fd :=_be .CharCode (0);
_fd <=256;_fd ++{_abf :=rune (_fd );_fbe ,_feb :=_gdb .Chars [_abf ];if !_feb {continue ;};var _gg GlyphName ;if int (_fbe )>=0&&int (_fbe )< len (_gdb .GlyphNames ){_gg =_gdb .GlyphNames [_fbe ];}else {_fbda :=rune (_fbe );if _befd ,_cd :=_be .RuneToGlyph (_fbda );
_cd {_gg =_befd ;};};if _gg !=""{_dgdc [_fd ]=_gg ;};};if len (_dgdc )==0{_ga .Log .Debug ("WA\u0052\u004eI\u004e\u0047\u003a\u0020\u005a\u0065\u0072\u006f\u0020l\u0065\u006e\u0067\u0074\u0068\u0020\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002e\u0020\u0074\u0074\u0066=\u0025s\u0020\u0043\u0068\u0061\u0072\u0073\u003d\u005b%\u00200\u0032\u0078]",_gdb ,_gdb .Chars );
};return _be .NewCustomSimpleTextEncoder (_dgdc ,nil );};func (_gbf *TtfType )NewEncoder ()_be .TextEncoder {return _be .NewTrueTypeFontEncoder (_gbf .Chars )};var _fac *RuneCharSafeMap ;var _dge *RuneCharSafeMap ;func (_adb *ttfParser )parseCmapFormat12 ()error {_agbb :=_adb .ReadULong ();
_ga .Log .Trace ("\u0070\u0061\u0072se\u0043\u006d\u0061\u0070\u0046\u006f\u0072\u006d\u0061t\u00312\u003a \u0025s\u0020\u006e\u0075\u006d\u0047\u0072\u006f\u0075\u0070\u0073\u003d\u0025\u0064",_adb ._def .String (),_agbb );for _fccc :=uint32 (0);_fccc < _agbb ;
_fccc ++{_cad :=_adb .ReadULong ();_ddec :=_adb .ReadULong ();_ffa :=_adb .ReadULong ();if _cad > 0x0010FFFF||(0xD800<=_cad &&_cad <=0xDFFF){return _g .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u0068\u0061\u0072\u0061c\u0074\u0065\u0072\u0073\u0020\u0063\u006f\u0064\u0065\u0073");
};if _ddec < _cad ||_ddec > 0x0010FFFF||(0xD800<=_ddec &&_ddec <=0xDFFF){return _g .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u0068\u0061\u0072\u0061c\u0074\u0065\u0072\u0073\u0020\u0063\u006f\u0064\u0065\u0073");};for _bccb :=_cad ;_bccb <=_ddec ;
_bccb ++{if _bccb > 0x10FFFF{_ga .Log .Debug ("\u0046\u006fr\u006d\u0061\u0074\u0020\u0031\u0032\u0020\u0063\u006d\u0061\u0070\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0062\u0065\u0079\u006f\u006e\u0064\u0020\u0055\u0043\u0053\u002d\u0034");
};_adb ._def .Chars [rune (_bccb )]=GID (_ffa );_ffa ++;};};return nil ;};func (_faa CharMetrics )String ()string {return _f .Sprintf ("<\u0025\u002e\u0031\u0066\u002c\u0025\u002e\u0031\u0066\u003e",_faa .Wx ,_faa .Wy );};