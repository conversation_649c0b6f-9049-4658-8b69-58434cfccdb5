//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package optimize ;import (_bf "bytes";_ce "crypto/md5";_ba "errors";_ee "fmt";_f "github.com/unidoc/unipdf/v4/common";_cf "github.com/unidoc/unipdf/v4/contentstream";_fd "github.com/unidoc/unipdf/v4/core";_dd "github.com/unidoc/unipdf/v4/extractor";_eb "github.com/unidoc/unipdf/v4/internal/imageutil";
_d "github.com/unidoc/unipdf/v4/internal/textencoding";_be "github.com/unidoc/unipdf/v4/model";_fc "github.com/unidoc/unitype";_e "golang.org/x/image/draw";_b "math";_ef "strings";);

// Chain allows to use sequence of optimizers.
// It implements interface model.Optimizer.
type Chain struct{_cef []_be .Optimizer };func _gce (_aebb _fd .PdfObject )(_ececa string ,_cacg []_fd .PdfObject ){var _caea _bf .Buffer ;switch _adae :=_aebb .(type ){case *_fd .PdfIndirectObject :_cacg =append (_cacg ,_adae );_aebb =_adae .PdfObject ;
};switch _bbb :=_aebb .(type ){case *_fd .PdfObjectStream :if _dgdg ,_dggg :=_fd .DecodeStream (_bbb );_dggg ==nil {_caea .Write (_dgdg );_cacg =append (_cacg ,_bbb );};case *_fd .PdfObjectArray :for _ ,_ggc :=range _bbb .Elements (){switch _eabb :=_ggc .(type ){case *_fd .PdfObjectStream :if _fegd ,_fbcc :=_fd .DecodeStream (_eabb );
_fbcc ==nil {_caea .Write (_fegd );_cacg =append (_cacg ,_eabb );};};};};return _caea .String (),_cacg ;};

// CombineDuplicateDirectObjects combines duplicated direct objects by its data hash.
// It implements interface model.Optimizer.
type CombineDuplicateDirectObjects struct{};func _abca (_eeace *_be .XObjectImage ,_cda imageModifications )error {_decd ,_gage :=_eeace .ToImage ();if _gage !=nil {return _gage ;};if _cda .Scale !=0{_decd ,_gage =_dcd (_decd ,_cda .Scale );if _gage !=nil {return _gage ;
};};if _cda .Encoding !=nil {_eeace .Filter =_cda .Encoding ;};_eeace .Decode =nil ;switch _cafd :=_eeace .Filter .(type ){case *_fd .FlateEncoder :if _cafd .Predictor !=1&&_cafd .Predictor !=11{_cafd .Predictor =1;};};if _gage =_eeace .SetImage (_decd ,nil );
_gage !=nil {_f .Log .Debug ("\u0045\u0072\u0072or\u0020\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0076",_gage );return _gage ;};_eeace .ToPdfObject ();return nil ;};func _dcdb (_agdb []_fd .PdfObject ,_cabb map[_fd .PdfObject ]_fd .PdfObject ){if len (_cabb )==0{return ;
};for _bcca ,_bfa :=range _agdb {if _edfdb ,_dcda :=_cabb [_bfa ];_dcda {_agdb [_bcca ]=_edfdb ;continue ;};_cabb [_bfa ]=_bfa ;switch _caeb :=_bfa .(type ){case *_fd .PdfObjectArray :_ddb :=make ([]_fd .PdfObject ,_caeb .Len ());copy (_ddb ,_caeb .Elements ());
_dcdb (_ddb ,_cabb );for _fced ,_bgff :=range _ddb {_caeb .Set (_fced ,_bgff );};case *_fd .PdfObjectStreams :_dcdb (_caeb .Elements (),_cabb );case *_fd .PdfObjectStream :_gbf :=[]_fd .PdfObject {_caeb .PdfObjectDictionary };_dcdb (_gbf ,_cabb );_caeb .PdfObjectDictionary =_gbf [0].(*_fd .PdfObjectDictionary );
case *_fd .PdfObjectDictionary :_fdd :=_caeb .Keys ();_dbg :=make ([]_fd .PdfObject ,len (_fdd ));for _agf ,_bdec :=range _fdd {_dbg [_agf ]=_caeb .Get (_bdec );};_dcdb (_dbg ,_cabb );for _facc ,_cgc :=range _fdd {_caeb .Set (_cgc ,_dbg [_facc ]);};case *_fd .PdfIndirectObject :_eae :=[]_fd .PdfObject {_caeb .PdfObject };
_dcdb (_eae ,_cabb );_caeb .PdfObject =_eae [0];};};};func _baca (_caa *_cf .ContentStreamOperations )*_cf .ContentStreamOperations {if _caa ==nil {return nil ;};_cg :=_cf .ContentStreamOperations {};for _ ,_ddc :=range *_caa {switch _ddc .Operand {case "\u0042\u0044\u0043","\u0042\u004d\u0043","\u0045\u004d\u0043":continue ;
case "\u0054\u006d":if len (_ddc .Params )==6{if _gg ,_ad :=_fd .GetNumbersAsFloat (_ddc .Params );_ad ==nil {if _gg [0]==1&&_gg [1]==0&&_gg [2]==0&&_gg [3]==1{_ddc =&_cf .ContentStreamOperation {Params :[]_fd .PdfObject {_ddc .Params [4],_ddc .Params [5]},Operand :"\u0054\u0064"};
};};};};_cg =append (_cg ,_ddc );};return &_cg ;};func _ffee (_eeb _fd .PdfObject ,_dbd map[_fd .PdfObject ]struct{})error {if _ffba ,_bdeb :=_eeb .(*_fd .PdfIndirectObject );_bdeb {_dbd [_eeb ]=struct{}{};_cefb :=_ffee (_ffba .PdfObject ,_dbd );if _cefb !=nil {return _cefb ;
};return nil ;};if _bcbc ,_gcad :=_eeb .(*_fd .PdfObjectStream );_gcad {_dbd [_bcbc ]=struct{}{};_ecb :=_ffee (_bcbc .PdfObjectDictionary ,_dbd );if _ecb !=nil {return _ecb ;};return nil ;};if _baf ,_dgbf :=_eeb .(*_fd .PdfObjectDictionary );_dgbf {for _ ,_cdg :=range _baf .Keys (){_cea :=_baf .Get (_cdg );
_ =_cea ;if _bcgf ,_gac :=_cea .(*_fd .PdfObjectReference );_gac {_cea =_bcgf .Resolve ();_baf .Set (_cdg ,_cea );};if _cdg !="\u0050\u0061\u0072\u0065\u006e\u0074"{if _cag :=_ffee (_cea ,_dbd );_cag !=nil {return _cag ;};};};return nil ;};if _ccc ,_gcg :=_eeb .(*_fd .PdfObjectArray );
_gcg {if _ccc ==nil {return _ba .New ("\u0061\u0072\u0072a\u0079\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};for _dfdb ,_eef :=range _ccc .Elements (){if _bgae ,_cdf :=_eef .(*_fd .PdfObjectReference );_cdf {_eef =_bgae .Resolve ();_ccc .Set (_dfdb ,_eef );
};if _cfbg :=_ffee (_eef ,_dbd );_cfbg !=nil {return _cfbg ;};};return nil ;};return nil ;};

// CleanFonts cleans up embedded fonts, reducing font sizes.
type CleanFonts struct{

// Subset embedded fonts if encountered (if true).
// Otherwise attempts to reduce the font program.
Subset bool ;};

// Image optimizes images by rewrite images into JPEG format with quality equals to ImageQuality.
// TODO(a5i): Add support for inline images.
// It implements interface model.Optimizer.
type Image struct{ImageQuality int ;};type objectStructure struct{_dbdd *_fd .PdfObjectDictionary ;_gebd *_fd .PdfObjectDictionary ;_gegd []*_fd .PdfIndirectObject ;};

// CompressStreams compresses uncompressed streams.
// It implements interface model.Optimizer.
type CompressStreams struct{};func _ffb (_ddcd _fd .PdfObject )[]content {if _ddcd ==nil {return nil ;};_ffa ,_agg :=_fd .GetArray (_ddcd );if !_agg {_f .Log .Debug ("\u0041\u006e\u006e\u006fts\u0020\u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079");
return nil ;};var _cab []content ;for _ ,_beef :=range _ffa .Elements (){_ecf ,_fab :=_fd .GetDict (_beef );if !_fab {_f .Log .Debug ("I\u0067\u006e\u006f\u0072\u0069\u006eg\u0020\u006e\u006f\u006e\u002d\u0064i\u0063\u0074\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006e\u0020\u0041\u006e\u006e\u006ft\u0073");
continue ;};_bbca ,_fab :=_fd .GetDict (_ecf .Get ("\u0041\u0050"));if !_fab {_f .Log .Debug ("\u004e\u006f\u0020\u0041P \u0065\u006e\u0074\u0072\u0079\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067");continue ;};_efbd :=_fd .TraceToDirectObject (_bbca .Get ("\u004e"));
if _efbd ==nil {_f .Log .Debug ("N\u006f\u0020\u004e\u0020en\u0074r\u0079\u0020\u002d\u0020\u0073k\u0069\u0070\u0070\u0069\u006e\u0067");continue ;};var _ccb *_fd .PdfObjectStream ;switch _aee :=_efbd .(type ){case *_fd .PdfObjectDictionary :_bcd ,_fcb :=_fd .GetName (_ecf .Get ("\u0041\u0053"));
if !_fcb {_f .Log .Debug ("\u004e\u006f\u0020\u0041S \u0065\u006e\u0074\u0072\u0079\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067");continue ;};_ccb ,_fcb =_fd .GetStream (_aee .Get (*_bcd ));if !_fcb {_f .Log .Debug ("\u0046o\u0072\u006d\u0020\u006eo\u0074\u0020\u0066\u006f\u0075n\u0064 \u002d \u0073\u006b\u0069\u0070\u0070\u0069\u006eg");
continue ;};case *_fd .PdfObjectStream :_ccb =_aee ;};if _ccb ==nil {_f .Log .Debug ("\u0046\u006f\u0072m\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0028n\u0069\u006c\u0029\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067");
continue ;};_adg ,_fec :=_be .NewXObjectFormFromStream (_ccb );if _fec !=nil {_f .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020l\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_fec );
continue ;};_abd ,_fec :=_adg .GetContentStream ();if _fec !=nil {_f .Log .Debug ("E\u0072\u0072\u006f\u0072\u0020\u0064e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0063\u006fn\u0074\u0065\u006et\u0073:\u0020\u0025\u0076",_fec );continue ;};_cab =append (_cab ,content {_age :string (_abd ),_adb :_adg .Resources });
};return _cab ;};

// Append appends optimizers to the chain.
func (_df *Chain )Append (optimizers ..._be .Optimizer ){_df ._cef =append (_df ._cef ,optimizers ...)};type imageInfo struct{BitsPerComponent int ;ColorComponents int ;Width int ;Height int ;Stream *_fd .PdfObjectStream ;PPI float64 ;};func _bcdf (_fbgf []_fd .PdfObject )[]*imageInfo {_egg :=_fd .PdfObjectName ("\u0053u\u0062\u0074\u0079\u0070\u0065");
_gcfb :=make (map[*_fd .PdfObjectStream ]struct{});var _dbc []*imageInfo ;for _ ,_aacb :=range _fbgf {_acab ,_bgaea :=_fd .GetStream (_aacb );if !_bgaea {continue ;};if _ ,_abf :=_gcfb [_acab ];_abf {continue ;};_gcfb [_acab ]=struct{}{};_ffd :=_acab .PdfObjectDictionary .Get (_egg );
_fcba ,_bgaea :=_fd .GetName (_ffd );if !_bgaea ||string (*_fcba )!="\u0049\u006d\u0061g\u0065"{continue ;};_aebg :=&imageInfo {Stream :_acab ,BitsPerComponent :8};if _feg ,_gfe :=_fd .GetIntVal (_acab .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));
_gfe {_aebg .BitsPerComponent =_feg ;};if _adfd ,_cfa :=_fd .GetIntVal (_acab .Get ("\u0057\u0069\u0064t\u0068"));_cfa {_aebg .Width =_adfd ;};if _bfe ,_agc :=_fd .GetIntVal (_acab .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));_agc {_aebg .Height =_bfe ;
};_edbf ,_gfbf :=_be .NewPdfColorspaceFromPdfObject (_acab .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));if _gfbf !=nil {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_gfbf );continue ;};if _edbf ==nil {_ede ,_ccd :=_fd .GetName (_acab .Get ("\u0046\u0069\u006c\u0074\u0065\u0072"));
if _ccd {switch _ede .String (){case "\u0043\u0043\u0049\u0054\u0054\u0046\u0061\u0078\u0044e\u0063\u006f\u0064\u0065","J\u0042\u0049\u0047\u0032\u0044\u0065\u0063\u006f\u0064\u0065":_edbf =_be .NewPdfColorspaceDeviceGray ();_aebg .BitsPerComponent =1;
};};};switch _eefa :=_edbf .(type ){case *_be .PdfColorspaceDeviceRGB :_aebg .ColorComponents =3;case *_be .PdfColorspaceDeviceGray :_aebg .ColorComponents =1;default:_f .Log .Debug ("\u004f\u0070\u0074\u0069\u006d\u0069\u007aa\u0074\u0069\u006fn\u0020\u0069\u0073 \u006e\u006ft\u0020\u0073\u0075\u0070\u0070\u006fr\u0074ed\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0025\u0054\u0020\u002d\u0020\u0073\u006b\u0069\u0070",_eefa );
continue ;};_dbc =append (_dbc ,_aebg );};return _dbc ;};

// CleanUnusedResources represents an optimizer used to clean unused resources.
type CleanUnusedResources struct{};func _agbf (_gacb string ,_feb []string )bool {for _ ,_adeg :=range _feb {if _gacb ==_adeg {return true ;};};return false ;};func _dcd (_dbec *_be .Image ,_ccae float64 )(*_be .Image ,error ){_feed ,_bfef :=_dbec .ToGoImage ();
if _bfef !=nil {return nil ,_bfef ;};var _dbcf _eb .Image ;_bdc ,_dcge :=_feed .(*_eb .Monochrome );if _dcge {if _bfef =_bdc .ResolveDecode ();_bfef !=nil {return nil ,_bfef ;};_dbcf ,_bfef =_bdc .Scale (_ccae );if _bfef !=nil {return nil ,_bfef ;};}else {_cce :=int (_b .RoundToEven (float64 (_dbec .Width )*_ccae ));
_fce :=int (_b .RoundToEven (float64 (_dbec .Height )*_ccae ));_dbcf ,_bfef =_eb .NewImage (_cce ,_fce ,int (_dbec .BitsPerComponent ),_dbec .ColorComponents ,nil ,nil ,nil );if _bfef !=nil {return nil ,_bfef ;};_e .CatmullRom .Scale (_dbcf ,_dbcf .Bounds (),_feed ,_feed .Bounds (),_e .Over ,&_e .Options {});
};_adgc :=_dbcf .Base ();_aacc :=&_be .Image {Width :int64 (_adgc .Width ),Height :int64 (_adgc .Height ),BitsPerComponent :int64 (_adgc .BitsPerComponent ),ColorComponents :_adgc .ColorComponents ,Data :_adgc .Data };_aacc .SetDecode (_adgc .Decode );
_aacc .SetAlpha (_adgc .Alpha );return _aacc ,nil ;};

// Optimize implements Optimizer interface.
func (_bcfc *CleanUnusedResources )Optimize (objects []_fd .PdfObject )(_fcc []_fd .PdfObject ,_efc error ){_bede ,_efc :=_acf (objects );if _efc !=nil {return nil ,_efc ;};_eaaa :=[]_fd .PdfObject {};for _ ,_eag :=range objects {_ ,_gbc :=_bede [_eag ];
if _gbc {continue ;};_eaaa =append (_eaaa ,_eag );};return _eaaa ,nil ;};func _gfde (_fed _fd .PdfObject )(string ,error ){_acc :=_fd .TraceToDirectObject (_fed );switch _ebg :=_acc .(type ){case *_fd .PdfObjectString :return _ebg .Str (),nil ;case *_fd .PdfObjectStream :_ada ,_dgc :=_fd .DecodeStream (_ebg );
if _dgc !=nil {return "",_dgc ;};return string (_ada ),nil ;};return "",_ee .Errorf ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072e\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0068\u006f\u006c\u0064\u0065\u0072\u0020\u0028\u0025\u0054\u0029",_acc );
};func _ac (_fg *_fd .PdfObjectStream )error {_ace ,_bcf :=_fd .DecodeStream (_fg );if _bcf !=nil {return _bcf ;};_fgb :=_cf .NewContentStreamParser (string (_ace ));_ge ,_bcf :=_fgb .Parse ();if _bcf !=nil {return _bcf ;};_ge =_baca (_ge );_bb :=_ge .Bytes ();
if len (_bb )>=len (_ace ){return nil ;};_aca ,_bcf :=_fd .MakeStream (_ge .Bytes (),_fd .NewFlateEncoder ());if _bcf !=nil {return _bcf ;};_fg .Stream =_aca .Stream ;_fg .Merge (_aca .PdfObjectDictionary );return nil ;};type content struct{_age string ;
_adb *_be .PdfPageResources ;};

// ImagePPI optimizes images by scaling images such that the PPI (pixels per inch) is never higher than ImageUpperPPI.
// TODO(a5i): Add support for inline images.
// It implements interface model.Optimizer.
type ImagePPI struct{ImageUpperPPI float64 ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_eeg *CompressStreams )Optimize (objects []_fd .PdfObject )(_abge []_fd .PdfObject ,_dag error ){_abge =make ([]_fd .PdfObject ,len (objects ));copy (_abge ,objects );for _ ,_edb :=range objects {_ecga ,_afcc :=_fd .GetStream (_edb );if !_afcc {continue ;
};if _dfdf :=_ecga .Get ("\u0046\u0069\u006c\u0074\u0065\u0072");_dfdf !=nil {if _ ,_dgg :=_fd .GetName (_dfdf );_dgg {continue ;};if _beeb ,_ffgd :=_fd .GetArray (_dfdf );_ffgd &&_beeb .Len ()> 0{continue ;};};_feeg :=_fd .NewFlateEncoder ();var _gfg []byte ;
_gfg ,_dag =_feeg .EncodeBytes (_ecga .Stream );if _dag !=nil {return _abge ,_dag ;};_dbb :=_feeg .MakeStreamDict ();if len (_gfg )+len (_dbb .Write ())< len (_ecga .Stream ){_ecga .Stream =_gfg ;_ecga .PdfObjectDictionary .Merge (_dbb );_ecga .PdfObjectDictionary .Set ("\u004c\u0065\u006e\u0067\u0074\u0068",_fd .MakeInteger (int64 (len (_ecga .Stream ))));
};};return _abge ,nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_bcbcc *CombineIdenticalIndirectObjects )Optimize (objects []_fd .PdfObject )(_dec []_fd .PdfObject ,_baec error ){_fbeec (objects );_fbga :=make (map[_fd .PdfObject ]_fd .PdfObject );_gag :=make (map[_fd .PdfObject ]struct{});_edgbg :=make (map[string ][]*_fd .PdfIndirectObject );
for _ ,_fee :=range objects {_cfge ,_dae :=_fee .(*_fd .PdfIndirectObject );if !_dae {continue ;};if _fbcb ,_cddb :=_cfge .PdfObject .(*_fd .PdfObjectDictionary );_cddb {if _daba ,_fdb :=_fbcb .Get ("\u0054\u0079\u0070\u0065").(*_fd .PdfObjectName );_fdb &&*_daba =="\u0050\u0061\u0067\u0065"{continue ;
};if _cgee :=_fbcb .Keys ();len (_cgee )==0{continue ;};_cb :=_ce .New ();_cb .Write (_fbcb .Write ());_gfdec :=string (_cb .Sum (nil ));_edgbg [_gfdec ]=append (_edgbg [_gfdec ],_cfge );};};for _ ,_affb :=range _edgbg {if len (_affb )< 2{continue ;};_dda :=_affb [0];
for _fag :=1;_fag < len (_affb );_fag ++{_ecba :=_affb [_fag ];_fbga [_ecba ]=_dda ;_gag [_ecba ]=struct{}{};};};_dec =make ([]_fd .PdfObject ,0,len (objects )-len (_gag ));for _ ,_cgf :=range objects {if _ ,_afef :=_gag [_cgf ];_afef {continue ;};_dec =append (_dec ,_cgf );
};_dcdb (_dec ,_fbga );return _dec ,nil ;};func _ebb (_edf []_fd .PdfObject )(_efb map[*_fd .PdfObjectStream ]struct{},_fgc error ){_efb =map[*_fd .PdfObjectStream ]struct{}{};_ege :=map[*_be .PdfFont ]struct{}{};_dca :=_egefc (_edf );for _ ,_abc :=range _dca ._gegd {_fgca ,_gb :=_fd .GetDict (_abc .PdfObject );
if !_gb {continue ;};_eaa ,_gb :=_fd .GetDict (_fgca .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));if !_gb {continue ;};_fe ,_ :=_gce (_fgca .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));_cfg ,_ead :=_be .NewPdfPageResourcesFromDict (_eaa );
if _ead !=nil {return nil ,_ead ;};_ff :=[]content {{_age :_fe ,_adb :_cfg }};_bad :=_ffb (_fgca .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if _bad !=nil {_ff =append (_ff ,_bad ...);};for _ ,_badg :=range _ff {_fac ,_aed :=_dd .NewFromContents (_badg ._age ,_badg ._adb );
if _aed !=nil {return nil ,_aed ;};_edff ,_ ,_ ,_aed :=_fac .ExtractPageText ();if _aed !=nil {return nil ,_aed ;};for _ ,_eed :=range _edff .Marks ().Elements (){if _eed .Font ==nil {continue ;};if _ ,_bdd :=_ege [_eed .Font ];!_bdd {_ege [_eed .Font ]=struct{}{};
};};};};_aac :=map[*_fd .PdfObjectStream ][]*_be .PdfFont {};for _cga :=range _ege {_gad :=_cga .FontDescriptor ();if _gad ==nil ||_gad .FontFile2 ==nil {continue ;};_db ,_fcd :=_fd .GetStream (_gad .FontFile2 );if !_fcd {continue ;};_aac [_db ]=append (_aac [_db ],_cga );
};for _gca :=range _aac {var _acdf []rune ;var _fea []_fc .GlyphIndex ;for _ ,_feae :=range _aac [_gca ]{switch _cgad :=_feae .Encoder ().(type ){case *_d .IdentityEncoder :_cfbd :=_cgad .RegisteredRunes ();_gadb :=make ([]_fc .GlyphIndex ,len (_cfbd ));
for _edg ,_cac :=range _cfbd {_gadb [_edg ]=_fc .GlyphIndex (_cac );};_fea =append (_fea ,_gadb ...);case *_d .TrueTypeFontEncoder :_edgb :=_cgad .RegisteredRunes ();_acdf =append (_acdf ,_edgb ...);case _d .SimpleEncoder :_dab :=_cgad .Charcodes ();for _ ,_cgadd :=range _dab {_cdd ,_agb :=_cgad .CharcodeToRune (_cgadd );
if !_agb {_f .Log .Debug ("\u0043\u0068a\u0072\u0063\u006f\u0064\u0065\u003c\u002d\u003e\u0072\u0075\u006e\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064: \u0025\u0064",_cgadd );continue ;};_acdf =append (_acdf ,_cdd );};};};_fgc =_efg (_gca ,_acdf ,_fea );
if _fgc !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0074\u0069\u006eg\u0020f\u006f\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u003a\u0020\u0025\u0076",_fgc );return nil ,_fgc ;};_efb [_gca ]=struct{}{};
};return _efb ,nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_a *Chain )Optimize (objects []_fd .PdfObject )(_ca []_fd .PdfObject ,_bac error ){_bed :=objects ;for _ ,_aa :=range _a ._cef {_dc ,_ae :=_aa .Optimize (_bed );if _ae !=nil {_f .Log .Debug ("\u0045\u0052\u0052OR\u0020\u004f\u0070\u0074\u0069\u006d\u0069\u007a\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u002b\u0076",_ae );
continue ;};_bed =_dc ;};return _bed ,nil ;};func _efg (_fbf *_fd .PdfObjectStream ,_dfgf []rune ,_dg []_fc .GlyphIndex )error {_fbf ,_fgf :=_fd .GetStream (_fbf );if !_fgf {_f .Log .Debug ("\u0045\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u006e\u006f\u0074\u0020\u0066o\u0075\u006e\u0064\u0020\u002d\u002d\u0020\u0041\u0042\u004f\u0052T\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0074\u0069\u006e\u0067");
return _ba .New ("\u0066\u006f\u006e\u0074fi\u006c\u0065\u0032\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_bce ,_ffg :=_fd .DecodeStream (_fbf );if _ffg !=nil {_f .Log .Debug ("\u0044\u0065c\u006f\u0064\u0065 \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_ffg );
return _ffg ;};_eea ,_ffg :=_fc .Parse (_bf .NewReader (_bce ));if _ffg !=nil {_f .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0070\u0061\u0072\u0073\u0069n\u0067\u0020\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0020f\u006f\u006e\u0074",len (_fbf .Stream ));
return _ffg ;};_bca :=_dg ;if len (_dfgf )> 0{_cde :=_eea .LookupRunes (_dfgf );_bca =append (_bca ,_cde ...);};_eea ,_ffg =_eea .SubsetKeepIndices (_bca );if _ffg !=nil {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u0020s\u0075\u0062\u0073\u0065\u0074t\u0069n\u0067 \u0066\u006f\u006e\u0074\u003a\u0020\u0025v",_ffg );
return _ffg ;};var _fff _bf .Buffer ;_ffg =_eea .Write (&_fff );if _ffg !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004fR \u0057\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076",_ffg );return _ffg ;};if _fff .Len ()> len (_bce ){_f .Log .Debug ("\u0052\u0065-\u0077\u0072\u0069\u0074\u0074\u0065\u006e\u0020\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u002d\u0020\u0073\u006b\u0069\u0070");
return nil ;};_dfd ,_ffg :=_fd .MakeStream (_fff .Bytes (),_fd .NewFlateEncoder ());if _ffg !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004fR \u0057\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076",_ffg );return _ffg ;
};*_fbf =*_dfd ;_fbf .Set ("\u004ce\u006e\u0067\u0074\u0068\u0031",_fd .MakeInteger (int64 (_fff .Len ())));return nil ;};func _aab (_gda []*_fd .PdfIndirectObject )map[string ][]string {_ece :=map[string ][]string {};for _ ,_cdde :=range _gda {_bgc ,_adf :=_fd .GetDict (_cdde .PdfObject );
if !_adf {continue ;};_fgg :=_bgc .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073");_dbab :=_fd .TraceToDirectObject (_fgg );_dgb :="";if _bbg ,_ggd :=_dbab .(*_fd .PdfObjectArray );_ggd {var _gbgd []string ;for _ ,_dbfb :=range _bbg .Elements (){_edd ,_eda :=_gfde (_dbfb );
if _eda !=nil {continue ;};_gbgd =append (_gbgd ,_edd );};_dgb =_ef .Join (_gbgd ,"\u0020");};if _deg ,_daa :=_dbab .(*_fd .PdfObjectStream );_daa {_fca ,_bae :=_fd .DecodeStream (_deg );if _bae !=nil {continue ;};_dgb =string (_fca );};_abg :=_cf .NewContentStreamParser (_dgb );
_ddg ,_aede :=_abg .Parse ();if _aede !=nil {continue ;};for _ ,_ffe :=range *_ddg {_ffaa :=_ffe .Operand ;_cgab :=_ffe .Params ;switch _ffaa {case "\u0044\u006f":_fge :=_cgab [0].String ();if _ ,_bbgf :=_ece ["\u0058O\u0062\u006a\u0065\u0063\u0074"];!_bbgf {_ece ["\u0058O\u0062\u006a\u0065\u0063\u0074"]=[]string {_fge };
}else {_ece ["\u0058O\u0062\u006a\u0065\u0063\u0074"]=append (_ece ["\u0058O\u0062\u006a\u0065\u0063\u0074"],_fge );};case "\u0054\u0066":_geb :=_cgab [0].String ();if _ ,_bba :=_ece ["\u0046\u006f\u006e\u0074"];!_bba {_ece ["\u0046\u006f\u006e\u0074"]=[]string {_geb };
}else {_ece ["\u0046\u006f\u006e\u0074"]=append (_ece ["\u0046\u006f\u006e\u0074"],_geb );};case "\u0067\u0073":_bcg :=_cgab [0].String ();if _ ,_faf :=_ece ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"];!_faf {_ece ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"]=[]string {_bcg };
}else {_ece ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"]=append (_ece ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"],_bcg );};};};};return _ece ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_gdg *Image )Optimize (objects []_fd .PdfObject )(_ebf []_fd .PdfObject ,_gace error ){if _gdg .ImageQuality <=0{return objects ,nil ;};_dgcb :=_bcdf (objects );if len (_dgcb )==0{return objects ,nil ;};_aea :=make (map[_fd .PdfObject ]_fd .PdfObject );
_fgde :=make (map[_fd .PdfObject ]struct{});for _ ,_cgd :=range _dgcb {_bfc :=_cgd .Stream .Get ("\u0053\u004d\u0061s\u006b");_fgde [_bfc ]=struct{}{};};for _adc ,_gdbg :=range _dgcb {_cbg :=_gdbg .Stream ;if _ ,_gagd :=_fgde [_cbg ];_gagd {continue ;};
_egef ,_edac :=_be .NewXObjectImageFromStream (_cbg );if _edac !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_edac );continue ;};switch _egef .Filter .(type ){case *_fd .JBIG2Encoder :continue ;case *_fd .CCITTFaxEncoder :continue ;
};_abcg ,_edac :=_egef .ToImage ();if _edac !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_edac );continue ;};_afa :=_fd .NewDCTEncoder ();_afa .ColorComponents =_abcg .ColorComponents ;_afa .Quality =_gdg .ImageQuality ;
_afa .BitsPerComponent =_gdbg .BitsPerComponent ;_afa .Width =_gdbg .Width ;_afa .Height =_gdbg .Height ;_cfgd ,_edac :=_afa .EncodeBytes (_abcg .Data );if _edac !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_edac );
continue ;};var _agdd _fd .StreamEncoder ;_agdd =_afa ;{_gbcd :=_fd .NewFlateEncoder ();_dccg :=_fd .NewMultiEncoder ();_dccg .AddEncoder (_gbcd );_dccg .AddEncoder (_afa );_efcd ,_beb :=_dccg .EncodeBytes (_abcg .Data );if _beb !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_beb );
continue ;};if len (_efcd )< len (_cfgd ){_f .Log .Trace ("\u004d\u0075\u006c\u0074\u0069\u0020\u0065\u006e\u0063\u0020\u0069\u006d\u0070\u0072\u006f\u0076\u0065\u0073\u003a\u0020\u0025\u0064\u0020\u0074o\u0020\u0025\u0064\u0020\u0028o\u0072\u0069g\u0020\u0025\u0064\u0029",len (_cfgd ),len (_efcd ),len (_cbg .Stream ));
_cfgd =_efcd ;_agdd =_dccg ;};};_dde :=len (_cbg .Stream );if _dde < len (_cfgd ){continue ;};_dga :=&_fd .PdfObjectStream {Stream :_cfgd };_dga .PdfObjectReference =_cbg .PdfObjectReference ;_dga .PdfObjectDictionary =_fd .MakeDict ();_dga .Merge (_cbg .PdfObjectDictionary );
_dga .Merge (_agdd .MakeStreamDict ());_dga .Set ("\u004c\u0065\u006e\u0067\u0074\u0068",_fd .MakeInteger (int64 (len (_cfgd ))));_aea [_cbg ]=_dga ;_dgcb [_adc ].Stream =_dga ;};_ebf =make ([]_fd .PdfObject ,len (objects ));copy (_ebf ,objects );_dcdb (_ebf ,_aea );
return _ebf ,nil ;};func _adge (_bdf *_fd .PdfObjectDictionary )[]string {_cdgd :=[]string {};for _ ,_ebbe :=range _bdf .Keys (){_cdgd =append (_cdgd ,_ebbe .String ());};return _cdgd ;};

// CleanContentstream cleans up redundant operands in content streams, including Page and XObject Form
// contents. This process includes:
// 1. Marked content operators are removed.
// 2. Some operands are simplified (shorter form).
// TODO: Add more reduction methods and improving the methods for identifying unnecessary operands.
type CleanContentstream struct{};

// Optimize optimizes PDF objects to decrease PDF size.
func (_fcdc *ObjectStreams )Optimize (objects []_fd .PdfObject )(_fggg []_fd .PdfObject ,_cff error ){_ebc :=&_fd .PdfObjectStreams {};_gcae :=make ([]_fd .PdfObject ,0,len (objects ));for _ ,_dgf :=range objects {if _gfdf ,_adaa :=_dgf .(*_fd .PdfIndirectObject );
_adaa &&_gfdf .GenerationNumber ==0{_ebc .Append (_dgf );}else {_gcae =append (_gcae ,_dgf );};};if _ebc .Len ()==0{return _gcae ,nil ;};_fggg =make ([]_fd .PdfObject ,0,len (_gcae )+_ebc .Len ()+1);if _ebc .Len ()> 1{_fggg =append (_fggg ,_ebc );};_fggg =append (_fggg ,_ebc .Elements ()...);
_fggg =append (_fggg ,_gcae ...);return _fggg ,nil ;};func _fbeec (_gcfa []_fd .PdfObject ){for _ced ,_fcae :=range _gcfa {switch _dfc :=_fcae .(type ){case *_fd .PdfIndirectObject :_dfc .ObjectNumber =int64 (_ced +1);_dfc .GenerationNumber =0;case *_fd .PdfObjectStream :_dfc .ObjectNumber =int64 (_ced +1);
_dfc .GenerationNumber =0;case *_fd .PdfObjectStreams :_dfc .ObjectNumber =int64 (_ced +1);_dfc .GenerationNumber =0;};};};

// Optimize optimizes PDF objects to decrease PDF size.
func (_ebbd *CombineDuplicateStreams )Optimize (objects []_fd .PdfObject )(_cebb []_fd .PdfObject ,_gdf error ){_dbe :=make (map[_fd .PdfObject ]_fd .PdfObject );_gaac :=make (map[_fd .PdfObject ]struct{});_egf :=make (map[string ][]*_fd .PdfObjectStream );
for _ ,_eccd :=range objects {if _ggdaf ,_cfbgg :=_eccd .(*_fd .PdfObjectStream );_cfbgg {_gabc :=_ce .New ();_gabc .Write (_ggdaf .Stream );_gabc .Write (_ggdaf .PdfObjectDictionary .Write ());_gedf :=string (_gabc .Sum (nil ));_egf [_gedf ]=append (_egf [_gedf ],_ggdaf );
};};for _ ,_gdfe :=range _egf {if len (_gdfe )< 2{continue ;};_fdf :=_gdfe [0];for _bdb :=1;_bdb < len (_gdfe );_bdb ++{_edfd :=_gdfe [_bdb ];_dbe [_edfd ]=_fdf ;_gaac [_edfd ]=struct{}{};};};_cebb =make ([]_fd .PdfObject ,0,len (objects )-len (_gaac ));
for _ ,_febb :=range objects {if _ ,_cded :=_gaac [_febb ];_cded {continue ;};_cebb =append (_cebb ,_febb );};_dcdb (_cebb ,_dbe );return _cebb ,nil ;};

// GetOptimizers gets the list of optimizers in chain `c`.
func (_cfe *Chain )GetOptimizers ()[]_be .Optimizer {return _cfe ._cef };

// Optimize optimizes PDF objects to decrease PDF size.
func (_fggc *ImagePPI )Optimize (objects []_fd .PdfObject )(_cgag []_fd .PdfObject ,_ffac error ){if _fggc .ImageUpperPPI <=0{return objects ,nil ;};_def :=_bcdf (objects );if len (_def )==0{return objects ,nil ;};_eab :=make (map[_fd .PdfObject ]struct{});
for _ ,_ggdg :=range _def {_dffa :=_ggdg .Stream .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b");_eab [_dffa ]=struct{}{};};_abgf :=make (map[*_fd .PdfObjectStream ]*imageInfo );for _ ,_gbcf :=range _def {_abgf [_gbcf .Stream ]=_gbcf ;};var _adfdb *_fd .PdfObjectDictionary ;
for _ ,_fdcc :=range objects {if _afb ,_cgdb :=_fd .GetDict (_fdcc );_adfdb ==nil &&_cgdb {if _fabf ,_fad :=_fd .GetName (_afb .Get ("\u0054\u0079\u0070\u0065"));_fad &&*_fabf =="\u0043a\u0074\u0061\u006c\u006f\u0067"{_adfdb =_afb ;};};};if _adfdb ==nil {return objects ,nil ;
};_bbe ,_caec :=_fd .GetDict (_adfdb .Get ("\u0050\u0061\u0067e\u0073"));if !_caec {return objects ,nil ;};_cfef ,_eega :=_fd .GetArray (_bbe .Get ("\u004b\u0069\u0064\u0073"));if !_eega {return objects ,nil ;};for _ ,_gbce :=range _cfef .Elements (){_bcc :=make (map[string ]*imageInfo );
_bega ,_dagf :=_fd .GetDict (_gbce );if !_dagf {continue ;};_ecda ,_ :=_gce (_bega .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));if len (_ecda )==0{continue ;};_caab ,_fdfd :=_fd .GetDict (_bega .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_fdfd {continue ;};_affc ,_aba :=_be .NewPdfPageResourcesFromDict (_caab );if _aba !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073\u0020-\u0020\u0069\u0067\u006e\u006fr\u0069\u006eg\u003a\u0020\u0025\u0076",_aba );
continue ;};_dfb ,_eebc :=_fd .GetDict (_caab .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));if !_eebc {continue ;};_ebd :=_dfb .Keys ();for _ ,_gga :=range _ebd {if _gdca ,_dfgd :=_fd .GetStream (_dfb .Get (_gga ));_dfgd {if _efbc ,_becb :=_abgf [_gdca ];
_becb {_bcc [string (_gga )]=_efbc ;};};};_bdeaf :=_cf .NewContentStreamParser (_ecda );_abgc ,_aba :=_bdeaf .Parse ();if _aba !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_aba );continue ;};_caae :=_cf .NewContentStreamProcessor (*_abgc );
_caae .AddHandler (_cf .HandlerConditionEnumAllOperands ,"",func (_ceg *_cf .ContentStreamOperation ,_aada _cf .GraphicsState ,_ggfb *_be .PdfPageResources )error {switch _ceg .Operand {case "\u0044\u006f":if len (_ceg .Params )!=1{_f .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0049\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0044\u006f\u0020w\u0069\u0074\u0068\u0020\u006c\u0065\u006e\u0028\u0070\u0061ra\u006d\u0073\u0029 \u0021=\u0020\u0031");
return nil ;};_gaaee ,_egc :=_fd .GetName (_ceg .Params [0]);if !_egc {_f .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0049\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0044\u006f\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f\u006e\u0020\u004e\u0061\u006d\u0065\u0020p\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072");
return nil ;};if _ded ,_fdg :=_bcc [string (*_gaaee )];_fdg {_ecce :=_aada .CTM .ScalingFactorX ();_afff :=_aada .CTM .ScalingFactorY ();_efbca ,_dea :=_ecce /72.0,_afff /72.0;_geg ,_gbb :=float64 (_ded .Width )/_efbca ,float64 (_ded .Height )/_dea ;if _efbca ==0||_dea ==0{_geg =72.0;
_gbb =72.0;};_ded .PPI =_b .Max (_ded .PPI ,_geg );_ded .PPI =_b .Max (_ded .PPI ,_gbb );};};return nil ;});_aba =_caae .Process (_affc );if _aba !=nil {_f .Log .Debug ("E\u0052\u0052\u004f\u0052 p\u0072o\u0063\u0065\u0073\u0073\u0069n\u0067\u003a\u0020\u0025\u002b\u0076",_aba );
continue ;};};for _ ,_fgef :=range _def {if _ ,_ecec :=_eab [_fgef .Stream ];_ecec {continue ;};if _fgef .PPI <=_fggc .ImageUpperPPI {continue ;};_dgec ,_begf :=_be .NewXObjectImageFromStream (_fgef .Stream );if _begf !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_begf );
continue ;};var _dbae imageModifications ;_dbae .Scale =_fggc .ImageUpperPPI /_fgef .PPI ;if _fgef .BitsPerComponent ==1&&_fgef .ColorComponents ==1{_eac :=_b .Round (_fgef .PPI /_fggc .ImageUpperPPI );_eeea :=_eb .NextPowerOf2 (uint (_eac ));if _eb .InDelta (float64 (_eeea ),1/_dbae .Scale ,0.3){_dbae .Scale =float64 (1)/float64 (_eeea );
};if _ ,_cbc :=_dgec .Filter .(*_fd .JBIG2Encoder );!_cbc {_dbae .Encoding =_fd .NewJBIG2Encoder ();};};if _begf =_abca (_dgec ,_dbae );_begf !=nil {_f .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u0073\u0063\u0061\u006c\u0065\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u006be\u0065\u0070\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_begf );
continue ;};_dbae .Encoding =nil ;if _afeb ,_bgd :=_fd .GetStream (_fgef .Stream .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b"));_bgd {_eage ,_bade :=_be .NewXObjectImageFromStream (_afeb );if _bade !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_bade );
continue ;};if _bade =_abca (_eage ,_dbae );_bade !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_bade );continue ;};};};return objects ,nil ;};type imageModifications struct{Scale float64 ;Encoding _fd .StreamEncoder ;
};

// New creates a optimizers chain from options.
func New (options Options )*Chain {_aafe :=new (Chain );if options .CleanFonts ||options .SubsetFonts {_aafe .Append (&CleanFonts {Subset :options .SubsetFonts });};if options .CleanContentstream {_aafe .Append (new (CleanContentstream ));};if options .ImageUpperPPI > 0{_dgee :=new (ImagePPI );
_dgee .ImageUpperPPI =options .ImageUpperPPI ;_aafe .Append (_dgee );};if options .ImageQuality > 0{_bfefa :=new (Image );_bfefa .ImageQuality =options .ImageQuality ;_aafe .Append (_bfefa );};if options .CombineDuplicateDirectObjects {_aafe .Append (new (CombineDuplicateDirectObjects ));
};if options .CombineDuplicateStreams {_aafe .Append (new (CombineDuplicateStreams ));};if options .CombineIdenticalIndirectObjects {_aafe .Append (new (CombineIdenticalIndirectObjects ));};if options .UseObjectStreams {_aafe .Append (new (ObjectStreams ));
};if options .CompressStreams {_aafe .Append (new (CompressStreams ));};if options .CleanUnusedResources {_aafe .Append (new (CleanUnusedResources ));};return _aafe ;};

// ObjectStreams groups PDF objects to object streams.
// It implements interface model.Optimizer.
type ObjectStreams struct{};func _acf (_gfb []_fd .PdfObject )(map[_fd .PdfObject ]struct{},error ){_gge :=_egefc (_gfb );_fbee :=_gge ._gegd ;_gabe :=make (map[_fd .PdfObject ]struct{});_adbg :=_aab (_fbee );for _ ,_dee :=range _fbee {_fgfg ,_agd :=_fd .GetDict (_dee .PdfObject );
if !_agd {continue ;};_fdc ,_agd :=_fd .GetDict (_fgfg .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));if !_agd {continue ;};_ffgf :=_adbg ["\u0058O\u0062\u006a\u0065\u0063\u0074"];_aff ,_agd :=_fd .GetDict (_fdc .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));
if _agd {_afe :=_adge (_aff );for _ ,_gcc :=range _afe {if _agbf (_gcc ,_ffgf ){continue ;};_affa :=*_fd .MakeName (_gcc );_bge :=_aff .Get (_affa );_gabe [_bge ]=struct{}{};_aff .Remove (_affa );_eee :=_ffee (_bge ,_gabe );if _eee !=nil {_f .Log .Debug ("\u0066\u0061\u0069\u006ce\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065r\u0073e\u0020\u006f\u0062\u006a\u0065\u0063\u0074 \u0025\u0076",_bge );
};};};_fcg ,_agd :=_fd .GetDict (_fdc .Get ("\u0046\u006f\u006e\u0074"));_bec :=_adbg ["\u0046\u006f\u006e\u0074"];if _agd {_fbd :=_adge (_fcg );for _ ,_egee :=range _fbd {if _agbf (_egee ,_bec ){continue ;};_dge :=*_fd .MakeName (_egee );_agee :=_fcg .Get (_dge );
_gabe [_agee ]=struct{}{};_fcg .Remove (_dge );_bdde :=_ffee (_agee ,_gabe );if _bdde !=nil {_f .Log .Debug ("\u0046\u0061i\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074 %\u0076\u000a",_agee );
};};};_cdc ,_agd :=_fd .GetDict (_fdc .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if _agd {_dcag :=_adge (_cdc );_gaa :=_adbg ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"];for _ ,_dba :=range _dcag {if _agbf (_dba ,_gaa ){continue ;};_gfc :=*_fd .MakeName (_dba );
_bfd :=_cdc .Get (_gfc );_gabe [_bfd ]=struct{}{};_cdc .Remove (_gfc );_fef :=_ffee (_bfd ,_gabe );if _fef !=nil {_f .Log .Debug ("\u0066\u0061i\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074 %\u0076\u000a",_bfd );
};};};};return _gabe ,nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_dfg *CleanContentstream )Optimize (objects []_fd .PdfObject )(_eec []_fd .PdfObject ,_gd error ){_ea :=map[*_fd .PdfObjectStream ]struct{}{};var _gdc []*_fd .PdfObjectStream ;_ggf :=func (_dce *_fd .PdfObjectStream ){if _ ,_cd :=_ea [_dce ];!_cd {_ea [_dce ]=struct{}{};
_gdc =append (_gdc ,_dce );};};_gc :=map[_fd .PdfObject ]bool {};_gcb :=map[_fd .PdfObject ]bool {};for _ ,_fb :=range objects {switch _bg :=_fb .(type ){case *_fd .PdfIndirectObject :switch _fa :=_bg .PdfObject .(type ){case *_fd .PdfObjectDictionary :if _fbc ,_acd :=_fd .GetName (_fa .Get ("\u0054\u0079\u0070\u0065"));
!_acd ||_fbc .String ()!="\u0050\u0061\u0067\u0065"{continue ;};if _eg ,_cae :=_fd .GetStream (_fa .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));_cae {_ggf (_eg );}else if _bd ,_fbe :=_fd .GetArray (_fa .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));
_fbe {var _add []*_fd .PdfObjectStream ;for _ ,_dff :=range _bd .Elements (){if _ged ,_gf :=_fd .GetStream (_dff );_gf {_add =append (_add ,_ged );};};if len (_add )> 0{var _fda _bf .Buffer ;for _ ,_da :=range _add {if _bcb ,_de :=_fd .DecodeStream (_da );
_de ==nil {_fda .Write (_bcb );};_gc [_da ]=true ;};_ga ,_fgba :=_fd .MakeStream (_fda .Bytes (),_fd .NewFlateEncoder ());if _fgba !=nil {return nil ,_fgba ;};_gcb [_ga ]=true ;_fa .Set ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073",_ga );_ggf (_ga );
};};};case *_fd .PdfObjectStream :if _caf ,_ed :=_fd .GetName (_bg .Get ("\u0054\u0079\u0070\u0065"));!_ed ||_caf .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};if _dcb ,_ceb :=_fd .GetName (_bg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
!_ceb ||_dcb .String ()!="\u0046\u006f\u0072\u006d"{continue ;};_ggf (_bg );};};for _ ,_caff :=range _gdc {_gd =_ac (_caff );if _gd !=nil {return nil ,_gd ;};};_eec =nil ;for _ ,_beda :=range objects {if _gc [_beda ]{continue ;};_eec =append (_eec ,_beda );
};for _af :=range _gcb {_eec =append (_eec ,_af );};return _eec ,nil ;};

// CombineDuplicateStreams combines duplicated streams by its data hash.
// It implements interface model.Optimizer.
type CombineDuplicateStreams struct{};

// Optimize optimizes PDF objects to decrease PDF size.
func (_agbc *CombineDuplicateDirectObjects )Optimize (objects []_fd .PdfObject )(_ddf []_fd .PdfObject ,_dcg error ){_fbeec (objects );_cca :=make (map[string ][]*_fd .PdfObjectDictionary );var _eeec func (_adbgg *_fd .PdfObjectDictionary );_eeec =func (_bdea *_fd .PdfObjectDictionary ){for _ ,_dbdc :=range _bdea .Keys (){_afcb :=_bdea .Get (_dbdc );
if _bea ,_aaf :=_afcb .(*_fd .PdfObjectDictionary );_aaf {if _ecc :=_bea .Keys ();len (_ecc )==0{continue ;};_cgaf :=_ce .New ();_cgaf .Write (_bea .Write ());_bfb :=string (_cgaf .Sum (nil ));_cca [_bfb ]=append (_cca [_bfb ],_bea );_eeec (_bea );};};
};for _ ,_ecd :=range objects {_ggda ,_bda :=_ecd .(*_fd .PdfIndirectObject );if !_bda {continue ;};if _ecfe ,_fbg :=_ggda .PdfObject .(*_fd .PdfObjectDictionary );_fbg {_eeec (_ecfe );};};_caffe :=make ([]_fd .PdfObject ,0,len (_cca ));_bece :=make (map[_fd .PdfObject ]_fd .PdfObject );
for _ ,_fggf :=range _cca {if len (_fggf )< 2{continue ;};_cfc :=_fd .MakeDict ();_cfc .Merge (_fggf [0]);_fgd :=_fd .MakeIndirectObject (_cfc );_caffe =append (_caffe ,_fgd );for _gcf :=0;_gcf < len (_fggf );_gcf ++{_egb :=_fggf [_gcf ];_bece [_egb ]=_fgd ;
};};_ddf =make ([]_fd .PdfObject ,len (objects ));copy (_ddf ,objects );_ddf =append (_caffe ,_ddf ...);_dcdb (_ddf ,_bece );return _ddf ,nil ;};

// CombineIdenticalIndirectObjects combines identical indirect objects.
// It implements interface model.Optimizer.
type CombineIdenticalIndirectObjects struct{};func _egefc (_fgfd []_fd .PdfObject )objectStructure {_aadg :=objectStructure {};_eddd :=false ;for _ ,_ceae :=range _fgfd {switch _feaef :=_ceae .(type ){case *_fd .PdfIndirectObject :_edba ,_edc :=_fd .GetDict (_feaef );
if !_edc {continue ;};_fdga ,_edc :=_fd .GetName (_edba .Get ("\u0054\u0079\u0070\u0065"));if !_edc {continue ;};switch _fdga .String (){case "\u0043a\u0074\u0061\u006c\u006f\u0067":_aadg ._dbdd =_edba ;_eddd =true ;};};if _eddd {break ;};};if !_eddd {return _aadg ;
};_eaaag ,_dgfa :=_fd .GetDict (_aadg ._dbdd .Get ("\u0050\u0061\u0067e\u0073"));if !_dgfa {return _aadg ;};_aadg ._gebd =_eaaag ;_gfbfb ,_dgfa :=_fd .GetArray (_eaaag .Get ("\u004b\u0069\u0064\u0073"));if !_dgfa {return _aadg ;};for _ ,_ffaf :=range _gfbfb .Elements (){_fae ,_dgd :=_fd .GetIndirect (_ffaf );
if !_dgd {break ;};_aadg ._gegd =append (_aadg ._gegd ,_fae );};return _aadg ;};

// Options describes PDF optimization parameters.
type Options struct{CombineDuplicateStreams bool ;CombineDuplicateDirectObjects bool ;ImageUpperPPI float64 ;ImageQuality int ;UseObjectStreams bool ;CombineIdenticalIndirectObjects bool ;CompressStreams bool ;CleanFonts bool ;SubsetFonts bool ;CleanContentstream bool ;
CleanUnusedResources bool ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_dcbf *CleanFonts )Optimize (objects []_fd .PdfObject )(_cge []_fd .PdfObject ,_bga error ){var _aaa map[*_fd .PdfObjectStream ]struct{};if _dcbf .Subset {var _dcee error ;_aaa ,_dcee =_ebb (objects );if _dcee !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0073u\u0062s\u0065\u0074\u0074\u0069\u006e\u0067\u003a \u0025\u0076",_dcee );
return nil ,_dcee ;};};for _ ,_aaca :=range objects {_bgg ,_gbg :=_fd .GetStream (_aaca );if !_gbg {continue ;};if _ ,_gdb :=_aaa [_bgg ];_gdb {continue ;};_bbc ,_cace :=_fd .NewEncoderFromStream (_bgg );if _cace !=nil {_f .Log .Debug ("\u0045\u0052RO\u0052\u0020\u0067e\u0074\u0074\u0069\u006eg e\u006eco\u0064\u0065\u0072\u003a\u0020\u0025\u0076 -\u0020\u0069\u0067\u006e\u006f\u0072\u0069n\u0067",_cace );
continue ;};_dbf ,_cace :=_bbc .DecodeStream (_bgg );if _cace !=nil {_f .Log .Debug ("\u0044\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065r\u0072\u006f\u0072\u0020\u003a\u0020\u0025v\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067",_cace );
continue ;};if len (_dbf )< 4{continue ;};_gec :=string (_dbf [:4]);if _gec =="\u004f\u0054\u0054\u004f"{continue ;};if _gec !="\u0000\u0001\u0000\u0000"&&_gec !="\u0074\u0072\u0075\u0065"{continue ;};_ecg ,_cace :=_fc .Parse (_bf .NewReader (_dbf ));if _cace !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020P\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_cace );
continue ;};_cace =_ecg .Optimize ();if _cace !=nil {_f .Log .Debug ("\u0045\u0052RO\u0052\u0020\u004fp\u0074\u0069\u006d\u0069zin\u0067 f\u006f\u006e\u0074\u003a\u0020\u0025\u0076 -\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067",_cace );continue ;};
var _cebf _bf .Buffer ;_cace =_ecg .Write (&_cebf );if _cace !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020W\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_cace );
continue ;};if _cebf .Len ()> len (_dbf ){_f .Log .Debug ("\u0052\u0065-\u0077\u0072\u0069\u0074\u0074\u0065\u006e\u0020\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u002d\u0020\u0073\u006b\u0069\u0070");
continue ;};_ddcb ,_cace :=_fd .MakeStream (_cebf .Bytes (),_fd .NewFlateEncoder ());if _cace !=nil {continue ;};*_bgg =*_ddcb ;_bgg .Set ("\u004ce\u006e\u0067\u0074\u0068\u0031",_fd .MakeInteger (int64 (_cebf .Len ())));};return objects ,nil ;};