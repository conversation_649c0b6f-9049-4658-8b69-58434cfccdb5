//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package pdfaid ;import (_b "fmt";_c "github.com/trimmer-io/go-xmp/xmp";_cc "github.com/unidoc/unipdf/v4/model/xmputil/pdfaextension";);

// GetTag implements xmp.Model interface.
func (_gg *Model )GetTag (tag string )(string ,error ){_eb ,_egg :=_c .GetNativeField (_gg ,tag );if _egg !=nil {return "",_b .Errorf ("\u0025\u0073\u003a\u0020\u0025\u0076",Namespace .GetName (),_egg );};return _eb ,nil ;};func init (){_c .Register (Namespace ,_c .XmpMetadata );
_cc .RegisterSchema (Namespace ,&Schema )};

// Model is the XMP model for the PdfA metadata.
type Model struct{Part int `xmp:"pdfaid:part"`;Conformance string `xmp:"pdfaid:conformance"`;};

// CanTag implements xmp.Model interface.
func (_gfa *Model )CanTag (tag string )bool {_ ,_fd :=_c .GetNativeField (_gfa ,tag );return _fd ==nil };

// MakeModel gets or create sa new model for PDF/A ID namespace.
func MakeModel (d *_c .Document )(*Model ,error ){_e ,_ce :=d .MakeModel (Namespace );if _ce !=nil {return nil ,_ce ;};return _e .(*Model ),nil ;};

// NewModel creates a new model.
func NewModel (name string )_c .Model {return &Model {}};

// Namespaces implements xmp.Model interface.
func (_ec *Model )Namespaces ()_c .NamespaceList {return _c .NamespaceList {Namespace }};

// Can implements xmp.Model interface.
func (_eg *Model )Can (nsName string )bool {return Namespace .GetName ()==nsName };

// SyncToXMP implements xmp.Model interface.
func (_ae *Model )SyncToXMP (d *_c .Document )error {return nil };

// SetTag implements xmp.Model interface.
func (_bb *Model )SetTag (tag ,value string )error {if _d :=_c .SetNativeField (_bb ,tag ,value );_d !=nil {return _b .Errorf ("\u0025\u0073\u003a\u0020\u0025\u0076",Namespace .GetName (),_d );};return nil ;};var Schema =_cc .Schema {NamespaceURI :Namespace .URI ,Prefix :Namespace .Name ,Schema :"\u0050D\u0046/\u0041\u0020\u0049\u0044\u0020\u0053\u0063\u0068\u0065\u006d\u0061",Property :[]_cc .Property {{Category :_cc .PropertyCategoryInternal ,Description :"\u0050\u0061\u0072\u0074 o\u0066\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064",Name :"\u0070\u0061\u0072\u0074",ValueType :_cc .ValueTypeNameInteger },{Category :_cc .PropertyCategoryInternal ,Description :"A\u006d\u0065\u006e\u0064\u006d\u0065n\u0074\u0020\u006f\u0066\u0020\u0050\u0044\u0046\u002fA\u0020\u0073\u0074a\u006ed\u0061\u0072\u0064",Name :"\u0061\u006d\u0064",ValueType :_cc .ValueTypeNameText },{Category :_cc .PropertyCategoryInternal ,Description :"C\u006f\u006e\u0066\u006f\u0072\u006da\u006e\u0063\u0065\u0020\u006c\u0065v\u0065\u006c\u0020\u006f\u0066\u0020\u0050D\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064",Name :"c\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065",ValueType :_cc .ValueTypeNameText }},ValueType :nil };
var Namespace =_c .NewNamespace ("\u0070\u0064\u0066\u0061\u0069\u0064","\u0068\u0074\u0074p\u003a\u002f\u002f\u0077w\u0077\u002e\u0061\u0069\u0069\u006d\u002eo\u0072\u0067\u002f\u0070\u0064\u0066\u0061\u002f\u006e\u0073\u002f\u0069\u0064\u002f",NewModel );


// SyncFromXMP implements xmp.Model interface.
func (_a *Model )SyncFromXMP (d *_c .Document )error {return nil };var _ _c .Model =(*Model )(nil );

// SyncModel implements xmp.Model interface.
func (_gf *Model )SyncModel (d *_c .Document )error {return nil };