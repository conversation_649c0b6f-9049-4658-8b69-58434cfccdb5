//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package ps implements various functionalities needed for handling Postscript for PDF uses, in particular
// for PDF function type 4.
//
// Package ps implements various functionalities needed for handling Postscript for PDF uses, in particular
// for PDF function type 4.
package ps ;import (_g "bufio";_a "bytes";_c "errors";_gg "fmt";_ad "github.com/unidoc/unipdf/v4/common";_b "github.com/unidoc/unipdf/v4/core";_d "io";_ee "math";);func (_adg *PSOperand )not (_ggeb *PSStack )error {_acc ,_abc :=_ggeb .Pop ();if _abc !=nil {return _abc ;
};if _gcaaa ,_acd :=_acc .(*PSBoolean );_acd {_abc =_ggeb .Push (MakeBool (!_gcaaa .Val ));return _abc ;}else if _fdaa ,_gdb :=_acc .(*PSInteger );_gdb {_abc =_ggeb .Push (MakeInteger (^_fdaa .Val ));return _abc ;}else {return ErrTypeCheck ;};};

// DebugString returns a descriptive string representation of the stack - intended for debugging.
func (_bcba *PSStack )DebugString ()string {_fba :="\u005b\u0020";for _ ,_bcf :=range *_bcba {_fba +=_bcf .DebugString ();_fba +="\u0020";};_fba +="\u005d";return _fba ;};func (_dde *PSInteger )String ()string {return _gg .Sprintf ("\u0025\u0064",_dde .Val )};


// PSReal represents a real number.
type PSReal struct{Val float64 ;};

// Parse parses the postscript and store as a program that can be executed.
func (_edbe *PSParser )Parse ()(*PSProgram ,error ){_edbe .skipSpaces ();_eebe ,_bafe :=_edbe ._ggaba .Peek (2);if _bafe !=nil {return nil ,_bafe ;};if _eebe [0]!='{'{return nil ,_c .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0050\u0053\u0020\u0050\u0072\u006f\u0067\u0072\u0061\u006d\u0020\u006e\u006f\u0074\u0020\u0073t\u0061\u0072\u0074\u0069\u006eg\u0020\u0077i\u0074\u0068\u0020\u007b");
};_dfe ,_bafe :=_edbe .parseFunction ();if _bafe !=nil &&_bafe !=_d .EOF {return nil ,_bafe ;};return _dfe ,_bafe ;};

// Exec executes the program, typically leaving output values on the stack.
func (_ac *PSProgram )Exec (stack *PSStack )error {for _ ,_gca :=range *_ac {var _da error ;switch _cc :=_gca .(type ){case *PSInteger :_gf :=_cc ;_da =stack .Push (_gf );case *PSReal :_eec :=_cc ;_da =stack .Push (_eec );case *PSBoolean :_bgc :=_cc ;_da =stack .Push (_bgc );
case *PSProgram :_cbf :=_cc ;_da =stack .Push (_cbf );case *PSOperand :_ae :=_cc ;_da =_ae .Exec (stack );default:return ErrTypeCheck ;};if _da !=nil {return _da ;};};return nil ;};var ErrUnsupportedOperand =_c .New ("\u0075\u006e\u0073\u0075pp\u006f\u0072\u0074\u0065\u0064\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064");


// PSStack defines a stack of PSObjects. PSObjects can be pushed on or pull from the stack.
type PSStack []PSObject ;func (_fdg *PSOperand )mod (_afgf *PSStack )error {_baa ,_ccd :=_afgf .Pop ();if _ccd !=nil {return _ccd ;};_gdge ,_ccd :=_afgf .Pop ();if _ccd !=nil {return _ccd ;};_ecbd ,_ffeg :=_baa .(*PSInteger );if !_ffeg {return ErrTypeCheck ;
};if _ecbd .Val ==0{return ErrUndefinedResult ;};_bgf ,_ffeg :=_gdge .(*PSInteger );if !_ffeg {return ErrTypeCheck ;};_gge :=_bgf .Val %_ecbd .Val ;_ccd =_afgf .Push (MakeInteger (_gge ));return _ccd ;};func (_cb *PSBoolean )String ()string {return _gg .Sprintf ("\u0025\u0076",_cb .Val )};
func (_cfd *PSOperand )ge (_eaa *PSStack )error {_fce ,_beb :=_eaa .PopNumberAsFloat64 ();if _beb !=nil {return _beb ;};_gfa ,_beb :=_eaa .PopNumberAsFloat64 ();if _beb !=nil {return _beb ;};if _ee .Abs (_gfa -_fce )< _gd {_ba :=_eaa .Push (MakeBool (true ));
return _ba ;}else if _gfa > _fce {_fbc :=_eaa .Push (MakeBool (true ));return _fbc ;}else {_dca :=_eaa .Push (MakeBool (false ));return _dca ;};};func (_fc *PSOperand )String ()string {return string (*_fc )};func (_gbg *PSOperand )le (_efcf *PSStack )error {_ddgg ,_fgc :=_efcf .PopNumberAsFloat64 ();
if _fgc !=nil {return _fgc ;};_dabdd ,_fgc :=_efcf .PopNumberAsFloat64 ();if _fgc !=nil {return _fgc ;};if _ee .Abs (_dabdd -_ddgg )< _gd {_fcbb :=_efcf .Push (MakeBool (true ));return _fcbb ;}else if _dabdd < _ddgg {_fgcg :=_efcf .Push (MakeBool (true ));
return _fgcg ;}else {_dggd :=_efcf .Push (MakeBool (false ));return _dggd ;};};func (_afg *PSReal )String ()string {return _gg .Sprintf ("\u0025\u002e\u0035\u0066",_afg .Val )};func (_ade *PSOperand )dup (_gccd *PSStack )error {_cde ,_dabd :=_gccd .Pop ();
if _dabd !=nil {return _dabd ;};_dabd =_gccd .Push (_cde );if _dabd !=nil {return _dabd ;};_dabd =_gccd .Push (_cde .Duplicate ());return _dabd ;};func (_age *PSOperand )exch (_ddbg *PSStack )error {_gec ,_fbe :=_ddbg .Pop ();if _fbe !=nil {return _fbe ;
};_bede ,_fbe :=_ddbg .Pop ();if _fbe !=nil {return _fbe ;};_fbe =_ddbg .Push (_gec );if _fbe !=nil {return _fbe ;};_fbe =_ddbg .Push (_bede );return _fbe ;};func (_f *PSReal )DebugString ()string {return _gg .Sprintf ("\u0072e\u0061\u006c\u003a\u0025\u002e\u0035f",_f .Val );
};func (_edge *PSOperand )pop (_bga *PSStack )error {_ ,_aef :=_bga .Pop ();if _aef !=nil {return _aef ;};return nil ;};

// Pop pops an object from the top of the stack.
func (_faf *PSStack )Pop ()(PSObject ,error ){if len (*_faf )< 1{return nil ,ErrStackUnderflow ;};_eaac :=(*_faf )[len (*_faf )-1];*_faf =(*_faf )[0:len (*_faf )-1];return _eaac ,nil ;};

// PSObjectArrayToFloat64Array converts []PSObject into a []float64 array. Each PSObject must represent a number,
// otherwise a ErrTypeCheck error occurs.
func PSObjectArrayToFloat64Array (objects []PSObject )([]float64 ,error ){var _db []float64 ;for _ ,_edg :=range objects {if _de ,_dd :=_edg .(*PSInteger );_dd {_db =append (_db ,float64 (_de .Val ));}else if _gdg ,_eb :=_edg .(*PSReal );_eb {_db =append (_db ,_gdg .Val );
}else {return nil ,ErrTypeCheck ;};};return _db ,nil ;};

// PSExecutor has its own execution stack and is used to executre a PS routine (program).
type PSExecutor struct{Stack *PSStack ;_ag *PSProgram ;};

// Exec executes the operand `op` in the state specified by `stack`.
func (_cg *PSOperand )Exec (stack *PSStack )error {_aec :=ErrUnsupportedOperand ;switch *_cg {case "\u0061\u0062\u0073":_aec =_cg .abs (stack );case "\u0061\u0064\u0064":_aec =_cg .add (stack );case "\u0061\u006e\u0064":_aec =_cg .and (stack );case "\u0061\u0074\u0061\u006e":_aec =_cg .atan (stack );
case "\u0062\u0069\u0074\u0073\u0068\u0069\u0066\u0074":_aec =_cg .bitshift (stack );case "\u0063e\u0069\u006c\u0069\u006e\u0067":_aec =_cg .ceiling (stack );case "\u0063\u006f\u0070\u0079":_aec =_cg .copy (stack );case "\u0063\u006f\u0073":_aec =_cg .cos (stack );
case "\u0063\u0076\u0069":_aec =_cg .cvi (stack );case "\u0063\u0076\u0072":_aec =_cg .cvr (stack );case "\u0064\u0069\u0076":_aec =_cg .div (stack );case "\u0064\u0075\u0070":_aec =_cg .dup (stack );case "\u0065\u0071":_aec =_cg .eq (stack );case "\u0065\u0078\u0063\u0068":_aec =_cg .exch (stack );
case "\u0065\u0078\u0070":_aec =_cg .exp (stack );case "\u0066\u006c\u006fo\u0072":_aec =_cg .floor (stack );case "\u0067\u0065":_aec =_cg .ge (stack );case "\u0067\u0074":_aec =_cg .gt (stack );case "\u0069\u0064\u0069\u0076":_aec =_cg .idiv (stack );
case "\u0069\u0066":_aec =_cg .ifCondition (stack );case "\u0069\u0066\u0065\u006c\u0073\u0065":_aec =_cg .ifelse (stack );case "\u0069\u006e\u0064e\u0078":_aec =_cg .index (stack );case "\u006c\u0065":_aec =_cg .le (stack );case "\u006c\u006f\u0067":_aec =_cg .log (stack );
case "\u006c\u006e":_aec =_cg .ln (stack );case "\u006c\u0074":_aec =_cg .lt (stack );case "\u006d\u006f\u0064":_aec =_cg .mod (stack );case "\u006d\u0075\u006c":_aec =_cg .mul (stack );case "\u006e\u0065":_aec =_cg .ne (stack );case "\u006e\u0065\u0067":_aec =_cg .neg (stack );
case "\u006e\u006f\u0074":_aec =_cg .not (stack );case "\u006f\u0072":_aec =_cg .or (stack );case "\u0070\u006f\u0070":_aec =_cg .pop (stack );case "\u0072\u006f\u0075n\u0064":_aec =_cg .round (stack );case "\u0072\u006f\u006c\u006c":_aec =_cg .roll (stack );
case "\u0073\u0069\u006e":_aec =_cg .sin (stack );case "\u0073\u0071\u0072\u0074":_aec =_cg .sqrt (stack );case "\u0073\u0075\u0062":_aec =_cg .sub (stack );case "\u0074\u0072\u0075\u006e\u0063\u0061\u0074\u0065":_aec =_cg .truncate (stack );case "\u0078\u006f\u0072":_aec =_cg .xor (stack );
};return _aec ;};func (_bag *PSParser )skipComments ()error {if _ ,_fgca :=_bag .skipSpaces ();_fgca !=nil {return _fgca ;};_dgdg :=true ;for {_daag ,_ebg :=_bag ._ggaba .Peek (1);if _ebg !=nil {_ad .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_ebg .Error ());
return _ebg ;};if _dgdg &&_daag [0]!='%'{return nil ;};_dgdg =false ;if (_daag [0]!='\r')&&(_daag [0]!='\n'){_bag ._ggaba .ReadByte ();}else {break ;};};return _bag .skipComments ();};func (_acfa *PSOperand )sin (_fgf *PSStack )error {_fbb ,_add :=_fgf .PopNumberAsFloat64 ();
if _add !=nil {return _add ;};_eeeg :=_ee .Sin (_fbb *_ee .Pi /180.0);_add =_fgf .Push (MakeReal (_eeeg ));return _add ;};func (_ddb *PSProgram )String ()string {_bbg :="\u007b\u0020";for _ ,_gcc :=range *_ddb {_bbg +=_gcc .String ();_bbg +="\u0020";};
_bbg +="\u007d";return _bbg ;};

// MakeReal returns a new PSReal object initialized with `val`.
func MakeReal (val float64 )*PSReal {_fab :=PSReal {};_fab .Val =val ;return &_fab };var ErrRangeCheck =_c .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");func (_ded *PSOperand )ceiling (_gfb *PSStack )error {_dfa ,_gfd :=_gfb .Pop ();
if _gfd !=nil {return _gfd ;};if _eece ,_eeef :=_dfa .(*PSReal );_eeef {_gfd =_gfb .Push (MakeReal (_ee .Ceil (_eece .Val )));}else if _fcb ,_ggad :=_dfa .(*PSInteger );_ggad {_gfd =_gfb .Push (MakeInteger (_fcb .Val ));}else {_gfd =ErrTypeCheck ;};return _gfd ;
};var ErrStackUnderflow =_c .New ("\u0073t\u0061c\u006b\u0020\u0075\u006e\u0064\u0065\u0072\u0066\u006c\u006f\u0077");func (_cgf *PSOperand )round (_gfc *PSStack )error {_dffa ,_cag :=_gfc .Pop ();if _cag !=nil {return _cag ;};if _eed ,_ecda :=_dffa .(*PSReal );
_ecda {_cag =_gfc .Push (MakeReal (_ee .Floor (_eed .Val +0.5)));}else if _fcf ,_bbfe :=_dffa .(*PSInteger );_bbfe {_cag =_gfc .Push (MakeInteger (_fcf .Val ));}else {return ErrTypeCheck ;};return _cag ;};var ErrUndefinedResult =_c .New ("\u0075\u006e\u0064\u0065fi\u006e\u0065\u0064\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u0065\u0072\u0072o\u0072");
func (_bb *PSInteger )DebugString ()string {return _gg .Sprintf ("\u0069\u006e\u0074\u003a\u0025\u0064",_bb .Val );};func (_fbfa *PSOperand )floor (_bdg *PSStack )error {_cadc ,_ddab :=_bdg .Pop ();if _ddab !=nil {return _ddab ;};if _fgd ,_edgd :=_cadc .(*PSReal );
_edgd {_ddab =_bdg .Push (MakeReal (_ee .Floor (_fgd .Val )));}else if _daaf ,_eeec :=_cadc .(*PSInteger );_eeec {_ddab =_bdg .Push (MakeInteger (_daaf .Val ));}else {return ErrTypeCheck ;};return _ddab ;};const _gd =0.000001;func (_afge *PSOperand )lt (_gag *PSStack )error {_gdeb ,_beg :=_gag .PopNumberAsFloat64 ();
if _beg !=nil {return _beg ;};_cfcc ,_beg :=_gag .PopNumberAsFloat64 ();if _beg !=nil {return _beg ;};if _ee .Abs (_cfcc -_gdeb )< _gd {_ceg :=_gag .Push (MakeBool (false ));return _ceg ;}else if _cfcc < _gdeb {_bad :=_gag .Push (MakeBool (true ));return _bad ;
}else {_eeb :=_gag .Push (MakeBool (false ));return _eeb ;};};

// PSInteger represents an integer.
type PSInteger struct{Val int ;};

// Push pushes an object on top of the stack.
func (_gabd *PSStack )Push (obj PSObject )error {if len (*_gabd )> 100{return ErrStackOverflow ;};*_gabd =append (*_gabd ,obj );return nil ;};func (_ddac *PSParser )parseNumber ()(PSObject ,error ){_cedg ,_abba :=_b .ParseNumber (_ddac ._ggaba );if _abba !=nil {return nil ,_abba ;
};switch _fdde :=_cedg .(type ){case *_b .PdfObjectFloat :return MakeReal (float64 (*_fdde )),nil ;case *_b .PdfObjectInteger :return MakeInteger (int (*_fdde )),nil ;};return nil ,_gg .Errorf ("\u0075n\u0068\u0061\u006e\u0064\u006c\u0065\u0064\u0020\u006e\u0075\u006db\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u0020\u0025\u0054",_cedg );
};

// NewPSProgram returns an empty, initialized PSProgram.
func NewPSProgram ()*PSProgram {return &PSProgram {}};

// PSObject represents a postscript object.
type PSObject interface{

// Duplicate makes a fresh copy of the PSObject.
Duplicate ()PSObject ;

// DebugString returns a descriptive representation of the PSObject with more information than String()
// for debugging purposes.
DebugString ()string ;

// String returns a string representation of the PSObject.
String ()string ;};func (_fdd *PSOperand )index (_aggg *PSStack )error {_dgd ,_bfaf :=_aggg .Pop ();if _bfaf !=nil {return _bfaf ;};_aac ,_gde :=_dgd .(*PSInteger );if !_gde {return ErrTypeCheck ;};if _aac .Val < 0{return ErrRangeCheck ;};if _aac .Val > len (*_aggg )-1{return ErrStackUnderflow ;
};_bcb :=(*_aggg )[len (*_aggg )-1-_aac .Val ];_bfaf =_aggg .Push (_bcb .Duplicate ());return _bfaf ;};func (_ceb *PSOperand )gt (_cdb *PSStack )error {_fff ,_egf :=_cdb .PopNumberAsFloat64 ();if _egf !=nil {return _egf ;};_gece ,_egf :=_cdb .PopNumberAsFloat64 ();
if _egf !=nil {return _egf ;};if _ee .Abs (_gece -_fff )< _gd {_ddba :=_cdb .Push (MakeBool (false ));return _ddba ;}else if _gece > _fff {_dbb :=_cdb .Push (MakeBool (true ));return _dbb ;}else {_dbgd :=_cdb .Push (MakeBool (false ));return _dbgd ;};};
func (_efc *PSOperand )idiv (_bge *PSStack )error {_cfca ,_gcf :=_bge .Pop ();if _gcf !=nil {return _gcf ;};_gcg ,_gcf :=_bge .Pop ();if _gcf !=nil {return _gcf ;};_feb ,_bce :=_cfca .(*PSInteger );if !_bce {return ErrTypeCheck ;};if _feb .Val ==0{return ErrUndefinedResult ;
};_ebb ,_bce :=_gcg .(*PSInteger );if !_bce {return ErrTypeCheck ;};_dfbe :=_ebb .Val /_feb .Val ;_gcf =_bge .Push (MakeInteger (_dfbe ));return _gcf ;};func (_afcf *PSReal )Duplicate ()PSObject {_gce :=PSReal {};_gce .Val =_afcf .Val ;return &_gce };func (_agg *PSProgram )Duplicate ()PSObject {_dddf :=&PSProgram {};
for _ ,_df :=range *_agg {_dddf .Append (_df .Duplicate ());};return _dddf ;};func (_agaf *PSOperand )ifelse (_aged *PSStack )error {_gcac ,_ecd :=_aged .Pop ();if _ecd !=nil {return _ecd ;};_gadg ,_ecd :=_aged .Pop ();if _ecd !=nil {return _ecd ;};_ffa ,_ecd :=_aged .Pop ();
if _ecd !=nil {return _ecd ;};_bbdg ,_dac :=_gcac .(*PSProgram );if !_dac {return ErrTypeCheck ;};_fac ,_dac :=_gadg .(*PSProgram );if !_dac {return ErrTypeCheck ;};_aeab ,_dac :=_ffa .(*PSBoolean );if !_dac {return ErrTypeCheck ;};if _aeab .Val {_gdgc :=_fac .Exec (_aged );
return _gdgc ;};_ecd =_bbdg .Exec (_aged );return _ecd ;};func (_abf *PSOperand )ne (_gcaa *PSStack )error {_gfbgc :=_abf .eq (_gcaa );if _gfbgc !=nil {return _gfbgc ;};_gfbgc =_abf .not (_gcaa );return _gfbgc ;};

// PSBoolean represents a boolean value.
type PSBoolean struct{Val bool ;};func (_gdea *PSParser )parseBool ()(*PSBoolean ,error ){_bea ,_aecd :=_gdea ._ggaba .Peek (4);if _aecd !=nil {return MakeBool (false ),_aecd ;};if (len (_bea )>=4)&&(string (_bea [:4])=="\u0074\u0072\u0075\u0065"){_gdea ._ggaba .Discard (4);
return MakeBool (true ),nil ;};_bea ,_aecd =_gdea ._ggaba .Peek (5);if _aecd !=nil {return MakeBool (false ),_aecd ;};if (len (_bea )>=5)&&(string (_bea [:5])=="\u0066\u0061\u006cs\u0065"){_gdea ._ggaba .Discard (5);return MakeBool (false ),nil ;};return MakeBool (false ),_c .New ("\u0075n\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0062o\u006fl\u0065a\u006e\u0020\u0073\u0074\u0072\u0069\u006eg");
};func (_gba *PSOperand )add (_aea *PSStack )error {_ca ,_gaa :=_aea .Pop ();if _gaa !=nil {return _gaa ;};_cbc ,_gaa :=_aea .Pop ();if _gaa !=nil {return _gaa ;};_ddbe ,_cf :=_ca .(*PSReal );_gad ,_ff :=_ca .(*PSInteger );if !_cf &&!_ff {return ErrTypeCheck ;
};_egd ,_bfc :=_cbc .(*PSReal );_aga ,_cbg :=_cbc .(*PSInteger );if !_bfc &&!_cbg {return ErrTypeCheck ;};if _ff &&_cbg {_gdc :=_gad .Val +_aga .Val ;_dbg :=_aea .Push (MakeInteger (_gdc ));return _dbg ;};var _ddg float64 ;if _cf {_ddg =_ddbe .Val ;}else {_ddg =float64 (_gad .Val );
};if _bfc {_ddg +=_egd .Val ;}else {_ddg +=float64 (_aga .Val );};_gaa =_aea .Push (MakeReal (_ddg ));return _gaa ;};

// MakeOperand returns a new PSOperand object based on string `val`.
func MakeOperand (val string )*PSOperand {_cdee :=PSOperand (val );return &_cdee };func (_ga *PSBoolean )DebugString ()string {return _gg .Sprintf ("\u0062o\u006f\u006c\u003a\u0025\u0076",_ga .Val );};

// MakeBool returns a new PSBoolean object initialized with `val`.
func MakeBool (val bool )*PSBoolean {_bdbb :=PSBoolean {};_bdbb .Val =val ;return &_bdbb };

// PSParser is a basic Postscript parser.
type PSParser struct{_ggaba *_g .Reader };func (_cbb *PSOperand )truncate (_badf *PSStack )error {_cfcce ,_bedf :=_badf .Pop ();if _bedf !=nil {return _bedf ;};if _cbca ,_eae :=_cfcce .(*PSReal );_eae {_edgee :=int (_cbca .Val );_bedf =_badf .Push (MakeReal (float64 (_edgee )));
}else if _eacf ,_dabg :=_cfcce .(*PSInteger );_dabg {_bedf =_badf .Push (MakeInteger (_eacf .Val ));}else {return ErrTypeCheck ;};return _bedf ;};func (_bfe *PSOperand )cvi (_gbab *PSStack )error {_eea ,_cdde :=_gbab .Pop ();if _cdde !=nil {return _cdde ;
};if _dcb ,_ged :=_eea .(*PSReal );_ged {_ggaf :=int (_dcb .Val );_cdde =_gbab .Push (MakeInteger (_ggaf ));}else if _eeg ,_cgc :=_eea .(*PSInteger );_cgc {_dbgc :=_eeg .Val ;_cdde =_gbab .Push (MakeInteger (_dbgc ));}else {return ErrTypeCheck ;};return _cdde ;
};func (_ddd *PSProgram )DebugString ()string {_fd :="\u007b\u0020";for _ ,_bg :=range *_ddd {_fd +=_bg .DebugString ();_fd +="\u0020";};_fd +="\u007d";return _fd ;};func (_dea *PSOperand )cvr (_daad *PSStack )error {_dbc ,_bed :=_daad .Pop ();if _bed !=nil {return _bed ;
};if _fea ,_gdac :=_dbc .(*PSReal );_gdac {_bed =_daad .Push (MakeReal (_fea .Val ));}else if _cfc ,_ggg :=_dbc .(*PSInteger );_ggg {_bed =_daad .Push (MakeReal (float64 (_cfc .Val )));}else {return ErrTypeCheck ;};return _bed ;};

// NewPSStack returns an initialized PSStack.
func NewPSStack ()*PSStack {return &PSStack {}};func (_gb *PSOperand )DebugString ()string {return _gg .Sprintf ("\u006fp\u003a\u0027\u0025\u0073\u0027",*_gb );};

// Append appends an object to the PSProgram.
func (_dec *PSProgram )Append (obj PSObject ){*_dec =append (*_dec ,obj )};func (_fbg *PSParser )parseOperand ()(*PSOperand ,error ){var _gdcd []byte ;for {_cebb ,_ccda :=_fbg ._ggaba .Peek (1);if _ccda !=nil {if _ccda ==_d .EOF {break ;};return nil ,_ccda ;
};if _b .IsDelimiter (_cebb [0]){break ;};if _b .IsWhiteSpace (_cebb [0]){break ;};_eebd ,_ :=_fbg ._ggaba .ReadByte ();_gdcd =append (_gdcd ,_eebd );};if len (_gdcd )==0{return nil ,_c .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u0020\u0028\u0065\u006d\u0070\u0074\u0079\u0029");
};return MakeOperand (string (_gdcd )),nil ;};func (_fa *PSOperand )atan (_gcb *PSStack )error {_gea ,_ef :=_gcb .PopNumberAsFloat64 ();if _ef !=nil {return _ef ;};_afe ,_ef :=_gcb .PopNumberAsFloat64 ();if _ef !=nil {return _ef ;};if _gea ==0{var _dcg error ;
if _afe < 0{_dcg =_gcb .Push (MakeReal (270));}else {_dcg =_gcb .Push (MakeReal (90));};return _dcg ;};_cfb :=_afe /_gea ;_bbe :=_ee .Atan (_cfb )*180/_ee .Pi ;_ef =_gcb .Push (MakeReal (_bbe ));return _ef ;};func (_acg *PSParser )skipSpaces ()(int ,error ){_gcgb :=0;
for {_fdc ,_cgge :=_acg ._ggaba .Peek (1);if _cgge !=nil {return 0,_cgge ;};if _b .IsWhiteSpace (_fdc [0]){_acg ._ggaba .ReadByte ();_gcgb ++;}else {break ;};};return _gcgb ,nil ;};func (_fbfb *PSOperand )sqrt (_beda *PSStack )error {_cfda ,_ebdc :=_beda .PopNumberAsFloat64 ();
if _ebdc !=nil {return _ebdc ;};if _cfda < 0{return ErrRangeCheck ;};_gceg :=_ee .Sqrt (_cfda );_ebdc =_beda .Push (MakeReal (_gceg ));return _ebdc ;};

// MakeInteger returns a new PSInteger object initialized with `val`.
func MakeInteger (val int )*PSInteger {_eada :=PSInteger {};_eada .Val =val ;return &_eada };func (_fe *PSBoolean )Duplicate ()PSObject {_ab :=PSBoolean {};_ab .Val =_fe .Val ;return &_ab };

// NewPSParser returns a new instance of the PDF Postscript parser from input data.
func NewPSParser (content []byte )*PSParser {_bdb :=PSParser {};_bde :=_a .NewBuffer (content );_bdb ._ggaba =_g .NewReader (_bde );return &_bdb ;};func (_eee *PSOperand )abs (_cga *PSStack )error {_dab ,_ge :=_cga .Pop ();if _ge !=nil {return _ge ;};if _bgg ,_bf :=_dab .(*PSReal );
_bf {_gff :=_bgg .Val ;if _gff < 0{_ge =_cga .Push (MakeReal (-_gff ));}else {_ge =_cga .Push (MakeReal (_gff ));};}else if _fed ,_gbf :=_dab .(*PSInteger );_gbf {_be :=_fed .Val ;if _be < 0{_ge =_cga .Push (MakeInteger (-_be ));}else {_ge =_cga .Push (MakeInteger (_be ));
};}else {return ErrTypeCheck ;};return _ge ;};func (_bdbg *PSParser )parseFunction ()(*PSProgram ,error ){_cac ,_ :=_bdbg ._ggaba .ReadByte ();if _cac !='{'{return nil ,_c .New ("\u0069\u006ev\u0061\u006c\u0069d\u0020\u0066\u0075\u006e\u0063\u0074\u0069\u006f\u006e");
};_acfd :=NewPSProgram ();for {_bdbg .skipSpaces ();_bdbg .skipComments ();_dgaa ,_fgb :=_bdbg ._ggaba .Peek (2);if _fgb !=nil {if _fgb ==_d .EOF {break ;};return nil ,_fgb ;};_ad .Log .Trace ("\u0050e\u0065k\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u003a\u0020\u0025\u0073",string (_dgaa ));
if _dgaa [0]=='}'{_ad .Log .Trace ("\u0045\u004f\u0046 \u0066\u0075\u006e\u0063\u0074\u0069\u006f\u006e");_bdbg ._ggaba .ReadByte ();break ;}else if _dgaa [0]=='{'{_ad .Log .Trace ("\u0046u\u006e\u0063\u0074\u0069\u006f\u006e!");_bcc ,_ead :=_bdbg .parseFunction ();
if _ead !=nil {return nil ,_ead ;};_acfd .Append (_bcc );}else if _b .IsDecimalDigit (_dgaa [0])||(_dgaa [0]=='-'&&_b .IsDecimalDigit (_dgaa [1])){_ad .Log .Trace ("\u002d>\u004e\u0075\u006d\u0062\u0065\u0072!");_cadd ,_cff :=_bdbg .parseNumber ();if _cff !=nil {return nil ,_cff ;
};_acfd .Append (_cadd );}else {_ad .Log .Trace ("\u002d>\u004fp\u0065\u0072\u0061\u006e\u0064 \u006f\u0072 \u0062\u006f\u006f\u006c\u003f");_dgaa ,_ =_bdbg ._ggaba .Peek (5);_eacfe :=string (_dgaa );_ad .Log .Trace ("\u0050\u0065\u0065k\u0020\u0073\u0074\u0072\u003a\u0020\u0025\u0073",_eacfe );
if (len (_eacfe )> 4)&&(_eacfe [:5]=="\u0066\u0061\u006cs\u0065"){_dcfe ,_gaag :=_bdbg .parseBool ();if _gaag !=nil {return nil ,_gaag ;};_acfd .Append (_dcfe );}else if (len (_eacfe )> 3)&&(_eacfe [:4]=="\u0074\u0072\u0075\u0065"){_bfad ,_cagf :=_bdbg .parseBool ();
if _cagf !=nil {return nil ,_cagf ;};_acfd .Append (_bfad );}else {_bdfd ,_ggcd :=_bdbg .parseOperand ();if _ggcd !=nil {return nil ,_ggcd ;};_acfd .Append (_bdfd );};};};return _acfd ,nil ;};

// Empty empties the stack.
func (_bef *PSStack )Empty (){*_bef =[]PSObject {}};func (_bfa *PSOperand )div (_bd *PSStack )error {_ccc ,_gcce :=_bd .Pop ();if _gcce !=nil {return _gcce ;};_aaa ,_gcce :=_bd .Pop ();if _gcce !=nil {return _gcce ;};_egg ,_dce :=_ccc .(*PSReal );_geb ,_def :=_ccc .(*PSInteger );
if !_dce &&!_def {return ErrTypeCheck ;};if _dce &&_egg .Val ==0{return ErrUndefinedResult ;};if _def &&_geb .Val ==0{return ErrUndefinedResult ;};_dfb ,_egb :=_aaa .(*PSReal );_adf ,_fg :=_aaa .(*PSInteger );if !_egb &&!_fg {return ErrTypeCheck ;};var _efa float64 ;
if _egb {_efa =_dfb .Val ;}else {_efa =float64 (_adf .Val );};if _dce {_efa /=_egg .Val ;}else {_efa /=float64 (_geb .Val );};_gcce =_bd .Push (MakeReal (_efa ));return _gcce ;};func (_ebdd *PSOperand )ln (_gbe *PSStack )error {_bdc ,_cfg :=_gbe .PopNumberAsFloat64 ();
if _cfg !=nil {return _cfg ;};_adaf :=_ee .Log (_bdc );_cfg =_gbe .Push (MakeReal (_adaf ));return _cfg ;};

// PopInteger specificially pops an integer from the top of the stack, returning the value as an int.
func (_bgd *PSStack )PopInteger ()(int ,error ){_bege ,_ggcc :=_bgd .Pop ();if _ggcc !=nil {return 0,_ggcc ;};if _deec ,_begd :=_bege .(*PSInteger );_begd {return _deec .Val ,nil ;};return 0,ErrTypeCheck ;};func (_bgfb *PSOperand )neg (_cddc *PSStack )error {_bbfd ,_fag :=_cddc .Pop ();
if _fag !=nil {return _fag ;};if _cgae ,_ccf :=_bbfd .(*PSReal );_ccf {_fag =_cddc .Push (MakeReal (-_cgae .Val ));return _fag ;}else if _aeb ,_ffeb :=_bbfd .(*PSInteger );_ffeb {_fag =_cddc .Push (MakeInteger (-_aeb .Val ));return _fag ;}else {return ErrTypeCheck ;
};};

// PopNumberAsFloat64 pops and return the numeric value of the top of the stack as a float64.
// Real or integer only.
func (_aff *PSStack )PopNumberAsFloat64 ()(float64 ,error ){_ccdaa ,_eegc :=_aff .Pop ();if _eegc !=nil {return 0,_eegc ;};if _eag ,_eff :=_ccdaa .(*PSReal );_eff {return _eag .Val ,nil ;}else if _fcdc ,_dbgcb :=_ccdaa .(*PSInteger );_dbgcb {return float64 (_fcdc .Val ),nil ;
}else {return 0,ErrTypeCheck ;};};func (_ce *PSOperand )exp (_fee *PSStack )error {_aaf ,_gaac :=_fee .PopNumberAsFloat64 ();if _gaac !=nil {return _gaac ;};_ggab ,_gaac :=_fee .PopNumberAsFloat64 ();if _gaac !=nil {return _gaac ;};if _ee .Abs (_aaf )< 1&&_ggab < 0{return ErrUndefinedResult ;
};_cad :=_ee .Pow (_ggab ,_aaf );_gaac =_fee .Push (MakeReal (_cad ));return _gaac ;};func (_dbe *PSOperand )and (_daa *PSStack )error {_gdf ,_aca :=_daa .Pop ();if _aca !=nil {return _aca ;};_ebf ,_aca :=_daa .Pop ();if _aca !=nil {return _aca ;};if _gda ,_ada :=_gdf .(*PSBoolean );
_ada {_ffe ,_ddga :=_ebf .(*PSBoolean );if !_ddga {return ErrTypeCheck ;};_aca =_daa .Push (MakeBool (_gda .Val &&_ffe .Val ));return _aca ;};if _gga ,_dda :=_gdf .(*PSInteger );_dda {_cdc ,_dc :=_ebf .(*PSInteger );if !_dc {return ErrTypeCheck ;};_aca =_daa .Push (MakeInteger (_gga .Val &_cdc .Val ));
return _aca ;};return ErrTypeCheck ;};func (_afga *PSOperand )ifCondition (_eac *PSStack )error {_edb ,_aabd :=_eac .Pop ();if _aabd !=nil {return _aabd ;};_dcf ,_aabd :=_eac .Pop ();if _aabd !=nil {return _aabd ;};_eaf ,_gcff :=_edb .(*PSProgram );if !_gcff {return ErrTypeCheck ;
};_cfa ,_gcff :=_dcf .(*PSBoolean );if !_gcff {return ErrTypeCheck ;};if _cfa .Val {_agd :=_eaf .Exec (_eac );return _agd ;};return nil ;};func (_ebd *PSOperand )bitshift (_egde *PSStack )error {_aab ,_fda :=_egde .PopInteger ();if _fda !=nil {return _fda ;
};_fde ,_fda :=_egde .PopInteger ();if _fda !=nil {return _fda ;};var _cdd int ;if _aab >=0{_cdd =_fde <<uint (_aab );}else {_cdd =_fde >>uint (-_aab );};_fda =_egde .Push (MakeInteger (_cdd ));return _fda ;};func (_facc *PSOperand )log (_gae *PSStack )error {_ggc ,_ffac :=_gae .PopNumberAsFloat64 ();
if _ffac !=nil {return _ffac ;};_ffg :=_ee .Log10 (_ggc );_ffac =_gae .Push (MakeReal (_ffg ));return _ffac ;};

// PSProgram defines a Postscript program which is a series of PS objects (arguments, commands, programs etc).
type PSProgram []PSObject ;

// String returns a string representation of the stack.
func (_gfee *PSStack )String ()string {_ggabae :="\u005b\u0020";for _ ,_bccg :=range *_gfee {_ggabae +=_bccg .String ();_ggabae +="\u0020";};_ggabae +="\u005d";return _ggabae ;};func (_dcgc *PSOperand )or (_bee *PSStack )error {_cgg ,_cbce :=_bee .Pop ();
if _cbce !=nil {return _cbce ;};_gdfg ,_cbce :=_bee .Pop ();if _cbce !=nil {return _cbce ;};if _dfbc ,_eca :=_cgg .(*PSBoolean );_eca {_ecc ,_ddf :=_gdfg .(*PSBoolean );if !_ddf {return ErrTypeCheck ;};_cbce =_bee .Push (MakeBool (_dfbc .Val ||_ecc .Val ));
return _cbce ;};if _cbab ,_efd :=_cgg .(*PSInteger );_efd {_bcg ,_becd :=_gdfg .(*PSInteger );if !_becd {return ErrTypeCheck ;};_cbce =_bee .Push (MakeInteger (_cbab .Val |_bcg .Val ));return _cbce ;};return ErrTypeCheck ;};func (_ddgf *PSOperand )sub (_eeed *PSStack )error {_cda ,_bbfea :=_eeed .Pop ();
if _bbfea !=nil {return _bbfea ;};_ced ,_bbfea :=_eeed .Pop ();if _bbfea !=nil {return _bbfea ;};_bbda ,_egfd :=_cda .(*PSReal );_dee ,_fcd :=_cda .(*PSInteger );if !_egfd &&!_fcd {return ErrTypeCheck ;};_gaab ,_cgcd :=_ced .(*PSReal );_cfbe ,_gdd :=_ced .(*PSInteger );
if !_cgcd &&!_gdd {return ErrTypeCheck ;};if _fcd &&_gdd {_gdab :=_cfbe .Val -_dee .Val ;_gagb :=_eeed .Push (MakeInteger (_gdab ));return _gagb ;};var _eafa float64 =0;if _cgcd {_eafa =_gaab .Val ;}else {_eafa =float64 (_cfbe .Val );};if _egfd {_eafa -=_bbda .Val ;
}else {_eafa -=float64 (_dee .Val );};_bbfea =_eeed .Push (MakeReal (_eafa ));return _bbfea ;};var ErrStackOverflow =_c .New ("\u0073\u0074\u0061\u0063\u006b\u0020\u006f\u0076\u0065r\u0066\u006c\u006f\u0077");func (_dbd *PSInteger )Duplicate ()PSObject {_gdgg :=PSInteger {};
_gdgg .Val =_dbd .Val ;return &_gdgg ;};

// Execute executes the program for an input parameters `objects` and returns a slice of output objects.
func (_cd *PSExecutor )Execute (objects []PSObject )([]PSObject ,error ){for _ ,_af :=range objects {_afc :=_cd .Stack .Push (_af );if _afc !=nil {return nil ,_afc ;};};_edc :=_cd ._ag .Exec (_cd .Stack );if _edc !=nil {_ad .Log .Debug ("\u0045x\u0065c\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_edc );
return nil ,_edc ;};_eg :=[]PSObject (*_cd .Stack );_cd .Stack .Empty ();return _eg ,nil ;};func (_cce *PSOperand )eq (_ecg *PSStack )error {_afgd ,_dgc :=_ecg .Pop ();if _dgc !=nil {return _dgc ;};_dgg ,_dgc :=_ecg .Pop ();if _dgc !=nil {return _dgc ;
};_gab ,_bgb :=_afgd .(*PSBoolean );_cccg ,_fec :=_dgg .(*PSBoolean );if _bgb ||_fec {var _edcd error ;if _bgb &&_fec {_edcd =_ecg .Push (MakeBool (_gab .Val ==_cccg .Val ));}else {_edcd =_ecg .Push (MakeBool (false ));};return _edcd ;};var _abb float64 ;
var _bc float64 ;if _acf ,_fbf :=_afgd .(*PSInteger );_fbf {_abb =float64 (_acf .Val );}else if _cgcf ,_dgga :=_afgd .(*PSReal );_dgga {_abb =_cgcf .Val ;}else {return ErrTypeCheck ;};if _fdf ,_deb :=_dgg .(*PSInteger );_deb {_bc =float64 (_fdf .Val );
}else if _bbeb ,_dff :=_dgg .(*PSReal );_dff {_bc =_bbeb .Val ;}else {return ErrTypeCheck ;};if _ee .Abs (_bc -_abb )< _gd {_dgc =_ecg .Push (MakeBool (true ));}else {_dgc =_ecg .Push (MakeBool (false ));};return _dgc ;};func (_ecb *PSOperand )copy (_bbd *PSStack )error {_dga ,_cfe :=_bbd .PopInteger ();
if _cfe !=nil {return _cfe ;};if _dga < 0{return ErrRangeCheck ;};if _dga > len (*_bbd ){return ErrRangeCheck ;};*_bbd =append (*_bbd ,(*_bbd )[len (*_bbd )-_dga :]...);return nil ;};func (_agf *PSOperand )xor (_afec *PSStack )error {_fddb ,_dddd :=_afec .Pop ();
if _dddd !=nil {return _dddd ;};_ebde ,_dddd :=_afec .Pop ();if _dddd !=nil {return _dddd ;};if _ffcc ,_bbc :=_fddb .(*PSBoolean );_bbc {_bfed ,_ebc :=_ebde .(*PSBoolean );if !_ebc {return ErrTypeCheck ;};_dddd =_afec .Push (MakeBool (_ffcc .Val !=_bfed .Val ));
return _dddd ;};if _ccb ,_adee :=_fddb .(*PSInteger );_adee {_dae ,_daac :=_ebde .(*PSInteger );if !_daac {return ErrTypeCheck ;};_dddd =_afec .Push (MakeInteger (_ccb .Val ^_dae .Val ));return _dddd ;};return ErrTypeCheck ;};var ErrTypeCheck =_c .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");
func _ega (_gafg int )int {if _gafg < 0{return -_gafg ;};return _gafg ;};func (_fdb *PSOperand )Duplicate ()PSObject {_aad :=*_fdb ;return &_aad };func (_ea *PSOperand )cos (_dba *PSStack )error {_bggd ,_bbf :=_dba .PopNumberAsFloat64 ();if _bbf !=nil {return _bbf ;
};_acb :=_ee .Cos (_bggd *_ee .Pi /180.0);_bbf =_dba .Push (MakeReal (_acb ));return _bbf ;};

// PSOperand represents a Postscript operand (text string).
type PSOperand string ;func (_bdd *PSOperand )mul (_egbf *PSStack )error {_bdf ,_baab :=_egbf .Pop ();if _baab !=nil {return _baab ;};_gac ,_baab :=_egbf .Pop ();if _baab !=nil {return _baab ;};_feab ,_dad :=_bdf .(*PSReal );_gfbg ,_cba :=_bdf .(*PSInteger );
if !_dad &&!_cba {return ErrTypeCheck ;};_gfe ,_bec :=_gac .(*PSReal );_baf ,_ffc :=_gac .(*PSInteger );if !_bec &&!_ffc {return ErrTypeCheck ;};if _cba &&_ffc {_gaf :=_gfbg .Val *_baf .Val ;_bgfa :=_egbf .Push (MakeInteger (_gaf ));return _bgfa ;};var _cgaa float64 ;
if _dad {_cgaa =_feab .Val ;}else {_cgaa =float64 (_gfbg .Val );};if _bec {_cgaa *=_gfe .Val ;}else {_cgaa *=float64 (_baf .Val );};_baab =_egbf .Push (MakeReal (_cgaa ));return _baab ;};func (_bcbg *PSOperand )roll (_cdg *PSStack )error {_ffegb ,_ecf :=_cdg .Pop ();
if _ecf !=nil {return _ecf ;};_ccg ,_ecf :=_cdg .Pop ();if _ecf !=nil {return _ecf ;};_fcfd ,_gcbf :=_ffegb .(*PSInteger );if !_gcbf {return ErrTypeCheck ;};_dcd ,_gcbf :=_ccg .(*PSInteger );if !_gcbf {return ErrTypeCheck ;};if _dcd .Val < 0{return ErrRangeCheck ;
};if _dcd .Val ==0||_dcd .Val ==1{return nil ;};if _dcd .Val > len (*_cdg ){return ErrStackUnderflow ;};for _fgg :=0;_fgg < _ega (_fcfd .Val );_fgg ++{var _gafc []PSObject ;_gafc =(*_cdg )[len (*_cdg )-(_dcd .Val ):len (*_cdg )];if _fcfd .Val > 0{_dfd :=_gafc [len (_gafc )-1];
_gafc =append ([]PSObject {_dfd },_gafc [0:len (_gafc )-1]...);}else {_afb :=_gafc [len (_gafc )-_dcd .Val ];_gafc =append (_gafc [1:],_afb );};_cbaf :=append ((*_cdg )[0:len (*_cdg )-_dcd .Val ],_gafc ...);_cdg =&_cbaf ;};return nil ;};

// NewPSExecutor returns an initialized PSExecutor for an input `program`.
func NewPSExecutor (program *PSProgram )*PSExecutor {_ed :=&PSExecutor {};_ed .Stack =NewPSStack ();_ed ._ag =program ;return _ed ;};