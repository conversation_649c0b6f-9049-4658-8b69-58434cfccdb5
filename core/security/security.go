//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package security ;import (_aa "bytes";_ca "crypto/aes";_e "crypto/cipher";_a "crypto/md5";_ba "crypto/rand";_cag "crypto/rc4";_bg "crypto/sha256";_c "crypto/sha512";_eed "encoding/binary";_d "errors";_cf "fmt";_cfg "github.com/unidoc/unipdf/v4/common";
_be "hash";_ee "io";_ad "math";);type ecb struct{_dg _e .Block ;_ef int ;};func (_dda stdHandlerR4 )alg3 (R int ,_bgg ,_fef []byte )([]byte ,error ){var _dee []byte ;if len (_fef )> 0{_dee =_dda .alg3Key (R ,_fef );}else {_dee =_dda .alg3Key (R ,_bgg );
};_aaa ,_eb :=_cag .NewCipher (_dee );if _eb !=nil {return nil ,_d .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");};_eea :=_dda .paddedPass (_bgg );_dbd :=make ([]byte ,len (_eea ));_aaa .XORKeyStream (_dbd ,_eea );
if R >=3{_ccb :=make ([]byte ,len (_dee ));for _beba :=0;_beba < 19;_beba ++{for _fcd :=0;_fcd < len (_dee );_fcd ++{_ccb [_fcd ]=_dee [_fcd ]^byte (_beba +1);};_edb ,_geb :=_cag .NewCipher (_ccb );if _geb !=nil {return nil ,_d .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");
};_edb .XORKeyStream (_dbd ,_dbd );};};return _dbd ,nil ;};func _dafe (_dfec ,_eacc ,_fcf []byte )([]byte ,error ){var (_fbe ,_adde ,_cfe _be .Hash ;);_fbe =_bg .New ();_dcc :=make ([]byte ,64);_dce :=_fbe ;_dce .Write (_dfec );K :=_dce .Sum (_dcc [:0]);
_cgb :=make ([]byte ,64*(127+64+48));_dgc :=func (_ccfa int )([]byte ,error ){_ffc :=len (_eacc )+len (K )+len (_fcf );_fbb :=_cgb [:_ffc ];_deed :=copy (_fbb ,_eacc );_deed +=copy (_fbb [_deed :],K [:]);_deed +=copy (_fbb [_deed :],_fcf );if _deed !=_ffc {_cfg .Log .Error ("E\u0052\u0052\u004f\u0052\u003a\u0020u\u006e\u0065\u0078\u0070\u0065\u0063t\u0065\u0064\u0020\u0072\u006f\u0075\u006ed\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0073\u0069\u007ae\u002e");
return nil ,_d .New ("\u0077\u0072\u006f\u006e\u0067\u0020\u0073\u0069\u007a\u0065");};K1 :=_cgb [:_ffc *64];_ecf (K1 ,_ffc );_fgbb ,_ddb :=_dfe (K [0:16]);if _ddb !=nil {return nil ,_ddb ;};_aed :=_e .NewCBCEncrypter (_fgbb ,K [16:32]);_aed .CryptBlocks (K1 ,K1 );
E :=K1 ;_bcd :=0;for _bfd :=0;_bfd < 16;_bfd ++{_bcd +=int (E [_bfd ]%3);};var _eaa _be .Hash ;switch _bcd %3{case 0:_eaa =_fbe ;case 1:if _adde ==nil {_adde =_c .New384 ();};_eaa =_adde ;case 2:if _cfe ==nil {_cfe =_c .New ();};_eaa =_cfe ;};_eaa .Reset ();
_eaa .Write (E );K =_eaa .Sum (_dcc [:0]);return E ,nil ;};for _efg :=0;;{E ,_ced :=_dgc (_efg );if _ced !=nil {return nil ,_ced ;};_deb :=E [len (E )-1];_efg ++;if _efg >=64&&_deb <=uint8 (_efg -32){break ;};};return K [:32],nil ;};var _ StdHandler =stdHandlerR4 {};


// Allowed checks if a set of permissions can be granted.
func (_ae Permissions )Allowed (p2 Permissions )bool {return _ae &p2 ==p2 };func (_egca stdHandlerR6 )alg11 (_adc *StdEncryptDict ,_feg []byte )([]byte ,error ){if _abbd :=_cg ("\u0061\u006c\u00671\u0031","\u0055",48,_adc .U );_abbd !=nil {return nil ,_abbd ;
};_aga :=make ([]byte ,len (_feg )+8);_cbb :=copy (_aga ,_feg );_cbb +=copy (_aga [_cbb :],_adc .U [32:40]);_gae ,_eeeb :=_egca .alg2b (_adc .R ,_aga ,_feg ,nil );if _eeeb !=nil {return nil ,_eeeb ;};_gae =_gae [:32];if !_aa .Equal (_gae ,_adc .U [:32]){return nil ,nil ;
};return _gae ,nil ;};var _ StdHandler =stdHandlerR6 {};

// StdEncryptDict is a set of additional fields used in standard encryption dictionary.
type StdEncryptDict struct{R int ;P Permissions ;EncryptMetadata bool ;O ,U []byte ;OE ,UE []byte ;Perms []byte ;};

// Authenticate implements StdHandler interface.
func (_age stdHandlerR4 )Authenticate (d *StdEncryptDict ,pass []byte )([]byte ,Permissions ,error ){_cfg .Log .Trace ("\u0044\u0065b\u0075\u0067\u0067\u0069n\u0067\u0020a\u0075\u0074\u0068\u0065\u006e\u0074\u0069\u0063a\u0074\u0069\u006f\u006e\u0020\u002d\u0020\u006f\u0077\u006e\u0065\u0072 \u0070\u0061\u0073\u0073");
_bef ,_aff :=_age .alg7 (d ,pass );if _aff !=nil {return nil ,0,_aff ;};if _bef !=nil {_cfg .Log .Trace ("\u0074h\u0069\u0073\u002e\u0061u\u0074\u0068\u0065\u006e\u0074i\u0063a\u0074e\u0064\u0020\u003d\u0020\u0054\u0072\u0075e");return _bef ,PermOwner ,nil ;
};_cfg .Log .Trace ("\u0044\u0065bu\u0067\u0067\u0069n\u0067\u0020\u0061\u0075the\u006eti\u0063\u0061\u0074\u0069\u006f\u006e\u0020- \u0075\u0073\u0065\u0072\u0020\u0070\u0061s\u0073");_bef ,_aff =_age .alg6 (d ,pass );if _aff !=nil {return nil ,0,_aff ;
};if _bef !=nil {_cfg .Log .Trace ("\u0074h\u0069\u0073\u002e\u0061u\u0074\u0068\u0065\u006e\u0074i\u0063a\u0074e\u0064\u0020\u003d\u0020\u0054\u0072\u0075e");return _bef ,d .P ,nil ;};return nil ,0,nil ;};const (EventDocOpen =AuthEvent ("\u0044o\u0063\u004f\u0070\u0065\u006e");
EventEFOpen =AuthEvent ("\u0045\u0046\u004f\u0070\u0065\u006e"););

// NewHandlerR6 creates a new standard security handler for R=5 and R=6.
func NewHandlerR6 ()StdHandler {return stdHandlerR6 {}};func _caa (_caf _e .Block )*ecb {return &ecb {_dg :_caf ,_ef :_caf .BlockSize ()}};func (stdHandlerR4 )paddedPass (_bc []byte )[]byte {_beg :=make ([]byte ,32);_ce :=copy (_beg ,_bc );for ;_ce < 32;
_ce ++{_beg [_ce ]=_dgd [_ce -len (_bc )];};return _beg ;};const _dgd ="\x28\277\116\136\x4e\x75\x8a\x41\x64\000\x4e\x56\377"+"\xfa\001\010\056\x2e\x00\xb6\xd0\x68\076\x80\x2f\014"+"\251\xfe\x64\x53\x69\172";type ecbDecrypter ecb ;

// GenerateParams is the algorithm opposite to alg2a (R>=5).
// It generates U,O,UE,OE,Perms fields using AESv3 encryption.
// There is no algorithm number assigned to this function in the spec.
// It expects R, P and EncryptMetadata fields to be set.
func (_fdf stdHandlerR6 )GenerateParams (d *StdEncryptDict ,opass ,upass []byte )([]byte ,error ){_acb :=make ([]byte ,32);if _ ,_cab :=_ee .ReadFull (_ba .Reader ,_acb );_cab !=nil {return nil ,_cab ;};d .U =nil ;d .O =nil ;d .UE =nil ;d .OE =nil ;d .Perms =nil ;
if len (upass )> 127{upass =upass [:127];};if len (opass )> 127{opass =opass [:127];};if _fccc :=_fdf .alg8 (d ,_acb ,upass );_fccc !=nil {return nil ,_fccc ;};if _cbdg :=_fdf .alg9 (d ,_acb ,opass );_cbdg !=nil {return nil ,_cbdg ;};if d .R ==5{return _acb ,nil ;
};if _dca :=_fdf .alg10 (d ,_acb );_dca !=nil {return nil ,_dca ;};return _acb ,nil ;};type stdHandlerR4 struct{Length int ;ID0 string ;};func (_ed errInvalidField )Error ()string {return _cf .Sprintf ("\u0025s\u003a\u0020e\u0078\u0070\u0065\u0063t\u0065\u0064\u0020%\u0073\u0020\u0066\u0069\u0065\u006c\u0064\u0020\u0074o \u0062\u0065\u0020%\u0064\u0020b\u0079\u0074\u0065\u0073\u002c\u0020g\u006f\u0074 \u0025\u0064",_ed .Func ,_ed .Field ,_ed .Exp ,_ed .Got );
};func (_afab stdHandlerR6 )alg10 (_fab *StdEncryptDict ,_dfab []byte )error {if _bge :=_cg ("\u0061\u006c\u00671\u0030","\u004b\u0065\u0079",32,_dfab );_bge !=nil {return _bge ;};_adf :=uint64 (uint32 (_fab .P ))|(_ad .MaxUint32 <<32);Perms :=make ([]byte ,16);
_eed .LittleEndian .PutUint64 (Perms [:8],_adf );if _fab .EncryptMetadata {Perms [8]='T';}else {Perms [8]='F';};copy (Perms [9:12],"\u0061\u0064\u0062");if _ ,_cadb :=_ee .ReadFull (_ba .Reader ,Perms [12:16]);_cadb !=nil {return _cadb ;};_abb ,_dfecc :=_dfe (_dfab [:32]);
if _dfecc !=nil {return _dfecc ;};_gdd :=_f (_abb );_gdd .CryptBlocks (Perms ,Perms );_fab .Perms =Perms [:16];return nil ;};func (_abf stdHandlerR6 )alg2b (R int ,_dgf ,_cgec ,_fbd []byte )([]byte ,error ){if R ==5{return _ceb (_dgf );};return _dafe (_dgf ,_cgec ,_fbd );
};func (_ccc stdHandlerR4 )alg7 (_ec *StdEncryptDict ,_cfgd []byte )([]byte ,error ){_eeda :=_ccc .alg3Key (_ec .R ,_cfgd );_fb :=make ([]byte ,len (_ec .O ));if _ec .R ==2{_ac ,_dfb :=_cag .NewCipher (_eeda );if _dfb !=nil {return nil ,_d .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0063\u0069\u0070\u0068\u0065\u0072");
};_ac .XORKeyStream (_fb ,_ec .O );}else if _ec .R >=3{_ffd :=append ([]byte {},_ec .O ...);for _fbg :=0;_fbg < 20;_fbg ++{_dag :=append ([]byte {},_eeda ...);for _begg :=0;_begg < len (_eeda );_begg ++{_dag [_begg ]^=byte (19-_fbg );};_cge ,_fac :=_cag .NewCipher (_dag );
if _fac !=nil {return nil ,_d .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0063\u0069\u0070\u0068\u0065\u0072");};_cge .XORKeyStream (_fb ,_ffd );_ffd =append ([]byte {},_fb ...);};}else {return nil ,_d .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020R");
};_ddg ,_bf :=_ccc .alg6 (_ec ,_fb );if _bf !=nil {return nil ,nil ;};return _ddg ,nil ;};

// AuthEvent is an event type that triggers authentication.
type AuthEvent string ;func _ecf (_bba []byte ,_fd int ){_dgb :=_fd ;for _dgb < len (_bba ){copy (_bba [_dgb :],_bba [:_dgb ]);_dgb *=2;};};func (_da *ecbEncrypter )BlockSize ()int {return _da ._ef };func (_dc stdHandlerR4 )alg4 (_gbe []byte ,_cbd []byte )([]byte ,error ){_dbf ,_fcc :=_cag .NewCipher (_gbe );
if _fcc !=nil {return nil ,_d .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");};_gg :=[]byte (_dgd );_cac :=make ([]byte ,len (_gg ));_dbf .XORKeyStream (_cac ,_gg );return _cac ,nil ;};func (_bcda stdHandlerR6 )alg8 (_edf *StdEncryptDict ,_bgaf []byte ,_bdg []byte )error {if _bfg :=_cg ("\u0061\u006c\u0067\u0038","\u004b\u0065\u0079",32,_bgaf );
_bfg !=nil {return _bfg ;};var _ccd [16]byte ;if _ ,_fgc :=_ee .ReadFull (_ba .Reader ,_ccd [:]);_fgc !=nil {return _fgc ;};_fbgf :=_ccd [0:8];_egg :=_ccd [8:16];_egd :=make ([]byte ,len (_bdg )+len (_fbgf ));_eaab :=copy (_egd ,_bdg );copy (_egd [_eaab :],_fbgf );
_cbge ,_cdb :=_bcda .alg2b (_edf .R ,_egd ,_bdg ,nil );if _cdb !=nil {return _cdb ;};U :=make ([]byte ,len (_cbge )+len (_fbgf )+len (_egg ));_eaab =copy (U ,_cbge [:32]);_eaab +=copy (U [_eaab :],_fbgf );copy (U [_eaab :],_egg );_edf .U =U ;_eaab =len (_bdg );
copy (_egd [_eaab :],_egg );_cbge ,_cdb =_bcda .alg2b (_edf .R ,_egd ,_bdg ,nil );if _cdb !=nil {return _cdb ;};_agf ,_cdb :=_dfe (_cbge [:32]);if _cdb !=nil {return _cdb ;};_addf :=make ([]byte ,_ca .BlockSize );_dbb :=_e .NewCBCEncrypter (_agf ,_addf );
UE :=make ([]byte ,32);_dbb .CryptBlocks (UE ,_bgaf [:32]);_edf .UE =UE ;return nil ;};func (_deg stdHandlerR6 )alg9 (_edd *StdEncryptDict ,_agc []byte ,_dgdc []byte )error {if _fbeg :=_cg ("\u0061\u006c\u0067\u0039","\u004b\u0065\u0079",32,_agc );_fbeg !=nil {return _fbeg ;
};if _gga :=_cg ("\u0061\u006c\u0067\u0039","\u0055",48,_edd .U );_gga !=nil {return _gga ;};var _gdce [16]byte ;if _ ,_cee :=_ee .ReadFull (_ba .Reader ,_gdce [:]);_cee !=nil {return _cee ;};_agcf :=_gdce [0:8];_dfg :=_gdce [8:16];_gcf :=_edd .U [:48];
_dfa :=make ([]byte ,len (_dgdc )+len (_agcf )+len (_gcf ));_geg :=copy (_dfa ,_dgdc );_geg +=copy (_dfa [_geg :],_agcf );_geg +=copy (_dfa [_geg :],_gcf );_afa ,_fee :=_deg .alg2b (_edd .R ,_dfa ,_dgdc ,_gcf );if _fee !=nil {return _fee ;};O :=make ([]byte ,len (_afa )+len (_agcf )+len (_dfg ));
_geg =copy (O ,_afa [:32]);_geg +=copy (O [_geg :],_agcf );_geg +=copy (O [_geg :],_dfg );_edd .O =O ;_geg =len (_dgdc );_geg +=copy (_dfa [_geg :],_dfg );_afa ,_fee =_deg .alg2b (_edd .R ,_dfa ,_dgdc ,_gcf );if _fee !=nil {return _fee ;};_egda ,_fee :=_dfe (_afa [:32]);
if _fee !=nil {return _fee ;};_fge :=make ([]byte ,_ca .BlockSize );_dbbf :=_e .NewCBCEncrypter (_egda ,_fge );OE :=make ([]byte ,32);_dbbf .CryptBlocks (OE ,_agc [:32]);_edd .OE =OE ;return nil ;};func _ceb (_ddc []byte )([]byte ,error ){_fca :=_bg .New ();
_fca .Write (_ddc );return _fca .Sum (nil ),nil ;};func (_cde stdHandlerR4 )alg3Key (R int ,_ff []byte )[]byte {_dbe :=_a .New ();_fed :=_cde .paddedPass (_ff );_dbe .Write (_fed );if R >=3{for _dd :=0;_dd < 50;_dd ++{_fa :=_dbe .Sum (nil );_dbe =_a .New ();
_dbe .Write (_fa );};};_fgb :=_dbe .Sum (nil );if R ==2{_fgb =_fgb [0:5];}else {_fgb =_fgb [0:_cde .Length /8];};return _fgb ;};

// NewHandlerR4 creates a new standard security handler for R<=4.
func NewHandlerR4 (id0 string ,length int )StdHandler {return stdHandlerR4 {ID0 :id0 ,Length :length }};func (_gdcec stdHandlerR6 )alg12 (_gebe *StdEncryptDict ,_bggf []byte )([]byte ,error ){if _efa :=_cg ("\u0061\u006c\u00671\u0032","\u0055",48,_gebe .U );
_efa !=nil {return nil ,_efa ;};if _feb :=_cg ("\u0061\u006c\u00671\u0032","\u004f",48,_gebe .O );_feb !=nil {return nil ,_feb ;};_aca :=make ([]byte ,len (_bggf )+8+48);_deaa :=copy (_aca ,_bggf );_deaa +=copy (_aca [_deaa :],_gebe .O [32:40]);_deaa +=copy (_aca [_deaa :],_gebe .U [0:48]);
_ceec ,_eacb :=_gdcec .alg2b (_gebe .R ,_aca ,_bggf ,_gebe .U [0:48]);if _eacb !=nil {return nil ,_eacb ;};_ceec =_ceec [:32];if !_aa .Equal (_ceec ,_gebe .O [:32]){return nil ,nil ;};return _ceec ,nil ;};type errInvalidField struct{Func string ;Field string ;
Exp int ;Got int ;};func _dfe (_ecd []byte )(_e .Block ,error ){_edg ,_dea :=_ca .NewCipher (_ecd );if _dea !=nil {_cfg .Log .Error ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u0072\u0065\u0061\u0074\u0065\u0020A\u0045\u0053\u0020\u0063\u0069p\u0068\u0065r\u003a\u0020\u0025\u0076",_dea );
return nil ,_dea ;};return _edg ,nil ;};func (_fcg stdHandlerR4 )alg2 (_cb *StdEncryptDict ,_beb []byte )[]byte {_cfg .Log .Trace ("\u0061\u006c\u0067\u0032");_eg :=_fcg .paddedPass (_beb );_df :=_a .New ();_df .Write (_eg );_df .Write (_cb .O );var _bb [4]byte ;
_eed .LittleEndian .PutUint32 (_bb [:],uint32 (_cb .P ));_df .Write (_bb [:]);_cfg .Log .Trace ("\u0067o\u0020\u0050\u003a\u0020\u0025\u0020x",_bb );_df .Write ([]byte (_fcg .ID0 ));_cfg .Log .Trace ("\u0074\u0068\u0069\u0073\u002e\u0052\u0020\u003d\u0020\u0025d\u0020\u0065\u006e\u0063\u0072\u0079\u0070t\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u0025\u0076",_cb .R ,_cb .EncryptMetadata );
if (_cb .R >=4)&&!_cb .EncryptMetadata {_df .Write ([]byte {0xff,0xff,0xff,0xff});};_ge :=_df .Sum (nil );if _cb .R >=3{_df =_a .New ();for _bgc :=0;_bgc < 50;_bgc ++{_df .Reset ();_df .Write (_ge [0:_fcg .Length /8]);_ge =_df .Sum (nil );};};if _cb .R >=3{return _ge [0:_fcg .Length /8];
};return _ge [0:5];};func _cg (_bga ,_bgd string ,_fc int ,_bd []byte )error {if len (_bd )< _fc {return errInvalidField {Func :_bga ,Field :_bgd ,Exp :_fc ,Got :len (_bd )};};return nil ;};

// Authenticate implements StdHandler interface.
func (_agfc stdHandlerR6 )Authenticate (d *StdEncryptDict ,pass []byte )([]byte ,Permissions ,error ){return _agfc .alg2a (d ,pass );};

// StdHandler is an interface for standard security handlers.
type StdHandler interface{

// GenerateParams uses owner and user passwords to set encryption parameters and generate an encryption key.
// It assumes that R, P and EncryptMetadata are already set.
GenerateParams (_fg *StdEncryptDict ,_de ,_cd []byte )([]byte ,error );

// Authenticate uses encryption dictionary parameters and the password to calculate
// the document encryption key. It also returns permissions that should be granted to a user.
// In case of failed authentication, it returns empty key and zero permissions with no error.
Authenticate (_fe *StdEncryptDict ,_add []byte )([]byte ,Permissions ,error );};

// Permissions is a bitmask of access permissions for a PDF file.
type Permissions uint32 ;func (_gba stdHandlerR6 )alg13 (_dbgd *StdEncryptDict ,_cgbc []byte )error {if _cga :=_cg ("\u0061\u006c\u00671\u0033","\u004b\u0065\u0079",32,_cgbc );_cga !=nil {return _cga ;};if _gad :=_cg ("\u0061\u006c\u00671\u0033","\u0050\u0065\u0072m\u0073",16,_dbgd .Perms );
_gad !=nil {return _gad ;};_fba :=make ([]byte ,16);copy (_fba ,_dbgd .Perms [:16]);_efgb ,_gbb :=_ca .NewCipher (_cgbc [:32]);if _gbb !=nil {return _gbb ;};_aaf :=_db (_efgb );_aaf .CryptBlocks (_fba ,_fba );if !_aa .Equal (_fba [9:12],[]byte ("\u0061\u0064\u0062")){return _d .New ("\u0064\u0065\u0063o\u0064\u0065\u0064\u0020p\u0065\u0072\u006d\u0069\u0073\u0073\u0069o\u006e\u0073\u0020\u0061\u0072\u0065\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064");
};_ceg :=Permissions (_eed .LittleEndian .Uint32 (_fba [0:4]));if _ceg !=_dbgd .P {return _d .New ("\u0070\u0065r\u006d\u0069\u0073\u0073\u0069\u006f\u006e\u0073\u0020\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0061il\u0065\u0064");
};var _fabe bool ;if _fba [8]=='T'{_fabe =true ;}else if _fba [8]=='F'{_fabe =false ;}else {return _d .New ("\u0064\u0065\u0063\u006f\u0064\u0065\u0064 \u006d\u0065\u0074a\u0064\u0061\u0074\u0061 \u0065\u006e\u0063\u0072\u0079\u0070\u0074\u0069\u006f\u006e\u0020\u0066\u006c\u0061\u0067\u0020\u0069\u0073\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064");
};if _fabe !=_dbgd .EncryptMetadata {return _d .New ("\u006d\u0065t\u0061\u0064\u0061\u0074a\u0020\u0065n\u0063\u0072\u0079\u0070\u0074\u0069\u006f\u006e \u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0069\u006f\u006e\u0020\u0066a\u0069\u006c\u0065\u0064");
};return nil ;};type ecbEncrypter ecb ;func (_ea stdHandlerR4 )alg5 (_fgg []byte ,_bbc []byte )([]byte ,error ){_bdea :=_a .New ();_bdea .Write ([]byte (_dgd ));_bdea .Write ([]byte (_ea .ID0 ));_fad :=_bdea .Sum (nil );_cfg .Log .Trace ("\u0061\u006c\u0067\u0035");
_cfg .Log .Trace ("\u0065k\u0065\u0079\u003a\u0020\u0025\u0020x",_fgg );_cfg .Log .Trace ("\u0049D\u003a\u0020\u0025\u0020\u0078",_ea .ID0 );if len (_fad )!=16{return nil ,_d .New ("\u0068a\u0073\u0068\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006eo\u0074\u0020\u0031\u0036\u0020\u0062\u0079\u0074\u0065\u0073");
};_ede ,_cbg :=_cag .NewCipher (_fgg );if _cbg !=nil {return nil ,_d .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");};_aea :=make ([]byte ,16);_ede .XORKeyStream (_aea ,_fad );_ag :=make ([]byte ,len (_fgg ));
for _gd :=0;_gd < 19;_gd ++{for _dde :=0;_dde < len (_fgg );_dde ++{_ag [_dde ]=_fgg [_dde ]^byte (_gd +1);};_ede ,_cbg =_cag .NewCipher (_ag );if _cbg !=nil {return nil ,_d .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");
};_ede .XORKeyStream (_aea ,_aea );_cfg .Log .Trace ("\u0069\u0020\u003d\u0020\u0025\u0064\u002c\u0020\u0065\u006b\u0065\u0079:\u0020\u0025\u0020\u0078",_gd ,_ag );_cfg .Log .Trace ("\u0069\u0020\u003d\u0020\u0025\u0064\u0020\u002d\u003e\u0020\u0025\u0020\u0078",_gd ,_aea );
};_ebd :=make ([]byte ,32);for _ab :=0;_ab < 16;_ab ++{_ebd [_ab ]=_aea [_ab ];};_ ,_cbg =_ba .Read (_ebd [16:32]);if _cbg !=nil {return nil ,_d .New ("\u0066a\u0069\u006c\u0065\u0064 \u0074\u006f\u0020\u0067\u0065n\u0020r\u0061n\u0064\u0020\u006e\u0075\u006d\u0062\u0065r");
};return _ebd ,nil ;};func _f (_g _e .Block )_e .BlockMode {return (*ecbEncrypter )(_caa (_g ))};func (_cc *ecbDecrypter )BlockSize ()int {return _cc ._ef };func (_gc *ecbDecrypter )CryptBlocks (dst ,src []byte ){if len (src )%_gc ._ef !=0{_cfg .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0045\u0043\u0042\u0020\u0064\u0065\u0063\u0072\u0079\u0070\u0074\u003a \u0069\u006e\u0070\u0075\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u0075\u006c\u006c\u0020\u0062\u006c\u006f\u0063\u006b\u0073");
return ;};if len (dst )< len (src ){_cfg .Log .Error ("\u0045R\u0052\u004fR\u003a\u0020\u0045C\u0042\u0020\u0064\u0065\u0063\u0072\u0079p\u0074\u003a\u0020\u006f\u0075\u0074p\u0075\u0074\u0020\u0073\u006d\u0061\u006c\u006c\u0065\u0072\u0020t\u0068\u0061\u006e\u0020\u0069\u006e\u0070\u0075\u0074");
return ;};for len (src )> 0{_gc ._dg .Decrypt (dst ,src [:_gc ._ef ]);src =src [_gc ._ef :];dst =dst [_gc ._ef :];};};type stdHandlerR6 struct{};const (PermOwner =Permissions (_ad .MaxUint32 );PermPrinting =Permissions (1<<2);PermModify =Permissions (1<<3);
PermExtractGraphics =Permissions (1<<4);PermAnnotate =Permissions (1<<5);PermFillForms =Permissions (1<<8);PermDisabilityExtract =Permissions (1<<9);PermRotateInsert =Permissions (1<<10);PermFullPrintQuality =Permissions (1<<11););func (_ega stdHandlerR4 )alg6 (_fggf *StdEncryptDict ,_dae []byte )([]byte ,error ){var (_dba []byte ;
_af error ;);_gf :=_ega .alg2 (_fggf ,_dae );if _fggf .R ==2{_dba ,_af =_ega .alg4 (_gf ,_dae );}else if _fggf .R >=3{_dba ,_af =_ega .alg5 (_gf ,_dae );}else {return nil ,_d .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020R");};if _af !=nil {return nil ,_af ;
};_cfg .Log .Trace ("\u0063\u0068\u0065\u0063k:\u0020\u0025\u0020\u0078\u0020\u003d\u003d\u0020\u0025\u0020\u0078\u0020\u003f",string (_dba ),string (_fggf .U ));_cda :=_dba ;_ga :=_fggf .U ;if _fggf .R >=3{if len (_cda )> 16{_cda =_cda [0:16];};if len (_ga )> 16{_ga =_ga [0:16];
};};if !_aa .Equal (_cda ,_ga ){return nil ,nil ;};return _gf ,nil ;};func _db (_gb _e .Block )_e .BlockMode {return (*ecbDecrypter )(_caa (_gb ))};func (_eee *ecbEncrypter )CryptBlocks (dst ,src []byte ){if len (src )%_eee ._ef !=0{_cfg .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0045\u0043\u0042\u0020\u0065\u006e\u0063\u0072\u0079\u0070\u0074\u003a \u0069\u006e\u0070\u0075\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u0075\u006c\u006c\u0020\u0062\u006c\u006f\u0063\u006b\u0073");
return ;};if len (dst )< len (src ){_cfg .Log .Error ("\u0045R\u0052\u004fR\u003a\u0020\u0045C\u0042\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u003a\u0020\u006f\u0075\u0074p\u0075\u0074\u0020\u0073\u006d\u0061\u006c\u006c\u0065\u0072\u0020t\u0068\u0061\u006e\u0020\u0069\u006e\u0070\u0075\u0074");
return ;};for len (src )> 0{_eee ._dg .Encrypt (dst ,src [:_eee ._ef ]);src =src [_eee ._ef :];dst =dst [_eee ._ef :];};};

// GenerateParams generates and sets O and U parameters for the encryption dictionary.
// It expects R, P and EncryptMetadata fields to be set.
func (_ade stdHandlerR4 )GenerateParams (d *StdEncryptDict ,opass ,upass []byte )([]byte ,error ){O ,_agg :=_ade .alg3 (d .R ,upass ,opass );if _agg !=nil {_cfg .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006ee\u0072\u0061\u0074\u0069\u006e\u0067 \u004f\u0020\u0066\u006f\u0072\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_agg );
return nil ,_agg ;};d .O =O ;_cfg .Log .Trace ("\u0067\u0065\u006e\u0020\u004f\u003a\u0020\u0025\u0020\u0078",O );_aag :=_ade .alg2 (d ,upass );U ,_agg :=_ade .alg5 (_aag ,upass );if _agg !=nil {_cfg .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006ee\u0072\u0061\u0074\u0069\u006e\u0067 \u004f\u0020\u0066\u006f\u0072\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_agg );
return nil ,_agg ;};d .U =U ;_cfg .Log .Trace ("\u0067\u0065\u006e\u0020\u0055\u003a\u0020\u0025\u0020\u0078",U );return _aag ,nil ;};func (_fea stdHandlerR6 )alg2a (_eac *StdEncryptDict ,_ccf []byte )([]byte ,Permissions ,error ){if _edc :=_cg ("\u0061\u006c\u00672\u0061","\u004f",48,_eac .O );
_edc !=nil {return nil ,0,_edc ;};if _eeaf :=_cg ("\u0061\u006c\u00672\u0061","\u0055",48,_eac .U );_eeaf !=nil {return nil ,0,_eeaf ;};if len (_ccf )> 127{_ccf =_ccf [:127];};_dbg ,_dage :=_fea .alg12 (_eac ,_ccf );if _dage !=nil {return nil ,0,_dage ;
};var (_afe []byte ;_acg []byte ;_begf []byte ;);var _fcb Permissions ;if len (_dbg )!=0{_fcb =PermOwner ;_cad :=make ([]byte ,len (_ccf )+8+48);_daf :=copy (_cad ,_ccf );_daf +=copy (_cad [_daf :],_eac .O [40:48]);copy (_cad [_daf :],_eac .U [0:48]);_afe =_cad ;
_acg =_eac .OE ;_begf =_eac .U [0:48];}else {_dbg ,_dage =_fea .alg11 (_eac ,_ccf );if _dage ==nil &&len (_dbg )==0{_dbg ,_dage =_fea .alg11 (_eac ,[]byte (""));};if _dage !=nil {return nil ,0,_dage ;}else if len (_dbg )==0{return nil ,0,nil ;};_fcb =_eac .P ;
_egc :=make ([]byte ,len (_ccf )+8);_acga :=copy (_egc ,_ccf );copy (_egc [_acga :],_eac .U [40:48]);_afe =_egc ;_acg =_eac .UE ;_begf =nil ;};if _ccbg :=_cg ("\u0061\u006c\u00672\u0061","\u004b\u0065\u0079",32,_acg );_ccbg !=nil {return nil ,0,_ccbg ;
};_acg =_acg [:32];_fbc ,_dage :=_fea .alg2b (_eac .R ,_afe ,_ccf ,_begf );if _dage !=nil {return nil ,0,_dage ;};_dcd ,_dage :=_ca .NewCipher (_fbc [:32]);if _dage !=nil {return nil ,0,_dage ;};_eca :=make ([]byte ,_ca .BlockSize );_ecc :=_e .NewCBCDecrypter (_dcd ,_eca );
_gfc :=make ([]byte ,32);_ecc .CryptBlocks (_gfc ,_acg );if _eac .R ==5{return _gfc ,_fcb ,nil ;};_dage =_fea .alg13 (_eac ,_gfc );if _dage !=nil {return nil ,0,_dage ;};return _gfc ,_fcb ,nil ;};