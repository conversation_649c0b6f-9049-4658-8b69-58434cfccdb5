//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package extractor is used for quickly extracting PDF content through a simple interface.
// Currently offers functionality for extracting textual content.
package extractor ;import (_bb "bytes";_cc "errors";_ad "fmt";_g "github.com/unidoc/unipdf/v4/common";_fc "github.com/unidoc/unipdf/v4/contentstream";_cg "github.com/unidoc/unipdf/v4/core";_ec "github.com/unidoc/unipdf/v4/internal/license";_ac "github.com/unidoc/unipdf/v4/internal/textencoding";
_dc "github.com/unidoc/unipdf/v4/internal/transform";_aa "github.com/unidoc/unipdf/v4/model";_a "golang.org/x/image/draw";_b "golang.org/x/text/unicode/norm";_ff "image";_ed "image/color";_da "io";_ea "math";_e "reflect";_bcc "regexp";_f "sort";_add "strings";
_c "unicode";_bc "unicode/utf8";);type textState struct{_debe float64 ;_bag float64 ;_beb float64 ;_dcfb float64 ;_dced float64 ;_cbffg RenderMode ;_bac float64 ;_ebca *_aa .PdfFont ;_bcgef _aa .PdfRectangle ;_accd int ;_gfbdb int ;};func (_gfff *subpath )close (){if !_cfaf (_gfff ._eae [0],_gfff .last ()){_gfff .add (_gfff ._eae [0]);
};_gfff ._bebg =true ;_gfff .removeDuplicates ();};func (_fbe *shapesState )moveTo (_egb ,_cbcc float64 ){_fbe ._geee =true ;_fbe ._cfddfa =_fbe .devicePoint (_egb ,_cbcc );if _fdee {_g .Log .Info ("\u006d\u006fv\u0065\u0054\u006f\u003a\u0020\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0020\u0064\u0065\u0076\u0069\u0063\u0065\u003d%.\u0032\u0066",_egb ,_cbcc ,_fbe ._cfddfa );
};};func (_aeac *textObject )setTextRise (_cdadg float64 ){if _aeac ==nil {return ;};_aeac ._abf ._bac =_cdadg ;};func (_fafcc rulingList )removeDuplicates ()rulingList {if len (_fafcc )==0{return nil ;};_fafcc .sort ();_afea :=rulingList {_fafcc [0]};
for _ ,_ggbe :=range _fafcc [1:]{if _ggbe .equals (_afea [len (_afea )-1]){continue ;};_afea =append (_afea ,_ggbe );};return _afea ;};const _ebec =1.0/1000.0;func _dadf (_cagd float64 )int {var _gbfe int ;if _cagd >=0{_gbfe =int (_cagd /_bbbd );}else {_gbfe =int (_cagd /_bbbd )-1;
};return _gbfe ;};type textMark struct{_aa .PdfRectangle ;_afbe int ;_gacc string ;_efdc string ;_acea *_aa .PdfFont ;_fadfd float64 ;_acbd float64 ;_ecef _dc .Matrix ;_fcfa _dc .Point ;_ebac _aa .PdfRectangle ;_fbbbc _ed .Color ;_abag _ed .Color ;_eecd _cg .PdfObject ;
_aacc []string ;Tw float64 ;Th float64 ;_ecfc int ;_dcdga int ;};func _bgdca (_ddea _aa .PdfRectangle ,_efac []*textLine )*textPara {return &textPara {PdfRectangle :_ddea ,_fdfb :_efac };};func _afgf (_fecc ,_eedf _aa .PdfRectangle )bool {return _bbgc (_fecc ,_eedf )&&_ffae (_fecc ,_eedf )};
func _afag (_ffca []byte ,_aeff *_aa .PdfFont )string {_eec :=_aeff .BytesToCharcodes (_ffca );_efb ,_fce ,_cbbg :=_aeff .CharcodesToStrings (_eec ,"");if _cbbg > 0{_g .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_fce ,_cbbg );
};_cdad :=_add .Join (_efb ,"");return _cdad ;};func _bbgf (_cdebg map[float64 ]map[float64 ]gridTile )[]float64 {_bggbf :=make ([]float64 ,0,len (_cdebg ));_aeebg :=make (map[float64 ]struct{},len (_cdebg ));for _ ,_ceea :=range _cdebg {for _edgc :=range _ceea {if _ ,_aecd :=_aeebg [_edgc ];
_aecd {continue ;};_bggbf =append (_bggbf ,_edgc );_aeebg [_edgc ]=struct{}{};};};_f .Float64s (_bggbf );return _bggbf ;};func _bbdec (_dbfbd *list ,_fcb *_add .Builder ,_fccd *string ){_debcc :=_ecfg (_dbfbd ,_fccd );_fcb .WriteString (_debcc );for _ ,_bea :=range _dbfbd ._bfaf {_gfbff :=*_fccd +"\u0020\u0020\u0020";
_bbdec (_bea ,_fcb ,&_gfbff );};};

// ToTextMark returns the public view of `tm`.
func (_fcdg *textMark )ToTextMark ()TextMark {return TextMark {Text :_fcdg ._gacc ,Original :_fcdg ._efdc ,BBox :_fcdg ._ebac ,Font :_fcdg ._acea ,FontSize :_fcdg ._fadfd ,FillColor :_fcdg ._fbbbc ,StrokeColor :_fcdg ._abag ,Orientation :_fcdg ._afbe ,DirectObject :_fcdg ._eecd ,ObjString :_fcdg ._aacc ,Tw :_fcdg .Tw ,Th :_fcdg .Th ,Tc :_fcdg ._acbd ,Index :_fcdg ._dcdga };
};func (_bddaf *textPara )getListLines ()[]*textLine {var _aaecc []*textLine ;_dabde :=_eeda (_bddaf ._fdfb );for _ ,_begb :=range _bddaf ._fdfb {_ecdc :=_begb ._dbcg [0]._ecegc [0];if _ggba (_ecdc ){_aaecc =append (_aaecc ,_begb );};};_aaecc =append (_aaecc ,_dabde ...);
return _aaecc ;};func (_acff *textObject )getStrokeColor ()_ed .Color {return _ggeda (_acff ._bffb .ColorspaceStroking ,_acff ._bffb .ColorStroking );};func _dedg (_bfgbd []*textWord ,_bbbbg int )[]*textWord {_fddad :=len (_bfgbd );copy (_bfgbd [_bbbbg :],_bfgbd [_bbbbg +1:]);
return _bfgbd [:_fddad -1];};func (_dfeg *textObject )setTextLeading (_deab float64 ){if _dfeg ==nil {return ;};_dfeg ._abf ._dcfb =_deab ;};func (_dccc *textTable )getComposite (_dedf ,_cgcab int )(paraList ,_aa .PdfRectangle ){_gcaae ,_ecaaf :=_dccc ._efcbeb [_cggdf (_dedf ,_cgcab )];
if _bffae {_ad .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0067\u0065\u0074\u0043\u006f\u006d\u0070o\u0073i\u0074\u0065\u0028\u0025\u0064\u002c\u0025\u0064\u0029\u002d\u003e\u0025\u0073\u000a",_dedf ,_cgcab ,_gcaae .String ());};if !_ecaaf {return nil ,_aa .PdfRectangle {};
};return _gcaae .parasBBox ();};type rulingList []*ruling ;func _ccfd (_ccfa *TextMarkArray ,_fdfd *string ,_afg *int ,_ceb string )error {var _cef TextMark ;for _ ,_becg :=range _ccfa .Elements (){_beff :=_becg .Text ;_cbb :=_becg .Font ;_adac :="";_ggeg :=*_fdfd ;
if len (_ggeg )> *_afg {_adac =_ggeg [*_afg :*_afg +len (_beff )];}else if *_afg ==len (_ceb )-1&&len (_ggeg )> *_afg {_adac =_ggeg [*_afg :];};_afa :=_becg .DirectObject ;if _afa ==nil &&_becg .Text =="\u0020"{_faaf :=_cef .ObjString ;_bgb :=_faaf [len (_faaf )-1];
if _bgb !=_becg .Text {_afa =_cef .DirectObject ;_cbb =_cef .Font ;_afda ,_bbe :=_cg .GetString (_afa );if !_bbe {return _ad .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_fead ,_bbe :=_cg .GetStringBytes (_afa );if !_bbe {return _cg .ErrTypeError ;};_age :=_afag (_fead ,_cbb );_adac =_age +_adac ;_cgdb (_afda ,_adac ,_cbb );*_afg +=len (_beff );continue ;};};_cdab ,_ace :=_cg .GetString (_afa );if !_ace {return _ad .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_afdf :="";_eebe ,_ace :=_cg .GetStringBytes (_afa );if !_ace {return _cg .ErrTypeError ;};_dcaf :=_afag (_eebe ,_cbb );_afdf =_add .Replace (_dcaf ,_beff ,_adac ,1);_cgdb (_cdab ,_afdf ,_cbb );*_afg +=len (_beff );_cef =_becg ;};return nil ;};

// Extractor stores and offers functionality for extracting content from PDF pages.
type Extractor struct{_gcc string ;_cgf *_aa .PdfPageResources ;_fgc _aa .PdfRectangle ;_ccb *_aa .PdfRectangle ;_faa int ;_feaa map[string ]fontEntry ;_dd map[string ]textResult ;_agad map[string ]textResult ;_cb int64 ;_fda *Options ;_dccb *_aa .StructTreeRoot ;
_cfa _cg .PdfObject ;_bfaa []*_aa .PdfAnnotation ;};func (_afdd rulingList )tidied (_eccd string )rulingList {_bdeg :=_afdd .removeDuplicates ();_bdeg .log ("\u0075n\u0069\u0071\u0075\u0065\u0073");_gfbfe :=_bdeg .snapToGroups ();if _gfbfe ==nil {return nil ;
};_gfbfe .sort ();if _ccccd {_g .Log .Info ("\u0074\u0069\u0064i\u0065\u0064\u003a\u0020\u0025\u0071\u0020\u0076\u0065\u0063\u0073\u003d\u0025\u0064\u0020\u0075\u006e\u0069\u0071\u0075\u0065\u0073\u003d\u0025\u0064\u0020\u0063\u006f\u0061l\u0065\u0073\u0063\u0065\u0064\u003d\u0025\u0064",_eccd ,len (_afdd ),len (_bdeg ),len (_gfbfe ));
};_gfbfe .log ("\u0063o\u0061\u006c\u0065\u0073\u0063\u0065d");return _gfbfe ;};func (_bcadb *wordBag )removeDuplicates (){if _agfcf {_g .Log .Info ("r\u0065m\u006f\u0076\u0065\u0044\u0075\u0070\u006c\u0069c\u0061\u0074\u0065\u0073: \u0025\u0071",_bcadb .text ());
};for _ ,_afgc :=range _bcadb .depthIndexes (){if len (_bcadb ._cdaf [_afgc ])==0{continue ;};_ebbd :=_bcadb ._cdaf [_afgc ][0];_abaggb :=_cegde *_ebbd ._cacfc ;_eecc :=_ebbd ._abace ;for _ ,_cdgdg :=range _bcadb .depthBand (_eecc ,_eecc +_abaggb ){_gaff :=map[*textWord ]struct{}{};
_adfb :=_bcadb ._cdaf [_cdgdg ];for _ ,_aabd :=range _adfb {if _ ,_gcbcd :=_gaff [_aabd ];_gcbcd {continue ;};for _ ,_gaca :=range _adfb {if _ ,_bfeff :=_gaff [_gaca ];_bfeff {continue ;};if _gaca !=_aabd &&_gaca ._ecegc ==_aabd ._ecegc &&_ea .Abs (_gaca .Llx -_aabd .Llx )< _abaggb &&_ea .Abs (_gaca .Urx -_aabd .Urx )< _abaggb &&_ea .Abs (_gaca .Lly -_aabd .Lly )< _abaggb &&_ea .Abs (_gaca .Ury -_aabd .Ury )< _abaggb {_gaff [_gaca ]=struct{}{};
};};};if len (_gaff )> 0{_addad :=0;for _ ,_fdbf :=range _adfb {if _ ,_dbfc :=_gaff [_fdbf ];!_dbfc {_adfb [_addad ]=_fdbf ;_addad ++;};};_bcadb ._cdaf [_cdgdg ]=_adfb [:len (_adfb )-len (_gaff )];if len (_bcadb ._cdaf [_cdgdg ])==0{delete (_bcadb ._cdaf ,_cdgdg );
};};};};};func (_cfea paraList )computeEBBoxes (){if _fgba {_g .Log .Info ("\u0063o\u006dp\u0075\u0074\u0065\u0045\u0042\u0042\u006f\u0078\u0065\u0073\u003a");};for _ ,_cgbdb :=range _cfea {_cgbdb ._fcagg =_cgbdb .PdfRectangle ;};_gffa :=_cfea .yNeighbours (0);
for _fcga ,_fdfdf :=range _cfea {_ggaea :=_fdfdf ._fcagg ;_afcfe ,_fffd :=-1.0e9,****e9;for _ ,_geddc :=range _gffa [_fdfdf ]{_eefe :=_cfea [_geddc ]._fcagg ;if _eefe .Urx < _ggaea .Llx {_afcfe =_ea .Max (_afcfe ,_eefe .Urx );}else if _ggaea .Urx < _eefe .Llx {_fffd =_ea .Min (_fffd ,_eefe .Llx );
};};for _ecdcg ,_bdfc :=range _cfea {_aefa :=_bdfc ._fcagg ;if _fcga ==_ecdcg ||_aefa .Ury > _ggaea .Lly {continue ;};if _afcfe <=_aefa .Llx &&_aefa .Llx < _ggaea .Llx {_ggaea .Llx =_aefa .Llx ;}else if _aefa .Urx <=_fffd &&_ggaea .Urx < _aefa .Urx {_ggaea .Urx =_aefa .Urx ;
};};if _fgba {_ad .Printf ("\u0025\u0034\u0064\u003a %\u0036\u002e\u0032\u0066\u2192\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0071\u000a",_fcga ,_fdfdf ._fcagg ,_ggaea ,_ecegb (_fdfdf .text (),50));};_fdfdf ._fcagg =_ggaea ;};if _dbddb {for _ ,_degd :=range _cfea {_degd .PdfRectangle =_degd ._fcagg ;
};};};func _dde (_agbf []int ,_dda *TextMarkArray ,_cfad string )(*TextMarkArray ,Box ,error ){_eaf :=Box {};_cdeg :=_agbf [0];_fab :=_agbf [1];_fdcc :=len (_cfad )-len (_add .TrimLeft (_cfad ,"\u0020"));_cbf :=len (_cfad )-len (_add .TrimRight (_cfad ,"\u0020\u000a"));
_cdeg =_cdeg +_fdcc ;_fab =_fab -_cbf ;_dfd ,_bdba :=_dda .RangeOffset (_cdeg ,_fab );if _bdba !=nil {return nil ,_eaf ,_bdba ;};_cbfd ,_ccbb :=_dfd .BBox ();if !_ccbb {return nil ,_eaf ,_ad .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_dfd );
};_eaf =Box {BBox :_cbfd };return _dfd ,_eaf ,nil ;};func (_dggd rulingList )splitSec ()[]rulingList {_f .Slice (_dggd ,func (_debce ,_cbce int )bool {_fcfcg ,_dfebe :=_dggd [_debce ],_dggd [_cbce ];if _fcfcg ._decc !=_dfebe ._decc {return _fcfcg ._decc < _dfebe ._decc ;
};return _fcfcg ._gcda < _dfebe ._gcda ;});_eddcc :=make (map[*ruling ]struct{},len (_dggd ));_fgbe :=func (_cbcdd *ruling )rulingList {_befb :=rulingList {_cbcdd };_eddcc [_cbcdd ]=struct{}{};for _ ,_ccaea :=range _dggd {if _ ,_fcffc :=_eddcc [_ccaea ];
_fcffc {continue ;};for _ ,_deadd :=range _befb {if _ccaea .alignsSec (_deadd ){_befb =append (_befb ,_ccaea );_eddcc [_ccaea ]=struct{}{};break ;};};};return _befb ;};_cbacf :=[]rulingList {_fgbe (_dggd [0])};for _ ,_fgcdb :=range _dggd [1:]{if _ ,_adffb :=_eddcc [_fgcdb ];
_adffb {continue ;};_cbacf =append (_cbacf ,_fgbe (_fgcdb ));};return _cbacf ;};func (_gddefa gridTiling )complete ()bool {for _ ,_ecfe :=range _gddefa ._ebegg {for _ ,_bbdcb :=range _ecfe {if !_bbdcb .complete (){return false ;};};};return true ;};func (_eadff TextTable )getCellInfo (_cfddf TextMark )[][]int {for _cbc ,_cdcg :=range _eadff .Cells {for _feaac :=range _cdcg {_cdcgf :=&_cdcg [_feaac ].Marks ;
if _cdcgf .exists (_cfddf ){return [][]int {{_cbc },{_feaac }};};};};return nil ;};func (_egga *textPara )depth ()float64 {if _egga ._caaa {return -1.0;};if len (_egga ._fdfb )> 0{return _egga ._fdfb [0]._agcc ;};return _egga ._eddc .depth ();};type cachedImage struct{_gaa *_aa .Image ;
_ded _aa .PdfColorspace ;};

// ApplyArea processes the page text only within the specified area `bbox`.
// Each time ApplyArea is called, it updates the result set in `pt`.
// Can be called multiple times in a row with different bounding boxes.
func (_eagb *PageText )ApplyArea (bbox _aa .PdfRectangle ){_bbgb :=make ([]*textMark ,0,len (_eagb ._gee ));for _ ,_dbgd :=range _eagb ._gee {if _afgf (_dbgd .bbox (),bbox ){_bbgb =append (_bbgb ,_dbgd );};};var _gafb paraList ;_aebb :="";_bbcc :=len (_bbgb );
for _gffg :=0;_gffg < 360&&_bbcc > 0;_gffg +=90{_effea :=make ([]*textMark ,0,len (_bbgb )-_bbcc );for _ ,_dffg :=range _bbgb {if _dffg ._afbe ==_gffg {_effea =append (_effea ,_dffg );};};if len (_effea )> 0{if _eagb ._cfcf ._beda ==ExtractionModePlain {_aebb +=_gfddb (_effea ,_eagb ._gff );
}else {_ddgd :=_egba (_effea ,_eagb ._gff ,nil ,nil ,_eagb ._cfcf ._beda ==ExtractionModeLayoutNoBreaks );_gafb =append (_gafb ,_ddgd ...);};_bbcc -=len (_effea );};};if _eagb ._cfcf ._beda ==ExtractionModePlain {_eagb ._cecf =_aebb ;}else {_dbgga :=new (_bb .Buffer );
_gafb .writeText (_dbgga );_eagb ._cecf =_dbgga .String ();_eagb ._eedcb =_gafb .toTextMarks ();_eagb ._gcee =_gafb .tables ();};};

// ExtractText processes and extracts all text data in content streams and returns as a string.
// It takes into account character encodings in the PDF file, which are decoded by
// CharcodeBytesToUnicode.
// Characters that can't be decoded are replaced with MissingCodeRune ('\ufffd' = �).
func (_bbf *Extractor )ExtractText ()(string ,error ){_aadfb ,_ ,_ ,_ggga :=_bbf .ExtractTextWithStats ();return _aadfb ,_ggga ;};type shapesState struct{_defg _dc .Matrix ;_deeb _dc .Matrix ;_agdg []*subpath ;_geee bool ;_cfddfa _dc .Point ;_baea *textObject ;
};const (ExtractionModeLayout =iota ;ExtractionModePlain ;ExtractionModeLayoutNoBreaks ;);func _bgfdb (_fgad []*textLine )map[float64 ][]*textLine {_f .Slice (_fgad ,func (_gecf ,_ebdde int )bool {return _fgad [_gecf ]._agcc < _fgad [_ebdde ]._agcc });
_bebb :=map[float64 ][]*textLine {};for _ ,_cccgc :=range _fgad {_bgad :=_fbfa (_cccgc );_bgad =_ea .Round (_bgad );_bebb [_bgad ]=append (_bebb [_bgad ],_cccgc );};return _bebb ;};func _agdcf (_eeec _dc .Matrix )_dc .Point {_gccbd ,_fged :=_eeec .Translation ();
return _dc .Point {X :_gccbd ,Y :_fged };};func _aee (_afagb *_fc .ContentStreamOperation )(float64 ,error ){if len (_afagb .Params )!=1{_ecba :=_cc .New ("\u0069n\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0070\u0061r\u0061m\u0065t\u0065\u0072\u0020\u0063\u006f\u0075\u006et");
_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u0023\u0071\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020h\u0061\u0076\u0065\u0020\u0025\u0064\u0020i\u006e\u0070\u0075\u0074\u0020\u0070\u0061\u0072\u0061\u006d\u0073,\u0020\u0067\u006f\u0074\u0020\u0025\u0064\u0020\u0025\u002b\u0076",_afagb .Operand ,1,len (_afagb .Params ),_afagb .Params );
return 0.0,_ecba ;};return _cg .GetNumberAsFloat (_afagb .Params [0]);};const (_bdb ="\u0045\u0052R\u004f\u0052\u003a\u0020\u0043\u0061\u006e\u0027\u0074\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065";
_ab ="\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043a\u006e\u0027\u0074 g\u0065\u0074\u0020\u0066\u006f\u006et\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u002c\u0020\u0066\u006fn\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006fu\u006e\u0064";
_ffea ="\u0045\u0052\u0052O\u0052\u003a\u0020\u0043\u0061\u006e\u0027\u0074\u0020\u0067\u0065\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002c\u0020\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065";
_cab ="E\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0066o\u006e\u0074\u0020\u0066ou\u006e\u0064";);func (_gdgd rulingList )isActualGrid ()(rulingList ,bool ){_geegb ,_dcffb :=_gdgd .augmentGrid ();if !(len (_geegb )>=_fagd +1&&len (_dcffb )>=_ecbf +1){if _ccccd {_g .Log .Info ("\u0069s\u0041\u0063t\u0075\u0061\u006c\u0047r\u0069\u0064\u003a \u004e\u006f\u0074\u0020\u0061\u006c\u0069\u0067\u006eed\u002e\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u003c\u0020\u0025d\u0020\u0078 \u0025\u0064",len (_geegb ),len (_dcffb ),_fagd +1,_ecbf +1);
};return nil ,false ;};if _ccccd {_g .Log .Info ("\u0069\u0073\u0041\u0063\u0074\u0075a\u006c\u0047\u0072\u0069\u0064\u003a\u0020\u0025\u0073\u0020\u003a\u0020\u0025t\u0020\u0026\u0020\u0025\u0074\u0020\u2192 \u0025\u0074",_gdgd ,len (_geegb )>=2,len (_dcffb )>=2,len (_geegb )>=2&&len (_dcffb )>=2);
for _ffda ,_dgadf :=range _gdgd {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0076\u000a",_ffda ,_dgadf );};};if _gdff {_decdc ,_bgggg :=_geegb [0],_geegb [len (_geegb )-1];_ebfb ,_gaeg :=_dcffb [0],_dcffb [len (_dcffb )-1];if !(_bbbaff (_decdc ._eced -_ebfb ._decc )&&_bbbaff (_bgggg ._eced -_ebfb ._gcda )&&_bbbaff (_ebfb ._eced -_decdc ._gcda )&&_bbbaff (_gaeg ._eced -_decdc ._decc )){if _ccccd {_g .Log .Info ("\u0069\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069d\u003a\u0020\u0020N\u006f\u0074 \u0061\u006c\u0069\u0067\u006e\u0065d\u002e\n\t\u0076\u0030\u003d\u0025\u0073\u000a\u0009\u0076\u0031\u003d\u0025\u0073\u000a\u0009\u0068\u0030\u003d\u0025\u0073\u000a\u0009\u0068\u0031\u003d\u0025\u0073",_decdc ,_bgggg ,_ebfb ,_gaeg );
};return nil ,false ;};}else {if !_geegb .aligned (){if _cgbfc {_g .Log .Info ("i\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069\u0064\u003a\u0020N\u006f\u0074\u0020\u0061\u006c\u0069\u0067n\u0065\u0064\u0020\u0076\u0065\u0072\u0074\u0073\u002e\u0020%\u0064",len (_geegb ));
};return nil ,false ;};if !_dcffb .aligned (){if _ccccd {_g .Log .Info ("i\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069\u0064\u003a\u0020N\u006f\u0074\u0020\u0061\u006c\u0069\u0067n\u0065\u0064\u0020\u0068\u006f\u0072\u007a\u0073\u002e\u0020%\u0064",len (_dcffb ));
};return nil ,false ;};};_agaa :=append (_geegb ,_dcffb ...);return _agaa ,true ;};func _bded (_gedb *wordBag ,_dbfe *textWord ,_abfc float64 )bool {return _dbfe .Llx < _gedb .Urx +_abfc &&_gedb .Llx -_abfc < _dbfe .Urx ;};func _gaga (_abgb *TextMarkArray ,_aba *string ,_fggg *int ,_fdc string )error {_ecg :=_abgb .Elements ()[0].DirectObject ;
_dcfc ,_bbdc :=_cg .GetString (_ecg );if !_bbdc {return _ad .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_aadf :=_aeg (_abgb );_dcab ,_bbdc :=_cg .GetStringBytes (_ecg );if !_bbdc {return _cg .ErrTypeError ;};_fgef :=_abgb .Elements ()[0].Font ;_egd :=_afag (_dcab ,_fgef );_fdabe :="";_efg :=*_aba ;if len (_efg )> *_fggg {_fdabe =_efg [*_fggg :*_fggg +len (_aadf )];
}else if *_fggg ==len (_fdc )-1&&len (_efg )> *_fggg {_fdabe =_efg [*_fggg :];};_eebb :="";_eacf :=_add .Split (_egd ,"\u0020");_febfe :=_eacf [len (_eacf )-1];if _febfe ==_aadf &&*_fggg ==0{_bgc :=_add .LastIndex (_egd ,_aadf );_eebb =_eeed (_egd ,_bgc ,len (_aadf )+_bgc ,_fdabe );
}else if *_fggg ==len (_fdc )-1&&len (_efg )> *_fggg {_eebb =_add .Replace (_egd ,_aadf ,_efg [*_fggg :],-1);}else {_eebb =_add .Replace (_egd ,_aadf ,_fdabe ,1);};_cgdb (_dcfc ,_eebb ,_fgef );*_fggg +=len (_aadf );return nil ;};func _aecec (_abec _aa .PdfRectangle )*ruling {return &ruling {_cafff :_cggd ,_eced :_abec .Llx ,_decc :_abec .Lly ,_gcda :_abec .Ury };
};func (_dfecc paraList )list ()[]*list {var _dabg []*textLine ;var _egdgf []*textLine ;for _ ,_eacag :=range _dfecc {_cgacb :=_eacag .getListLines ();_dabg =append (_dabg ,_cgacb ...);_egdgf =append (_egdgf ,_eacag ._fdfb ...);};_aaeba :=_bgfdb (_dabg );
_bgfbb :=_eeabd (_egdgf ,_aaeba );return _bgfbb ;};

// Tables returns the tables extracted from the page.
func (_ffcae PageText )Tables ()[]TextTable {if _bffae {_g .Log .Info ("\u0054\u0061\u0062\u006c\u0065\u0073\u003a\u0020\u0025\u0064",len (_ffcae ._gcee ));};return _ffcae ._gcee ;};

// String returns a string describing the current state of the textState stack.
func (_daa *stateStack )String ()string {_gdfb :=[]string {_ad .Sprintf ("\u002d\u002d\u002d\u002d f\u006f\u006e\u0074\u0020\u0073\u0074\u0061\u0063\u006b\u003a\u0020\u0025\u0064",len (*_daa ))};for _eedc ,_acce :=range *_daa {_caeg :="\u003c\u006e\u0069l\u003e";
if _acce !=nil {_caeg =_acce .String ();};_gdfb =append (_gdfb ,_ad .Sprintf ("\u0009\u0025\u0032\u0064\u003a\u0020\u0025\u0073",_eedc ,_caeg ));};return _add .Join (_gdfb ,"\u000a");};func (_ffaae *textTable )growTable (){_bfeaf :=func (_bfag paraList ){_ffaae ._gacfc ++;
for _ffgdf :=0;_ffgdf < _ffaae ._ebdg ;_ffgdf ++{_agbgf :=_bfag [_ffgdf ];_ffaae .put (_ffgdf ,_ffaae ._gacfc -1,_agbgf );};};_facba :=func (_bdggd paraList ){_ffaae ._ebdg ++;for _ggbee :=0;_ggbee < _ffaae ._gacfc ;_ggbee ++{_bddeg :=_bdggd [_ggbee ];
_ffaae .put (_ffaae ._ebdg -1,_ggbee ,_bddeg );};};if _abaa {_ffaae .log ("\u0067r\u006f\u0077\u0054\u0061\u0062\u006ce");};for _ggcaa :=0;;_ggcaa ++{_aggbf :=false ;_cdafc :=_ffaae .getDown ();_fedf :=_ffaae .getRight ();if _abaa {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ggcaa ,_ffaae );
_ad .Printf ("\u0020\u0020 \u0020\u0020\u0020 \u0020\u0064\u006f\u0077\u006e\u003d\u0025\u0073\u000a",_cdafc );_ad .Printf ("\u0020\u0020 \u0020\u0020\u0020 \u0072\u0069\u0067\u0068\u0074\u003d\u0025\u0073\u000a",_fedf );};if _cdafc !=nil &&_fedf !=nil {_eabef :=_cdafc [len (_cdafc )-1];
if !_eabef .taken ()&&_eabef ==_fedf [len (_fedf )-1]{_bfeaf (_cdafc );if _fedf =_ffaae .getRight ();_fedf !=nil {_facba (_fedf );_ffaae .put (_ffaae ._ebdg -1,_ffaae ._gacfc -1,_eabef );};_aggbf =true ;};};if !_aggbf &&_cdafc !=nil {_bfeaf (_cdafc );_aggbf =true ;
};if !_aggbf &&_fedf !=nil {_facba (_fedf );_aggbf =true ;};if !_aggbf {break ;};};};func (_fgfg *textPara )bbox ()_aa .PdfRectangle {return _fgfg .PdfRectangle };func (_fddd rulingList )toGrids ()[]rulingList {if _ccccd {_g .Log .Info ("t\u006f\u0047\u0072\u0069\u0064\u0073\u003a\u0020\u0025\u0073",_fddd );
};_ebfg :=_fddd .intersections ();if _ccccd {_g .Log .Info ("\u0074\u006f\u0047r\u0069\u0064\u0073\u003a \u0076\u0065\u0063\u0073\u003d\u0025\u0064 \u0069\u006e\u0074\u0065\u0072\u0073\u0065\u0063\u0074\u0073\u003d\u0025\u0064\u0020",len (_fddd ),len (_ebfg ));
for _ ,_dbgb :=range _daca (_ebfg ){_ad .Printf ("\u00254\u0064\u003a\u0020\u0025\u002b\u0076\n",_dbgb ,_ebfg [_dbgb ]);};};_bcca :=make (map[int ]intSet ,len (_fddd ));for _cffbd :=range _fddd {_gdagf :=_fddd .connections (_ebfg ,_cffbd );if len (_gdagf )> 0{_bcca [_cffbd ]=_gdagf ;
};};if _ccccd {_g .Log .Info ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u0063\u006fn\u006e\u0065\u0063\u0074s=\u0025\u0064",len (_bcca ));for _ ,_fafbe :=range _daca (_bcca ){_ad .Printf ("\u00254\u0064\u003a\u0020\u0025\u002b\u0076\n",_fafbe ,_bcca [_fafbe ]);
};};_bdcf :=_bffc (len (_fddd ),func (_bdca ,_geedb int )bool {_ebbf ,_deeec :=len (_bcca [_bdca ]),len (_bcca [_geedb ]);if _ebbf !=_deeec {return _ebbf > _deeec ;};return _fddd .comp (_bdca ,_geedb );});if _ccccd {_g .Log .Info ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u006f\u0072d\u0065\u0072\u0069\u006eg=\u0025\u0076",_bdcf );
};_fdeg :=[][]int {{_bdcf [0]}};_afbc :for _ ,_dfeef :=range _bdcf [1:]{for _dcga ,_aaafa :=range _fdeg {for _ ,_efcc :=range _aaafa {if _bcca [_efcc ].has (_dfeef ){_fdeg [_dcga ]=append (_aaafa ,_dfeef );continue _afbc ;};};};_fdeg =append (_fdeg ,[]int {_dfeef });
};if _ccccd {_g .Log .Info ("\u0074o\u0047r\u0069\u0064\u0073\u003a\u0020i\u0067\u0072i\u0064\u0073\u003d\u0025\u0076",_fdeg );};_f .SliceStable (_fdeg ,func (_ggca ,_cgace int )bool {return len (_fdeg [_ggca ])> len (_fdeg [_cgace ])});for _ ,_gfdde :=range _fdeg {_f .Slice (_gfdde ,func (_bcff ,_gbeefb int )bool {return _fddd .comp (_gfdde [_bcff ],_gfdde [_gbeefb ])});
};_fdegc :=make ([]rulingList ,len (_fdeg ));for _gadf ,_aadgb :=range _fdeg {_gfda :=make (rulingList ,len (_aadgb ));for _agbg ,_acbfa :=range _aadgb {_gfda [_agbg ]=_fddd [_acbfa ];};_fdegc [_gadf ]=_gfda ;};if _ccccd {_g .Log .Info ("\u0074o\u0047r\u0069\u0064\u0073\u003a\u0020g\u0072\u0069d\u0073\u003d\u0025\u002b\u0076",_fdegc );
};var _gagb []rulingList ;for _ ,_egaa :=range _fdegc {if _degc ,_eeefa :=_egaa .isActualGrid ();_eeefa {_egaa =_degc ;_egaa =_egaa .snapToGroups ();_gagb =append (_gagb ,_egaa );};};if _ccccd {_bcaec ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u0061\u0063t\u0075\u0061\u006c\u0047ri\u0064\u0073",_gagb );
_g .Log .Info ("\u0074\u006f\u0047\u0072\u0069\u0064\u0073\u003a\u0020\u0067\u0072\u0069\u0064\u0073\u003d%\u0064 \u0061\u0063\u0074\u0075\u0061\u006c\u0047\u0072\u0069\u0064\u0073\u003d\u0025\u0064",len (_fdegc ),len (_gagb ));};return _gagb ;};func (_cfde intSet )del (_agfaa int ){delete (_cfde ,_agfaa )};
func _agbaa (_cddg []pathSection ){if _fabad < 0.0{return ;};if _ccccd {_g .Log .Info ("\u0067\u0072\u0061\u006e\u0075\u006c\u0061\u0072\u0069\u007a\u0065\u003a\u0020\u0025\u0064 \u0073u\u0062\u0070\u0061\u0074\u0068\u0020\u0073\u0065\u0063\u0074\u0069\u006f\u006e\u0073",len (_cddg ));
};for _deacc ,_bcadc :=range _cddg {for _dggfc ,_eagd :=range _bcadc ._cdbe {for _bfefg ,_cfegb :=range _eagd ._eae {_eagd ._eae [_bfefg ]=_dc .Point {X :_cfdfb (_cfegb .X ),Y :_cfdfb (_cfegb .Y )};if _ccccd {_degf :=_eagd ._eae [_bfefg ];if !_cfaf (_cfegb ,_degf ){_dbab :=_dc .Point {X :_degf .X -_cfegb .X ,Y :_degf .Y -_cfegb .Y };
_ad .Printf ("\u0025\u0034d \u002d\u0020\u00254\u0064\u0020\u002d\u0020%4d\u003a %\u002e\u0032\u0066\u0020\u2192\u0020\u0025.2\u0066\u0020\u0028\u0025\u0067\u0029\u000a",_deacc ,_dggfc ,_bfefg ,_cfegb ,_degf ,_dbab );};};};};};};func (_dccf *textMark )bbox ()_aa .PdfRectangle {return _dccf .PdfRectangle };
func (_edcgc *wordBag )makeRemovals ()map[int ]map[*textWord ]struct{}{_eccg :=make (map[int ]map[*textWord ]struct{},len (_edcgc ._cdaf ));for _dgdgg :=range _edcgc ._cdaf {_eccg [_dgdgg ]=make (map[*textWord ]struct{});};return _eccg ;};

// String returns a human readable description of `path`.
func (_agef *subpath )String ()string {_dcaee :=_agef ._eae ;_ceba :=len (_dcaee );if _ceba <=5{return _ad .Sprintf ("\u0025d\u003a\u0020\u0025\u0036\u002e\u0032f",_ceba ,_dcaee );};return _ad .Sprintf ("\u0025d\u003a\u0020\u0025\u0036.\u0032\u0066\u0020\u0025\u0036.\u0032f\u0020.\u002e\u002e\u0020\u0025\u0036\u002e\u0032f",_ceba ,_dcaee [0],_dcaee [1],_dcaee [_ceba -1]);
};func _efdea (_bffbc ,_efaa int )int {if _bffbc > _efaa {return _bffbc ;};return _efaa ;};func _cdec (_dcdf ,_addfb bounded )float64 {_fgae :=_ecac (_dcdf ,_addfb );if !_bbage (_fgae ){return _fgae ;};return _abga (_dcdf ,_addfb );};var _edfcd string ="\u0028\u003f\u0069\u0029\u005e\u0028\u004d\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0043\u0028?\u003a\u0044\u007cM\u0029\u007c\u0044\u003f\u0043{\u0030\u002c\u0033\u007d\u0029\u0028\u0058\u0028\u003f\u003a\u004c\u007c\u0043\u0029\u007cL\u003f\u0058\u007b\u0030\u002c\u0033}\u0029\u0028\u0049\u0028\u003f\u003a\u0056\u007c\u0058\u0029\u007c\u0056\u003f\u0049\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u005c\u0029\u007c\u005c\u002e\u0029\u007c\u005e\u005c\u0028\u0028\u004d\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0043\u0028\u003f\u003aD\u007cM\u0029\u007c\u0044\u003f\u0043\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0058\u0028?\u003a\u004c\u007c\u0043\u0029\u007c\u004c?\u0058\u007b0\u002c\u0033\u007d\u0029(\u0049\u0028\u003f\u003a\u0056|\u0058\u0029\u007c\u0056\u003f\u0049\u007b\u0030\u002c\u0033\u007d\u0029\u005c\u0029";
func (_edfc *textObject )getFont (_cagb string )(*_aa .PdfFont ,error ){if _edfc ._addg ._feaa !=nil {_dgg ,_ddge :=_edfc .getFontDict (_cagb );if _ddge !=nil {_g .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0067\u0065\u0074\u0046\u006f\u006e\u0074:\u0020n\u0061m\u0065=\u0025\u0073\u002c\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0073",_cagb ,_ddge .Error ());
return nil ,_ddge ;};_edfc ._addg ._cb ++;_dbaa ,_edgda :=_edfc ._addg ._feaa [_dgg .String ()];if _edgda {_dbaa ._egdc =_edfc ._addg ._cb ;return _dbaa ._dcgd ,nil ;};};_cbffd ,_acecbb :=_edfc .getFontDict (_cagb );if _acecbb !=nil {return nil ,_acecbb ;
};_cfff ,_acecbb :=_edfc .getFontDirect (_cagb );if _acecbb !=nil {return nil ,_acecbb ;};if _edfc ._addg ._feaa !=nil {_gedd :=fontEntry {_cfff ,_edfc ._addg ._cb };if len (_edfc ._addg ._feaa )>=_edgdf {var _bbbae []string ;for _fcdd :=range _edfc ._addg ._feaa {_bbbae =append (_bbbae ,_fcdd );
};_f .Slice (_bbbae ,func (_cgad ,_deabg int )bool {return _edfc ._addg ._feaa [_bbbae [_cgad ]]._egdc < _edfc ._addg ._feaa [_bbbae [_deabg ]]._egdc ;});delete (_edfc ._addg ._feaa ,_bbbae [0]);};_edfc ._addg ._feaa [_cbffd .String ()]=_gedd ;};return _cfff ,nil ;
};func _bbgg (_acbfe *textLine ,_bbdb []*textLine )float64 {var _gfde float64 =-1;for _ ,_febb :=range _bbdb {if _febb ._agcc > _acbfe ._agcc {if _ea .Round (_febb .Llx )>=_ea .Round (_acbfe .Llx ){_gfde =_febb ._agcc ;}else {break ;};};};return _gfde ;
};func (_edbc *textObject )getFontDirect (_fcgdd string )(*_aa .PdfFont ,error ){_gebg ,_acag :=_edbc .getFontDict (_fcgdd );if _acag !=nil {return nil ,_acag ;};_ggab ,_acag :=_aa .NewPdfFontFromPdfObject (_gebg );if _acag !=nil {_g .Log .Debug ("\u0067\u0065\u0074\u0046\u006f\u006e\u0074\u0044\u0069\u0072\u0065\u0063\u0074\u003a\u0020\u004e\u0065\u0077Pd\u0066F\u006f\u006e\u0074\u0046\u0072\u006f\u006d\u0050\u0064\u0066\u004f\u0062j\u0065\u0063\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u006e\u0061\u006d\u0065\u003d%\u0023\u0071\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fcgdd ,_acag );
};return _ggab ,_acag ;};func (_gged *Editor )getMatches (_dbe string ,_gcd []int )(map[int ]Match ,map[int ][]*TextMarkArray ,error ){_gfa :=map[int ]Match {};_dfed :=map[int ][]*TextMarkArray {};for _ ,_fac :=range _gcd {_bg ,_beg :=_gged ._fedd .GetPage (_fac );
if _beg !=nil {return nil ,nil ,_beg ;};_fgf ,_beg :=New (_bg );if _beg !=nil {return nil ,nil ,_beg ;};_cdbf ,_ ,_ ,_beg :=_fgf .ExtractPageText ();if _beg !=nil {return nil ,nil ,_beg ;};_cbd :=_cdbf .Text ();_dbee ,_beg :=_cfce (_dbe ,_cbd );if _beg !=nil {return nil ,nil ,_beg ;
};if len (_dbee )==0{_g .Log .Info ("\u004e\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0066\u006f\u0075\u006e\u0064\u0020f\u006fr\u0020\u0025\u0073\u0020\u006f\u006e\u0020\u0070\u0061\u0067\u0065\u0020\u0025\u0064",_dbe ,_fac );};_ffa :=_cdbf .Marks ();
_gad :=[]Box {};for _ ,_fgcd :=range _dbee {_ ,_dbgf ,_bfad :=_dde (_fgcd ,_ffa ,_dbe );if _bfad !=nil {return nil ,nil ,_bfad ;};_gad =append (_gad ,_dbgf );};_bbd :=Match {Pattern :_dbe ,Indexes :_dbee ,Locations :_gad };_gfa [_fac ]=_bbd ;};return _gfa ,_dfed ,nil ;
};func _dbebb (_gecfd _cg .PdfObject ,_fagf _ed .Color )(_ff .Image ,error ){_deccd ,_dded :=_cg .GetStream (_gecfd );if !_dded {return nil ,nil ;};_cggf ,_gdcb :=_aa .NewXObjectImageFromStream (_deccd );if _gdcb !=nil {return nil ,_gdcb ;};_eeaad ,_gdcb :=_cggf .ToImage ();
if _gdcb !=nil {return nil ,_gdcb ;};return _cdafd (_eeaad ,_fagf ),nil ;};func (_dfea *textObject )moveLP (_dcff ,_dbdd float64 ){_dfea ._bbbb .Concat (_dc .NewMatrix (1,0,0,1,_dcff ,_dbdd ));_dfea ._ddaf =_dfea ._bbbb ;};

// String returns a string describing `ma`.
func (_gabaeg TextMarkArray )String ()string {_afde :=len (_gabaeg ._edgd );if _afde ==0{return "\u0045\u004d\u0050T\u0059";};_cabe :=_gabaeg ._edgd [0];_cffc :=_gabaeg ._edgd [_afde -1];return _ad .Sprintf ("\u007b\u0054\u0045\u0058\u0054\u004d\u0041\u0052K\u0041\u0052\u0052AY\u003a\u0020\u0025\u0064\u0020\u0065l\u0065\u006d\u0065\u006e\u0074\u0073\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025s\u000a\u0009\u0020\u006c\u0061\u0073\u0074\u003d%\u0073\u007d",_afde ,_cabe ,_cffc );
};

// String returns a human readable description of `ss`.
func (_gdg *shapesState )String ()string {return _ad .Sprintf ("\u007b\u0025\u0064\u0020su\u0062\u0070\u0061\u0074\u0068\u0073\u0020\u0066\u0072\u0065\u0073\u0068\u003d\u0025t\u007d",len (_gdg ._agdg ),_gdg ._geee );};func (_eabb *textTable )emptyCompositeRow (_egefe int )bool {for _effefa :=0;
_effefa < _eabb ._ebdg ;_effefa ++{if _cecgf ,_ccbc :=_eabb ._efcbeb [_cggdf (_effefa ,_egefe )];_ccbc {if len (_cecgf .paraList )> 0{return false ;};};};return true ;};func (_bgaa paraList )toTextMarks ()[]TextMark {_acad :=0;var _gdgf []TextMark ;for _gcaa ,_dgbe :=range _bgaa {if _dgbe ._caaa {continue ;
};_cdaea :=_dgbe .toTextMarks (&_acad );_gdgf =append (_gdgf ,_cdaea ...);if _gcaa !=len (_bgaa )-1{if _agaec (_dgbe ,_bgaa [_gcaa +1]){_gdgf =_abgab (_gdgf ,&_acad ,"\u0020");}else {_gdgf =_abgab (_gdgf ,&_acad ,"\u000a");_gdgf =_abgab (_gdgf ,&_acad ,"\u000a");
};};};_gdgf =_abgab (_gdgf ,&_acad ,"\u000a");_gdgf =_abgab (_gdgf ,&_acad ,"\u000a");return _gdgf ;};func _cgfa (_cffa _dc .Point )*subpath {return &subpath {_eae :[]_dc .Point {_cffa }}};func (_bfef *shapesState )drawRectangle (_fbbb ,_egdb ,_dabd ,_gbdb float64 ){if _fdee {_aaff :=_bfef .devicePoint (_fbbb ,_egdb );
_afdg :=_bfef .devicePoint (_fbbb +_dabd ,_egdb +_gbdb );_edbg :=_aa .PdfRectangle {Llx :_aaff .X ,Lly :_aaff .Y ,Urx :_afdg .X ,Ury :_afdg .Y };_g .Log .Info ("d\u0072a\u0077\u0052\u0065\u0063\u0074\u0061\u006e\u0067l\u0065\u003a\u0020\u00256.\u0032\u0066",_edbg );
};_bfef .newSubPath ();_bfef .moveTo (_fbbb ,_egdb );_bfef .lineTo (_fbbb +_dabd ,_egdb );_bfef .lineTo (_fbbb +_dabd ,_egdb +_gbdb );_bfef .lineTo (_fbbb ,_egdb +_gbdb );_bfef .closePath ();};func (_edfca *textLine )pullWord (_ffbc *wordBag ,_bedae *textWord ,_geda int ){_edfca .appendWord (_bedae );
_ffbc .removeWord (_bedae ,_geda );};func _eaff (_ggegf []*textWord ,_ffed *textWord )[]*textWord {for _dacba ,_fadaf :=range _ggegf {if _fadaf ==_ffed {return _dedg (_ggegf ,_dacba );};};_g .Log .Error ("\u0072\u0065\u006d\u006f\u0076e\u0057\u006f\u0072\u0064\u003a\u0020\u0077\u006f\u0072\u0064\u0073\u0020\u0064o\u0065\u0073\u006e\u0027\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0077\u006f\u0072\u0064\u003d\u0025\u0073",_ffed );
return nil ;};func _cf (_dcg int )bool {return (_dcg &1)!=0};func (_fcfcb paraList )inTile (_gddeg gridTile )paraList {var _gcbde paraList ;for _ ,_beae :=range _fcfcb {if _gddeg .contains (_beae .PdfRectangle ){_gcbde =append (_gcbde ,_beae );};};if _bffae {_ad .Printf ("\u0020 \u0020\u0069\u006e\u0054i\u006c\u0065\u003a\u0020\u0020%\u0073 \u0069n\u0073\u0069\u0064\u0065\u003d\u0025\u0064\n",_gddeg ,len (_gcbde ));
for _abffe ,_dgbg :=range _gcbde {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_abffe ,_dgbg );};_ad .Println ("");};return _gcbde ;};func _gfcg (_gadg *Extractor ,_gceg *_aa .PdfPageResources ,_bfgc _fc .GraphicsState ,_ecc *textState ,_bagf *stateStack )*textObject {return &textObject {_addg :_gadg ,_gfce :_gceg ,_bffb :_bfgc ,_bbfab :_bagf ,_abf :_ecc ,_ddaf :_dc .IdentityMatrix (),_bbbb :_dc .IdentityMatrix ()};
};func (_eafed paraList )yNeighbours (_efbfab float64 )map[*textPara ][]int {_cgbda :=make ([]event ,2*len (_eafed ));if _efbfab ==0{for _gbeg ,_ggabc :=range _eafed {_cgbda [2*_gbeg ]=event {_ggabc .Lly ,true ,_gbeg };_cgbda [2*_gbeg +1]=event {_ggabc .Ury ,false ,_gbeg };
};}else {for _abeef ,_abggg :=range _eafed {_cgbda [2*_abeef ]=event {_abggg .Lly -_efbfab *_abggg .fontsize (),true ,_abeef };_cgbda [2*_abeef +1]=event {_abggg .Ury +_efbfab *_abggg .fontsize (),false ,_abeef };};};return _eafed .eventNeighbours (_cgbda );
};type stateStack []*textState ;func _ecegb (_dabga string ,_aaddf int )string {if len (_dabga )< _aaddf {return _dabga ;};return _dabga [:_aaddf ];};

// String returns a string describing `tm`.
func (_gfad TextMark )String ()string {_abbf :=_gfad .BBox ;var _efea string ;if _gfad .Font !=nil {_efea =_gfad .Font .String ();if len (_efea )> 50{_efea =_efea [:50]+"\u002e\u002e\u002e";};};var _abba string ;if _gfad .Meta {_abba ="\u0020\u002a\u004d\u002a";
};return _ad .Sprintf ("\u007b\u0054\u0065\u0078t\u004d\u0061\u0072\u006b\u003a\u0020\u0025\u0064\u0020%\u0071\u003d\u0025\u0030\u0032\u0078\u0020\u0028\u0025\u0036\u002e\u0032\u0066\u002c\u0020\u0025\u0036\u002e2\u0066\u0029\u0020\u0028\u00256\u002e\u0032\u0066\u002c\u0020\u0025\u0036\u002e\u0032\u0066\u0029\u0020\u0025\u0073\u0025\u0073\u007d",_gfad .Offset ,_gfad .Text ,[]rune (_gfad .Text ),_abbf .Llx ,_abbf .Lly ,_abbf .Urx ,_abbf .Ury ,_efea ,_abba );
};

// Box represents the bounding box of a given textMark on pdf page.
// This might be used for different kinds of high lighting after doing the search
type Box struct{BBox _aa .PdfRectangle ;};func (_gccf *textObject )getFontDict (_cdag string )(_efga _cg .PdfObject ,_gacb error ){_dfgf :=_gccf ._gfce ;if _dfgf ==nil {_g .Log .Debug ("g\u0065\u0074\u0046\u006f\u006e\u0074D\u0069\u0063\u0074\u002e\u0020\u004eo\u0020\u0072\u0065\u0073\u006f\u0075\u0072c\u0065\u0073\u002e\u0020\u006e\u0061\u006d\u0065\u003d\u0025#\u0071",_cdag );
return nil ,nil ;};_efga ,_bfade :=_dfgf .GetFontByName (_cg .PdfObjectName (_cdag ));if !_bfade {_g .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0067\u0065t\u0046\u006f\u006et\u0044\u0069\u0063\u0074\u003a\u0020\u0046\u006f\u006et \u006e\u006f\u0074 \u0066\u006fu\u006e\u0064\u003a\u0020\u006e\u0061m\u0065\u003d%\u0023\u0071",_cdag );
return nil ,_cc .New ("f\u006f\u006e\u0074\u0020no\u0074 \u0069\u006e\u0020\u0072\u0065s\u006f\u0075\u0072\u0063\u0065\u0073");};return _efga ,nil ;};func _edbce (_bgdd []*wordBag )[]*wordBag {if len (_bgdd )<=1{return _bgdd ;};if _adcf {_g .Log .Info ("\u006d\u0065\u0072\u0067\u0065\u0057\u006f\u0072\u0064B\u0061\u0067\u0073\u003a");
};_f .Slice (_bgdd ,func (_dead ,_bdg int )bool {_dagd ,_cffac :=_bgdd [_dead ],_bgdd [_bdg ];_dafb :=_dagd .Width ()*_dagd .Height ();_fceg :=_cffac .Width ()*_cffac .Height ();if _dafb !=_fceg {return _dafb > _fceg ;};if _dagd .Height ()!=_cffac .Height (){return _dagd .Height ()> _cffac .Height ();
};return _dead < _bdg ;});var _egeb []*wordBag ;_fceb :=make (intSet );for _eefg :=0;_eefg < len (_bgdd );_eefg ++{if _fceb .has (_eefg ){continue ;};_ggggb :=_bgdd [_eefg ];for _fbcc :=_eefg +1;_fbcc < len (_bgdd );_fbcc ++{if _fceb .has (_eefg ){continue ;
};_ggfe :=_bgdd [_fbcc ];_gdee :=_ggggb .PdfRectangle ;_gdee .Llx -=_ggggb ._abcf ;if _agaea (_gdee ,_ggfe .PdfRectangle ){_ggggb .absorb (_ggfe );_fceb .add (_fbcc );};};_egeb =append (_egeb ,_ggggb );};if len (_bgdd )!=len (_egeb )+len (_fceb ){_g .Log .Error ("\u006d\u0065\u0072ge\u0057\u006f\u0072\u0064\u0042\u0061\u0067\u0073\u003a \u0025d\u2192%\u0064 \u0061\u0062\u0073\u006f\u0072\u0062\u0065\u0064\u003d\u0025\u0064",len (_bgdd ),len (_egeb ),len (_fceb ));
};return _egeb ;};func _ecac (_faea ,_becd bounded )float64 {return _faea .bbox ().Llx -_becd .bbox ().Llx };func _dbeef (_egbaf ,_degb _dc .Point ,_fbeae _ed .Color )(*ruling ,bool ){_aeecf :=lineRuling {_cfdad :_egbaf ,_bddd :_degb ,_fbfg :_cgaa (_egbaf ,_degb ),Color :_fbeae };
if _aeecf ._fbfg ==_bccgg {return nil ,false ;};return _aeecf .asRuling ();};

// String returns a string describing `pt`.
func (_fbd PageText )String ()string {_gffd :=_ad .Sprintf ("P\u0061\u0067\u0065\u0054ex\u0074:\u0020\u0025\u0064\u0020\u0065l\u0065\u006d\u0065\u006e\u0074\u0073",len (_fbd ._gee ));_bega :=[]string {"\u002d"+_gffd };for _ ,_edcb :=range _fbd ._gee {_bega =append (_bega ,_edcb .String ());
};_bega =append (_bega ,"\u002b"+_gffd );return _add .Join (_bega ,"\u000a");};func (_fffdd lineRuling )yMean ()float64 {return 0.5*(_fffdd ._cfdad .Y +_fffdd ._bddd .Y )};func _gfddb (_egcae []*textMark ,_fbced _aa .PdfRectangle )string {_g .Log .Trace ("\u006d\u0061\u006b\u0065\u0053i\u006d\u0070\u006c\u0065\u0054\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020e\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0020\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066",len (_egcae ),_fbced );
_cdcd :="";if len (_egcae )==0{return _cdcd ;};_cada :=_eafee (_egcae ,_fbced ,true );if len (_cada )==0{return _cdcd ;};_dbfa :=0.0;_cadc :=true ;_egcc :="";for _ ,_dbae :=range _cada {_gcdfd :=_dbae ._cacfc ;if _gcdfd > _dfeb {_gcdfd =_dfeb ;};if (_dbae ._abace -_dbfa > _dfff *_gcdfd &&_dbfa !=0.0)||(_dbfa -_dbae ._abace > _gcdfd *10){_fgebe :=_fea ([]rune (_egcc ));
_egcc =_fgebe ._cge ;_egcc +="\u000a";_cdcd +=_egcc ;_egcc ="";}else {if !_cadc {_egcc +="\u0020";};};_egcc +=_dbae ._ecegc ;_cadc =false ;_dbfa =_dbae ._abace ;};if _egcc !=""{_aedd :=_fea ([]rune (_egcc ));_egcc =_aedd ._cge ;_egcc +="\u000a";_cdcd +=_egcc ;
};return _cdcd ;};func _bgfbc (_ecdd bounded )float64 {return -_ecdd .bbox ().Lly };

// ExtractPageImages returns the image contents of the page extractor, including data
// and position, size information for each image.
// A set of options to control page image extraction can be passed in. The options
// parameter can be nil for the default options. By default, inline stencil masks
// are not extracted.
func (_edc *Extractor )ExtractPageImages (options *ImageExtractOptions )(*PageImages ,error ){_ffg :=&imageExtractContext {_cag :options };_gfc :=_ffg .extractContentStreamImages (_edc ._gcc ,_edc ._cgf );if _gfc !=nil {return nil ,_gfc ;};return &PageImages {Images :_ffg ._fbb },nil ;
};func (_gaccf paraList )extractTables (_cbfdc []gridTiling )paraList {if _bffae {_g .Log .Debug ("\u0065\u0078\u0074r\u0061\u0063\u0074\u0054\u0061\u0062\u006c\u0065\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_gaccf ));
};if len (_gaccf )< _cabaf {return _gaccf ;};_dbggbd :=_gaccf .findTables (_cbfdc );if _bffae {_g .Log .Info ("c\u006f\u006d\u0062\u0069\u006e\u0065d\u0020\u0074\u0061\u0062\u006c\u0065s\u0020\u0025\u0064\u0020\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d",len (_dbggbd ));
for _aeca ,_afbeg :=range _dbggbd {_afbeg .log (_ad .Sprintf ("c\u006f\u006d\u0062\u0069\u006e\u0065\u0064\u0020\u0025\u0064",_aeca ));};};return _gaccf .applyTables (_dbggbd );};type textLine struct{_aa .PdfRectangle ;_agcc float64 ;_dbcg []*textWord ;
_cadd float64 ;};func (_cgfb *textLine )toTextMarks (_dagfg *int )[]TextMark {var _bcccb []TextMark ;for _ ,_aggfd :=range _cgfb ._dbcg {if _aggfd ._ccdbc {_bcccb =_abgab (_bcccb ,_dagfg ,"\u0020");};_gbgf :=_aggfd .toTextMarks (_dagfg );_bcccb =append (_bcccb ,_gbgf ...);
};return _bcccb ;};func (_cea *PageText )getText ()string {_gfeb :="";_effef :=len (_cea ._gee );for _gddea :=0;_gddea < 360&&_effef > 0;_gddea +=90{_agga :=make ([]*textMark ,0,len (_cea ._gee )-_effef );for _ ,_bgdc :=range _cea ._gee {if _bgdc ._afbe ==_gddea {_agga =append (_agga ,_bgdc );
};};if len (_agga )> 0{_gfeb +=_gfddb (_agga ,_cea ._gff );_effef -=len (_agga );};};return _gfeb ;};

// TextMarkArray is a collection of TextMarks.
type TextMarkArray struct{_edgd []TextMark };func (_aabg *shapesState )clearPath (){_aabg ._agdg =nil ;_aabg ._geee =false ;if _fdee {_g .Log .Info ("\u0043\u004c\u0045A\u0052\u003a\u0020\u0073\u0073\u003d\u0025\u0073",_aabg );};};func (_cfcb *wordBag )blocked (_acfd *textWord )bool {if _acfd .Urx < _cfcb .Llx {_efeee :=_bagef (_acfd .PdfRectangle );
_fcaf :=_aecec (_cfcb .PdfRectangle );if _cfcb ._aada .blocks (_efeee ,_fcaf ){if _gdbc {_g .Log .Info ("\u0062\u006c\u006f\u0063ke\u0064\u0020\u2190\u0078\u003a\u0020\u0025\u0073\u0020\u0025\u0073",_acfd ,_cfcb );};return true ;};}else if _cfcb .Urx < _acfd .Llx {_facbg :=_bagef (_cfcb .PdfRectangle );
_cee :=_aecec (_acfd .PdfRectangle );if _cfcb ._aada .blocks (_facbg ,_cee ){if _gdbc {_g .Log .Info ("b\u006co\u0063\u006b\u0065\u0064\u0020\u0078\u2192\u0020:\u0020\u0025\u0073\u0020%s",_acfd ,_cfcb );};return true ;};};if _acfd .Ury < _cfcb .Lly {_dffga :=_eebd (_acfd .PdfRectangle );
_aeaf :=_ecegf (_cfcb .PdfRectangle );if _cfcb ._bcdc .blocks (_dffga ,_aeaf ){if _gdbc {_g .Log .Info ("\u0062\u006c\u006f\u0063ke\u0064\u0020\u2190\u0079\u003a\u0020\u0025\u0073\u0020\u0025\u0073",_acfd ,_cfcb );};return true ;};}else if _cfcb .Ury < _acfd .Lly {_gbfc :=_eebd (_cfcb .PdfRectangle );
_bbecf :=_ecegf (_acfd .PdfRectangle );if _cfcb ._bcdc .blocks (_gbfc ,_bbecf ){if _gdbc {_g .Log .Info ("b\u006co\u0063\u006b\u0065\u0064\u0020\u0079\u2192\u0020:\u0020\u0025\u0073\u0020%s",_acfd ,_cfcb );};return true ;};};return false ;};

// TableInfo gets table information of the textmark `tm`.
func (_dcbe *TextMark )TableInfo ()(*TextTable ,[][]int ){if !_dcbe ._ffcc {return nil ,nil ;};_ccdc :=_dcbe ._aadfbe ;_efcb :=_ccdc .getCellInfo (*_dcbe );return _ccdc ,_efcb ;};func (_ebcf *wordBag )empty (_ffbdg int )bool {_ ,_ffbdd :=_ebcf ._cdaf [_ffbdg ];
return !_ffbdd };func (_ccca *shapesState )establishSubpath ()*subpath {_cedb ,_bbfbc :=_ccca .lastpointEstablished ();if !_bbfbc {_ccca ._agdg =append (_ccca ._agdg ,_cgfa (_cedb ));};if len (_ccca ._agdg )==0{return nil ;};_ccca ._geee =false ;return _ccca ._agdg [len (_ccca ._agdg )-1];
};func _afae (_cfac func (*wordBag ,*textWord ,float64 )bool ,_gebfc float64 )func (*wordBag ,*textWord )bool {return func (_dfbd *wordBag ,_aaee *textWord )bool {return _cfac (_dfbd ,_aaee ,_gebfc )};};

// Replace takes a pattern and replaces all the texts that much the pattern with `replacement`.
func (_gfb *Editor )Replace (pattern string ,replacement string ,pages []int )error {_dfa :=map[int ]Match {};for _ ,_bgf :=range pages {_dff ,_fcg :=_gfb ._fedd .GetPage (_bgf );if _fcg !=nil {return _fcg ;};_dac ,_fcg :=New (_dff );if _fcg !=nil {return _fcg ;
};_dbed ,_ ,_ ,_fcg :=_dac .ExtractPageText ();if _fcg !=nil {return _fcg ;};_gae :="";_edec :=_dbed .Text ();_fcgc ,_fcg :=_cfce (pattern ,_edec );if _fcg !=nil {return _fcg ;};_cda :=_dbed .Marks ();_dcad :=[]Box {};for _ ,_gde :=range _fcgc {_gfg ,_gdd ,_fegc :=_dde (_gde ,_cda ,pattern );
if _fegc !=nil {return _fegc ;};_cbgf :=_febf (_gfg );_dcad =append (_dcad ,_gdd );replacement ,_fegc =_bbba (_cbgf ,replacement ,pattern );if _fegc !=nil {return _fegc ;};};_gae =_dbed .GetContentStreamOps ().String ();_acbf :=Match {Pattern :pattern ,Indexes :_fcgc ,Locations :_dcad };
_dff .SetContentStreams ([]string {_gae },_cg .NewFlateEncoder ());_dfa [_bgf ]=_acbf ;};return nil ;};func (_gcaec *textLine )bbox ()_aa .PdfRectangle {return _gcaec .PdfRectangle };func _fafc (_bbdf float64 ,_eaba int )int {if _eaba ==0{_eaba =1;};_dcdca :=float64 (_eaba );
return int (_ea .Round (_bbdf /_dcdca )*_dcdca );};func _dbfd (_gbcff []TextMark ,_gcdb *int )[]TextMark {_dceda :=_gbcff [len (_gbcff )-1];_daaa :=[]rune (_dceda .Text );if len (_daaa )==1{_gbcff =_gbcff [:len (_gbcff )-1];_dgdeg :=_gbcff [len (_gbcff )-1];
*_gcdb =_dgdeg .Offset +len (_dgdeg .Text );}else {_gdba :=_gecd (_dceda .Text );*_gcdb +=len (_gdba )-len (_dceda .Text );_dceda .Text =_gdba ;};return _gbcff ;};func (_cgba paraList )xNeighbours (_ccfdc float64 )map[*textPara ][]int {_eafa :=make ([]event ,2*len (_cgba ));
if _ccfdc ==0{for _agde ,_gebfcc :=range _cgba {_eafa [2*_agde ]=event {_gebfcc .Llx ,true ,_agde };_eafa [2*_agde +1]=event {_gebfcc .Urx ,false ,_agde };};}else {for _befce ,_dccbd :=range _cgba {_eafa [2*_befce ]=event {_dccbd .Llx -_ccfdc *_dccbd .fontsize (),true ,_befce };
_eafa [2*_befce +1]=event {_dccbd .Urx +_ccfdc *_dccbd .fontsize (),false ,_befce };};};return _cgba .eventNeighbours (_eafa );};func _bgaag (_cegb map[int ][]float64 )string {_facbe :=_geea (_cegb );_dbbba :=make ([]string ,len (_cegb ));for _cebf ,_ebfa :=range _facbe {_dbbba [_cebf ]=_ad .Sprintf ("\u0025\u0064\u003a\u0020\u0025\u002e\u0032\u0066",_ebfa ,_cegb [_ebfa ]);
};return _ad .Sprintf ("\u007b\u0025\u0073\u007d",_add .Join (_dbbba ,"\u002c\u0020"));};func (_gaaeg lineRuling )xMean ()float64 {return 0.5*(_gaaeg ._cfdad .X +_gaaeg ._bddd .X )};func (_cccg *textObject )setTextMatrix (_edde []float64 ){if len (_edde )!=6{_g .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u006c\u0065\u006e\u0028\u0066\u0029\u0020\u0021\u003d\u0020\u0036\u0020\u0028\u0025\u0064\u0029",len (_edde ));
return ;};_gca ,_ffee ,_cdd ,_cbdg ,_dgd ,_eabg :=_edde [0],_edde [1],_edde [2],_edde [3],_edde [4],_edde [5];_cccg ._ddaf =_dc .NewMatrix (_gca ,_ffee ,_cdd ,_cbdg ,_dgd ,_eabg );_cccg ._bbbb =_cccg ._ddaf ;};func _ag (_bf int )bool {return (_bf &1)==0};
func (_gaeb *subpath )last ()_dc .Point {return _gaeb ._eae [len (_gaeb ._eae )-1]};type bounded interface{bbox ()_aa .PdfRectangle };var _dabgd =_bcc .MustCompile ("\u005e\u005c\u0073\u002a\u0028\u005c\u0064\u002b\u005c\u002e\u003f|\u005b\u0049\u0069\u0076\u005d\u002b\u0029\u005c\u0073\u002a\\\u0029\u003f\u0024");


// PageTextOptions holds various options available in extraction process.
type PageTextOptions struct{_cfdd bool ;_beda ExtractionMode ;};func (_aaag *textPara )isAtom ()*textTable {_eefea :=_aaag ;_adegeb :=_aaag ._caec ;_ebbbg :=_aaag ._fgfd ;if _adegeb .taken ()||_ebbbg .taken (){return nil ;};_dgeac :=_adegeb ._fgfd ;if _dgeac .taken ()||_dgeac !=_ebbbg ._caec {return nil ;
};return _afeg (_eefea ,_adegeb ,_ebbbg ,_dgeac );};func _ggbag (_aebaf ,_dbeb *textPara )bool {return _bbgc (_aebaf ._fcagg ,_dbeb ._fcagg )};func _bbbaff (_cdbff float64 )bool {return _ea .Abs (_cdbff )< _cafa };func (_fcfgbe *textTable )computeBbox ()_aa .PdfRectangle {var _beadg _aa .PdfRectangle ;
_aacf :=false ;for _dfcdf :=0;_dfcdf < _fcfgbe ._gacfc ;_dfcdf ++{for _dbagf :=0;_dbagf < _fcfgbe ._ebdg ;_dbagf ++{_gedge :=_fcfgbe .get (_dbagf ,_dfcdf );if _gedge ==nil {continue ;};if !_aacf {_beadg =_gedge .PdfRectangle ;_aacf =true ;}else {_beadg =_bdda (_beadg ,_gedge .PdfRectangle );
};};};return _beadg ;};func (_bdgg paraList )sortTopoOrder (){_bdfed :=_bdgg .topoOrder ();_bdgg .reorder (_bdfed )};

// PageImages represents extracted images on a PDF page with spatial information:
// display position and size.
type PageImages struct{Images []ImageMark ;};

// String returns a description of `tm`.
func (_ccfg *textMark )String ()string {return _ad .Sprintf ("\u0025\u002e\u0032f \u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\u0022\u0025\u0073\u0022",_ccfg .PdfRectangle ,_ccfg ._fadfd ,_ccfg ._gacc );};func (_fff *shapesState )lastpointEstablished ()(_dc .Point ,bool ){if _fff ._geee {return _fff ._cfddfa ,false ;
};_egfa :=len (_fff ._agdg );if _egfa > 0&&_fff ._agdg [_egfa -1]._bebg {return _fff ._agdg [_egfa -1].last (),false ;};return _dc .Point {},true ;};func (_ccdca *textLine )endsInHyphen ()bool {_gaee :=_ccdca ._dbcg [len (_ccdca ._dbcg )-1];_adefa :=_gaee ._ecegc ;
_dafg ,_ebgb :=_bc .DecodeLastRuneInString (_adefa );if _ebgb <=0||!_c .Is (_c .Hyphen ,_dafg ){return false ;};if _gaee ._ccdbc &&_babc (_adefa ){return true ;};return _babc (_ccdca .text ());};type textPara struct{_aa .PdfRectangle ;_fcagg _aa .PdfRectangle ;
_fdfb []*textLine ;_eddc *textTable ;_fdge bool ;_caaa bool ;_bfaad *textPara ;_caec *textPara ;_bgde *textPara ;_fgfd *textPara ;_gcaee []list ;};func (_cefg *textPara )fontsize ()float64 {return _cefg ._fdfb [0]._cadd };func _fg (_fad string ,_ecd bool ,_fd bool )BidiText {_cd :="\u006c\u0074\u0072";
if _fd {_cd ="\u0074\u0074\u0062";}else if !_ecd {_cd ="\u0072\u0074\u006c";};return BidiText {_cge :_fad ,_df :_cd };};const (_cgcg =true ;_aabga =true ;_bfdg =true ;_dbddb =false ;_ddfc =true ;_gcbe =true ;_bcbb =true ;_gbfg =true ;_gdff =false ;);func (_eagg gridTile )contains (_cddeb _aa .PdfRectangle )bool {if _eagg .numBorders ()< 3{return false ;
};if _eagg ._geeegg &&_cddeb .Llx < _eagg .Llx -_aaeeb {return false ;};if _eagg ._ccge &&_cddeb .Urx > _eagg .Urx +_aaeeb {return false ;};if _eagg ._fcfc &&_cddeb .Lly < _eagg .Lly -_aaeeb {return false ;};if _eagg ._aaaff &&_cddeb .Ury > _eagg .Ury +_aaeeb {return false ;
};return true ;};

// RangeOffset returns the TextMarks in `ma` that overlap text[start:end] in the extracted text.
// These are tm: `start` <= tm.Offset + len(tm.Text) && tm.Offset < `end` where
// `start` and `end` are offsets in the extracted text.
// NOTE: TextMarks can contain multiple characters. e.g. "ffi" for the ﬃ ligature so the first and
// last elements of the returned TextMarkArray may only partially overlap text[start:end].
func (_agfa *TextMarkArray )RangeOffset (start ,end int )(*TextMarkArray ,error ){if _agfa ==nil {return nil ,_cc .New ("\u006da\u003d\u003d\u006e\u0069\u006c");};if end < start {return nil ,_ad .Errorf ("\u0065\u006e\u0064\u0020\u003c\u0020\u0073\u0074\u0061\u0072\u0074\u002e\u0020\u0052\u0061n\u0067\u0065\u004f\u0066\u0066\u0073\u0065\u0074\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064\u002e\u0020\u0073\u0074\u0061\u0072t=\u0025\u0064\u0020\u0065\u006e\u0064\u003d\u0025\u0064\u0020",start ,end );
};_abed :=len (_agfa ._edgd );if _abed ==0{return _agfa ,nil ;};if start < _agfa ._edgd [0].Offset {start =_agfa ._edgd [0].Offset ;};if end > _agfa ._edgd [_abed -1].Offset +1{end =_agfa ._edgd [_abed -1].Offset +1;};_ebfd :=_f .Search (_abed ,func (_ecggb int )bool {return _agfa ._edgd [_ecggb ].Offset +len (_agfa ._edgd [_ecggb ].Text )-1>=start });
if !(0<=_ebfd &&_ebfd < _abed ){_adgca :=_ad .Errorf ("\u004f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u002e\u0020\u0073\u0074\u0061\u0072\u0074\u003d%\u0064\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020\u006c\u0065\u006e\u003d\u0025\u0064\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025\u0076\u000a\u0009 \u006c\u0061\u0073\u0074\u003d%\u0076",start ,_ebfd ,_abed ,_agfa ._edgd [0],_agfa ._edgd [_abed -1]);
return nil ,_adgca ;};_dgfg :=_f .Search (_abed ,func (_gaafd int )bool {return _agfa ._edgd [_gaafd ].Offset > end -1});if !(0<=_dgfg &&_dgfg < _abed ){_gacde :=_ad .Errorf ("\u004f\u0075\u0074\u0020\u006f\u0066\u0020r\u0061\u006e\u0067e\u002e\u0020\u0065n\u0064\u003d%\u0064\u0020\u0069\u0045\u006e\u0064=\u0025d \u006c\u0065\u006e\u003d\u0025\u0064\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025\u0076\u000a\u0009\u0020\u006c\u0061\u0073\u0074\u003d\u0025\u0076",end ,_dgfg ,_abed ,_agfa ._edgd [0],_agfa ._edgd [_abed -1]);
return nil ,_gacde ;};if _dgfg <=_ebfd {return nil ,_ad .Errorf ("\u0069\u0045\u006e\u0064\u0020\u003c=\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003a\u0020\u0073\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020\u0065\u006ed\u003d\u0025\u0064\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020i\u0045n\u0064\u003d\u0025\u0064",start ,end ,_ebfd ,_dgfg );
};return &TextMarkArray {_edgd :_agfa ._edgd [_ebfd :_dgfg ]},nil ;};func (_efbd *textPara )toTextMarks (_fbeb *int )[]TextMark {if _efbd ._eddc ==nil {return _efbd .toCellTextMarks (_fbeb );};var _dgdgc []TextMark ;for _fgfba :=0;_fgfba < _efbd ._eddc ._gacfc ;
_fgfba ++{for _ggea :=0;_ggea < _efbd ._eddc ._ebdg ;_ggea ++{_bcbc :=_efbd ._eddc .get (_ggea ,_fgfba );if _bcbc ==nil {_dgdgc =_abgab (_dgdgc ,_fbeb ,"\u0009");}else {_ddgga :=_bcbc .toCellTextMarks (_fbeb );_dgdgc =append (_dgdgc ,_ddgga ...);};_dgdgc =_abgab (_dgdgc ,_fbeb ,"\u0020");
};if _fgfba < _efbd ._eddc ._gacfc -1{_dgdgc =_abgab (_dgdgc ,_fbeb ,"\u000a");};};_ccfgc :=_efbd ._eddc ;if _ccfgc .isExportable (){_cbee :=_ccfgc .toTextTable ();_dgdgc =_bbdba (_dgdgc ,&_cbee );};return _dgdgc ;};func (_dedca *subpath )makeRectRuling (_bgcad _ed .Color )(*ruling ,bool ){if _ebcfc {_g .Log .Info ("\u006d\u0061\u006beR\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0070\u0061\u0074\u0068\u003d\u0025\u0076",_dedca );
};_gafdg :=_dedca ._eae [:4];_cfeee :=make (map[int ]rulingKind ,len (_gafdg ));for _abgd ,_bdbb :=range _gafdg {_baag :=_dedca ._eae [(_abgd +1)%4];_cfeee [_abgd ]=_cfgg (_bdbb ,_baag );if _ebcfc {_ad .Printf ("\u0025\u0034\u0064: \u0025\u0073\u0020\u003d\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u002d\u0020\u0025\u0036\u002e\u0032\u0066",_abgd ,_cfeee [_abgd ],_bdbb ,_baag );
};};if _ebcfc {_ad .Printf ("\u0020\u0020\u0020\u006b\u0069\u006e\u0064\u0073\u003d\u0025\u002b\u0076\u000a",_cfeee );};var _cbec ,_beee []int ;for _cabb ,_bddg :=range _cfeee {switch _bddg {case _feed :_beee =append (_beee ,_cabb );case _cggd :_cbec =append (_cbec ,_cabb );
};};if _ebcfc {_ad .Printf ("\u0020\u0020 \u0068\u006f\u0072z\u0073\u003d\u0025\u0064\u0020\u0025\u002b\u0076\u000a",len (_beee ),_beee );_ad .Printf ("\u0020\u0020 \u0076\u0065\u0072t\u0073\u003d\u0025\u0064\u0020\u0025\u002b\u0076\u000a",len (_cbec ),_cbec );
};_fgaa :=(len (_beee )==2&&len (_cbec )==2)||(len (_beee )==2&&len (_cbec )==0&&_bgdg (_gafdg [_beee [0]],_gafdg [_beee [1]]))||(len (_cbec )==2&&len (_beee )==0&&_dfbda (_gafdg [_cbec [0]],_gafdg [_cbec [1]]));if _ebcfc {_ad .Printf (" \u0020\u0020\u0068\u006f\u0072\u007as\u003d\u0025\u0064\u0020\u0076\u0065\u0072\u0074\u0073=\u0025\u0064\u0020o\u006b=\u0025\u0074\u000a",len (_beee ),len (_cbec ),_fgaa );
};if !_fgaa {if _ebcfc {_g .Log .Error ("\u0021!\u006d\u0061\u006b\u0065R\u0065\u0063\u0074\u0052\u0075l\u0069n\u0067:\u0020\u0070\u0061\u0074\u0068\u003d\u0025v",_dedca );_ad .Printf (" \u0020\u0020\u0068\u006f\u0072\u007as\u003d\u0025\u0064\u0020\u0076\u0065\u0072\u0074\u0073=\u0025\u0064\u0020o\u006b=\u0025\u0074\u000a",len (_beee ),len (_cbec ),_fgaa );
};return &ruling {},false ;};if len (_cbec )==0{for _afdge ,_egbe :=range _cfeee {if _egbe !=_feed {_cbec =append (_cbec ,_afdge );};};};if len (_beee )==0{for _edfcac ,_cdbb :=range _cfeee {if _cdbb !=_cggd {_beee =append (_beee ,_edfcac );};};};if _ebcfc {_g .Log .Info ("\u006da\u006b\u0065R\u0065\u0063\u0074\u0052u\u006c\u0069\u006eg\u003a\u0020\u0068\u006f\u0072\u007a\u0073\u003d\u0025d \u0076\u0065\u0072t\u0073\u003d%\u0064\u0020\u0070\u006f\u0069\u006et\u0073\u003d%\u0064\u000a"+"\u0009\u0020\u0068o\u0072\u007a\u0073\u003d\u0025\u002b\u0076\u000a"+"\u0009\u0020\u0076e\u0072\u0074\u0073\u003d\u0025\u002b\u0076\u000a"+"\t\u0070\u006f\u0069\u006e\u0074\u0073\u003d\u0025\u002b\u0076",len (_beee ),len (_cbec ),len (_gafdg ),_beee ,_cbec ,_gafdg );
};var _aacd ,_edaa ,_aaafc ,_cffcg _dc .Point ;if _gafdg [_beee [0]].Y > _gafdg [_beee [1]].Y {_aaafc ,_cffcg =_gafdg [_beee [0]],_gafdg [_beee [1]];}else {_aaafc ,_cffcg =_gafdg [_beee [1]],_gafdg [_beee [0]];};if _gafdg [_cbec [0]].X > _gafdg [_cbec [1]].X {_aacd ,_edaa =_gafdg [_cbec [0]],_gafdg [_cbec [1]];
}else {_aacd ,_edaa =_gafdg [_cbec [1]],_gafdg [_cbec [0]];};_bbffe :=_aa .PdfRectangle {Llx :_aacd .X ,Urx :_edaa .X ,Lly :_cffcg .Y ,Ury :_aaafc .Y };if _bbffe .Llx > _bbffe .Urx {_bbffe .Llx ,_bbffe .Urx =_bbffe .Urx ,_bbffe .Llx ;};if _bbffe .Lly > _bbffe .Ury {_bbffe .Lly ,_bbffe .Ury =_bbffe .Ury ,_bbffe .Lly ;
};_eecdc :=rectRuling {PdfRectangle :_bbffe ,_abbd :_bggba (_bbffe ),Color :_bgcad };if _eecdc ._abbd ==_bccgg {if _ebcfc {_g .Log .Error ("\u006da\u006b\u0065\u0052\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006eg\u003a\u0020\u006b\u0069\u006e\u0064\u003d\u006e\u0069\u006c");
};return nil ,false ;};_caeb ,_fdea :=_eecdc .asRuling ();if !_fdea {if _ebcfc {_g .Log .Error ("\u006da\u006b\u0065\u0052\u0065c\u0074\u0052\u0075\u006c\u0069n\u0067:\u0020!\u0069\u0073\u0052\u0075\u006c\u0069\u006eg");};return nil ,false ;};if _ccccd {_ad .Printf ("\u0020\u0020\u0020\u0072\u003d\u0025\u0073\u000a",_caeb .String ());
};return _caeb ,true ;};func _fea (_ga []rune )BidiText {_ge :=-1;_adf :=false ;_dfg :=true ;_be :=len (_ga );_eb :=make ([]string ,_be );_dcf :=make ([]string ,_be );if _be ==0||_adf {return _fg (string (_ga ),_dfg ,_adf );};_fdb :=0;for _cde ,_gf :=range _ga {_eb [_cde ]=string (_gf );
_aad :="\u004c";if _gf <=0x00ff{_aad =_ae [_gf ];}else if 0x0590<=_gf &&_gf <=0x05f4{_aad ="\u0052";}else if 0x0600<=_gf &&_gf <=0x06ff{_bd :=_gf &0xff;if int (_bd )>=len (_dae ){_g .Log .Debug ("\u0042\u0069\u0064\u0069\u003a\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0055n\u0069c\u006f\u0064\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020"+string (_gf ));
};_aad =_dae [_gf &0xff];}else if (0x0700<=_gf &&_gf <=0x08ac)||(0xfb50<=_gf &&_gf <=0xfdff)||(0xfe70<=_gf &&_gf <=0xfeff){_aad ="\u0041\u004c";};if _aad =="\u0052"||_aad =="\u0041\u004c"||_aad =="\u0041\u004e"{_fdb ++;};_dcf [_cde ]=_aad ;};if _fdb ==0{_dfg =true ;
return _fg (string (_ga ),_dfg ,false );};if _ge ==-1{if float64 (_fdb )/float64 (_be )< 0.3&&_be > 4{_dfg =true ;_ge =0;}else {_dfg =false ;_ge =1;};};var _eda []int ;for range _ga {_eda =append (_eda ,_ge );};_ggb :="\u004c";if _cf (_ge ){_ggb ="\u0052";
};_ggg :=_ggb ;_ebe :=_ggg ;_aaa :=_ggg ;for _ade :=range _ga {if _dcf [_ade ]=="\u004e\u0053\u004d"{_dcf [_ade ]=_aaa ;}else {_aaa =_dcf [_ade ];};};_aaa =_ggg ;var _bfab string ;for _gb :=range _ga {_bfab =_dcf [_gb ];if _bfab =="\u0045\u004e"{if _aaa =="\u0041\u004c"{_dcf [_gb ]="\u0041\u004e";
}else {_dcf [_gb ]="\u0045\u004e";};}else if _bfab =="\u0052"||_bfab =="\u004c"||_bfab =="\u0041\u004c"{_aaa =_bfab ;};};for _acc :=range _ga {_gbd :=_dcf [_acc ];if _gbd =="\u0041\u004c"{_dcf [_acc ]="\u0052";};};for _ce :=1;_ce < (len (_ga )-1);_ce ++{if _dcf [_ce ]=="\u0045\u0053"&&_dcf [_ce -1]=="\u0045\u004e"&&_dcf [_ce +1]=="\u0045\u004e"{_dcf [_ce ]="\u0045\u004e";
};if _dcf [_ce ]=="\u0043\u0053"&&(_dcf [_ce -1]=="\u0045\u004e"||_dcf [_ce -1]=="\u0041\u004e")&&_dcf [_ce +1]==_dcf [_ce -1]{_dcf [_ce ]=_dcf [_ce -1];};};for _aga :=range _ga {if _dcf [_aga ]=="\u0045\u004e"{for _bec :=_aga -1;_bec >=0;_bec --{if _dcf [_bec ]!="\u0045\u0054"{break ;
};_dcf [_bec ]="\u0045\u004e";};for _dcc :=_aga +1;_dcc < _be ;_dcc ++{if _dcf [_dcc ]!="\u0045\u0054"{break ;};_dcf [_dcc ]="\u0045\u004e";};};};for _ead :=range _ga {_gbf :=_dcf [_ead ];if _gbf =="\u0057\u0053"||_gbf =="\u0045\u0053"||_gbf =="\u0045\u0054"||_gbf =="\u0043\u0053"{_dcf [_ead ]="\u004f\u004e";
};};_aaa ="\u0073\u006f\u0072";for _adef :=range _ga {_gbc :=_dcf [_adef ];if _gbc =="\u0045\u004e"{if _aaa =="\u004c"{_dcf [_adef ]="\u004c";}else {_dcf [_adef ]="\u0045\u004e";};}else if _gbc =="\u0052"||_gbc =="\u004c"{_aaa =_gbc ;};};for _deb :=0;_deb < len (_ga );
_deb ++{if _dcf [_deb ]=="\u004f\u004e"{_cdf :=_gg (_dcf ,_deb +1,"\u004f\u004e");_gdf :=_ebe ;if _deb > 0{_gdf =_dcf [_deb -1];};_ca :=_ebe ;if _cdf +1< _be {_ca =_dcf [_cdf +1];};if _gdf !="\u004c"{_gdf ="\u0052";};if _ca !="\u004c"{_ca ="\u0052";};if _gdf ==_ca {_db (_dcf ,_deb ,_cdf ,_gdf );
};_deb =_cdf -1;};};for _ee :=range _ga {if _dcf [_ee ]=="\u004f\u004e"{_dcf [_ee ]=_ggb ;};};for _bca :=range _ga {_ccf :=_dcf [_bca ];if _ag (_eda [_bca ]){if _ccf =="\u0052"{_eda [_bca ]++;}else if _ccf =="\u0041\u004e"||_ccf =="\u0045\u004e"{_eda [_bca ]+=2;
};}else if _ccf =="\u004c"||_ccf =="\u0041\u004e"||_ccf =="\u0045\u004e"{_eda [_bca ]++;};};_gge :=-1;_ef :=99;var _daed int ;for _fdba :=0;_fdba < len (_eda );_fdba ++{_daed =_eda [_fdba ];if _gge < _daed {_gge =_daed ;};if _ef > _daed &&_cf (_daed ){_ef =_daed ;
};};for _gab :=_gge ;_gab >=_ef ;_gab --{_eadd :=-1;for _dbf :=0;_dbf < len (_eda );_dbf ++{if _eda [_dbf ]< _gab {if _eadd >=0{_dfc (_eb ,_eadd ,_dbf );_eadd =-1;};}else if _eadd < 0{_eadd =_dbf ;};};if _eadd >=0{_dfc (_eb ,_eadd ,len (_eda ));};};for _eg :=0;
_eg < len (_eb );_eg ++{_cfb :=_eb [_eg ];if _cfb =="\u003c"||_cfb =="\u003e"{_eb [_eg ]="";};};return _fg (_add .Join (_eb ,""),_dfg ,false );};

// String returns a description of `k`.
func (_bbcg rulingKind )String ()string {_caeec ,_aeffb :=_acceg [_bbcg ];if !_aeffb {return _ad .Sprintf ("\u004e\u006ft\u0020\u0061\u0020r\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0025\u0064",_bbcg );};return _caeec ;};func _eafee (_caed []*textMark ,_dacc _aa .PdfRectangle ,_babe bool )[]*textWord {var _gcgc []*textWord ;
var _eggd *textWord ;if _ffdc {_g .Log .Info ("\u006d\u0061\u006beT\u0065\u0078\u0074\u0057\u006f\u0072\u0064\u0073\u003a\u0020\u0025\u0064\u0020\u006d\u0061\u0072\u006b\u0073",len (_caed ));};_gfef :=func (){if _eggd !=nil {_cafg :=_eggd .computeText ();
if !_acfcg (_cafg ){_eggd ._ecegc =_cafg ;_gcgc =append (_gcgc ,_eggd );if _ffdc {_g .Log .Info ("\u0061\u0064\u0064Ne\u0077\u0057\u006f\u0072\u0064\u003a\u0020\u0025\u0064\u003a\u0020\u0077\u006f\u0072\u0064\u003d\u0025\u0073",len (_gcgc )-1,_eggd .String ());
for _gdeada ,_ebcfe :=range _eggd ._fcacg {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gdeada ,_ebcfe .String ());};};};_eggd =nil ;};};for _ ,_ddga :=range _caed {if _bfdg &&_eggd !=nil &&len (_eggd ._fcacg )> 0{_caaf :=_eggd ._fcacg [len (_eggd ._fcacg )-1];
_dbbdc ,_bgdcd :=_eecgf (_ddga ._gacc );_fedda ,_dgage :=_eecgf (_caaf ._gacc );if _bgdcd &&!_dgage &&_caaf .inDiacriticArea (_ddga ){_eggd .addDiacritic (_dbbdc );continue ;};if _dgage &&!_bgdcd &&_ddga .inDiacriticArea (_caaf ){_eggd ._fcacg =_eggd ._fcacg [:len (_eggd ._fcacg )-1];
_eggd .appendMark (_ddga ,_dacc );_eggd .addDiacritic (_fedda );continue ;};};_ccada :=_acfcg (_ddga ._gacc );if _ccada {_gfef ();continue ;};if _eggd ==nil &&!_ccada {_eggd =_deecf ([]*textMark {_ddga },_dacc );continue ;};_adbf :=_eggd ._cacfc ;_adcec :=_ea .Abs (_fddg (_dacc ,_ddga )-_eggd ._abace )/_adbf ;
_cgcd :=_fbg (_ddga ,_eggd )/_adbf ;_ceafda :=_eggd .Width ()+_ddga .Width ();if _babe &&_ddga ._ecef .Angle ()!=0.0{_dcbc :=_ea .Abs (_ceafda *_ea .Tan (_ddga ._ecef .Angle ())/_adbf );if _adcec > _dcbc {_adcec -=_dcbc ;}else {_adcec =0.0;};};if _cgcd >=_gggb ||!(-_aebbf <=_cgcd &&_adcec <=_edabd ){_gfef ();
_eggd =_deecf ([]*textMark {_ddga },_dacc );continue ;};_eggd .appendMark (_ddga ,_dacc );};_gfef ();return _gcgc ;};const _edgdf =10;func (_dadc *textObject )moveTextSetLeading (_dgeg ,_befcb float64 ){_dadc ._abf ._dcfb =-_befcb ;_dadc .moveLP (_dgeg ,_befcb );
};func (_eabf *textObject )setFont (_gcae string ,_agbc float64 )error {if _eabf ==nil {return nil ;};_eabf ._abf ._dced =_agbc ;_gade ,_cfdf :=_eabf .getFont (_gcae );if _cfdf !=nil {return _cfdf ;};_eabf ._abf ._ebca =_gade ;return nil ;};type textTable struct{_aa .PdfRectangle ;
_ebdg ,_gacfc int ;_ccfad bool ;_fdgeg map[uint64 ]*textPara ;_efcbeb map[uint64 ]compositeCell ;};func (_bgdcf *ruling )intersects (_eecbf *ruling )bool {_eafb :=(_bgdcf ._cafff ==_cggd &&_eecbf ._cafff ==_feed )||(_eecbf ._cafff ==_cggd &&_bgdcf ._cafff ==_feed );
_eeea :=func (_cebad ,_dbcfd *ruling )bool {return _cebad ._decc -_adag <=_dbcfd ._eced &&_dbcfd ._eced <=_cebad ._gcda +_adag ;};_fcbg :=_eeea (_bgdcf ,_eecbf );_cbcfd :=_eeea (_eecbf ,_bgdcf );if _ccccd {_ad .Printf ("\u0020\u0020\u0020\u0020\u0069\u006e\u0074\u0065\u0072\u0073\u0065\u0063\u0074\u0073\u003a\u0020\u0020\u006fr\u0074\u0068\u006f\u0067\u006f\u006e\u0061l\u003d\u0025\u0074\u0020\u006f\u0031\u003d\u0025\u0074\u0020\u006f2\u003d\u0025\u0074\u0020\u2192\u0020\u0025\u0074\u000a"+"\u0020\u0020\u0020 \u0020\u0020\u0020\u0076\u003d\u0025\u0073\u000a"+" \u0020\u0020\u0020\u0020\u0020\u0077\u003d\u0025\u0073\u000a",_eafb ,_fcbg ,_cbcfd ,_eafb &&_fcbg &&_cbcfd ,_bgdcf ,_eecbf );
};return _eafb &&_fcbg &&_cbcfd ;};func (_debf *subpath )removeDuplicates (){if len (_debf ._eae )==0{return ;};_agcge :=[]_dc .Point {_debf ._eae [0]};for _ ,_eadb :=range _debf ._eae [1:]{if !_cfaf (_eadb ,_agcge [len (_agcge )-1]){_agcge =append (_agcge ,_eadb );
};};_debf ._eae =_agcge ;};type imageExtractContext struct{_fbb []ImageMark ;_dcae int ;_dbg int ;_dcb int ;_gafab map[*_cg .PdfObjectStream ]*cachedImage ;_cag *ImageExtractOptions ;_befd bool ;};var _feagc string ="\u005e\u005b\u0061\u002d\u007a\u0041\u002dZ\u005d\u0028\u005c)\u007c\u005c\u002e)\u007c\u005e[\u005c\u0064\u005d\u002b\u0028\u005c)\u007c\\.\u0029\u007c\u005e\u005c\u0028\u005b\u0061\u002d\u007a\u0041\u002d\u005a\u005d\u005c\u0029\u007c\u005e\u005c\u0028\u005b\u005c\u0064\u005d\u002b\u005c\u0029";


// ImageExtractOptions contains options for controlling image extraction from
// PDF pages.
type ImageExtractOptions struct{IncludeInlineStencilMasks bool ;};func (_aecf rulingList )vertsHorzs ()(rulingList ,rulingList ){var _fgedd ,_edfge rulingList ;for _ ,_afad :=range _aecf {switch _afad ._cafff {case _cggd :_fgedd =append (_fgedd ,_afad );
case _feed :_edfge =append (_edfge ,_afad );};};return _fgedd ,_edfge ;};func (_fcad *textObject )getFillColor ()_ed .Color {return _ggeda (_fcad ._bffb .ColorspaceNonStroking ,_fcad ._bffb .ColorNonStroking );};func (_fcca *textPara )toCellTextMarks (_defcg *int )[]TextMark {var _cegdd []TextMark ;
for _gfcc ,_eabff :=range _fcca ._fdfb {_bbgcf :=_eabff .toTextMarks (_defcg );_abgba :=_cgcg &&_eabff .endsInHyphen ()&&_gfcc !=len (_fcca ._fdfb )-1;if _abgba {_bbgcf =_dbfd (_bbgcf ,_defcg );};_cegdd =append (_cegdd ,_bbgcf ...);if !(_abgba ||_gfcc ==len (_fcca ._fdfb )-1){_cegdd =_abgab (_cegdd ,_defcg ,_gbdbg (_eabff ._agcc ,_fcca ._fdfb [_gfcc +1]._agcc ));
};};return _cegdd ;};func _aaabg (_egccf map[float64 ]map[float64 ]gridTile )[]float64 {_bbbaa :=make ([]float64 ,0,len (_egccf ));for _acd :=range _egccf {_bbbaa =append (_bbbaa ,_acd );};_f .Float64s (_bbbaa );_eaae :=len (_bbbaa );for _efgag :=0;_efgag < _eaae /2;
_efgag ++{_bbbaa [_efgag ],_bbbaa [_eaae -1-_efgag ]=_bbbaa [_eaae -1-_efgag ],_bbbaa [_efgag ];};return _bbbaa ;};func (_cebgf *wordBag )maxDepth ()float64 {return _cebgf ._dagf -_cebgf .Lly };func _dfbda (_aeegc ,_acead _dc .Point )bool {_ecbab :=_ea .Abs (_aeegc .X -_acead .X );
_fceac :=_ea .Abs (_aeegc .Y -_acead .Y );return _bede (_ecbab ,_fceac );};func (_acegb *textTable )compositeRowCorridors ()map[int ][]float64 {_efgf :=make (map[int ][]float64 ,_acegb ._gacfc );if _bffae {_g .Log .Info ("c\u006f\u006d\u0070\u006f\u0073\u0069t\u0065\u0052\u006f\u0077\u0043\u006f\u0072\u0072\u0069d\u006f\u0072\u0073:\u0020h\u003d\u0025\u0064",_acegb ._gacfc );
};for _adce :=1;_adce < _acegb ._gacfc ;_adce ++{var _bgbe []compositeCell ;for _bbeea :=0;_bbeea < _acegb ._ebdg ;_bbeea ++{if _fefa ,_fecf :=_acegb ._efcbeb [_cggdf (_bbeea ,_adce )];_fecf {_bgbe =append (_bgbe ,_fefa );};};if len (_bgbe )==0{continue ;
};_cedf :=_gfec (_bgbe );_efgf [_adce ]=_cedf ;if _bffae {_ad .Printf ("\u0020\u0020\u0020\u0025\u0032\u0064\u003a\u0020\u00256\u002e\u0032\u0066\u000a",_adce ,_cedf );};};return _efgf ;};func _adfg (_bdef *wordBag ,_cdde int )*textLine {_cbfff :=_bdef .firstWord (_cdde );
_ffcg :=textLine {PdfRectangle :_cbfff .PdfRectangle ,_cadd :_cbfff ._cacfc ,_agcc :_cbfff ._abace };_ffcg .pullWord (_bdef ,_cbfff ,_cdde );return &_ffcg ;};

// GetContentStreamOps returns the contentStreamOps field of `pt`.
func (_beba *PageText )GetContentStreamOps ()*_fc .ContentStreamOperations {return _beba ._dacb };func _fabb (_bfeg _dc .Point )_dc .Matrix {return _dc .TranslationMatrix (_bfeg .X ,_bfeg .Y )};func (_aged *wordBag )removeWord (_abfe *textWord ,_gede int ){_aggf :=_aged ._cdaf [_gede ];
_aggf =_eaff (_aggf ,_abfe );if len (_aggf )==0{delete (_aged ._cdaf ,_gede );}else {_aged ._cdaf [_gede ]=_aggf ;};};func (_fadf *TextMarkArray )exists (_accef TextMark )bool {for _ ,_dgfe :=range _fadf .Elements (){if _e .DeepEqual (_accef .DirectObject ,_dgfe .DirectObject )&&_e .DeepEqual (_accef .BBox ,_dgfe .BBox )&&_dgfe .Text ==_accef .Text {return true ;
};};return false ;};func (_gded *PageText )getParagraphs ()paraList {var _cad rulingList ;if _bcbb {_ggff :=_cbgd (_gded ._acecb );_cad =append (_cad ,_ggff ...);};if _gbfg {_daab :=_adafd (_gded ._fedc );_cad =append (_cad ,_daab ...);};_cad ,_ecgb :=_cad .toTilings ();
var _agff paraList ;_cadg :=len (_gded ._gee );for _bcbe :=0;_bcbe < 360&&_cadg > 0;_bcbe +=90{_acaa :=make ([]*textMark ,0,len (_gded ._gee )-_cadg );for _ ,_abae :=range _gded ._gee {if _abae ._afbe ==_bcbe {_acaa =append (_acaa ,_abae );};};if len (_acaa )> 0{_dbgeea :=_egba (_acaa ,_gded ._gff ,_cad ,_ecgb ,_gded ._cfcf ._beda ==ExtractionModeLayoutNoBreaks );
_agff =append (_agff ,_dbgeea ...);_cadg -=len (_acaa );};};return _agff ;};func _bede (_eebbb ,_ddfb float64 )bool {return _eebbb /_ea .Max (_fafb ,_ddfb )< _agefe };func (_bdaf *textTable )reduceTiling (_dbgeed gridTiling ,_dddfa float64 )*textTable {_caac :=make ([]int ,0,_bdaf ._gacfc );
_cdggg :=make ([]int ,0,_bdaf ._ebdg );_aaeg :=_dbgeed ._ddfcb ;_fegdg :=_dbgeed ._bced ;for _aadaa :=0;_aadaa < _bdaf ._gacfc ;_aadaa ++{_bgcde :=_aadaa > 0&&_ea .Abs (_fegdg [_aadaa -1]-_fegdg [_aadaa ])< _dddfa &&_bdaf .emptyCompositeRow (_aadaa );if !_bgcde {_caac =append (_caac ,_aadaa );
};};for _cecag :=0;_cecag < _bdaf ._ebdg ;_cecag ++{_aefb :=_cecag < _bdaf ._ebdg -1&&_ea .Abs (_aaeg [_cecag +1]-_aaeg [_cecag ])< _dddfa &&_bdaf .emptyCompositeColumn (_cecag );if !_aefb {_cdggg =append (_cdggg ,_cecag );};};if len (_caac )==_bdaf ._gacfc &&len (_cdggg )==_bdaf ._ebdg {return _bdaf ;
};_cceef :=textTable {_ccfad :_bdaf ._ccfad ,_ebdg :len (_cdggg ),_gacfc :len (_caac ),_efcbeb :make (map[uint64 ]compositeCell ,len (_cdggg )*len (_caac ))};if _bffae {_g .Log .Info ("\u0072\u0065\u0064\u0075c\u0065\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0025d\u0078%\u0064\u0020\u002d\u003e\u0020\u0025\u0064x\u0025\u0064",_bdaf ._ebdg ,_bdaf ._gacfc ,len (_cdggg ),len (_caac ));
_g .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0043\u006f\u006c\u0073\u003a\u0020\u0025\u002b\u0076",_cdggg );_g .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0052\u006f\u0077\u0073\u003a\u0020\u0025\u002b\u0076",_caac );};for _baecf ,_gaddf :=range _caac {for _fdfa ,_afbdb :=range _cdggg {_bbggg ,_cedfa :=_bdaf .getComposite (_afbdb ,_gaddf );
if len (_bbggg )==0{continue ;};if _bffae {_ad .Printf ("\u0020 \u0025\u0032\u0064\u002c \u0025\u0032\u0064\u0020\u0028%\u0032d\u002c \u0025\u0032\u0064\u0029\u0020\u0025\u0071\n",_fdfa ,_baecf ,_afbdb ,_gaddf ,_ecegb (_bbggg .merge ().text (),50));};_cceef .putComposite (_fdfa ,_baecf ,_bbggg ,_cedfa );
};};return &_cceef ;};

// TableCell is a cell in a TextTable.
type TableCell struct{_aa .PdfRectangle ;

// Text is the extracted text.
Text string ;

// Marks returns the TextMarks corresponding to the text in Text.
Marks TextMarkArray ;};func _eeed (_fcdb string ,_agfd ,_aegb int ,_debb string )string {if _aegb > len (_fcdb )-1{return _fcdb [:_agfd ]+_debb ;};return _fcdb [:_agfd ]+_debb +_fcdb [_aegb :];};func (_bcdee *textTable )bbox ()_aa .PdfRectangle {return _bcdee .PdfRectangle };
func _fddg (_dfgfc _aa .PdfRectangle ,_gafbg bounded )float64 {return _dfgfc .Ury -_gafbg .bbox ().Lly };func _afgfa (_agcga []TextMark ,_cbfef *int ,_fgdfa TextMark )[]TextMark {_fgdfa .Offset =*_cbfef ;_agcga =append (_agcga ,_fgdfa );*_cbfef +=len (_fgdfa .Text );
return _agcga ;};func _gefd (_aebeb []*textLine ){_f .Slice (_aebeb ,func (_egag ,_ddadd int )bool {_fbbee ,_egbf :=_aebeb [_egag ],_aebeb [_ddadd ];return _fbbee ._agcc < _egbf ._agcc ;});};func (_cddc rulingList )primaries ()[]float64 {_gdgdf :=make (map[float64 ]struct{},len (_cddc ));
for _ ,_gcfg :=range _cddc {_gdgdf [_gcfg ._eced ]=struct{}{};};_dfca :=make ([]float64 ,len (_gdgdf ));_fcge :=0;for _dfdg :=range _gdgdf {_dfca [_fcge ]=_dfdg ;_fcge ++;};_f .Float64s (_dfca );return _dfca ;};func (_cfda paraList )merge ()*textPara {_g .Log .Trace ("\u006d\u0065\u0072\u0067\u0065:\u0020\u0070\u0061\u0072\u0061\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_cfda ));
if len (_cfda )==0{return nil ;};_cfda .sortReadingOrder ();_cgeeba :=_cfda [0].PdfRectangle ;_ecggd :=_cfda [0]._fdfb ;for _ ,_cbga :=range _cfda [1:]{_cgeeba =_bdda (_cgeeba ,_cbga .PdfRectangle );_ecggd =append (_ecggd ,_cbga ._fdfb ...);};return _bgdca (_cgeeba ,_ecggd );
};func (_afcb *textMark )inDiacriticArea (_ddcb *textMark )bool {_gdaa :=_afcb .Llx -_ddcb .Llx ;_dgad :=_afcb .Urx -_ddcb .Urx ;_ebfdc :=_afcb .Lly -_ddcb .Lly ;return _ea .Abs (_gdaa +_dgad )< _afcb .Width ()*_ddfa &&_ea .Abs (_ebfdc )< _afcb .Height ()*_ddfa ;
};func _eebgf (_bfabg *_aa .Image ,_babb _ed .Color )_ff .Image {_febbe ,_gfdag :=int (_bfabg .Width ),int (_bfabg .Height );_dadfg :=_ff .NewRGBA (_ff .Rect (0,0,_febbe ,_gfdag ));for _baage :=0;_baage < _gfdag ;_baage ++{for _efge :=0;_efge < _febbe ;
_efge ++{_bbfda ,_gada :=_bfabg .ColorAt (_efge ,_baage );if _gada !=nil {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_efge ,_baage );
continue ;};_ccefa ,_fegec ,_gccga ,_ :=_bbfda .RGBA ();var _badb _ed .Color ;if _ccefa +_fegec +_gccga ==0{_badb =_ed .Transparent ;}else {_badb =_babb ;};_dadfg .Set (_efge ,_baage ,_badb );};};return _dadfg ;};func (_adgg rulingList )secMinMax ()(float64 ,float64 ){_dggfb ,_eccgb :=_adgg [0]._decc ,_adgg [0]._gcda ;
for _ ,_adgdae :=range _adgg [1:]{if _adgdae ._decc < _dggfb {_dggfb =_adgdae ._decc ;};if _adgdae ._gcda > _eccgb {_eccgb =_adgdae ._gcda ;};};return _dggfb ,_eccgb ;};type pathSection struct{_cdbe []*subpath ;_ed .Color ;};func (_efgc paraList )llyOrdering ()[]int {_gacbf :=make ([]int ,len (_efgc ));
for _ffef :=range _efgc {_gacbf [_ffef ]=_ffef ;};_f .SliceStable (_gacbf ,func (_edfg ,_cebc int )bool {_dbddg ,_dgda :=_gacbf [_edfg ],_gacbf [_cebc ];return _efgc [_dbddg ].Lly < _efgc [_dgda ].Lly ;});return _gacbf ;};func (_gfgcb *wordBag )arrangeText (_fgdde bool )*textPara {_gfgcb .sort ();
if _aabga {_gfgcb .removeDuplicates ();};var _cede []*textLine ;for _ ,_abcd :=range _gfgcb .depthIndexes (){for !_gfgcb .empty (_abcd ){_adbg :=_gfgcb .firstReadingIndex (_abcd );_feef :=_gfgcb .firstWord (_adbg );_gecde :=_adfg (_gfgcb ,_adbg );_aece :=_feef ._cacfc ;
if _aece < _dfeb {_aece =_dfeb ;};_bebd :=_feef ._abace -_dfff *_aece ;_cdge :=_feef ._abace +_dfff *_aece ;_adefb :=_dbcc *_aece ;_gfgd :=_gfcb *_aece ;_dcbf :for {var _eddea *textWord ;_begba :=0;for _ ,_acfdc :=range _gfgcb .depthBand (_bebd ,_cdge ){_aeffc :=_gfgcb .highestWord (_acfdc ,_bebd ,_cdge );
if _aeffc ==nil {continue ;};_gafd :=_fbg (_aeffc ,_gecde ._dbcg [len (_gecde ._dbcg )-1]);if _gafd < -_gfgd {break _dcbf ;};if !_fgdde &&_gafd > _adefb {continue ;};if _eddea !=nil &&_ecac (_aeffc ,_eddea )>=0{continue ;};_eddea =_aeffc ;_begba =_acfdc ;
};if _eddea ==nil {break ;};_gecde .pullWord (_gfgcb ,_eddea ,_begba );};_gecde .markWordBoundaries ();_cede =append (_cede ,_gecde );};};if len (_cede )==0{return nil ;};_f .Slice (_cede ,func (_dbgc ,_cegg int )bool {return _dfcf (_cede [_dbgc ],_cede [_cegg ])< 0});
_gcgd :=_bgdca (_gfgcb .PdfRectangle ,_cede );if _adcf {_g .Log .Info ("\u0061\u0072\u0072an\u0067\u0065\u0054\u0065\u0078\u0074\u0020\u0021\u0021\u0021\u0020\u0070\u0061\u0072\u0061\u003d\u0025\u0073",_gcgd .String ());if _ceed {for _ffag ,_bgada :=range _gcgd ._fdfb {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ffag ,_bgada .String ());
if _dgcg {for _cbae ,_fdbd :=range _bgada ._dbcg {_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_cbae ,_fdbd .String ());for _gedg ,_eaeed :=range _fdbd ._fcacg {_ad .Printf ("\u00251\u0032\u0064\u003a\u0020\u0025\u0073\n",_gedg ,_eaeed .String ());
};};};};};};return _gcgd ;};

// NewWithOptions an Extractor instance for extracting content from the input PDF page with options.
func NewWithOptions (page *_aa .PdfPage ,options *Options )(*Extractor ,error ){const _ffe ="\u0065x\u0074\u0072\u0061\u0063\u0074\u006f\u0072\u002e\u004e\u0065\u0077W\u0069\u0074\u0068\u004f\u0070\u0074\u0069\u006f\u006e\u0073";_aaf ,_ba :=page .GetAllContentStreams ();
if _ba !=nil {return nil ,_ba ;};var _eea *_aa .StructTreeRoot ;_ccd ,_gaf :=page .GetStructTreeRoot ();if !_gaf {_g .Log .Debug ("T\u0068\u0065\u0020\u0070\u0064\u0066\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0074\u0061\u0067g\u0065d\u002e\u0020\u0053\u0074r\u0075\u0063t\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074\u0020\u0065\u0078\u0069\u0073\u0074\u002e");
}else {_eea ,_ba =_aa .NewStructTreeRootFromPdfObject (*_ccd );if _ba !=nil {return nil ,_ad .Errorf ("\u0065\u0072\u0072or\u0020\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020s\u0074r\u0075c\u0074 \u0074\u0072\u0065\u0065\u0020\u0072\u006f\u006f\u0074\u003a\u0020\u0025\u0076",_ba );
};};_gggg :=page .GetContainingPdfObject ();_eac ,_ba :=page .GetMediaBox ();if _ba !=nil {return nil ,_ad .Errorf ("\u0065\u0078\u0074r\u0061\u0063\u0074\u006fr\u0020\u0072\u0065\u0071\u0075\u0069\u0072e\u0073\u0020\u006d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u002e\u0020\u0025\u0076",_ba );
};_egf :=&Extractor {_gcc :_aaf ,_cgf :page .Resources ,_fgc :*_eac ,_ccb :page .CropBox ,_faa :page .GetStructParentsKey (),_feaa :map[string ]fontEntry {},_dd :map[string ]textResult {},_agad :map[string ]textResult {},_fda :options ,_dccb :_eea ,_cfa :_gggg };
if _egf ._fgc .Llx > _egf ._fgc .Urx {_g .Log .Info ("\u004d\u0065\u0064\u0069\u0061\u0042o\u0078\u0020\u0068\u0061\u0073\u0020\u0058\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073\u0020r\u0065\u0076\u0065\u0072\u0073\u0065\u0064\u002e\u0020\u0025\u002e\u0032\u0066\u0020F\u0069x\u0069\u006e\u0067\u002e",_egf ._fgc );
_egf ._fgc .Llx ,_egf ._fgc .Urx =_egf ._fgc .Urx ,_egf ._fgc .Llx ;};if _egf ._fgc .Lly > _egf ._fgc .Ury {_g .Log .Info ("\u004d\u0065\u0064\u0069\u0061\u0042o\u0078\u0020\u0068\u0061\u0073\u0020\u0059\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073\u0020r\u0065\u0076\u0065\u0072\u0073\u0065\u0064\u002e\u0020\u0025\u002e\u0032\u0066\u0020F\u0069x\u0069\u006e\u0067\u002e",_egf ._fgc );
_egf ._fgc .Lly ,_egf ._fgc .Ury =_egf ._fgc .Ury ,_egf ._fgc .Lly ;};if _egf ._fda !=nil {if _egf ._fda .IncludeAnnotations {_egf ._bfaa ,_ba =page .GetAnnotations ();if _ba !=nil {_g .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0073: \u0025\u0076",_ba );
};};};_ec .TrackUse (_ffe );return _egf ,nil ;};func (_edccg *textTable )markCells (){for _gdbcb :=0;_gdbcb < _edccg ._gacfc ;_gdbcb ++{for _afegf :=0;_afegf < _edccg ._ebdg ;_afegf ++{_afgfb :=_edccg .get (_afegf ,_gdbcb );if _afgfb !=nil {_afgfb ._fdge =true ;
};};};};

// TextTable represents a table.
// Cells are ordered top-to-bottom, left-to-right.
// Cells[y] is the (0-offset) y'th row in the table.
// Cells[y][x] is the (0-offset) x'th column in the table.
type TextTable struct{_aa .PdfRectangle ;W ,H int ;Cells [][]TableCell ;};func (_aefg *subpath )add (_efcbe ..._dc .Point ){_aefg ._eae =append (_aefg ._eae ,_efcbe ...)};func (_cdb *imageExtractContext )extractFormImages (_acf *_cg .PdfObjectName ,_gga _fc .GraphicsState ,_eeb *_aa .PdfPageResources )error {_gbfb ,_fege :=_eeb .GetXObjectFormByName (*_acf );
if _fege !=nil {return _fege ;};if _gbfb ==nil {return nil ;};_fae ,_fege :=_gbfb .GetContentStream ();if _fege !=nil {return _fege ;};_egeg :=_gbfb .Resources ;if _egeg ==nil {_egeg =_eeb ;};_fege =_cdb .extractContentStreamImages (string (_fae ),_egeg );
if _fege !=nil {return _fege ;};_cdb ._dcb ++;return nil ;};func (_gceag rulingList )sortStrict (){_f .Slice (_gceag ,func (_bcgea ,_bdaac int )bool {_bdgac ,_dabdd :=_gceag [_bcgea ],_gceag [_bdaac ];_cacc ,_ebceg :=_bdgac ._cafff ,_dabdd ._cafff ;if _cacc !=_ebceg {return _cacc > _ebceg ;
};_begc ,_cfeeec :=_bdgac ._eced ,_dabdd ._eced ;if !_bbage (_begc -_cfeeec ){return _begc < _cfeeec ;};_begc ,_cfeeec =_bdgac ._decc ,_dabdd ._decc ;if _begc !=_cfeeec {return _begc < _cfeeec ;};return _bdgac ._gcda < _dabdd ._gcda ;});};func _abgab (_bfeaa []TextMark ,_dceg *int ,_gdfd string )[]TextMark {_dbad :=_abc ;
_dbad .Text =_gdfd ;return _afgfa (_bfeaa ,_dceg ,_dbad );};type fontEntry struct{_dcgd *_aa .PdfFont ;_egdc int64 ;};func (_gedbf *textTable )compositeColCorridors ()map[int ][]float64 {_cecg :=make (map[int ][]float64 ,_gedbf ._ebdg );if _bffae {_g .Log .Info ("\u0063\u006f\u006d\u0070o\u0073\u0069\u0074\u0065\u0043\u006f\u006c\u0043\u006f\u0072r\u0069d\u006f\u0072\u0073\u003a\u0020\u0077\u003d%\u0064\u0020",_gedbf ._ebdg );
};for _gdcc :=0;_gdcc < _gedbf ._ebdg ;_gdcc ++{_cecg [_gdcc ]=nil ;};return _cecg ;};func (_dgce rulingList )intersections ()map[int ]intSet {var _dgdb ,_bdeb []int ;for _bgbb ,_eaaa :=range _dgce {switch _eaaa ._cafff {case _cggd :_dgdb =append (_dgdb ,_bgbb );
case _feed :_bdeb =append (_bdeb ,_bgbb );};};if len (_dgdb )< _fagd +1||len (_bdeb )< _ecbf +1{return nil ;};if len (_dgdb )+len (_bdeb )> _gdea {_g .Log .Debug ("\u0069\u006e\u0074\u0065\u0072\u0073e\u0063\u0074\u0069\u006f\u006e\u0073\u003a\u0020\u0054\u004f\u004f\u0020\u004d\u0041\u004e\u0059\u0020\u0072\u0075\u006ci\u006e\u0067\u0073\u0020\u0076\u0065\u0063\u0073\u003d\u0025\u0064\u0020\u003d\u0020%\u0064 \u0078\u0020\u0025\u0064",len (_dgce ),len (_dgdb ),len (_bdeb ));
return nil ;};_abbc :=make (map[int ]intSet ,len (_dgdb )+len (_bdeb ));for _ ,_gebe :=range _dgdb {for _ ,_eeae :=range _bdeb {if _dgce [_gebe ].intersects (_dgce [_eeae ]){if _ ,_egbb :=_abbc [_gebe ];!_egbb {_abbc [_gebe ]=make (intSet );};if _ ,_gebdg :=_abbc [_eeae ];
!_gebdg {_abbc [_eeae ]=make (intSet );};_abbc [_gebe ].add (_eeae );_abbc [_eeae ].add (_gebe );};};};return _abbc ;};func _cdgfc (_dbdbc *textLine )bool {_cdcea :=true ;_gegfc :=-1;for _ ,_agdge :=range _dbdbc ._dbcg {for _ ,_babf :=range _agdge ._fcacg {_gdedb :=_babf ._ecfc ;
if _gegfc ==-1{_gegfc =_gdedb ;}else {if _gegfc !=_gdedb {_cdcea =false ;break ;};};};};return _cdcea ;};func (_fgag intSet )has (_cagf int )bool {_ ,_afef :=_fgag [_cagf ];return _afef };func (_bcec paraList )sortReadingOrder (){_g .Log .Trace ("\u0073\u006fr\u0074\u0052\u0065\u0061\u0064i\u006e\u0067\u004f\u0072\u0064e\u0072\u003a\u0020\u0070\u0061\u0072\u0061\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_bcec ));
if len (_bcec )<=1{return ;};_bcec .computeEBBoxes ();_f .Slice (_bcec ,func (_dgcd ,_befg int )bool {return _dfcf (_bcec [_dgcd ],_bcec [_befg ])<=0});};type gridTiling struct{_aa .PdfRectangle ;_ddfcb []float64 ;_bced []float64 ;_ebegg map[float64 ]map[float64 ]gridTile ;
};func _abga (_bgfb ,_edab bounded )float64 {return _bgfbc (_bgfb )-_bgfbc (_edab )};func (_agge *textTable )get (_cbeg ,_feada int )*textPara {return _agge ._fdgeg [_cggdf (_cbeg ,_feada )]};func _eeda (_aaeb []*textLine )[]*textLine {_bgfd :=[]*textLine {};
for _ ,_gdce :=range _aaeb {_dggc :=_gdce .text ();_bfafb :=_dagfgb .Find ([]byte (_dggc ));if _bfafb !=nil {_bgfd =append (_bgfd ,_gdce );};};return _bgfd ;};func (_aead gridTile )complete ()bool {return _aead .numBorders ()==4};

// Elements returns the TextMarks in `ma`.
func (_dbbg *TextMarkArray )Elements ()[]TextMark {return _dbbg ._edgd };func (_dcdd rulingList )asTiling ()gridTiling {if _bcbed {_g .Log .Info ("r\u0075\u006ci\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0076\u0065\u0063s\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u002b\u002b\u002b\u0020\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d",len (_dcdd ));
};for _dgea ,_gdad :=range _dcdd [1:]{_fdfbg :=_dcdd [_dgea ];if _fdfbg .alignsPrimary (_gdad )&&_fdfbg .alignsSec (_gdad ){_g .Log .Error ("a\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0044\u0075\u0070\u006c\u0069\u0063\u0061\u0074\u0065 \u0072\u0075\u006c\u0069\u006e\u0067\u0073\u002e\u000a\u0009v=\u0025\u0073\u000a\t\u0077=\u0025\u0073",_gdad ,_fdfbg );
};};_dcdd .sortStrict ();_dcdd .log ("\u0073n\u0061\u0070\u0070\u0065\u0064");_efce ,_efde :=_dcdd .vertsHorzs ();_cbac :=_efce .primaries ();_cbab :=_efde .primaries ();_cagg :=len (_cbac )-1;_edae :=len (_cbab )-1;if _cagg ==0||_edae ==0{return gridTiling {};
};_fffdg :=_aa .PdfRectangle {Llx :_cbac [0],Urx :_cbac [_cagg ],Lly :_cbab [0],Ury :_cbab [_edae ]};if _bcbed {_g .Log .Info ("\u0072\u0075l\u0069\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0076\u0065\u0072\u0074s=\u0025\u0064",len (_efce ));
for _ccdb ,_eadda :=range _efce {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ccdb ,_eadda );};_g .Log .Info ("\u0072\u0075l\u0069\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0068\u006f\u0072\u007as=\u0025\u0064",len (_efde ));
for _gega ,_ggefc :=range _efde {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gega ,_ggefc );};_g .Log .Info ("\u0072\u0075\u006c\u0069\u006eg\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067:\u0020\u0020\u0077\u0078\u0068\u003d\u0025\u0064\u0078\u0025\u0064\u000a\u0009\u006c\u006c\u0078\u003d\u0025\u002e\u0032\u0066\u000a\u0009\u006c\u006c\u0079\u003d\u0025\u002e\u0032f",_cagg ,_edae ,_cbac ,_cbab );
};_bcdcc :=make ([]gridTile ,_cagg *_edae );for _ccba :=_edae -1;_ccba >=0;_ccba --{_cdceed :=_cbab [_ccba ];_efdg :=_cbab [_ccba +1];for _ebfc :=0;_ebfc < _cagg ;_ebfc ++{_gegad :=_cbac [_ebfc ];_gdcf :=_cbac [_ebfc +1];_edcdc :=_efce .findPrimSec (_gegad ,_cdceed );
_edgeg :=_efce .findPrimSec (_gdcf ,_cdceed );_fadfa :=_efde .findPrimSec (_cdceed ,_gegad );_edcf :=_efde .findPrimSec (_efdg ,_gegad );_bedf :=_aa .PdfRectangle {Llx :_gegad ,Urx :_gdcf ,Lly :_cdceed ,Ury :_efdg };_bgcb :=_gfffb (_bedf ,_edcdc ,_edgeg ,_fadfa ,_edcf );
_bcdcc [_ccba *_cagg +_ebfc ]=_bgcb ;if _bcbed {_ad .Printf ("\u0020\u0020\u0078\u003d\u0025\u0032\u0064\u0020\u0079\u003d\u0025\u0032\u0064\u003a\u0020%\u0073 \u0025\u0036\u002e\u0032\u0066\u0020\u0078\u0020\u0025\u0036\u002e\u0032\u0066\u000a",_ebfc ,_ccba ,_bgcb .String (),_bgcb .Width (),_bgcb .Height ());
};};};if _bcbed {_g .Log .Info ("r\u0075\u006c\u0069\u006e\u0067\u004c\u0069\u0073\u0074.\u0061\u0073\u0054\u0069\u006c\u0069\u006eg:\u0020\u0063\u006f\u0061l\u0065\u0073\u0063\u0065\u0020\u0068\u006f\u0072\u0069zo\u006e\u0074a\u006c\u002e\u0020\u0025\u0036\u002e\u0032\u0066",_fffdg );
};_fabg :=make ([]map[float64 ]gridTile ,_edae );for _egdec :=_edae -1;_egdec >=0;_egdec --{if _bcbed {_ad .Printf ("\u0020\u0020\u0079\u003d\u0025\u0032\u0064\u000a",_egdec );};_fabg [_egdec ]=make (map[float64 ]gridTile ,_cagg );for _bccad :=0;_bccad < _cagg ;
_bccad ++{_dgaff :=_bcdcc [_egdec *_cagg +_bccad ];if _bcbed {_ad .Printf ("\u0020\u0020\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_bccad ,_dgaff );};if !_dgaff ._geeegg {continue ;};_bbfc :=_bccad ;for _aace :=_bccad +1;!_dgaff ._ccge &&_aace < _cagg ;
_aace ++{_dfgec :=_bcdcc [_egdec *_cagg +_aace ];_dgaff .Urx =_dfgec .Urx ;_dgaff ._aaaff =_dgaff ._aaaff ||_dfgec ._aaaff ;_dgaff ._fcfc =_dgaff ._fcfc ||_dfgec ._fcfc ;_dgaff ._ccge =_dfgec ._ccge ;if _bcbed {_ad .Printf ("\u0020 \u0020%\u0034\u0064\u003a\u0020\u0025s\u0020\u2192 \u0025\u0073\u000a",_aace ,_dfgec ,_dgaff );
};_bbfc =_aace ;};if _bcbed {_ad .Printf (" \u0020 \u0025\u0032\u0064\u0020\u002d\u0020\u0025\u0032d\u0020\u2192\u0020\u0025s\n",_bccad ,_bbfc ,_dgaff );};_bccad =_bbfc ;_fabg [_egdec ][_dgaff .Llx ]=_dgaff ;};};_ecggdf :=make (map[float64 ]map[float64 ]gridTile ,_edae );
_dfdgf :=make (map[float64 ]map[float64 ]struct{},_edae );for _fbed :=_edae -1;_fbed >=0;_fbed --{_gbba :=_bcdcc [_fbed *_cagg ].Lly ;_ecggdf [_gbba ]=make (map[float64 ]gridTile ,_cagg );_dfdgf [_gbba ]=make (map[float64 ]struct{},_cagg );};if _bcbed {_g .Log .Info ("\u0072u\u006c\u0069n\u0067\u004c\u0069s\u0074\u002e\u0061\u0073\u0054\u0069\u006ci\u006e\u0067\u003a\u0020\u0063\u006fa\u006c\u0065\u0073\u0063\u0065\u0020\u0076\u0065\u0072\u0074\u0069c\u0061\u006c\u002e\u0020\u0025\u0036\u002e\u0032\u0066",_fffdg );
};for _dcgdg :=_edae -1;_dcgdg >=0;_dcgdg --{_gcecf :=_bcdcc [_dcgdg *_cagg ].Lly ;_ggdbg :=_fabg [_dcgdg ];if _bcbed {_ad .Printf ("\u0020\u0020\u0079\u003d\u0025\u0032\u0064\u000a",_dcgdg );};for _ ,_bdff :=range _dfgeg (_ggdbg ){if _ ,_edff :=_dfdgf [_gcecf ][_bdff ];
_edff {continue ;};_afeae :=_ggdbg [_bdff ];if _bcbed {_ad .Printf (" \u0020\u0020\u0020\u0020\u0076\u0030\u003d\u0025\u0073\u000a",_afeae .String ());};for _abgg :=_dcgdg -1;_abgg >=0;_abgg --{if _afeae ._fcfc {break ;};_ffagd :=_fabg [_abgg ];_aecfd ,_adcc :=_ffagd [_bdff ];
if !_adcc {break ;};if _aecfd .Urx !=_afeae .Urx {break ;};_afeae ._fcfc =_aecfd ._fcfc ;_afeae .Lly =_aecfd .Lly ;if _bcbed {_ad .Printf ("\u0020\u0020\u0020\u0020  \u0020\u0020\u0076\u003d\u0025\u0073\u0020\u0076\u0030\u003d\u0025\u0073\u000a",_aecfd .String (),_afeae .String ());
};_dfdgf [_aecfd .Lly ][_aecfd .Llx ]=struct{}{};};if _dcgdg ==0{_afeae ._fcfc =true ;};if _afeae .complete (){_ecggdf [_gcecf ][_bdff ]=_afeae ;};};};_gdead :=gridTiling {PdfRectangle :_fffdg ,_ddfcb :_bbgf (_ecggdf ),_bced :_aaabg (_ecggdf ),_ebegg :_ecggdf };
_gdead .log ("\u0043r\u0065\u0061\u0074\u0065\u0064");return _gdead ;};func (_dgbgc *textWord )computeText ()string {_cfgb :=make ([]string ,len (_dgbgc ._fcacg ));for _cege ,_gdae :=range _dgbgc ._fcacg {_cfgb [_cege ]=_gdae ._gacc ;};return _add .Join (_cfgb ,"");
};const (_bccgg rulingKind =iota ;_feed ;_cggd ;);func (_eeefg *PageText )computeViews (){if _eeefg ._cfcf ._beda ==ExtractionModePlain {_eeefg ._cecf =_eeefg .getText ();return ;};_eca :=_eeefg .getParagraphs ();_eagf :=new (_bb .Buffer );_eca .writeText (_eagf );
_eeefg ._cecf =_eagf .String ();_eeefg ._eedcb =_eca .toTextMarks ();_eeefg ._gcee =_eca .tables ();if _bffae {_g .Log .Info ("\u0063\u006f\u006dpu\u0074\u0065\u0056\u0069\u0065\u0077\u0073\u003a\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u003d\u0025\u0064",len (_eeefg ._gcee ));
};};func _agaea (_adgd ,_ebdfe _aa .PdfRectangle )bool {return _adgd .Llx <=_ebdfe .Llx &&_ebdfe .Urx <=_adgd .Urx &&_adgd .Lly <=_ebdfe .Lly &&_ebdfe .Ury <=_adgd .Ury ;};func (_gdfdc rulingList )merge ()*ruling {_fgaae :=_gdfdc [0]._eced ;_ceca :=_gdfdc [0]._decc ;
_acfb :=_gdfdc [0]._gcda ;for _ ,_bgae :=range _gdfdc [1:]{_fgaae +=_bgae ._eced ;if _bgae ._decc < _ceca {_ceca =_bgae ._decc ;};if _bgae ._gcda > _acfb {_acfb =_bgae ._gcda ;};};_gegd :=&ruling {_cafff :_gdfdc [0]._cafff ,_bcgbe :_gdfdc [0]._bcgbe ,Color :_gdfdc [0].Color ,_eced :_fgaae /float64 (len (_gdfdc )),_decc :_ceca ,_gcda :_acfb };
if _cgbfc {_g .Log .Info ("\u006de\u0072g\u0065\u003a\u0020\u0025\u0032d\u0020\u0076e\u0063\u0073\u0020\u0025\u0073",len (_gdfdc ),_gegd );for _efbb ,_acaaa :=range _gdfdc {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_efbb ,_acaaa );
};};return _gegd ;};func _gg (_fcf []string ,_de int ,_gc string )int {_agd :=_de ;for ;_agd < len (_fcf );_agd ++{if _fcf [_agd ]!=_gc {return _agd ;};};return _agd ;};func _aeg (_dea *TextMarkArray )string {_cga :="";for _ ,_eddd :=range _dea .Elements (){_cga +=_eddd .Text ;
};return _cga ;};type markKind int ;

// ExtractionMode defines different types of extraction mode.
type ExtractionMode int ;func (_dceb *textTable )getRight ()paraList {_eefd :=make (paraList ,_dceb ._gacfc );for _gcbee :=0;_gcbee < _dceb ._gacfc ;_gcbee ++{_gdcee :=_dceb .get (_dceb ._ebdg -1,_gcbee )._caec ;if _gdcee .taken (){return nil ;};_eefd [_gcbee ]=_gdcee ;
};for _fbbf :=0;_fbbf < _dceb ._gacfc -1;_fbbf ++{if _eefd [_fbbf ]._fgfd !=_eefd [_fbbf +1]{return nil ;};};return _eefd ;};func _dfgeg (_ebbdc map[float64 ]gridTile )[]float64 {_bbdg :=make ([]float64 ,0,len (_ebbdc ));for _accgd :=range _ebbdc {_bbdg =append (_bbdg ,_accgd );
};_f .Float64s (_bbdg );return _bbdg ;};var _acceg =map[rulingKind ]string {_bccgg :"\u006e\u006f\u006e\u0065",_feed :"\u0068\u006f\u0072\u0069\u007a\u006f\u006e\u0074\u0061\u006c",_cggd :"\u0076\u0065\u0072\u0074\u0069\u0063\u0061\u006c"};func (_ggdb rectRuling )checkWidth (_eeeda ,_bcccg float64 )(float64 ,bool ){_ebdfb :=_bcccg -_eeeda ;
_cbfed :=_ebdfb <=_cafa ;return _ebdfb ,_cbfed ;};func _bcaeg (_cacec []_cg .PdfObject )(_eabfd ,_efgaf float64 ,_dbgfd error ){if len (_cacec )!=2{return 0,0,_ad .Errorf ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0070\u0061\u0072\u0061\u006d\u0073\u003a \u0025\u0064",len (_cacec ));
};_cbeeg ,_dbgfd :=_cg .GetNumbersAsFloat (_cacec );if _dbgfd !=nil {return 0,0,_dbgfd ;};return _cbeeg [0],_cbeeg [1],nil ;};func _febf (_dgab *TextMarkArray )[]*TextMarkArray {_ece :=_dgab .Elements ();_bbg :=len (_ece );var _dbc _cg .PdfObject ;_cdc :=[]*TextMarkArray {};
_adg :=&TextMarkArray {};_dbd :=-1;for _bdcc ,_ddc :=range _ece {_aab :=_ddc .DirectObject ;_dbd =_ddc .Index ;if _aab ==nil {_ebf :=_cfg (_dgab ,_bdcc );if _dbc !=nil {if _ebf ==-1||_ebf > _bdcc {_cdc =append (_cdc ,_adg );_adg =&TextMarkArray {};};};
}else if _dbc ==nil {if _dbd ==0&&_bdcc > 0{_cdc =append (_cdc ,_adg );_adg =&TextMarkArray {};};}else {if _aab !=_dbc {_cdc =append (_cdc ,_adg );_adg =&TextMarkArray {};};};_dbc =_aab ;_adg .Append (_ddc );if _bdcc ==(_bbg -1){_cdc =append (_cdc ,_adg );
};};return _cdc ;};type textObject struct{_addg *Extractor ;_gfce *_aa .PdfPageResources ;_bffb _fc .GraphicsState ;_abf *textState ;_bbfab *stateStack ;_ddaf _dc .Matrix ;_bbbb _dc .Matrix ;_ebcg []*textMark ;_cccd bool ;};func (_abd *textLine )text ()string {var _dbff []string ;
for _ ,_bbag :=range _abd ._dbcg {if _bbag ._ccdbc {_dbff =append (_dbff ,"\u0020");};_dbff =append (_dbff ,_bbag ._ecegc );};_cfab :=_add .Join (_dbff ,"");_acfa :=_fea ([]rune (_cfab ));return _acfa ._cge ;};func (_fdeeab rulingList )blocks (_dbadd ,_faabd *ruling )bool {if _dbadd ._decc > _faabd ._gcda ||_faabd ._decc > _dbadd ._gcda {return false ;
};_ebed :=_ea .Max (_dbadd ._decc ,_faabd ._decc );_ggedg :=_ea .Min (_dbadd ._gcda ,_faabd ._gcda );if _dbadd ._eced > _faabd ._eced {_dbadd ,_faabd =_faabd ,_dbadd ;};for _ ,_baga :=range _fdeeab {if _dbadd ._eced <=_baga ._eced +_cafa &&_baga ._eced <=_faabd ._eced +_cafa &&_baga ._decc <=_ggedg &&_ebed <=_baga ._gcda {return true ;
};};return false ;};func (_baeae *textWord )bbox ()_aa .PdfRectangle {return _baeae .PdfRectangle };

// BidiText represents a bidi text organized in its visual order
// with base direction of the text.
type BidiText struct{_cge string ;_df string ;};func _cdgd (_ebgbb *list )[]*list {var _bbce []*list ;for _ ,_aeab :=range _ebgbb ._bfaf {switch _aeab ._ccae {case "\u004c\u0049":_accbg :=_gfdc (_aeab );_eece :=_cdgd (_aeab );_fcec :=_cgbd (_accbg ,"\u0062\u0075\u006c\u006c\u0065\u0074",_eece );
_fegb :=_egef (_accbg ,"");_fcec ._cdfd =_fegb ;_bbce =append (_bbce ,_fcec );case "\u004c\u0042\u006fd\u0079":return _cdgd (_aeab );case "\u004c":_dbddc :=_cdgd (_aeab );_bbce =append (_bbce ,_dbddc ...);return _bbce ;};};return _bbce ;};func _bbgc (_gbade ,_aggff _aa .PdfRectangle )bool {return _aggff .Llx <=_gbade .Urx &&_gbade .Llx <=_aggff .Urx ;
};func (_daeed rulingList )findPrimSec (_decce ,_dbbb float64 )*ruling {for _ ,_ebff :=range _daeed {if _bbage (_ebff ._eced -_decce )&&_ebff ._decc -_adag <=_dbbb &&_dbbb <=_ebff ._gcda +_adag {return _ebff ;};};return nil ;};func (_eedgc rectRuling )asRuling ()(*ruling ,bool ){_cgbbc :=ruling {_cafff :_eedgc ._abbd ,Color :_eedgc .Color ,_bcgbe :_cfcbf };
switch _eedgc ._abbd {case _cggd :_cgbbc ._eced =0.5*(_eedgc .Llx +_eedgc .Urx );_cgbbc ._decc =_eedgc .Lly ;_cgbbc ._gcda =_eedgc .Ury ;_baed ,_caaad :=_eedgc .checkWidth (_eedgc .Llx ,_eedgc .Urx );if !_caaad {if _ebcfc {_g .Log .Error ("\u0072\u0065\u0063\u0074\u0052\u0075l\u0069\u006e\u0067\u002e\u0061\u0073\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0072\u0075\u006c\u0069\u006e\u0067V\u0065\u0072\u0074\u0020\u0021\u0063\u0068\u0065\u0063\u006b\u0057\u0069\u0064\u0074h\u0020v\u003d\u0025\u002b\u0076",_eedgc );
};return nil ,false ;};_cgbbc ._fdfcc =_baed ;case _feed :_cgbbc ._eced =0.5*(_eedgc .Lly +_eedgc .Ury );_cgbbc ._decc =_eedgc .Llx ;_cgbbc ._gcda =_eedgc .Urx ;_fdcf ,_ecefa :=_eedgc .checkWidth (_eedgc .Lly ,_eedgc .Ury );if !_ecefa {if _ebcfc {_g .Log .Error ("\u0072\u0065\u0063\u0074\u0052\u0075l\u0069\u006e\u0067\u002e\u0061\u0073\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0072\u0075\u006c\u0069\u006e\u0067H\u006f\u0072\u007a\u0020\u0021\u0063\u0068\u0065\u0063\u006b\u0057\u0069\u0064\u0074h\u0020v\u003d\u0025\u002b\u0076",_eedgc );
};return nil ,false ;};_cgbbc ._fdfcc =_fdcf ;default:_g .Log .Error ("\u0062\u0061\u0064\u0020pr\u0069\u006d\u0061\u0072\u0079\u0020\u006b\u0069\u006e\u0064\u003d\u0025\u0064",_eedgc ._abbd );return nil ,false ;};return &_cgbbc ,true ;};func (_aebe compositeCell )String ()string {_abbb :="";
if len (_aebe .paraList )> 0{_abbb =_ecegb (_aebe .paraList .merge ().text (),50);};return _ad .Sprintf ("\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0064\u0020\u0070\u0061\u0072a\u0073\u0020\u0025\u0071",_aebe .PdfRectangle ,len (_aebe .paraList ),_abbb );
};func _bgdg (_dbgfa ,_ccaa _dc .Point )bool {_abgbb :=_ea .Abs (_dbgfa .X -_ccaa .X );_gef :=_ea .Abs (_dbgfa .Y -_ccaa .Y );return _bede (_gef ,_abgbb );};func _fbfa (_decf *textLine )float64 {return _decf ._dbcg [0].Llx };

// BBox returns the smallest axis-aligned rectangle that encloses all the TextMarks in `ma`.
func (_cdgbc *TextMarkArray )BBox ()(_aa .PdfRectangle ,bool ){var _cdeb _aa .PdfRectangle ;_edac :=false ;for _ ,_cfed :=range _cdgbc ._edgd {if _cfed .Meta ||_acfcg (_cfed .Text ){continue ;};if _edac {_cdeb =_bdda (_cdeb ,_cfed .BBox );}else {_cdeb =_cfed .BBox ;
_edac =true ;};};return _cdeb ,_edac ;};func _eeaa (_dddf map[float64 ][]*textLine )[]float64 {_bgea :=[]float64 {};for _dfaa :=range _dddf {_bgea =append (_bgea ,_dfaa );};_f .Float64s (_bgea );return _bgea ;};var _dae =[]string {"\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u004f\u004e","\u004f\u004e","\u0041\u004c","\u0045\u0054","\u0045\u0054","\u0041\u004c","\u0043\u0053","\u0041\u004c","\u004f\u004e","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0045\u0054","\u0041\u004e","\u0041\u004e","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004e","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c"};
func _dfcf (_faffd ,_cacb bounded )float64 {_cdba :=_abga (_faffd ,_cacb );if !_bbage (_cdba ){return _cdba ;};return _ecac (_faffd ,_cacb );};

// String returns a description of `v`.
func (_ddcf *ruling )String ()string {if _ddcf ._cafff ==_bccgg {return "\u004e\u004f\u0054\u0020\u0052\u0055\u004c\u0049\u004e\u0047";};_bgbf ,_afaga :="\u0078","\u0079";if _ddcf ._cafff ==_feed {_bgbf ,_afaga ="\u0079","\u0078";};_cbcf :="";if _ddcf ._fdfcc !=0.0{_cbcf =_ad .Sprintf (" \u0077\u0069\u0064\u0074\u0068\u003d\u0025\u002e\u0032\u0066",_ddcf ._fdfcc );
};return _ad .Sprintf ("\u0025\u00310\u0073\u0020\u0025\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u0020\u002d\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0028\u0025\u0036\u002e\u0032\u0066\u0029\u0020\u0025\u0073\u0020\u0025\u0076\u0025\u0073",_ddcf ._cafff ,_bgbf ,_ddcf ._eced ,_afaga ,_ddcf ._decc ,_ddcf ._gcda ,_ddcf ._gcda -_ddcf ._decc ,_ddcf ._bcgbe ,_ddcf .Color ,_cbcf );
};func (_gcbc *wordBag )firstWord (_fdae int )*textWord {return _gcbc ._cdaf [_fdae ][0]};

// RenderMode specifies the text rendering mode (Tmode), which determines whether showing text shall cause
// glyph outlines to be  stroked, filled, used as a clipping boundary, or some combination of the three.
// Stroking, filling, and clipping shall have the same effects for a text object as they do for a path object
// (see 8.5.3, "Path-Painting Operators" and 8.5.4, "Clipping Path Operators").
type RenderMode int ;func (_gagae paraList )eventNeighbours (_abac []event )map[*textPara ][]int {_f .Slice (_abac ,func (_eeaec ,_cggg int )bool {_ebbbd ,_bgff :=_abac [_eeaec ],_abac [_cggg ];_bfae ,_adfa :=_ebbbd ._aeegb ,_bgff ._aeegb ;if _bfae !=_adfa {return _bfae < _adfa ;
};if _ebbbd ._beec !=_bgff ._beec {return _ebbbd ._beec ;};return _eeaec < _cggg ;});_egfd :=make (map[int ]intSet );_bgcbb :=make (intSet );for _ ,_fbad :=range _abac {if _fbad ._beec {_egfd [_fbad ._gaecf ]=make (intSet );for _ebfdf :=range _bgcbb {if _ebfdf !=_fbad ._gaecf {_egfd [_fbad ._gaecf ].add (_ebfdf );
_egfd [_ebfdf ].add (_fbad ._gaecf );};};_bgcbb .add (_fbad ._gaecf );}else {_bgcbb .del (_fbad ._gaecf );};};_ddfd :=map[*textPara ][]int {};for _gbcee ,_effbg :=range _egfd {_egfc :=_gagae [_gbcee ];if len (_effbg )==0{_ddfd [_egfc ]=nil ;continue ;};
_cdecb :=make ([]int ,len (_effbg ));_dcgbc :=0;for _cccae :=range _effbg {_cdecb [_dcgbc ]=_cccae ;_dcgbc ++;};_ddfd [_egfc ]=_cdecb ;};return _ddfd ;};

// Editor represents a document editor object
type Editor struct{_fedd *_aa .PdfReader };func (_fced *shapesState )addPoint (_ecaa ,_bcad float64 ){_gddfg :=_fced .establishSubpath ();_fcfgb :=_fced .devicePoint (_ecaa ,_bcad );if _gddfg ==nil {_fced ._geee =true ;_fced ._cfddfa =_fcfgb ;}else {_gddfg .add (_fcfgb );
};};

// Search searches the pages specified by `pages`.
func (_fge *Editor )Search (pattern string ,pages []int )(map[int ]Match ,error ){_faae ,_ ,_bdbf :=_fge .getMatches (pattern ,pages );return _faae ,_bdbf ;};func (_gaea *wordBag )absorb (_gcab *wordBag ){_afdbe :=_gcab .makeRemovals ();for _dfaec ,_edgb :=range _gcab ._cdaf {for _ ,_fccb :=range _edgb {_gaea .pullWord (_fccb ,_dfaec ,_afdbe );
};};_gcab .applyRemovals (_afdbe );};func (_edfaa gridTiling )log (_ccbfg string ){if !_bcbed {return ;};_g .Log .Info ("\u0074i\u006ci\u006e\u0067\u003a\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0071",len (_edfaa ._ddfcb ),len (_edfaa ._bced ),_ccbfg );
_ad .Printf ("\u0020\u0020\u0020l\u006c\u0078\u003d\u0025\u002e\u0032\u0066\u000a",_edfaa ._ddfcb );_ad .Printf ("\u0020\u0020\u0020l\u006c\u0079\u003d\u0025\u002e\u0032\u0066\u000a",_edfaa ._bced );for _bgcd ,_ecceg :=range _edfaa ._bced {_fbcca ,_edca :=_edfaa ._ebegg [_ecceg ];
if !_edca {continue ;};_ad .Printf ("%\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u000a",_bgcd ,_ecceg );for _bbeb ,_fadc :=range _edfaa ._ddfcb {_aeebf ,_ffeed :=_fbcca [_fadc ];if !_ffeed {continue ;};_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_bbeb ,_aeebf .String ());
};};};func _geea (_ffcfe map[int ][]float64 )[]int {_bceb :=make ([]int ,len (_ffcfe ));_feacd :=0;for _ecge :=range _ffcfe {_bceb [_feacd ]=_ecge ;_feacd ++;};_f .Ints (_bceb );return _bceb ;};func (_egbg *wordBag )pullWord (_cgbb *textWord ,_ffegg int ,_fbf map[int ]map[*textWord ]struct{}){_egbg .PdfRectangle =_bdda (_egbg .PdfRectangle ,_cgbb .PdfRectangle );
if _cgbb ._cacfc > _egbg ._abcf {_egbg ._abcf =_cgbb ._cacfc ;};_egbg ._cdaf [_ffegg ]=append (_egbg ._cdaf [_ffegg ],_cgbb );_fbf [_ffegg ][_cgbb ]=struct{}{};};func (_agbd paraList )applyTables (_accega []*textTable )paraList {var _cbca paraList ;for _ ,_gcaf :=range _accega {_cbca =append (_cbca ,_gcaf .newTablePara ());
};for _ ,_eada :=range _agbd {if _eada ._fdge {continue ;};_cbca =append (_cbca ,_eada );};return _cbca ;};func (_gbdcg *textObject )setHorizScaling (_bbde float64 ){if _gbdcg ==nil {return ;};_gbdcg ._abf ._beb =_bbde ;};func _cfaf (_bacf ,_dgabg _dc .Point )bool {return _bacf .X ==_dgabg .X &&_bacf .Y ==_dgabg .Y };
func _feede (_faec ,_gdag ,_gebbe float64 )rulingKind {if _faec >=_gebbe &&_bede (_gdag ,_faec ){return _feed ;};if _gdag >=_gebbe &&_bede (_faec ,_gdag ){return _cggd ;};return _bccgg ;};func _bggba (_ddgeb _aa .PdfRectangle )rulingKind {_dafa :=_ddgeb .Width ();
_fbgb :=_ddgeb .Height ();if _dafa > _fbgb {if _dafa >=_agaf {return _feed ;};}else {if _fbgb >=_agaf {return _cggd ;};};return _bccgg ;};type lists []*list ;

// ExtractStrokePaths processes and extracts all stroke paths in content streams.
func (_bbef *Extractor )ExtractStrokePaths ()([]StrokePath ,error ){_cgb ,_ ,_ ,_gbe :=_bbef .ExtractPageText ();if _gbe !=nil {return nil ,_gbe ;};_gfaa :=[]StrokePath {};for _ ,_bge :=range _cgb ._acecb {_adc :=StrokePath {Color :_bge .Color ,Points :[]_dc .Point {}};
for _ ,_caba :=range _bge ._cdbe {_adc .Points =append (_adc .Points ,_caba ._eae ...);};_gfaa =append (_gfaa ,_adc );};return _gfaa ,nil ;};func (_acgb *textPara )taken ()bool {return _acgb ==nil ||_acgb ._fdge };type textWord struct{_aa .PdfRectangle ;
_abace float64 ;_ecegc string ;_fcacg []*textMark ;_cacfc float64 ;_ccdbc bool ;};func (_eddg *textObject )renderText (_cdbfd _cg .PdfObject ,_ebdd []byte ,_cba int ,_cecb string )error {if _eddg ._cccd {_g .Log .Debug ("\u0072\u0065\u006e\u0064\u0065r\u0054\u0065\u0078\u0074\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u0066\u006f\u006e\u0074\u002e\u0020\u004e\u006f\u0074\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u002e");
return nil ;};_deabb :=_eddg .getCurrentFont ();_afec :=_deabb .BytesToCharcodes (_ebdd );var (_aggb []string ;_degg int ;_ffde int ;);if _cecb !=""{_aggb =[]string {_cg .MakeString (_cecb ).Decoded ()};_degg =1;}else {_aggb ,_degg ,_ffde =_deabb .CharcodesToStrings (_afec ,"");
if _ffde > 0{_g .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_degg ,_ffde );};};_eddg ._abf ._accd +=_degg ;_eddg ._abf ._gfbdb +=_ffde ;
_geae :=_eddg ._abf ;_dgeb :=_geae ._dced ;_fdcb :=_geae ._beb /100.0;_defb :=_ebec ;if _deabb .Subtype ()=="\u0054\u0079\u0070e\u0033"{_defb =1;};_ddf ,_ebaff :=_deabb .GetRuneMetrics (' ');if !_ebaff {_ddf ,_ebaff =_deabb .GetCharMetrics (32);};if !_ebaff {_ddf ,_ =_aa .DefaultFont ().GetRuneMetrics (' ');
};_cff :=_ddf .Wx *_defb ;_g .Log .Trace ("\u0073p\u0061\u0063e\u0057\u0069\u0064t\u0068\u003d\u0025\u002e\u0032\u0066\u0020t\u0065\u0078\u0074\u003d\u0025\u0071 \u0066\u006f\u006e\u0074\u003d\u0025\u0073\u0020\u0066\u006f\u006et\u0053\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066",_cff ,_aggb ,_deabb ,_dgeb );
_dabc :=_dc .NewMatrix (_dgeb *_fdcb ,0,0,_dgeb ,0,_geae ._bac );if _bfga {_g .Log .Info ("\u0072\u0065\u006e\u0064\u0065\u0072T\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020\u0063\u006f\u0064\u0065\u0073=\u0025\u002b\u0076\u0020\u0074\u0065\u0078t\u0073\u003d\u0025\u0071",len (_afec ),_afec ,_aggb );
};_g .Log .Trace ("\u0072\u0065\u006e\u0064\u0065\u0072T\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020\u0063\u006f\u0064\u0065\u0073=\u0025\u002b\u0076\u0020\u0072\u0075\u006ee\u0073\u003d\u0025\u0071",len (_afec ),_afec ,len (_aggb ));_ffebc :=_eddg .getFillColor ();
_dccg :=_eddg .getStrokeColor ();for _ebebc ,_gacd :=range _aggb {_eacc :=[]rune (_gacd );if len (_eacc )==1&&_eacc [0]=='\x00'{continue ;};_gabae :=_afec [_ebebc ];_dddg :=_eddg ._bffb .CTM .Mult (_eddg ._ddaf ).Mult (_dabc );_bdd :=0.0;if len (_eacc )==1&&_eacc [0]==32{_bdd =_geae ._bag ;
};_gbcd ,_eag :=_deabb .GetCharMetrics (_gabae );if !_eag {_g .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u004e\u006f \u006d\u0065\u0074r\u0069\u0063\u0020\u0066\u006f\u0072\u0020\u0063\u006fde\u003d\u0025\u0064 \u0072\u003d0\u0078\u0025\u0030\u0034\u0078\u003d%\u002b\u0071 \u0025\u0073",_gabae ,_eacc ,_eacc ,_deabb );
return _ad .Errorf ("\u006e\u006f\u0020\u0063\u0068\u0061\u0072\u0020\u006d\u0065\u0074\u0072\u0069\u0063\u0073:\u0020f\u006f\u006e\u0074\u003d\u0025\u0073\u0020\u0063\u006f\u0064\u0065\u003d\u0025\u0064",_deabb .String (),_gabae );};_abff :=_dc .Point {X :_gbcd .Wx *_defb ,Y :_gbcd .Wy *_defb };
_cbfe :=_dc .Point {X :(_abff .X *_dgeb +_bdd )*_fdcb };_cgg :=_dc .Point {X :(_abff .X *_dgeb +_geae ._debe +_bdd )*_fdcb };if _bfga {_g .Log .Info ("\u0074\u0066\u0073\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0063\u003d\u0025\u002e\u0032f\u0020t\u0077\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0068\u003d\u0025\u002e\u0032\u0066",_dgeb ,_geae ._debe ,_geae ._bag ,_fdcb );
_g .Log .Info ("\u0064x\u002c\u0064\u0079\u003d%\u002e\u0033\u0066\u0020\u00740\u003d%\u002e3\u0066\u0020\u0074\u003d\u0025\u002e\u0033f",_abff ,_cbfe ,_cgg );};_afaa :=_fabb (_cbfe );_dbdb :=_fabb (_cgg );_dgf :=_eddg ._bffb .CTM .Mult (_eddg ._ddaf ).Mult (_afaa );
if _gbcgg {_g .Log .Info ("e\u006e\u0064\u003a\u000a\tC\u0054M\u003d\u0025\u0073\u000a\u0009 \u0074\u006d\u003d\u0025\u0073\u000a"+"\u0009\u0020t\u0064\u003d\u0025s\u0020\u0078\u006c\u0061\u0074\u003d\u0025\u0073\u000a"+"\u0009t\u0064\u0030\u003d\u0025s\u000a\u0009\u0020\u0020\u2192 \u0025s\u0020x\u006c\u0061\u0074\u003d\u0025\u0073",_eddg ._bffb .CTM ,_eddg ._ddaf ,_dbdb ,_agdcf (_eddg ._bffb .CTM .Mult (_eddg ._ddaf ).Mult (_dbdb )),_afaa ,_dgf ,_agdcf (_dgf ));
};_effe ,_dba :=_eddg .newTextMark (_ac .ExpandLigatures (_eacc ),_dddg ,_agdcf (_dgf ),_ea .Abs (_cff *_dddg .ScalingFactorX ()),_deabb ,_eddg ._abf ._debe ,_ffebc ,_dccg ,_cdbfd ,_aggb ,_ebebc ,_cba );if !_dba {_g .Log .Debug ("\u0054\u0065\u0078\u0074\u0020\u006d\u0061\u0072\u006b\u0020\u006f\u0075\u0074\u0073\u0069d\u0065 \u0070\u0061\u0067\u0065\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067");
continue ;};if _deabb ==nil {_g .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u004e\u006f\u0020\u0066\u006f\u006e\u0074\u002e");}else if _deabb .Encoder ()==nil {_g .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020N\u006f\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006eg\u002e\u0020\u0066o\u006et\u003d\u0025\u0073",_deabb );
}else {if _cdgb ,_ffgbc :=_deabb .Encoder ().CharcodeToRune (_gabae );_ffgbc {_effe ._efdc =string (_cdgb );};};_g .Log .Trace ("i\u003d\u0025\u0064\u0020\u0063\u006fd\u0065\u003d\u0025\u0064\u0020\u006d\u0061\u0072\u006b=\u0025\u0073\u0020t\u0072m\u003d\u0025\u0073",_ebebc ,_gabae ,_effe ,_dddg );
_eddg ._ebcg =append (_eddg ._ebcg ,&_effe );_eddg ._ddaf .Concat (_dbdb );};return nil ;};func (_bdbbf rulingList )snapToGroups ()rulingList {_dgec ,_cbba :=_bdbbf .vertsHorzs ();if len (_dgec )> 0{_dgec =_dgec .snapToGroupsDirection ();};if len (_cbba )> 0{_cbba =_cbba .snapToGroupsDirection ();
};_edcfb :=append (_dgec ,_cbba ...);_edcfb .log ("\u0073\u006e\u0061p\u0054\u006f\u0047\u0072\u006f\u0075\u0070\u0073");return _edcfb ;};type event struct{_aeegb float64 ;_beec bool ;_gaecf int ;};func (_gace *wordBag )sort (){for _ ,_cdga :=range _gace ._cdaf {_f .Slice (_cdga ,func (_eeg ,_cccb int )bool {return _ecac (_cdga [_eeg ],_cdga [_cccb ])< 0});
};};func _gbcb (_cbed _cg .PdfObject ,_begaa _ed .Color )(_ff .Image ,error ){_baebd ,_baefge :=_cg .GetStream (_cbed );if !_baefge {return nil ,nil ;};_ccfe ,_fcgdef :=_aa .NewXObjectImageFromStream (_baebd );if _fcgdef !=nil {return nil ,_fcgdef ;};_deacf ,_fcgdef :=_ccfe .ToImage ();
if _fcgdef !=nil {return nil ,_fcgdef ;};return _eebgf (_deacf ,_begaa ),nil ;};func _bgcdd (_caebf *PageText )error {
	// Always return nil - no license check
	return nil ;};func (_fbfd paraList )addNeighbours (){_bbfdd :=func (_ebad []int ,_fbfb *textPara )([]*textPara ,[]*textPara ){_gfgf :=make ([]*textPara ,0,len (_ebad )-1);
_bgag :=make ([]*textPara ,0,len (_ebad )-1);for _ ,_afbg :=range _ebad {_abgbg :=_fbfd [_afbg ];if _abgbg .Urx <=_fbfb .Llx {_gfgf =append (_gfgf ,_abgbg );}else if _abgbg .Llx >=_fbfb .Urx {_bgag =append (_bgag ,_abgbg );};};return _gfgf ,_bgag ;};_gcff :=func (_eeaae []int ,_cbaf *textPara )([]*textPara ,[]*textPara ){_cbafc :=make ([]*textPara ,0,len (_eeaae )-1);
_cceeb :=make ([]*textPara ,0,len (_eeaae )-1);for _ ,_abfg :=range _eeaae {_fdbg :=_fbfd [_abfg ];if _fdbg .Ury <=_cbaf .Lly {_cceeb =append (_cceeb ,_fdbg );}else if _fdbg .Lly >=_cbaf .Ury {_cbafc =append (_cbafc ,_fdbg );};};return _cbafc ,_cceeb ;
};_bccbd :=_fbfd .yNeighbours (_fddggf );for _ ,_acbac :=range _fbfd {_ccga :=_bccbd [_acbac ];if len (_ccga )==0{continue ;};_eadbe ,_bcbde :=_bbfdd (_ccga ,_acbac );if len (_eadbe )==0&&len (_bcbde )==0{continue ;};if len (_eadbe )> 0{_acaag :=_eadbe [0];
for _ ,_ebdfea :=range _eadbe [1:]{if _ebdfea .Urx >=_acaag .Urx {_acaag =_ebdfea ;};};for _ ,_ecddd :=range _eadbe {if _ecddd !=_acaag &&_ecddd .Urx > _acaag .Llx {_acaag =nil ;break ;};};if _acaag !=nil &&_ffae (_acbac .PdfRectangle ,_acaag .PdfRectangle ){_acbac ._bfaad =_acaag ;
};};if len (_bcbde )> 0{_ebadc :=_bcbde [0];for _ ,_ecgd :=range _bcbde [1:]{if _ecgd .Llx <=_ebadc .Llx {_ebadc =_ecgd ;};};for _ ,_agcf :=range _bcbde {if _agcf !=_ebadc &&_agcf .Llx < _ebadc .Urx {_ebadc =nil ;break ;};};if _ebadc !=nil &&_ffae (_acbac .PdfRectangle ,_ebadc .PdfRectangle ){_acbac ._caec =_ebadc ;
};};};_bccbd =_fbfd .xNeighbours (_eaeb );for _ ,_cecbb :=range _fbfd {_cbbbf :=_bccbd [_cecbb ];if len (_cbbbf )==0{continue ;};_caebc ,_eeee :=_gcff (_cbbbf ,_cecbb );if len (_caebc )==0&&len (_eeee )==0{continue ;};if len (_eeee )> 0{_dgga :=_eeee [0];
for _ ,_dabed :=range _eeee [1:]{if _dabed .Ury >=_dgga .Ury {_dgga =_dabed ;};};for _ ,_aabeb :=range _eeee {if _aabeb !=_dgga &&_aabeb .Ury > _dgga .Lly {_dgga =nil ;break ;};};if _dgga !=nil &&_bbgc (_cecbb .PdfRectangle ,_dgga .PdfRectangle ){_cecbb ._fgfd =_dgga ;
};};if len (_caebc )> 0{_cade :=_caebc [0];for _ ,_gfbdc :=range _caebc [1:]{if _gfbdc .Lly <=_cade .Lly {_cade =_gfbdc ;};};for _ ,_dfeefb :=range _caebc {if _dfeefb !=_cade &&_dfeefb .Lly < _cade .Ury {_cade =nil ;break ;};};if _cade !=nil &&_bbgc (_cecbb .PdfRectangle ,_cade .PdfRectangle ){_cecbb ._bgde =_cade ;
};};};for _ ,_ddgdg :=range _fbfd {if _ddgdg ._bfaad !=nil &&_ddgdg ._bfaad ._caec !=_ddgdg {_ddgdg ._bfaad =nil ;};if _ddgdg ._bgde !=nil &&_ddgdg ._bgde ._fgfd !=_ddgdg {_ddgdg ._bgde =nil ;};if _ddgdg ._caec !=nil &&_ddgdg ._caec ._bfaad !=_ddgdg {_ddgdg ._caec =nil ;
};if _ddgdg ._fgfd !=nil &&_ddgdg ._fgfd ._bgde !=_ddgdg {_ddgdg ._fgfd =nil ;};};};func (_cdabc *ruling )alignsPrimary (_fffb *ruling )bool {return _cdabc ._cafff ==_fffb ._cafff &&_ea .Abs (_cdabc ._eced -_fffb ._eced )< _cafa *0.5;};func _efba (_faab []*textLine ,_fgdc map[float64 ][]*textLine ,_dgde []float64 ,_gbbd int ,_bgee ,_cecea float64 )[]*list {_dfege :=[]*list {};
_bfgg :=_gbbd ;_gbbd =_gbbd +1;_affe :=_dgde [_bfgg ];_fee :=_fgdc [_affe ];_ega :=_cgca (_fee ,_cecea ,_bgee );for _baaf ,_egca :=range _ega {var _aabc float64 ;_eaagd :=[]*list {};_gabcd :=_egca ._agcc ;_dbdc :=_cecea ;if _baaf < len (_ega )-1{_dbdc =_ega [_baaf +1]._agcc ;
};if _gbbd < len (_dgde ){_eaagd =_efba (_faab ,_fgdc ,_dgde ,_gbbd ,_gabcd ,_dbdc );};_aabc =_dbdc ;if len (_eaagd )> 0{_baae :=_eaagd [0];if len (_baae ._feff )> 0{_aabc =_baae ._feff [0]._agcc ;};};_gagaf :=[]*textLine {_egca };_bbgbf :=_cdca (_egca ,_faab ,_gabcd ,_aabc );
_gagaf =append (_gagaf ,_bbgbf ...);_gfee :=_cgbd (_gagaf ,"\u0062\u0075\u006c\u006c\u0065\u0074",_eaagd );_gfee ._cdfd =_egef (_gagaf ,"");_dfege =append (_dfege ,_gfee );};return _dfege ;};func _cafe (_deec []rulingList )(rulingList ,rulingList ){var _faebf rulingList ;
for _ ,_dbca :=range _deec {_faebf =append (_faebf ,_dbca ...);};return _faebf .vertsHorzs ();};const (_fgba =false ;_ffdc =false ;_dggf =false ;_gbcgg =false ;_fdee =false ;_bfga =false ;_dgb =false ;_gacf =false ;_adcf =false ;_ceed =_adcf &&true ;_dgcg =_ceed &&false ;
_agfcf =_adcf &&true ;_bffae =false ;_abaa =_bffae &&false ;_cdgg =_bffae &&true ;_ccccd =false ;_gbab =_ccccd &&false ;_cgbfc =_ccccd &&false ;_bcbed =_ccccd &&true ;_ebcfc =_ccccd &&false ;_gdbc =_ccccd &&false ;);func (_bgcg *textWord )toTextMarks (_bgefd *int )[]TextMark {var _fbdb []TextMark ;
for _ ,_aececg :=range _bgcg ._fcacg {_fbdb =_afgfa (_fbdb ,_bgefd ,_aececg .ToTextMark ());};return _fbdb ;};func _fcdbg (_bcgb *textWord ,_fbbe float64 ,_faba ,_fedb rulingList )*wordBag {_ggd :=_dadf (_bcgb ._abace );_effb :=[]*textWord {_bcgb };_egff :=wordBag {_cdaf :map[int ][]*textWord {_ggd :_effb },PdfRectangle :_bcgb .PdfRectangle ,_abcf :_bcgb ._cacfc ,_dagf :_fbbe ,_aada :_faba ,_bcdc :_fedb };
return &_egff ;};

// New returns an Extractor instance for extracting content from the input PDF page.
func New (page *_aa .PdfPage )(*Extractor ,error ){return NewWithOptions (page ,nil )};type ruling struct{_cafff rulingKind ;_bcgbe markKind ;_ed .Color ;_eced float64 ;_decc float64 ;_gcda float64 ;_fdfcc float64 ;};func (_edgdd *shapesState )stroke (_dcabc *[]pathSection ){_gfba :=pathSection {_cdbe :_edgdd ._agdg ,Color :_edgdd ._baea .getStrokeColor ()};
*_dcabc =append (*_dcabc ,_gfba );if _ccccd {_ad .Printf ("\u0020 \u0020\u0020S\u0054\u0052\u004fK\u0045\u003a\u0020\u0025\u0064\u0020\u0073t\u0072\u006f\u006b\u0065\u0073\u0020s\u0073\u003d\u0025\u0073\u0020\u0063\u006f\u006c\u006f\u0072\u003d%\u002b\u0076\u0020\u0025\u0036\u002e\u0032\u0066\u000a",len (*_dcabc ),_edgdd ,_edgdd ._baea .getStrokeColor (),_gfba .bbox ());
if _gbab {for _ebg ,_cceg :=range _edgdd ._agdg {_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_ebg ,_cceg );if _ebg ==10{break ;};};};};};func _adafd (_ecfae []pathSection )rulingList {_agbaa (_ecfae );if _ccccd {_g .Log .Info ("\u006da\u006b\u0065\u0046\u0069l\u006c\u0052\u0075\u006c\u0069n\u0067s\u003a \u0025\u0064\u0020\u0066\u0069\u006c\u006cs",len (_ecfae ));
};var _eabd rulingList ;for _ ,_gfab :=range _ecfae {for _ ,_ebced :=range _gfab ._cdbe {if !_ebced .isQuadrilateral (){if _ccccd {_g .Log .Error ("!\u0069s\u0051\u0075\u0061\u0064\u0072\u0069\u006c\u0061t\u0065\u0072\u0061\u006c: \u0025\u0073",_ebced );
};continue ;};if _edge ,_gdaf :=_ebced .makeRectRuling (_gfab .Color );_gdaf {_eabd =append (_eabd ,_edge );}else {if _ebcfc {_g .Log .Error ("\u0021\u006d\u0061\u006beR\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0025\u0073",_ebced );
};};};};if _ccccd {_g .Log .Info ("\u006d\u0061\u006b\u0065Fi\u006c\u006c\u0052\u0075\u006c\u0069\u006e\u0067\u0073\u003a\u0020\u0025\u0073",_eabd .String ());};return _eabd ;};

// String returns a description of `t`.
func (_eafe *textTable )String ()string {return _ad .Sprintf ("\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u0020\u0025\u0074",_eafe ._ebdg ,_eafe ._gacfc ,_eafe ._ccfad );};func _cfce (_gfca string ,_acb string )([][]int ,error ){_fba ,_cae :=_bcc .Compile (_gfca );
if _cae !=nil {return nil ,_ad .Errorf ("\u0065\u0072\u0072\u006f\u0072\u0020c\u006f\u006d\u0070\u0069\u006c\u0069\u006e\u0067\u0020\u0072\u0065\u0067\u0065x\u0020\u0070\u0061\u0074\u0074\u0065\u0072n\u003a\u0020\u0025\u0077",_cae );};_bfb :=_fba .FindAllStringIndex (_acb ,-1);
return _bfb ,nil ;};const _gcdf =20;

// String returns a description of `b`.
func (_adae *wordBag )String ()string {var _cgc []string ;for _ ,_dacg :=range _adae .depthIndexes (){_gdec :=_adae ._cdaf [_dacg ];for _ ,_gdge :=range _gdec {_cgc =append (_cgc ,_gdge ._ecegc );};};return _ad .Sprintf ("\u0025.\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065=\u0025\u002e\u0032\u0066\u0020\u0025\u0064\u0020\u0025\u0071",_adae .PdfRectangle ,_adae ._abcf ,len (_cgc ),_cgc );
};func _db (_cgd []string ,_fb int ,_gd int ,_addb string ){for _fe :=_fb ;_fe < _gd ;_fe ++{_cgd [_fe ]=_addb ;};};

// Text returns the text content of the `bulletLists`.
func (_bdde *lists )Text ()string {_fgga :=&_add .Builder {};for _ ,_gbgba :=range *_bdde {_ecf :=_gbgba .Text ();_fgga .WriteString (_ecf );};return _fgga .String ();};func (_fgcc *wordBag )allWords ()[]*textWord {var _edcc []*textWord ;for _ ,_dbbf :=range _fgcc ._cdaf {_edcc =append (_edcc ,_dbbf ...);
};return _edcc ;};func (_fbcd *textObject )getCurrentFont ()*_aa .PdfFont {_dgcb :=_fbcd ._abf ._ebca ;if _dgcb ==nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u002e\u0020U\u0073\u0069\u006e\u0067\u0020d\u0065\u0066a\u0075\u006c\u0074\u002e");
return _aa .DefaultFont ();};return _dgcb ;};func _ggeda (_badc _aa .PdfColorspace ,_bfdgf _aa .PdfColor )_ed .Color {if _badc ==nil ||_bfdgf ==nil {return _ed .Black ;};_dgfb ,_fddae :=_badc .ColorToRGB (_bfdgf );if _fddae !=nil {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006fu\u006c\u0064\u0020no\u0074\u0020\u0063\u006f\u006e\u0076e\u0072\u0074\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0025\u0076\u0020\u0028\u0025\u0076)\u0020\u0074\u006f\u0020\u0052\u0047\u0042\u003a \u0025\u0073",_bfdgf ,_badc ,_fddae );
return _ed .Black ;};_bdcca ,_fecd :=_dgfb .(*_aa .PdfColorDeviceRGB );if !_fecd {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0065\u0064 \u0063\u006f\u006c\u006f\u0072\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0052\u0047\u0042\u0020\u0063\u006flo\u0072\u0073\u0070\u0061\u0063\u0065\u003a\u0020\u0025\u0076",_dgfb );
return _ed .Black ;};return _ed .NRGBA {R :uint8 (_bdcca .R ()*255),G :uint8 (_bdcca .G ()*255),B :uint8 (_bdcca .B ()*255),A :uint8 (255)};};func (_abeaa rulingList )primMinMax ()(float64 ,float64 ){_egaab ,_ddfe :=_abeaa [0]._eced ,_abeaa [0]._eced ;
for _ ,_daeg :=range _abeaa [1:]{if _daeg ._eced < _egaab {_egaab =_daeg ._eced ;}else if _daeg ._eced > _ddfe {_ddfe =_daeg ._eced ;};};return _egaab ,_ddfe ;};func (_efdgc *textWord )appendMark (_bdefa *textMark ,_bgef _aa .PdfRectangle ){_efdgc ._fcacg =append (_efdgc ._fcacg ,_bdefa );
_efdgc .PdfRectangle =_bdda (_efdgc .PdfRectangle ,_bdefa .PdfRectangle );if _bdefa ._fadfd > _efdgc ._cacfc {_efdgc ._cacfc =_bdefa ._fadfd ;};_efdgc ._abace =_bgef .Ury -_efdgc .PdfRectangle .Lly ;};func (_gebd *Extractor )extractPageText (_caf string ,_fga *_aa .PdfPageResources ,_edcg _dc .Matrix ,_gedf int ,_gcde bool )(*PageText ,int ,int ,error ){_g .Log .Trace ("\u0065x\u0074\u0072\u0061\u0063t\u0050\u0061\u0067\u0065\u0054e\u0078t\u003a \u006c\u0065\u0076\u0065\u006c\u003d\u0025d",_gedf );
_feac :=&PageText {_gff :_gebd ._fgc ,_bgac :_gebd ._dccb ,_cacg :_gebd ._cfa };_ggec :=_cdcb (_gebd ._fgc );var _agfc stateStack ;_baf :=_gfcg (_gebd ,_fga ,_fc .GraphicsState {},&_ggec ,&_agfc );_bcf :=shapesState {_deeb :_edcg ,_defg :_dc .IdentityMatrix (),_baea :_baf };
var _egc bool ;_fbag :=-1;_gfd :="";if _gedf > _gcdf {_cgeeb :=_cc .New ("\u0066\u006f\u0072\u006d s\u0074\u0061\u0063\u006b\u0020\u006f\u0076\u0065\u0072\u0066\u006c\u006f\u0077");_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0065\u0078\u0074\u0072\u0061\u0063\u0074\u0050\u0061\u0067\u0065\u0054\u0065\u0078\u0074\u002e\u0020\u0072\u0065\u0063u\u0072\u0073\u0069\u006f\u006e\u0020\u006c\u0065\u0076\u0065\u006c\u003d\u0025\u0064 \u0065r\u0072\u003d\u0025\u0076",_gedf ,_cgeeb );
return _feac ,_ggec ._accd ,_ggec ._gfbdb ,_cgeeb ;};_ccbg :=_fc .NewContentStreamParser (_caf );_dcd ,_ffeb :=_ccbg .Parse ();if _ffeb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020e\u0078\u0074\u0072a\u0063\u0074\u0050\u0061g\u0065\u0054\u0065\u0078\u0074\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ffeb );
return _feac ,_ggec ._accd ,_ggec ._gfbdb ,_ffeb ;};_feac ._dacb =_dcd ;_bga :=_fc .NewContentStreamProcessor (*_dcd );if _gebd ._fda !=nil {_bga .SetRelaxedMode (_gebd ._fda .RelaxedMode );};_bga .AddHandler (_fc .HandlerConditionEnumAllOperands ,"",func (_cgde *_fc .ContentStreamOperation ,_dce _fc .GraphicsState ,_ebc *_aa .PdfPageResources )error {_acca :=_cgde .Operand ;
if _dggf {_g .Log .Info ("\u0026&\u0026\u0020\u006f\u0070\u003d\u0025s",_cgde );};switch _acca {case "\u0071":if _fdee {_g .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_bcf ._defg );};_agfc .push (&_ggec );case "\u0051":if !_agfc .empty (){_ggec =*_agfc .pop ();
};_bcf ._defg =_dce .CTM ;if _fdee {_g .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_bcf ._defg );};case "\u0042\u0044\u0043":_gbdc ,_fcdf :=_cg .GetDict (_cgde .Params [1]);if !_fcdf {_g .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0042D\u0043\u0020\u006f\u0070\u003d\u0025\u0073 \u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_cgde );
return _ffeb ;};_gdb :=_gbdc .Get ("\u004d\u0043\u0049\u0044");if _gdb !=nil {_fcag ,_ebeb :=_cg .GetIntVal (_gdb );if !_ebeb {_g .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0042\u0044C\u0020\u006f\u0070=\u0025\u0073\u002e\u0020\u0042\u0061\u0064\u0020\u006eum\u0065\u0072\u0069c\u0061\u006c \u006f\u0062\u006a\u0065\u0063\u0074.\u0020\u006f=\u0025\u0073",_cgde ,_gdb );
};_fbag =_fcag ;}else {_fbag =-1;};if _gebd ._dccb !=nil &&_fbag !=-1&&_gebd ._faa !=-1&&_gebd ._dccb .ParentTree !=nil {_gbcf :=_gebd ._faa ;var _dbb func (_eecg []*_aa .KValue )bool ;_dbb =func (_gbee []*_aa .KValue )bool {for _ ,_acec :=range _gbee {if _fdcd :=_acec .GetKDict ();
_fdcd !=nil {_bgd :=_fdcd .GetChildren ();if len (_bgd )==1&&_bgd [0].GetMCID ()!=nil {if *_bgd [0].GetMCID ()==_fbag {if _fdcd .ActualText !=nil {_gfd =_add .TrimSpace (_fdcd .ActualText .Str ());return true ;};return false ;};}else {return _dbb (_bgd );
};};};return false ;};if _fdbb :=_gebd ._dccb .ParentTree .Get ("\u004e\u0075\u006d\u0073");_fdbb !=nil {_accb ,_eff :=_cg .GetArray (_fdbb );if !_eff {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0042\u0044\u0043\u0020\u006f\u0070\u003d\u0025\u0073\u002e\u0020\u0042\u0061\u0064\u0020\u004e\u0075m\u0073\u0020\u0061\u0072\u0072a\u0079\u002e \u006f\u003d\u0025\u0073",_cgde ,_fdbb );
}else {for _ageg :=0;_ageg < _accb .Len ();_ageg +=2{if _affc ,_ffac :=_cg .GetInt (_accb .Get (_ageg ));_ffac {if int (*_affc )==_gbcf {if _aeaa :=_accb .Get (_ageg +1);_aeaa !=nil {if _afbf ,_ccdd :=_cg .GetArray (_aeaa );_ccdd {for _ ,_agdce :=range _afbf .Elements (){_edf ,_dfdb :=_aa .NewKDictFromPdfObject (_agdce );
if _dfdb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0042\u0044\u0043\u0020\u006f\u0070\u003d\u0025\u0073\u002e\u0020\u0042\u0061d\u0020\u004b\u0044\u0069\u0063\u0074\u002e \u006f\u003d\u0025\u0073",_cgde ,_agdce );continue ;};_deeg :=_edf .GetChildren ();
if len (_deeg )==1&&_deeg [0].GetMCID ()!=nil {if *_deeg [0].GetMCID ()==_fbag {if _edf .ActualText !=nil {_gfd =_add .TrimSpace (_edf .ActualText .Str ());};break ;}else if _dbb (_deeg ){break ;};};};};};};};};};};};if _gfd ==""{_agbed :=_gbdc .Get ("\u0041\u0063\u0074\u0075\u0061\u006c\u0054\u0065\u0078\u0074");
if _agbed !=nil {_gfd =_add .TrimSpace (_agbed .String ());};};case "\u0045\u004d\u0043":_fbag =-1;_gfd ="";case "\u0042\u0054":if _egc {_g .Log .Debug ("\u0042\u0054\u0020\u0063\u0061\u006c\u006c\u0065\u0064\u0020\u0077\u0068\u0069\u006c\u0065 \u0069n\u0020\u0061\u0020\u0074\u0065\u0078\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
_feac ._gee =append (_feac ._gee ,_baf ._ebcg ...);};_egc =true ;_dbfb :=_dce ;if _gcde {_dbfb =_fc .GraphicsState {};_dbfb .CTM =_bcf ._defg ;};_dbfb .CTM =_edcg .Mult (_dbfb .CTM );_baf =_gfcg (_gebd ,_ebc ,_dbfb ,&_ggec ,&_agfc );_bcf ._baea =_baf ;
case "\u0045\u0054":if !_egc {_g .Log .Debug ("\u0045\u0054\u0020ca\u006c\u006c\u0065\u0064\u0020\u006f\u0075\u0074\u0073i\u0064e\u0020o\u0066 \u0061\u0020\u0074\u0065\u0078\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");};_egc =false ;_feac ._gee =append (_feac ._gee ,_baf ._ebcg ...);
_baf .reset ();case "\u0054\u002a":_baf .nextLine ();case "\u0054\u0064":if _acg ,_def :=_baf .checkOp (_cgde ,2,true );!_acg {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_def );return _def ;};_dbef ,_ebcd ,_bcfb :=_bcaeg (_cgde .Params );
if _bcfb !=nil {return _bcfb ;};_baf .moveText (_dbef ,_ebcd );case "\u0054\u0044":if _dfaf ,_fgbb :=_baf .checkOp (_cgde ,2,true );!_dfaf {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fgbb );return _fgbb ;
};_gcea ,_ffbd ,_ecee :=_bcaeg (_cgde .Params );if _ecee !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ecee );return _ecee ;};_baf .moveTextSetLeading (_gcea ,_ffbd );case "\u0054\u006a":if _dcgb ,_bbff :=_baf .checkOp (_cgde ,1,true );
!_dcgb {_g .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0054\u006a\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0065\u0072\u0072\u003d%\u0076",_cgde ,_bbff );return _bbff ;};_dad :=_cg .TraceToDirectObject (_cgde .Params [0]);_dfcb ,_edg :=_cg .GetStringBytes (_dad );
if !_edg {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020T\u006a\u0020o\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074S\u0074\u0072\u0069\u006e\u0067\u0042\u0079\u0074\u0065\u0073\u0020\u0066a\u0069\u006c\u0065\u0064",_cgde );return _cg .ErrTypeError ;
};return _baf .showText (_dad ,_dfcb ,_fbag ,_gfd );case "\u0054\u004a":if _gbeef ,_cgeb :=_baf .checkOp (_cgde ,1,true );!_gbeef {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u004a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cgeb );return _cgeb ;
};_gec ,_bfff :=_cg .GetArray (_cgde .Params [0]);if !_bfff {_g .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0054\u004a\u0020\u006f\u0070\u003d\u0025s\u0020G\u0065t\u0041r\u0072\u0061\u0079\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_cgde );
return _ffeb ;};return _baf .showTextAdjusted (_gec ,_fbag ,_gfd );case "\u0027":if _gcf ,_dgc :=_baf .checkOp (_cgde ,1,true );!_gcf {_g .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u0027\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_dgc );return _dgc ;
};_feag :=_cg .TraceToDirectObject (_cgde .Params [0]);_bfffe ,_efd :=_cg .GetStringBytes (_feag );if !_efd {_g .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020'\u0020\u006f\u0070\u003d%s \u0047et\u0053\u0074\u0072\u0069\u006e\u0067\u0042yt\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064",_cgde );
return _cg .ErrTypeError ;};_baf .nextLine ();return _baf .showText (_feag ,_bfffe ,_fbag ,_gfd );case "\u0022":if _eef ,_cbgfg :=_baf .checkOp (_cgde ,3,true );!_eef {_g .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u0022\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cbgfg );
return _cbgfg ;};_gfbd ,_cdae ,_fgcdc :=_bcaeg (_cgde .Params [:2]);if _fgcdc !=nil {return _fgcdc ;};_fbca :=_cg .TraceToDirectObject (_cgde .Params [2]);_dab ,_bgg :=_cg .GetStringBytes (_fbca );if !_bgg {_g .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020\"\u0020\u006f\u0070\u003d%s \u0047et\u0053\u0074\u0072\u0069\u006e\u0067\u0042yt\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064",_cgde );
return _cg .ErrTypeError ;};_baf .setCharSpacing (_gfbd );_baf .setWordSpacing (_cdae );_baf .nextLine ();return _baf .showText (_fbca ,_dab ,_fbag ,_gfd );case "\u0054\u004c":_ecb ,_dfec :=_aee (_cgde );if _dfec !=nil {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u004c\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_dfec );
return _dfec ;};_baf .setTextLeading (_ecb );case "\u0054\u0063":_bccb ,_bfg :=_aee (_cgde );if _bfg !=nil {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0063\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_bfg );return _bfg ;};_baf .setCharSpacing (_bccb );
case "\u0054\u0066":if _dedc ,_egdg :=_baf .checkOp (_cgde ,2,true );!_dedc {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0066\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_egdg );return _egdg ;};_baba ,_aaad :=_cg .GetNameVal (_cgde .Params [0]);
if !_aaad {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_cgde );return _cg .ErrTypeError ;};_affd ,_eceb :=_cg .GetNumberAsFloat (_cgde .Params [1]);
if !_aaad {_g .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u0046\u006c\u006f\u0061\u0074\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065d\u002e\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cgde ,_eceb );
return _eceb ;};_eceb =_baf .setFont (_baba ,_affd );_baf ._cccd =_cc .Is (_eceb ,_cg .ErrNotSupported );if _eceb !=nil &&!_baf ._cccd {return _eceb ;};case "\u0054\u006d":if _efa ,_gaaf :=_baf .checkOp (_cgde ,6,true );!_efa {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u006d\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_gaaf );
return _gaaf ;};_fbce ,_gfe :=_cg .GetNumbersAsFloat (_cgde .Params );if _gfe !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_gfe );return _gfe ;};_baf .setTextMatrix (_fbce );case "\u0054\u0072":if _ecgg ,_eadf :=_baf .checkOp (_cgde ,1,true );
!_ecgg {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0072\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_eadf );return _eadf ;};_fgab ,_bcd :=_cg .GetIntVal (_cgde .Params [0]);if !_bcd {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0072\u0020\u006f\u0070\u003d\u0025\u0073 \u0047e\u0074\u0049\u006e\u0074\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_cgde );
return _cg .ErrTypeError ;};_baf .setTextRenderMode (_fgab );case "\u0054\u0073":if _fcdfc ,_cca :=_baf .checkOp (_cgde ,1,true );!_fcdfc {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0073\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cca );return _cca ;
};_dbgec ,_gfgc :=_cg .GetNumberAsFloat (_cgde .Params [0]);if _gfgc !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_gfgc );return _gfgc ;};_baf .setTextRise (_dbgec );case "\u0054\u0077":if _efdb ,_fcgd :=_baf .checkOp (_cgde ,1,true );
!_efdb {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fcgd );return _fcgd ;};_bbfa ,_fgfb :=_cg .GetNumberAsFloat (_cgde .Params [0]);if _fgfb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fgfb );
return _fgfb ;};_baf .setWordSpacing (_bbfa );case "\u0054\u007a":if _dbgee ,_bdbab :=_baf .checkOp (_cgde ,1,true );!_dbgee {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_bdbab );return _bdbab ;};_ggee ,_cfgd :=_cg .GetNumberAsFloat (_cgde .Params [0]);
if _cfgd !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cfgd );return _cfgd ;};_baf .setHorizScaling (_ggee );case "\u0063\u006d":if !_gcde {_bcf ._defg =_dce .CTM ;};if _bcf ._defg .Singular (){_cbff :=_dc .IdentityMatrix ().Translate (_bcf ._defg .Translation ());
_g .Log .Debug ("S\u0069n\u0067\u0075\u006c\u0061\u0072\u0020\u0063\u0074m\u003d\u0025\u0073\u2192%s",_bcf ._defg ,_cbff );_bcf ._defg =_cbff ;};if _fdee {_g .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_bcf ._defg );};case "\u006d":if len (_cgde .Params )!=2{_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006d\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_fdf );
return nil ;};_aed ,_aec :=_cg .GetNumbersAsFloat (_cgde .Params );if _aec !=nil {return _aec ;};_bcf .moveTo (_aed [0],_aed [1]);case "\u006c":if len (_cgde .Params )!=2{_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006c\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_fdf );
return nil ;};_adaa ,_dfcd :=_cg .GetNumbersAsFloat (_cgde .Params );if _dfcd !=nil {return _dfcd ;};_bcf .lineTo (_adaa [0],_adaa [1]);case "\u0063":if len (_cgde .Params )!=6{return _fdf ;};_gfbe ,_ffeg :=_cg .GetNumbersAsFloat (_cgde .Params );if _ffeg !=nil {return _ffeg ;
};_g .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020b\u0065\u007a\u0069\u0065\u0072 \u0070a\u0072a\u006d\u0073\u003a\u0020\u0025\u002e\u0032f",_gfbe );_bcf .cubicTo (_gfbe [0],_gfbe [1],_gfbe [2],_gfbe [3],_gfbe [4],_gfbe [5]);case "\u0076","\u0079":if len (_cgde .Params )!=4{return _fdf ;
};_dbdf ,_gaba :=_cg .GetNumbersAsFloat (_cgde .Params );if _gaba !=nil {return _gaba ;};_g .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020b\u0065\u007a\u0069\u0065\u0072 \u0070a\u0072a\u006d\u0073\u003a\u0020\u0025\u002e\u0032f",_dbdf );_bcf .quadraticTo (_dbdf [0],_dbdf [1],_dbdf [2],_dbdf [3]);
case "\u0068":_bcf .closePath ();case "\u0072\u0065":if len (_cgde .Params )!=4{return _fdf ;};_ggedd ,_bfd :=_cg .GetNumbersAsFloat (_cgde .Params );if _bfd !=nil {return _bfd ;};_bcf .drawRectangle (_ggedd [0],_ggedd [1],_ggedd [2],_ggedd [3]);_bcf .closePath ();
case "\u0053":_bcf .stroke (&_feac ._acecb );_bcf .clearPath ();case "\u0073":_bcf .closePath ();_bcf .stroke (&_feac ._acecb );_bcf .clearPath ();case "\u0046":_bcf .fill (&_feac ._fedc );_bcf .clearPath ();case "\u0066","\u0066\u002a":_bcf .closePath ();
_bcf .fill (&_feac ._fedc );_bcf .clearPath ();case "\u0042","\u0042\u002a":_bcf .fill (&_feac ._fedc );_bcf .stroke (&_feac ._acecb );_bcf .clearPath ();case "\u0062","\u0062\u002a":_bcf .closePath ();_bcf .fill (&_feac ._fedc );_bcf .stroke (&_feac ._acecb );
_bcf .clearPath ();case "\u006e":_bcf .clearPath ();case "\u0044\u006f":if len (_cgde .Params )==0{_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0058\u004fbj\u0065c\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0070\u0065\u0072\u0061n\u0064\u0020\u0066\u006f\u0072\u0020\u0044\u006f\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072.\u0020\u0047\u006f\u0074\u0020\u0025\u002b\u0076\u002e",_cgde .Params );
return _cg .ErrRangeError ;};_cec ,_agg :=_cg .GetName (_cgde .Params [0]);if !_agg {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0044\u006f\u0020\u006f\u0070e\u0072a\u0074\u006f\u0072\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006fp\u0065\u0072\u0061\u006e\u0064\u003a\u0020\u0025\u002b\u0076\u002e",_cgde .Params [0]);
return _cg .ErrTypeError ;};_ ,_bbfb :=_ebc .GetXObjectByName (*_cec );if _bbfb !=_aa .XObjectTypeForm {break ;};_eba ,_agg :=_gebd ._dd [_cec .String ()];if !_agg {_bbec ,_efad :=_ebc .GetXObjectFormByName (*_cec );if _efad !=nil {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_efad );
return _efad ;};_cdff ,_efad :=_bbec .GetContentStream ();if _efad !=nil {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_efad );return _efad ;};_cfd :=_bbec .Resources ;if _cfd ==nil {_cfd =_ebc ;};_dge :=_dce .CTM ;if _afdc ,_ebd :=_cg .GetArray (_bbec .Matrix );
_ebd {_bba ,_ccbf :=_afdc .GetAsFloat64Slice ();if _ccbf !=nil {return _ccbf ;};if len (_bba )!=6{return _fdf ;};_afe :=_dc .NewMatrix (_bba [0],_bba [1],_bba [2],_bba [3],_bba [4],_bba [5]);_dge =_dce .CTM .Mult (_afe );};_dfcg ,_adgc ,_ffbb ,_efad :=_gebd .extractPageText (string (_cdff ),_cfd ,_edcg .Mult (_dge ),_gedf +1,false );
if _efad !=nil {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_efad );return _efad ;};_eba =textResult {*_dfcg ,_adgc ,_ffbb };_gebd ._dd [_cec .String ()]=_eba ;};_bcf ._defg =_dce .CTM ;if _fdee {_g .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_bcf ._defg );
};_feac ._gee =append (_feac ._gee ,_eba ._affg ._gee ...);_feac ._acecb =append (_feac ._acecb ,_eba ._affg ._acecb ...);_feac ._fedc =append (_feac ._fedc ,_eba ._affg ._fedc ...);_ggec ._accd +=_eba ._aedb ;_ggec ._gfbdb +=_eba ._fgcb ;case "\u0072\u0067","\u0067","\u006b","\u0063\u0073","\u0073\u0063","\u0073\u0063\u006e":_baf ._bffb .ColorspaceNonStroking =_dce .ColorspaceNonStroking ;
_baf ._bffb .ColorNonStroking =_dce .ColorNonStroking ;case "\u0052\u0047","\u0047","\u004b","\u0043\u0053","\u0053\u0043","\u0053\u0043\u004e":_baf ._bffb .ColorspaceStroking =_dce .ColorspaceStroking ;_baf ._bffb .ColorStroking =_dce .ColorStroking ;
};return nil ;});_ffeb =_bga .Process (_fga );if _gebd ._fda !=nil &&_gebd ._fda .IncludeAnnotations &&!_gcde {for _ ,_eead :=range _gebd ._bfaa {_befcg ,_ffgb :=_cg .GetDict (_eead .AP );if !_ffgb {continue ;};_gbdd ,_ffgb :=_befcg .Get ("\u004e").(*_cg .PdfObjectStream );
if !_ffgb {continue ;};_bcgec ,_adda :=_cg .DecodeStream (_gbdd );if _adda !=nil {_g .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u006f\u006e\u0020\u0064\u0065c\u006f\u0064\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d:\u0020\u0025\u0076",_adda );
continue ;};_fafg :=_gbdd .PdfObjectDictionary .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s");_fegce ,_adda :=_aa .NewPdfPageResourcesFromDict (_fafg .(*_cg .PdfObjectDictionary ));if _adda !=nil {_g .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u006f\u006e\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073\u003a\u0020\u0025\u0076",_adda );
continue ;};_bbee :=_dc .IdentityMatrix ();_fegg ,_ffgb :=_gbdd .PdfObjectDictionary .Get ("\u004d\u0061\u0074\u0072\u0069\u0078").(*_cg .PdfObjectArray );if _ffgb {_gebb ,_bcfc :=_fegg .GetAsFloat64Slice ();if _bcfc !=nil {_g .Log .Debug ("\u0045\u0072\u0072or\u0020\u006f\u006e\u0020\u0067\u0065\u0074\u0074\u0069n\u0067 \u0066l\u006fa\u0074\u0036\u0034\u0020\u0073\u006c\u0069\u0063\u0065\u003a\u0020\u0025\u0076",_bcfc );
continue ;};if len (_gebb )!=6{_g .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u006d\u0061\u0074\u0072\u0069\u0078\u0020\u0073\u006ci\u0063\u0065\u0020l\u0065n\u0067\u0074\u0068");continue ;};_bbee =_dc .NewMatrix (_gebb [0],_gebb [1],_gebb [2],_gebb [3],_gebb [4],_gebb [5]);
};_ffcb ,_ffgb :=_gebd ._agad [_gbdd .String ()];if !_ffgb {_cgfe ,_deee ,_eddb ,_bggg :=_gebd .extractPageText (string (_bcgec ),_fegce ,_bbee ,_gedf +1,true );if _bggg !=nil {_g .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0065x\u0074\u0072a\u0063\u0074\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0074\u0065\u0078\u0074s\u003a\u0020\u0025\u0076",_bggg );
continue ;};_ffcb =textResult {*_cgfe ,_deee ,_eddb };_gebd ._agad [_gbdd .String ()]=_ffcb ;};_feac ._gee =append (_feac ._gee ,_ffcb ._affg ._gee ...);_feac ._acecb =append (_feac ._acecb ,_ffcb ._affg ._acecb ...);_feac ._fedc =append (_feac ._fedc ,_ffcb ._affg ._fedc ...);
_ggec ._accd +=_ffcb ._aedb ;_ggec ._gfbdb +=_ffcb ._fgcb ;};};return _feac ,_ggec ._accd ,_ggec ._gfbdb ,_ffeb ;};

// String returns a description of `k`.
func (_dgcc markKind )String ()string {_bacba ,_acbe :=_fffa [_dgcc ];if !_acbe {return _ad .Sprintf ("\u004e\u006f\u0074\u0020\u0061\u0020\u006d\u0061\u0072k\u003a\u0020\u0025\u0064",_dgcc );};return _bacba ;};func _bcaec (_accae string ,_ccce []rulingList ){_g .Log .Info ("\u0024\u0024 \u0025\u0064\u0020g\u0072\u0069\u0064\u0073\u0020\u002d\u0020\u0025\u0073",len (_ccce ),_accae );
for _bedaee ,_efega :=range _ccce {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_bedaee ,_efega .String ());};};

// Match defines the structure for each match, including pattern, indexes, and locations.
type Match struct{Pattern string ;Indexes [][]int ;Locations []Box ;};func _fbg (_bfbd ,_aac bounded )float64 {return _bfbd .bbox ().Llx -_aac .bbox ().Urx };func _ccg (_gce []Font ,_dca string )bool {for _ ,_gafa :=range _gce {if _gafa .FontName ==_dca {return true ;
};};return false ;};type compositeCell struct{_aa .PdfRectangle ;paraList ;};func (_fdfbge intSet )add (_adefbe int ){_fdfbge [_adefbe ]=struct{}{}};func (_eceg *textObject )showTextAdjusted (_cdege *_cg .PdfObjectArray ,_eeef int ,_aca string )error {_gcebc :=false ;
for _ ,_fade :=range _cdege .Elements (){switch _fade .(type ){case *_cg .PdfObjectFloat ,*_cg .PdfObjectInteger :_fgde ,_cafc :=_cg .GetNumberAsFloat (_fade );if _cafc !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0073\u0068\u006f\u0077\u0054\u0065\u0078t\u0041\u0064\u006a\u0075\u0073\u0074\u0065\u0064\u002e\u0020\u0042\u0061\u0064\u0020\u006e\u0075\u006d\u0065r\u0069\u0063\u0061\u006c\u0020a\u0072\u0067\u002e\u0020\u006f\u003d\u0025\u0073\u0020\u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fade ,_cdege );
return _cafc ;};_geac ,_gaec :=-_fgde *0.001*_eceg ._abf ._dced ,0.0;if _gcebc {_gaec ,_geac =_geac ,_gaec ;};_effa :=_fabb (_dc .Point {X :_geac ,Y :_gaec });_eceg ._ddaf .Concat (_effa );case *_cg .PdfObjectString :_cdgf :=_cg .TraceToDirectObject (_fade );
_cfcc ,_aaae :=_cg .GetStringBytes (_cdgf );if !_aaae {_g .Log .Trace ("s\u0068\u006f\u0077\u0054\u0065\u0078\u0074\u0041\u0064j\u0075\u0073\u0074\u0065\u0064\u003a\u0020Ba\u0064\u0020\u0073\u0074r\u0069\u006e\u0067\u0020\u0061\u0072\u0067\u002e\u0020o=\u0025\u0073 \u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fade ,_cdege );
return _cg .ErrTypeError ;};_eceg .renderText (_cdgf ,_cfcc ,_eeef ,_aca );default:_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0073\u0068\u006f\u0077\u0054\u0065\u0078\u0074A\u0064\u006a\u0075\u0073\u0074\u0065\u0064\u002e\u0020\u0055\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065\u0020\u0028%T\u0029\u0020\u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fade ,_cdege );
return _cg .ErrTypeError ;};};return nil ;};

// String returns a description of `w`.
func (_bedee *textWord )String ()string {return _ad .Sprintf ("\u0025\u002e2\u0066\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\"%\u0073\u0022",_bedee ._abace ,_bedee .PdfRectangle ,_bedee ._cacfc ,_bedee ._ecegc );
};func _aeeb (_bgdcc []int )[]int {_feec :=make ([]int ,len (_bgdcc ));for _feea ,_aabge :=range _bgdcc {_feec [len (_bgdcc )-1-_feea ]=_aabge ;};return _feec ;};

// Text returns the extracted page text.
func (_bdea PageText )Text ()string {return _bdea ._cecf };func (_cgga rulingList )mergePrimary ()float64 {_gbdf :=_cgga [0]._eced ;for _ ,_dfbb :=range _cgga [1:]{_gbdf +=_dfbb ._eced ;};return _gbdf /float64 (len (_cgga ));};func (_bfabe *textTable )newTablePara ()*textPara {_beab :=_bfabe .computeBbox ();
_ccee :=&textPara {PdfRectangle :_beab ,_fcagg :_beab ,_eddc :_bfabe };if _bffae {_g .Log .Info ("\u006e\u0065w\u0054\u0061\u0062l\u0065\u0050\u0061\u0072\u0061\u003a\u0020\u0025\u0073",_ccee );};return _ccee ;};func (_gfcba *compositeCell )updateBBox (){for _ ,_afbd :=range _gfcba .paraList {_gfcba .PdfRectangle =_bdda (_gfcba .PdfRectangle ,_afbd .PdfRectangle );
};};func (_dbcfa compositeCell )hasLines (_bgacb []*textLine )bool {for _bbgef ,_bdfcg :=range _bgacb {_fbbd :=_afgf (_dbcfa .PdfRectangle ,_bdfcg .PdfRectangle );if _bffae {_ad .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u005e\u005e\u005e\u0069\u006e\u0074\u0065\u0072\u0073e\u0063t\u0073\u003d\u0025\u0074\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u000a",_fbbd ,_bbgef ,len (_bgacb ));
_ad .Printf ("\u0020\u0020\u0020\u0020  \u005e\u005e\u005e\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u003d\u0025s\u000a",_dbcfa );_ad .Printf ("\u0020 \u0020 \u0020\u0020\u0020\u006c\u0069\u006e\u0065\u003d\u0025\u0073\u000a",_bdfcg );};if _fbbd {return true ;
};};return false ;};func _ecfg (_agcb *list ,_bedg *string )string {_bbca :=_add .Split (_agcb ._cdfd ,"\u000a");_acagc :=&_add .Builder {};for _ ,_fgbc :=range _bbca {if _fgbc !=""{_acagc .WriteString (*_bedg );_acagc .WriteString (_fgbc );_acagc .WriteString ("\u000a");
};};return _acagc .String ();};func (_abe *imageExtractContext )extractInlineImage (_aag *_fc .ContentStreamInlineImage ,_agcg _fc .GraphicsState ,_eeag *_aa .PdfPageResources )error {_gbgb ,_dcfd :=_aag .ToImage (_eeag );if _dcfd !=nil {return _dcfd ;
};_efe ,_dcfd :=_aag .GetColorSpace (_eeag );if _dcfd !=nil {return _dcfd ;};if _efe ==nil {_efe =_aa .NewPdfColorspaceDeviceGray ();};_bfcb ,_dcfd :=_efe .ImageToRGB (*_gbgb );if _dcfd !=nil {return _dcfd ;};_bad :=ImageMark {Image :&_bfcb ,Width :_agcg .CTM .ScalingFactorX (),Height :_agcg .CTM .ScalingFactorY (),Angle :_agcg .CTM .Angle ()};
_bad .X ,_bad .Y =_agcg .CTM .Translation ();_abe ._fbb =append (_abe ._fbb ,_bad );_abe ._dcae ++;return nil ;};func _cefd (_ddb []*textWord ,_fffe float64 ,_ccda ,_dcdc rulingList )*wordBag {_cebaf :=_fcdbg (_ddb [0],_fffe ,_ccda ,_dcdc );for _ ,_dcea :=range _ddb [1:]{_cfeg :=_dadf (_dcea ._abace );
_cebaf ._cdaf [_cfeg ]=append (_cebaf ._cdaf [_cfeg ],_dcea );_cebaf .PdfRectangle =_bdda (_cebaf .PdfRectangle ,_dcea .PdfRectangle );};_cebaf .sort ();return _cebaf ;};func (_feecc *textWord )absorb (_bfefb *textWord ){_feecc .PdfRectangle =_bdda (_feecc .PdfRectangle ,_bfefb .PdfRectangle );
_feecc ._fcacg =append (_feecc ._fcacg ,_bfefb ._fcacg ...);};func (_fceacc gridTile )numBorders ()int {_eccgc :=0;if _fceacc ._geeegg {_eccgc ++;};if _fceacc ._ccge {_eccgc ++;};if _fceacc ._fcfc {_eccgc ++;};if _fceacc ._aaaff {_eccgc ++;};return _eccgc ;
};func (_gbaf rulingList )sort (){_f .Slice (_gbaf ,_gbaf .comp )};func _bagef (_cfeag _aa .PdfRectangle )*ruling {return &ruling {_cafff :_cggd ,_eced :_cfeag .Urx ,_decc :_cfeag .Lly ,_gcda :_cfeag .Ury };};type lineRuling struct{_fbfg rulingKind ;_ccfb markKind ;
_ed .Color ;_cfdad ,_bddd _dc .Point ;};func (_cfeeb paraList )log (_egfbc string ){if !_gacf {return ;};_g .Log .Info ("%\u0038\u0073\u003a\u0020\u0025\u0064 \u0070\u0061\u0072\u0061\u0073\u0020=\u003d\u003d\u003d\u003d\u003d\u003d\u002d-\u002d\u002d\u002d\u002d\u002d\u003d\u003d\u003d\u003d\u003d=\u003d",_egfbc ,len (_cfeeb ));
for _beaf ,_dccbg :=range _cfeeb {if _dccbg ==nil {continue ;};_gdcd :=_dccbg .text ();_baac :="\u0020\u0020";if _dccbg ._eddc !=nil {_baac =_ad .Sprintf ("\u005b%\u0064\u0078\u0025\u0064\u005d",_dccbg ._eddc ._ebdg ,_dccbg ._eddc ._gacfc );};_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0025s\u0020\u0025\u0071\u000a",_beaf ,_dccbg .PdfRectangle ,_baac ,_ecegb (_gdcd ,50));
};};func (_aadd *ruling )gridIntersecting (_cccgb *ruling )bool {return _dgbc (_aadd ._decc ,_cccgb ._decc )&&_dgbc (_aadd ._gcda ,_cccgb ._gcda );};func (_efdd *shapesState )fill (_fgdf *[]pathSection ){_gdda :=pathSection {_cdbe :_efdd ._agdg ,Color :_efdd ._baea .getFillColor ()};
*_fgdf =append (*_fgdf ,_gdda );if _ccccd {_aecb :=_gdda .bbox ();_ad .Printf ("\u0020 \u0020\u0020\u0046\u0049\u004c\u004c\u003a %\u0032\u0064\u0020\u0066\u0069\u006c\u006c\u0073\u0020\u0028\u0025\u0064\u0020\u006ee\u0077\u0029 \u0073\u0073\u003d%\u0073\u0020\u0063\u006f\u006c\u006f\u0072\u003d\u0025\u0033\u0076\u0020\u0025\u0036\u002e\u0032f\u003d\u00256.\u0032\u0066\u0078%\u0036\u002e\u0032\u0066\u000a",len (*_fgdf ),len (_gdda ._cdbe ),_efdd ,_gdda .Color ,_aecb ,_aecb .Width (),_aecb .Height ());
if _gbab {for _bccc ,_ccbbc :=range _gdda ._cdbe {_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_bccc ,_ccbbc );if _bccc ==10{break ;};};};};};func _eaag (_fegda *_aa .StructTreeRoot ,_acgc map[int ][]*textLine ,_ggge _cg .PdfObject )[]*list {if _fegda ==nil {_g .Log .Debug ("\u0062\u0075\u0069\u006c\u0064\u004c\u0069\u0073\u0074\u003a\u0020t\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0069\u0073 \u006e\u0069\u006c");
return nil ;};_aegbd :=[]*_aa .KValue {};var _eddgg func (_ecag *_aa .KValue );_eddgg =func (_bafe *_aa .KValue ){if _ceafg :=_bafe .GetKDict ();_ceafg !=nil {if _ceafg .S .String ()=="\u004c"{_aegbd =append (_aegbd ,_bafe );}else {for _ ,_decd :=range _ceafg .GetChildren (){_eddgg (_decd );
};};};};for _ ,_dgdd :=range _fegda .K {_bacb :=_aa .NewKValue ();_bacb .SetKDict (_dgdd );_eddgg (_bacb );};_aaaf :=_dcdg (_aegbd ,_acgc ,_ggge );var _adgda []*list ;for _ ,_bagfb :=range _aaaf {_aeba :=_cdgd (_bagfb );_adgda =append (_adgda ,_aeba ...);
};return _adgda ;};func (_bebgd paraList )findTables (_gccba []gridTiling )[]*textTable {_bebgd .addNeighbours ();_f .Slice (_bebgd ,func (_dfdec ,_fadeg int )bool {return _cdec (_bebgd [_dfdec ],_bebgd [_fadeg ])< 0});var _cbeb []*textTable ;if _ddfc {_cbcec :=_bebgd .findGridTables (_gccba );
_cbeb =append (_cbeb ,_cbcec ...);};if _gcbe {_eeddb :=_bebgd .findTextTables ();_cbeb =append (_cbeb ,_eeddb ...);};return _cbeb ;};func (_gedeg paraList )findGridTables (_begce []gridTiling )[]*textTable {if _bffae {_g .Log .Info ("\u0066i\u006e\u0064\u0047\u0072\u0069\u0064\u0054\u0061\u0062\u006c\u0065s\u003a\u0020\u0025\u0064\u0020\u0070\u0061\u0072\u0061\u0073",len (_gedeg ));
for _gabd ,_dcdb :=range _gedeg {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gabd ,_dcdb );};};var _deda []*textTable ;for _bdggc ,_gdgb :=range _begce {_fddf ,_fgccb :=_gedeg .findTableGrid (_gdgb );if _fddf !=nil {_fddf .log (_ad .Sprintf ("\u0066\u0069\u006e\u0064Ta\u0062\u006c\u0065\u0057\u0069\u0074\u0068\u0047\u0072\u0069\u0064\u0073\u003a\u0020%\u0064",_bdggc ));
_deda =append (_deda ,_fddf );_fddf .markCells ();};for _ecddg :=range _fgccb {_ecddg ._fdge =true ;};};if _bffae {_g .Log .Info ("\u0066i\u006e\u0064\u0047\u0072i\u0064\u0054\u0061\u0062\u006ce\u0073:\u0020%\u0064\u0020\u0074\u0061\u0062\u006c\u0065s",len (_deda ));
};return _deda ;};var (_gdbb =map[rune ]string {0x0060:"\u0300",0x02CB:"\u0300",0x0027:"\u0301",0x00B4:"\u0301",0x02B9:"\u0301",0x02CA:"\u0301",0x005E:"\u0302",0x02C6:"\u0302",0x007E:"\u0303",0x02DC:"\u0303",0x00AF:"\u0304",0x02C9:"\u0304",0x02D8:"\u0306",0x02D9:"\u0307",0x00A8:"\u0308",0x00B0:"\u030a",0x02DA:"\u030a",0x02BA:"\u030b",0x02DD:"\u030b",0x02C7:"\u030c",0x02C8:"\u030d",0x0022:"\u030e",0x02BB:"\u0312",0x02BC:"\u0313",0x0486:"\u0313",0x055A:"\u0313",0x02BD:"\u0314",0x0485:"\u0314",0x0559:"\u0314",0x02D4:"\u031d",0x02D5:"\u031e",0x02D6:"\u031f",0x02D7:"\u0320",0x02B2:"\u0321",0x00B8:"\u0327",0x02CC:"\u0329",0x02B7:"\u032b",0x02CD:"\u0331",0x005F:"\u0332",0x204E:"\u0359"};
);

// List returns all the list objects detected on the page.
// It detects all the bullet point Lists from a given pdf page and builds a slice of bullet list objects.
// A given bullet list object has a tree structure.
// Each bullet point list is extracted with the text content it contains and all the sub lists found under it as children in the tree.
// The rest content of the pdf is ignored and only text in the bullet point lists are extracted.
// The list extraction is done in two ways.
// 1. If the document is tagged then the lists are extracted using the tags provided in the document.
// 2. Otherwise the bullet lists are extracted from the raw text using regex matching.
// By default the document tag is used if available.
// However this can be disabled using `DisableDocumentTags` in the `Options` object.
// Sometimes disabling document tags option might give a better bullet list extraction if the document was tagged incorrectly.
//
//	    options := &Options{
//		     DisableDocumentTags: false, // this means use document tag if available
//	    }
//	    ex, err := NewWithOptions(page, options)
//	    // handle error
//	    pageText, _, _, err := ex.ExtractPageText()
//	    // handle error
//	    lists := pageText.List()
//	    txt := lists.Text()
func (_cffb PageText )List ()lists {_febc :=!_cffb ._cfcf ._cfdd ;_cgef :=_cffb .getParagraphs ();_eegg :=_cgef .list ();if _cffb ._bgac !=nil &&_febc {_dgfc :=_begf (&_cgef );if len (_cffb ._bgac .K )==0{_g .Log .Debug ("\u004c\u0069\u0073\u0074\u003a\u0020\u0073t\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0064\u006f\u0065\u0073\u006e'\u0074\u0020\u0068\u0061\u0076e\u0020\u0061\u006e\u0079\u0020\u0063\u006f\u006e\u0074e\u006e\u0074\u002c\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0065\u0078\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0069\u006e\u0067\u0020\u006d\u0065\u0074\u0068\u006f\u0064\u0020\u0069\u006e\u0073\u0074\u0065\u0061\u0064\u002e");
return _eegg ;};_eegg =_eaag (_cffb ._bgac ,_dgfc ,_cffb ._cacg );};return _eegg ;};func (_fgac *stateStack )top ()*textState {if _fgac .empty (){return nil ;};return (*_fgac )[_fgac .size ()-1];};func (_gfdbe *textTable )subdivide ()*textTable {_gfdbe .logComposite ("\u0073u\u0062\u0064\u0069\u0076\u0069\u0064e");
_cfbbb :=_gfdbe .compositeRowCorridors ();_acfc :=_gfdbe .compositeColCorridors ();if _bffae {_g .Log .Info ("\u0073u\u0062\u0064i\u0076\u0069\u0064\u0065:\u000a\u0009\u0072o\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s=\u0025\u0073\u000a\t\u0063\u006fl\u0043\u006f\u0072\u0072\u0069\u0064o\u0072\u0073=\u0025\u0073",_bgaag (_cfbbb ),_bgaag (_acfc ));
};if len (_cfbbb )==0||len (_acfc )==0{return _gfdbe ;};_dbgbd (_cfbbb );_dbgbd (_acfc );if _bffae {_g .Log .Info ("\u0073\u0075\u0062\u0064\u0069\u0076\u0069\u0064\u0065\u0020\u0066\u0069\u0078\u0065\u0064\u003a\u000a\u0009r\u006f\u0077\u0043\u006f\u0072\u0072\u0069d\u006f\u0072\u0073\u003d\u0025\u0073\u000a\u0009\u0063\u006f\u006cC\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u003d\u0025\u0073",_bgaag (_cfbbb ),_bgaag (_acfc ));
};_cace ,_dfeca :=_accdg (_gfdbe ._gacfc ,_cfbbb );_cbbgf ,_bceda :=_accdg (_gfdbe ._ebdg ,_acfc );_bfeb :=make (map[uint64 ]*textPara ,_bceda *_dfeca );_bgaae :=&textTable {PdfRectangle :_gfdbe .PdfRectangle ,_ccfad :_gfdbe ._ccfad ,_gacfc :_dfeca ,_ebdg :_bceda ,_fdgeg :_bfeb };
if _bffae {_g .Log .Info ("\u0073\u0075b\u0064\u0069\u0076\u0069\u0064\u0065\u003a\u0020\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u0020\u0063\u0065\u006c\u006c\u0073\u003d\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u000a"+"\u0009\u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s\u003d\u0025\u0073\u000a"+"\u0009\u0063\u006f\u006c\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s\u003d\u0025\u0073\u000a"+"\u0009\u0079\u004f\u0066\u0066\u0073\u0065\u0074\u0073=\u0025\u002b\u0076\u000a"+"\u0009\u0078\u004f\u0066\u0066\u0073\u0065\u0074\u0073\u003d\u0025\u002b\u0076",_gfdbe ._ebdg ,_gfdbe ._gacfc ,_bceda ,_dfeca ,_bgaag (_cfbbb ),_bgaag (_acfc ),_cace ,_cbbgf );
};for _geag :=0;_geag < _gfdbe ._gacfc ;_geag ++{_fecg :=_cace [_geag ];for _egbbb :=0;_egbbb < _gfdbe ._ebdg ;_egbbb ++{_egad :=_cbbgf [_egbbb ];if _bffae {_ad .Printf ("\u0025\u0036\u0064\u002c %\u0032\u0064\u003a\u0020\u0078\u0030\u003d\u0025\u0064\u0020\u0079\u0030\u003d\u0025d\u000a",_egbbb ,_geag ,_egad ,_fecg );
};_acbcf ,_abeb :=_gfdbe ._efcbeb [_cggdf (_egbbb ,_geag )];if !_abeb {continue ;};_fcfac :=_acbcf .split (_cfbbb [_geag ],_acfc [_egbbb ]);for _egdgg :=0;_egdgg < _fcfac ._gacfc ;_egdgg ++{for _egdeg :=0;_egdeg < _fcfac ._ebdg ;_egdeg ++{_gfea :=_fcfac .get (_egdeg ,_egdgg );
_bgaae .put (_egad +_egdeg ,_fecg +_egdgg ,_gfea );if _bffae {_ad .Printf ("\u0025\u0038\u0064\u002c\u0020\u0025\u0032\u0064\u003a\u0020\u0025\u0073\u000a",_egad +_egdeg ,_fecg +_egdgg ,_gfea );};};};};};return _bgaae ;};func (_dfaed *stateStack )pop ()*textState {if _dfaed .empty (){return nil ;
};_feba :=*(*_dfaed )[len (*_dfaed )-1];*_dfaed =(*_dfaed )[:len (*_dfaed )-1];return &_feba ;};type gridTile struct{_aa .PdfRectangle ;_aaaff ,_geeegg ,_fcfc ,_ccge bool ;};func (_fcef *textTable )toTextTable ()TextTable {if _bffae {_g .Log .Info ("t\u006fT\u0065\u0078\u0074\u0054\u0061\u0062\u006c\u0065:\u0020\u0025\u0064\u0020x \u0025\u0064",_fcef ._ebdg ,_fcef ._gacfc );
};_acgd :=make ([][]TableCell ,_fcef ._gacfc );for _baec :=0;_baec < _fcef ._gacfc ;_baec ++{_acgd [_baec ]=make ([]TableCell ,_fcef ._ebdg );for _defd :=0;_defd < _fcef ._ebdg ;_defd ++{_fgabb :=_fcef .get (_defd ,_baec );if _fgabb ==nil {continue ;};
_gefd (_fgabb ._fdfb );if _bffae {_ad .Printf ("\u0025\u0034\u0064 \u0025\u0032\u0064\u003a\u0020\u0025\u0073\u000a",_defd ,_baec ,_fgabb );};_acgd [_baec ][_defd ].Text =_fgabb .text ();_gaced :=0;_acgd [_baec ][_defd ].Marks ._edgd =_fgabb .toTextMarks (&_gaced );
};};_gfbab :=TextTable {W :_fcef ._ebdg ,H :_fcef ._gacfc ,Cells :_acgd };_gfbab .PdfRectangle =_fcef .bbox ();return _gfbab ;};func (_affee rulingList )aligned ()bool {if len (_affee )< 2{return false ;};_ggfg :=make (map[*ruling ]int );_ggfg [_affee [0]]=0;
for _ ,_addfg :=range _affee [1:]{_abce :=false ;for _geab :=range _ggfg {if _addfg .gridIntersecting (_geab ){_ggfg [_geab ]++;_abce =true ;break ;};};if !_abce {_ggfg [_addfg ]=0;};};_dgae :=0;for _ ,_fdeea :=range _ggfg {if _fdeea ==0{_dgae ++;};};_feged :=float64 (_dgae )/float64 (len (_affee ));
_abbae :=_feged <=1.0-_gcbd ;if _ccccd {_g .Log .Info ("\u0061\u006c\u0069\u0067\u006e\u0065\u0064\u003d\u0025\u0074\u0020\u0075\u006em\u0061\u0074\u0063\u0068\u0065\u0064=\u0025\u002e\u0032\u0066\u003d\u0025\u0064\u002f\u0025\u0064\u0020\u0076\u0065c\u0073\u003d\u0025\u0073",_abbae ,_feged ,_dgae ,len (_affee ),_affee .String ());
};return _abbae ;};func (_fca *PageFonts )extractPageResourcesToFont (_agae *_aa .PdfPageResources )error {if _agae .Font ==nil {return _cc .New (_cab );};_eeab ,_feb :=_cg .GetDict (_agae .Font );if !_feb {return _cc .New (_bdb );};for _ ,_ffb :=range _eeab .Keys (){var (_agbe =true ;
_dec []byte ;_fcd string ;);_accg ,_gbg :=_agae .GetFontByName (_ffb );if !_gbg {return _cc .New (_ab );};_abg ,_bed :=_aa .NewPdfFontFromPdfObject (_accg );if _bed !=nil {return _bed ;};_fgg :=_abg .FontDescriptor ();_agc :=_abg .FontDescriptor ().FontName .String ();
_fcc :=_abg .Subtype ();if _ccg (_fca .Fonts ,_agc ){continue ;};if len (_abg .ToUnicode ())==0{_agbe =false ;};if _fgg .FontFile !=nil {if _fdbe ,_cgda :=_cg .GetStream (_fgg .FontFile );_cgda {_dec ,_bed =_cg .DecodeStream (_fdbe );if _bed !=nil {return _bed ;
};_fcd =_agc +"\u002e\u0070\u0066\u0062";};}else if _fgg .FontFile2 !=nil {if _dg ,_bef :=_cg .GetStream (_fgg .FontFile2 );_bef {_dec ,_bed =_cg .DecodeStream (_dg );if _bed !=nil {return _bed ;};_fcd =_agc +"\u002e\u0074\u0074\u0066";};}else if _fgg .FontFile3 !=nil {if _cfe ,_faf :=_cg .GetStream (_fgg .FontFile3 );
_faf {_dec ,_bed =_cg .DecodeStream (_cfe );if _bed !=nil {return _bed ;};_fcd =_agc +"\u002e\u0063\u0066\u0066";};};if len (_fcd )< 1{_g .Log .Debug (_ffea );};_dfe :=Font {FontName :_agc ,PdfFont :_abg ,IsCID :_abg .IsCID (),IsSimple :_abg .IsSimple (),ToUnicode :_agbe ,FontType :_fcc ,FontData :_dec ,FontFileName :_fcd ,FontDescriptor :_fgg };
_fca .Fonts =append (_fca .Fonts ,_dfe );};return nil ;};

// Len returns the number of TextMarks in `ma`.
func (_bddb *TextMarkArray )Len ()int {if _bddb ==nil {return 0;};return len (_bddb ._edgd );};var _afc =false ;func (_ebeef *textObject )showText (_aagf _cg .PdfObject ,_gceb []byte ,_dgdg int ,_cddd string )error {return _ebeef .renderText (_aagf ,_gceb ,_dgdg ,_cddd );
};

// String returns a description of `l`.
func (_cacf *textLine )String ()string {return _ad .Sprintf ("\u0025\u002e2\u0066\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\"%\u0073\u0022",_cacf ._agcc ,_cacf .PdfRectangle ,_cacf ._cadd ,_cacf .text ());
};func (_accag rulingList )log (_fage string ){if !_ccccd {return ;};_g .Log .Info ("\u0023\u0023\u0023\u0020\u0025\u0031\u0030\u0073\u003a\u0020\u0076\u0065c\u0073\u003d\u0025\u0073",_fage ,_accag .String ());for _fddaa ,_bgdcag :=range _accag {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_fddaa ,_bgdcag .String ());
};};

// Marks returns the TextMark collection for a page. It represents all the text on the page.
func (_ebb PageText )Marks ()*TextMarkArray {return &TextMarkArray {_edgd :_ebb ._eedcb }};func (_cgdf paraList )reorder (_abaeb []int ){_cbfg :=make (paraList ,len (_cgdf ));for _fegcg ,_geeg :=range _abaeb {_cbfg [_fegcg ]=_cgdf [_geeg ];};copy (_cgdf ,_cbfg );
};func _cdafd (_ceeg *_aa .Image ,_cacgg _ed .Color )_ff .Image {_fgged ,_eafg :=int (_ceeg .Width ),int (_ceeg .Height );_edcae :=_ff .NewRGBA (_ff .Rect (0,0,_fgged ,_eafg ));for _cegc :=0;_cegc < _eafg ;_cegc ++{for _addga :=0;_addga < _fgged ;_addga ++{_gadb ,_fcgdc :=_ceeg .ColorAt (_addga ,_cegc );
if _fcgdc !=nil {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_addga ,_cegc );
continue ;};_acbdb ,_ccbbce ,_ggeb ,_ :=_gadb .RGBA ();var _eebfa _ed .Color ;if _acbdb +_ccbbce +_ggeb ==0{_eebfa =_cacgg ;}else {_eebfa =_ed .Transparent ;};_edcae .Set (_addga ,_cegc ,_eebfa );};};return _edcae ;};func (_eeggd *textTable )getDown ()paraList {_dgbd :=make (paraList ,_eeggd ._ebdg );
for _geff :=0;_geff < _eeggd ._ebdg ;_geff ++{_cdeba :=_eeggd .get (_geff ,_eeggd ._gacfc -1)._fgfd ;if _cdeba .taken (){return nil ;};_dgbd [_geff ]=_cdeba ;};for _feeba :=0;_feeba < _eeggd ._ebdg -1;_feeba ++{if _dgbd [_feeba ]._caec !=_dgbd [_feeba +1]{return nil ;
};};return _dgbd ;};func _cfdfb (_feab float64 )float64 {return _fabad *_ea .Round (_feab /_fabad )};func (_aefe *wordBag )scanBand (_aeeg string ,_bbgbe *wordBag ,_fcff func (_deebf *wordBag ,_cfee *textWord )bool ,_ccbe ,_bfceb ,_ecbac float64 ,_gbbe ,_fgeb bool )int {_efec :=_bbgbe ._abcf ;
var _ccgf map[int ]map[*textWord ]struct{};if !_gbbe {_ccgf =_aefe .makeRemovals ();};_dacgc :=_dfff *_efec ;_gead :=0;for _ ,_faeb :=range _aefe .depthBand (_ccbe -_dacgc ,_bfceb +_dacgc ){if len (_aefe ._cdaf [_faeb ])==0{continue ;};for _ ,_ecgf :=range _aefe ._cdaf [_faeb ]{if !(_ccbe -_dacgc <=_ecgf ._abace &&_ecgf ._abace <=_bfceb +_dacgc ){continue ;
};if !_fcff (_bbgbe ,_ecgf ){continue ;};_ffgg :=2.0*_ea .Abs (_ecgf ._cacfc -_bbgbe ._abcf )/(_ecgf ._cacfc +_bbgbe ._abcf );_gdgg :=_ea .Max (_ecgf ._cacfc /_bbgbe ._abcf ,_bbgbe ._abcf /_ecgf ._cacfc );_gacda :=_ea .Min (_ffgg ,_gdgg );if _ecbac > 0&&_gacda > _ecbac {continue ;
};if _bbgbe .blocked (_ecgf ){continue ;};if !_gbbe {_bbgbe .pullWord (_ecgf ,_faeb ,_ccgf );};_gead ++;if !_fgeb {if _ecgf ._abace < _ccbe {_ccbe =_ecgf ._abace ;};if _ecgf ._abace > _bfceb {_bfceb =_ecgf ._abace ;};};if _gbbe {break ;};};};if !_gbbe {_aefe .applyRemovals (_ccgf );
};return _gead ;};func (_gccc *imageExtractContext )extractContentStreamImages (_gbce string ,_ffc *_aa .PdfPageResources )error {_dbge :=_fc .NewContentStreamParser (_gbce );_feg ,_abb :=_dbge .Parse ();if _abb !=nil {return _abb ;};if _gccc ._gafab ==nil {_gccc ._gafab =map[*_cg .PdfObjectStream ]*cachedImage {};
};if _gccc ._cag ==nil {_gccc ._cag =&ImageExtractOptions {};};_edd :=_fc .NewContentStreamProcessor (*_feg );_edd .AddHandler (_fc .HandlerConditionEnumAllOperands ,"",_gccc .processOperand );return _edd .Process (_ffc );};func _cdcb (_ddcc _aa .PdfRectangle )textState {return textState {_beb :100,_cbffg :RenderModeFill ,_bcgef :_ddcc };
};func (_aabe pathSection )bbox ()_aa .PdfRectangle {_gba :=_aabe ._cdbe [0]._eae [0];_faff :=_aa .PdfRectangle {Llx :_gba .X ,Urx :_gba .X ,Lly :_gba .Y ,Ury :_gba .Y };_abfa :=func (_babg _dc .Point ){if _babg .X < _faff .Llx {_faff .Llx =_babg .X ;}else if _babg .X > _faff .Urx {_faff .Urx =_babg .X ;
};if _babg .Y < _faff .Lly {_faff .Lly =_babg .Y ;}else if _babg .Y > _faff .Ury {_faff .Ury =_babg .Y ;};};for _ ,_eaa :=range _aabe ._cdbe [0]._eae [1:]{_abfa (_eaa );};for _ ,_afbb :=range _aabe ._cdbe [1:]{for _ ,_cfbb :=range _afbb ._eae {_abfa (_cfbb );
};};return _faff ;};func _cgca (_faeg []*textLine ,_fggga ,_fafe float64 )[]*textLine {var _aebg []*textLine ;for _ ,_effce :=range _faeg {if _fggga ==-1{if _effce ._agcc > _fafe {_aebg =append (_aebg ,_effce );};}else {if _effce ._agcc > _fafe &&_effce ._agcc < _fggga {_aebg =append (_aebg ,_effce );
};};};return _aebg ;};func (_dcgbe *shapesState )quadraticTo (_fcac ,_cgec ,_fef ,_fdgd float64 ){if _fdee {_g .Log .Info ("\u0071\u0075\u0061d\u0072\u0061\u0074\u0069\u0063\u0054\u006f\u003a");};_dcgbe .addPoint (_fef ,_fdgd );};func (_dacbd rulingList )snapToGroupsDirection ()rulingList {_dacbd .sortStrict ();
_affec :=make (map[*ruling ]rulingList ,len (_dacbd ));_fdbdf :=_dacbd [0];_begd :=func (_ddggc *ruling ){_fdbdf =_ddggc ;_affec [_fdbdf ]=rulingList {_ddggc }};_begd (_dacbd [0]);for _ ,_afeaf :=range _dacbd [1:]{if _afeaf ._eced < _fdbdf ._eced -_fdag {_g .Log .Error ("\u0073\u006e\u0061\u0070T\u006f\u0047\u0072\u006f\u0075\u0070\u0073\u0044\u0069r\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0057\u0072\u006f\u006e\u0067\u0020\u0070\u0072\u0069\u006da\u0072\u0079\u0020\u006f\u0072d\u0065\u0072\u002e\u000a\u0009\u0076\u0030\u003d\u0025\u0073\u000a\u0009\u0020\u0076\u003d\u0025\u0073",_fdbdf ,_afeaf );
};if _afeaf ._eced > _fdbdf ._eced +_cafa {_begd (_afeaf );}else {_affec [_fdbdf ]=append (_affec [_fdbdf ],_afeaf );};};_aege :=make (map[*ruling ]float64 ,len (_affec ));_cadf :=make (map[*ruling ]*ruling ,len (_dacbd ));for _cfae ,_afbcc :=range _affec {_aege [_cfae ]=_afbcc .mergePrimary ();
for _ ,_dfad :=range _afbcc {_cadf [_dfad ]=_cfae ;};};for _ ,_debd :=range _dacbd {_debd ._eced =_aege [_cadf [_debd ]];};_bfgge :=make (rulingList ,0,len (_dacbd ));for _ ,_cbgg :=range _affec {_ggde :=_cbgg .splitSec ();for _fgbbbf ,_gfgg :=range _ggde {_feced :=_gfgg .merge ();
if len (_bfgge )> 0{_dcabe :=_bfgge [len (_bfgge )-1];if _dcabe .alignsPrimary (_feced )&&_dcabe .alignsSec (_feced ){_g .Log .Error ("\u0073\u006e\u0061\u0070\u0054\u006fG\u0072\u006f\u0075\u0070\u0073\u0044\u0069\u0072\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0044\u0075\u0070\u006ci\u0063\u0061\u0074\u0065\u0020\u0069\u003d\u0025\u0064\u000a\u0009\u0077\u003d\u0025s\u000a\t\u0076\u003d\u0025\u0073",_fgbbbf ,_dcabe ,_feced );
continue ;};};_bfgge =append (_bfgge ,_feced );};};_bfgge .sortStrict ();return _bfgge ;};type paraList []*textPara ;func _dcbd (_ebaef ,_adec int )int {if _ebaef < _adec {return _ebaef ;};return _adec ;};func (_fafba compositeCell )parasBBox ()(paraList ,_aa .PdfRectangle ){return _fafba .paraList ,_fafba .PdfRectangle ;
};func (_gfbc *textObject )newTextMark (_gadc string ,_aefc _dc .Matrix ,_aecba _dc .Point ,_bfggg float64 ,_ccccdg *_aa .PdfFont ,_bbaa float64 ,_dfegc ,_afba _ed .Color ,_cgefd _cg .PdfObject ,_eddaa []string ,_bce int ,_fcbd int )(textMark ,bool ){_acbcc :=_aefc .Angle ();
_eagfbb :=_fafc (_acbcc ,_fgaed );var _ddfg float64 ;if _eagfbb %180!=90{_ddfg =_aefc .ScalingFactorY ();}else {_ddfg =_aefc .ScalingFactorX ();};_gbceb :=_agdcf (_aefc );_abdd :=_aa .PdfRectangle {Llx :_gbceb .X ,Lly :_gbceb .Y ,Urx :_aecba .X ,Ury :_aecba .Y };
switch _eagfbb %360{case 90:_abdd .Urx -=_ddfg ;case 180:_abdd .Ury -=_ddfg ;case 270:_abdd .Urx +=_ddfg ;case 0:_abdd .Ury +=_ddfg ;default:_eagfbb =0;_abdd .Ury +=_ddfg ;};if _abdd .Llx > _abdd .Urx {_abdd .Llx ,_abdd .Urx =_abdd .Urx ,_abdd .Llx ;};
if _abdd .Lly > _abdd .Ury {_abdd .Lly ,_abdd .Ury =_abdd .Ury ,_abdd .Lly ;};_ecgbd :=true ;if _gfbc ._addg ._fgc .Width ()> 0{_baeb ,_afgd :=_cgeed (_abdd ,_gfbc ._addg ._fgc );if !_afgd {_ecgbd =false ;_g .Log .Debug ("\u0054\u0065\u0078\u0074\u0020m\u0061\u0072\u006b\u0020\u006f\u0075\u0074\u0073\u0069\u0064\u0065\u0020\u0070a\u0067\u0065\u002e\u0020\u0062\u0062\u006f\u0078\u003d\u0025\u0067\u0020\u006d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u003d\u0025\u0067\u0020\u0074\u0065\u0078\u0074\u003d\u0025q",_abdd ,_gfbc ._addg ._fgc ,_gadc );
};_abdd =_baeb ;};_adcd :=_abdd ;_dcfbe :=_gfbc ._addg ._fgc ;switch _eagfbb %360{case 90:_dcfbe .Urx ,_dcfbe .Ury =_dcfbe .Ury ,_dcfbe .Urx ;_adcd =_aa .PdfRectangle {Llx :_dcfbe .Urx -_abdd .Ury ,Urx :_dcfbe .Urx -_abdd .Lly ,Lly :_abdd .Llx ,Ury :_abdd .Urx };
case 180:_adcd =_aa .PdfRectangle {Llx :_dcfbe .Urx -_abdd .Llx ,Urx :_dcfbe .Urx -_abdd .Urx ,Lly :_dcfbe .Ury -_abdd .Lly ,Ury :_dcfbe .Ury -_abdd .Ury };case 270:_dcfbe .Urx ,_dcfbe .Ury =_dcfbe .Ury ,_dcfbe .Urx ;_adcd =_aa .PdfRectangle {Llx :_abdd .Ury ,Urx :_abdd .Lly ,Lly :_dcfbe .Ury -_abdd .Llx ,Ury :_dcfbe .Ury -_abdd .Urx };
};if _adcd .Llx > _adcd .Urx {_adcd .Llx ,_adcd .Urx =_adcd .Urx ,_adcd .Llx ;};if _adcd .Lly > _adcd .Ury {_adcd .Lly ,_adcd .Ury =_adcd .Ury ,_adcd .Lly ;};_ffgba :=textMark {_gacc :_gadc ,PdfRectangle :_adcd ,_ebac :_abdd ,_acea :_ccccdg ,_fadfd :_ddfg ,_acbd :_bbaa ,_ecef :_aefc ,_fcfa :_aecba ,_afbe :_eagfbb ,_fbbbc :_dfegc ,_abag :_afba ,_eecd :_cgefd ,_aacc :_eddaa ,Th :_gfbc ._abf ._beb ,Tw :_gfbc ._abf ._bag ,_ecfc :_fcbd ,_dcdga :_bce };
if _ffdc {_g .Log .Info ("n\u0065\u0077\u0054\u0065\u0078\u0074M\u0061\u0072\u006b\u003a\u0020\u0073t\u0061\u0072\u0074\u003d\u0025\u002e\u0032f\u0020\u0065\u006e\u0064\u003d\u0025\u002e\u0032\u0066\u0020%\u0073",_gbceb ,_aecba ,_ffgba .String ());};return _ffgba ,_ecgbd ;
};

// Append appends `mark` to the mark array.
func (_fdfc *TextMarkArray )Append (mark TextMark ){_fdfc ._edgd =append (_fdfc ._edgd ,mark )};type subpath struct{_eae []_dc .Point ;_bebg bool ;};func (_gdbaf paraList )findTableGrid (_beeg gridTiling )(*textTable ,map[*textPara ]struct{}){_cfca :=len (_beeg ._ddfcb );
_bfgab :=len (_beeg ._bced );_ecca :=textTable {_ccfad :true ,_ebdg :_cfca ,_gacfc :_bfgab ,_fdgeg :make (map[uint64 ]*textPara ,_cfca *_bfgab ),_efcbeb :make (map[uint64 ]compositeCell ,_cfca *_bfgab )};_ecca .PdfRectangle =_beeg .PdfRectangle ;_fbbbf :=make (map[*textPara ]struct{});
_ccdbf :=int ((1.0-_bged )*float64 (_cfca *_bfgab ));_cdecc :=0;if _bcbed {_g .Log .Info ("\u0066\u0069\u006e\u0064Ta\u0062\u006c\u0065\u0047\u0072\u0069\u0064\u003a\u0020\u0025\u0064\u0020\u0078\u0020%\u0064",_cfca ,_bfgab );};for _cefe ,_ccegc :=range _beeg ._bced {_bfgb ,_dfbe :=_beeg ._ebegg [_ccegc ];
if !_dfbe {continue ;};for _eeaf ,_deecd :=range _beeg ._ddfcb {_bffg ,_gaacc :=_bfgb [_deecd ];if !_gaacc {continue ;};_daff :=_gdbaf .inTile (_bffg );if len (_daff )==0{_cdecc ++;if _cdecc > _ccdbf {if _bcbed {_g .Log .Info ("\u0021\u006e\u0075m\u0045\u006d\u0070\u0074\u0079\u003d\u0025\u0064",_cdecc );
};return nil ,nil ;};}else {_ecca .putComposite (_eeaf ,_cefe ,_daff ,_bffg .PdfRectangle );for _ ,_adfbe :=range _daff {_fbbbf [_adfbe ]=struct{}{};};};};};_bdegd :=0;for _ddgdf :=0;_ddgdf < _cfca ;_ddgdf ++{_gcfa :=_ecca .get (_ddgdf ,0);if _gcfa ==nil ||!_gcfa ._caaa {_bdegd ++;
};};if _bdegd ==0{if _bcbed {_g .Log .Info ("\u0021\u006e\u0075m\u0048\u0065\u0061\u0064\u0065\u0072\u003d\u0030");};return nil ,nil ;};_dffa :=_ecca .reduceTiling (_beeg ,_acecg );_dffa =_dffa .subdivide ();return _dffa ,_fbbbf ;};

// PageFonts represents extracted fonts on a PDF page.
type PageFonts struct{Fonts []Font ;};func (_beabg *textTable )put (_gdfc ,_dbba int ,_facd *textPara ){_beabg ._fdgeg [_cggdf (_gdfc ,_dbba )]=_facd ;};func _cdca (_affdg *textLine ,_dbggb []*textLine ,_dgac ,_dfcdd float64 )[]*textLine {_eccfc :=[]*textLine {};
for _ ,_edagd :=range _dbggb {if _edagd ._agcc >=_dgac {if _dfcdd !=-1&&_edagd ._agcc < _dfcdd {if _edagd .text ()!=_affdg .text (){if _ea .Round (_edagd .Llx )< _ea .Round (_affdg .Llx ){break ;};_eccfc =append (_eccfc ,_edagd );};}else if _dfcdd ==-1{if _edagd ._agcc ==_affdg ._agcc {if _edagd .text ()!=_affdg .text (){_eccfc =append (_eccfc ,_edagd );
};continue ;};_befde :=_bbgg (_affdg ,_dbggb );if _befde !=-1&&_edagd ._agcc <=_befde {_eccfc =append (_eccfc ,_edagd );};};};};return _eccfc ;};func (_ggda rulingList )connections (_ceef map[int ]intSet ,_defe int )intSet {_fbgg :=make (intSet );_ggdda :=make (intSet );
var _fgec func (int );_fgec =func (_acbg int ){if !_ggdda .has (_acbg ){_ggdda .add (_acbg );for _bcae :=range _ggda {if _ceef [_bcae ].has (_acbg ){_fbgg .add (_bcae );};};for _fbee :=range _ggda {if _fbgg .has (_fbee ){_fgec (_fbee );};};};};_fgec (_defe );
return _fbgg ;};func (_dfag *ruling )encloses (_fdeee ,_ebfff float64 )bool {return _dfag ._decc -_adag <=_fdeee &&_ebfff <=_dfag ._gcda +_adag ;};func _eecgf (_bbeef string )(string ,bool ){_bfee :=[]rune (_bbeef );if len (_bfee )!=1{return "",false ;
};_fcde ,_fbbc :=_gdbb [_bfee [0]];return _fcde ,_fbbc ;};func _cggdf (_cgfc ,_edccf int )uint64 {return uint64 (_cgfc )*0x1000000+uint64 (_edccf )};func _egce (_dffgb *wordBag ,_ggef *textWord ,_fegd float64 )bool {return _dffgb .Urx <=_ggef .Llx &&_ggef .Llx < _dffgb .Urx +_fegd ;
};func (_gadd *wordBag )depthBand (_edda ,_gaebd float64 )[]int {if len (_gadd ._cdaf )==0{return nil ;};return _gadd .depthRange (_gadd .getDepthIdx (_edda ),_gadd .getDepthIdx (_gaebd ));};func _bffc (_dcaea int ,_afbcb func (int ,int )bool )[]int {_eacac :=make ([]int ,_dcaea );
for _cccee :=range _eacac {_eacac [_cccee ]=_cccee ;};_f .Slice (_eacac ,func (_afgcf ,_dbgbc int )bool {return _afbcb (_eacac [_afgcf ],_eacac [_dbgbc ])});return _eacac ;};func _eebd (_gfebf _aa .PdfRectangle )*ruling {return &ruling {_cafff :_feed ,_eced :_gfebf .Ury ,_decc :_gfebf .Llx ,_gcda :_gfebf .Urx };
};func _bbage (_fgbeb float64 )bool {return _ea .Abs (_fgbeb )< _fdag };var _ae =[]string {"\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0053","\u0042","\u0053","\u0057\u0053","\u0042","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042","\u0042","\u0042","\u0053","\u0057\u0053","\u004f\u004e","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0045\u0053","\u0043\u0053","\u0045\u0053","\u0043\u0053","\u0043\u0053","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0043\u0053","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0043\u0053","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u0042\u004e","\u004f\u004e","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u004e","\u0045\u004e","\u004f\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0045\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c"};


// String returns a string descibing `i`.
func (_agac gridTile )String ()string {_efgb :=func (_cefb bool ,_bgdb string )string {if _cefb {return _bgdb ;};return "\u005f";};return _ad .Sprintf ("\u00256\u002e2\u0066\u0020\u0025\u0031\u0073%\u0031\u0073%\u0031\u0073\u0025\u0031\u0073",_agac .PdfRectangle ,_efgb (_agac ._geeegg ,"\u004c"),_efgb (_agac ._ccge ,"\u0052"),_efgb (_agac ._fcfc ,"\u0042"),_efgb (_agac ._aaaff ,"\u0054"));
};func (_cbbc *subpath )clear (){*_cbbc =subpath {}};func (_gcec *textObject )reset (){_gcec ._ddaf =_dc .IdentityMatrix ();_gcec ._bbbb =_dc .IdentityMatrix ();_gcec ._ebcg =nil ;};

// PageText represents the layout of text on a device page.
type PageText struct{_gee []*textMark ;_cecf string ;_eedcb []TextMark ;_gcee []TextTable ;_gff _aa .PdfRectangle ;_acecb []pathSection ;_fedc []pathSection ;_bgac *_aa .StructTreeRoot ;_cacg _cg .PdfObject ;_dacb *_fc .ContentStreamOperations ;_cfcf PageTextOptions ;
};func (_bbfd paraList )readBefore (_cgeea []int ,_accba ,_adfdf int )bool {_cedg ,_dagb :=_bbfd [_accba ],_bbfd [_adfdf ];if _ggbag (_cedg ,_dagb )&&_cedg .Lly > _dagb .Lly {return true ;};if !(_cedg ._fcagg .Urx < _dagb ._fcagg .Llx ){return false ;};
_dcef ,_fadd :=_cedg .Lly ,_dagb .Lly ;if _dcef > _fadd {_fadd ,_dcef =_dcef ,_fadd ;};_babcg :=_ea .Max (_cedg ._fcagg .Llx ,_dagb ._fcagg .Llx );_babga :=_ea .Min (_cedg ._fcagg .Urx ,_dagb ._fcagg .Urx );_bddc :=_bbfd .llyRange (_cgeea ,_dcef ,_fadd );
for _ ,_gdab :=range _bddc {if _gdab ==_accba ||_gdab ==_adfdf {continue ;};_fcgg :=_bbfd [_gdab ];if _fcgg ._fcagg .Llx <=_babga &&_babcg <=_fcgg ._fcagg .Urx {return false ;};};return true ;};func (_adaae *textObject )checkOp (_ggcb *_fc .ContentStreamOperation ,_fbcb int ,_gegf bool )(_ebaf bool ,_deac error ){if _adaae ==nil {var _eecb []_cg .PdfObject ;
if _fbcb > 0{_eecb =_ggcb .Params ;if len (_eecb )> _fbcb {_eecb =_eecb [:_fbcb ];};};_g .Log .Debug ("\u0025\u0023q \u006f\u0070\u0065r\u0061\u006e\u0064\u0020out\u0073id\u0065\u0020\u0074\u0065\u0078\u0074\u002e p\u0061\u0072\u0061\u006d\u0073\u003d\u0025+\u0076",_ggcb .Operand ,_eecb );
};if _fbcb >=0{if len (_ggcb .Params )!=_fbcb {if _gegf {_deac =_cc .New ("\u0069n\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0070\u0061r\u0061m\u0065t\u0065\u0072\u0020\u0063\u006f\u0075\u006et");};_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u0023\u0071\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020h\u0061\u0076\u0065\u0020\u0025\u0064\u0020i\u006e\u0070\u0075\u0074\u0020\u0070\u0061\u0072\u0061\u006d\u0073,\u0020\u0067\u006f\u0074\u0020\u0025\u0064\u0020\u0025\u002b\u0076",_ggcb .Operand ,_fbcb ,len (_ggcb .Params ),_ggcb .Params );
return false ,_deac ;};};return true ,nil ;};func _begf (_gdgef *paraList )map[int ][]*textLine {_bcfd :=map[int ][]*textLine {};for _ ,_aega :=range *_gdgef {for _ ,_ccad :=range _aega ._fdfb {if !_cdgfc (_ccad ){_g .Log .Debug ("g\u0072\u006f\u0075p\u004c\u0069\u006e\u0065\u0073\u003a\u0020\u0054\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u006c\u0069\u006e\u0065\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0073 \u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u006e\u0065 \u006d\u0063\u0069\u0064 \u006e\u0075\u006d\u0062e\u0072\u002e\u0020\u0049\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0073p\u006c\u0069\u0074\u002e");
continue ;};_fcea :=_ccad ._dbcg [0]._fcacg [0]._ecfc ;_bcfd [_fcea ]=append (_bcfd [_fcea ],_ccad );};if _aega ._eddc !=nil {_edag :=_aega ._eddc ._fdgeg ;for _ ,_fbcae :=range _edag {for _ ,_adege :=range _fbcae ._fdfb {if !_cdgfc (_adege ){_g .Log .Debug ("g\u0072\u006f\u0075p\u004c\u0069\u006e\u0065\u0073\u003a\u0020\u0054\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u006c\u0069\u006e\u0065\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0073 \u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u006e\u0065 \u006d\u0063\u0069\u0064 \u006e\u0075\u006d\u0062e\u0072\u002e\u0020\u0049\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0073p\u006c\u0069\u0074\u002e");
continue ;};_gfddc :=_adege ._dbcg [0]._fcacg [0]._ecfc ;_bcfd [_gfddc ]=append (_bcfd [_gfddc ],_adege );};};};};return _bcfd ;};func _ffae (_dgegc ,_acbc _aa .PdfRectangle )bool {return _dgegc .Lly <=_acbc .Ury &&_acbc .Lly <=_dgegc .Ury ;};

// StrokePath is a stroked path.
type StrokePath struct{Points []_dc .Point ;_ed .Color ;};func _accdg (_eeegb int ,_ecfde map[int ][]float64 )([]int ,int ){_dcbee :=make ([]int ,_eeegb );_ddbf :=0;for _fdefg :=0;_fdefg < _eeegb ;_fdefg ++{_dcbee [_fdefg ]=_ddbf ;_ddbf +=len (_ecfde [_fdefg ])+1;
};return _dcbee ,_ddbf ;};func (_facb *stateStack )empty ()bool {return len (*_facb )==0};type rulingKind int ;var (_ccc =_cc .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");_fdf =_cc .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072");
);func _gfaag (_dged *wordBag ,_baef float64 ,_fbab ,_egcg rulingList ,_ceafd bool )[]*wordBag {var _fdef []*wordBag ;for _ ,_fcbb :=range _dged .depthIndexes (){_eeca :=false ;for !_dged .empty (_fcbb ){_eaef :=_dged .firstReadingIndex (_fcbb );_cafba :=_dged .firstWord (_eaef );
_ecfd :=_fcdbg (_cafba ,_baef ,_fbab ,_egcg );_dged .removeWord (_cafba ,_eaef );if _dgb {_g .Log .Info ("\u0066\u0069\u0072\u0073\u0074\u0057\u006f\u0072\u0064\u0020\u005e\u005e^\u005e\u0020\u0025\u0073",_cafba .String ());};for _eebg :=true ;_eebg ;_eebg =_eeca {_eeca =false ;
_gebfa :=_ccdag *_ecfd ._abcf ;_eede :=_ffaa *_ecfd ._abcf ;if _ceafd {_eede =_ea .MaxFloat64 ;};_faed :=_abfea *_ecfd ._abcf ;if _dgb {_g .Log .Info ("\u0070a\u0072a\u0057\u006f\u0072\u0064\u0073\u0020\u0064\u0065\u0070\u0074\u0068 \u0025\u002e\u0032\u0066 \u002d\u0020\u0025\u002e\u0032f\u0020\u006d\u0061\u0078\u0049\u006e\u0074\u0072\u0061\u0044\u0065\u0070\u0074\u0068\u0047\u0061\u0070\u003d\u0025\u002e\u0032\u0066\u0020\u006d\u0061\u0078\u0049\u006e\u0074\u0072\u0061R\u0065\u0061\u0064\u0069\u006e\u0067\u0047\u0061p\u003d\u0025\u002e\u0032\u0066",_ecfd .minDepth (),_ecfd .maxDepth (),_faed ,_eede );
};if _dged .scanBand ("\u0076\u0065\u0072\u0074\u0069\u0063\u0061\u006c",_ecfd ,_afae (_bded ,0),_ecfd .minDepth ()-_faed ,_ecfd .maxDepth ()+_faed ,_fefg ,false ,false )> 0{_eeca =true ;};if _dged .scanBand ("\u0068\u006f\u0072\u0069\u007a\u006f\u006e\u0074\u0061\u006c",_ecfd ,_afae (_bded ,_eede ),_ecfd .minDepth (),_ecfd .maxDepth (),_eaca ,false ,false )> 0{_eeca =true ;
};if _eeca {continue ;};_bbbed :=_dged .scanBand ("",_ecfd ,_afae (_egce ,_gebfa ),_ecfd .minDepth (),_ecfd .maxDepth (),_fddgg ,true ,false );if _bbbed > 0{_bbbeb :=(_ecfd .maxDepth ()-_ecfd .minDepth ())/_ecfd ._abcf ;if (_bbbed > 1&&float64 (_bbbed )> 0.3*_bbbeb )||_bbbed <=10{if _dged .scanBand ("\u006f\u0074\u0068e\u0072",_ecfd ,_afae (_egce ,_gebfa ),_ecfd .minDepth (),_ecfd .maxDepth (),_fddgg ,false ,true )> 0{_eeca =true ;
};};};};_fdef =append (_fdef ,_ecfd );};};return _fdef ;};func _gecd (_fcab string )string {_agfag :=[]rune (_fcab );return string (_agfag [:len (_agfag )-1])};func _gfffb (_dedda _aa .PdfRectangle ,_geaea ,_fcddf ,_cgdbf ,_fcgcb *ruling )gridTile {_aebaa :=_dedda .Llx ;
_eeeg :=_dedda .Urx ;_afed :=_dedda .Lly ;_fcgde :=_dedda .Ury ;return gridTile {PdfRectangle :_dedda ,_geeegg :_geaea !=nil &&_geaea .encloses (_afed ,_fcgde ),_ccge :_fcddf !=nil &&_fcddf .encloses (_afed ,_fcgde ),_fcfc :_cgdbf !=nil &&_cgdbf .encloses (_aebaa ,_eeeg ),_aaaff :_fcgcb !=nil &&_fcgcb .encloses (_aebaa ,_eeeg )};
};func _gfdc (_defbg *list )[]*textLine {for _ ,_dfegf :=range _defbg ._bfaf {switch _dfegf ._ccae {case "\u004c\u0042\u006fd\u0079":if len (_dfegf ._feff )!=0{return _dfegf ._feff ;};return _gfdc (_dfegf );case "\u0053\u0070\u0061\u006e":return _dfegf ._feff ;
case "I\u006e\u006c\u0069\u006e\u0065\u0053\u0068\u0061\u0070\u0065":return _dfegf ._feff ;};};return nil ;};func (_cccc *wordBag )text ()string {_cgac :=_cccc .allWords ();_addf :=make ([]string ,len (_cgac ));for _effeb ,_adfe :=range _cgac {_addf [_effeb ]=_adfe ._ecegc ;
};return _add .Join (_addf ,"\u0020");};

// TextMark represents extracted text on a page with information regarding both textual content,
// formatting (font and size) and positioning.
// It is the smallest unit of text on a PDF page, typically a single character.
//
// getBBox() in test_text.go shows how to compute bounding boxes of substrings of extracted text.
// The following code extracts the text on PDF page `page` into `text` then finds the bounding box
// `bbox` of substring `term` in `text`.
//
//	ex, _ := New(page)
//	// handle errors
//	pageText, _, _, err := ex.ExtractPageText()
//	// handle errors
//	text := pageText.Text()
//	textMarks := pageText.Marks()
//
//		start := strings.Index(text, term)
//	 end := start + len(term)
//	 spanMarks, err := textMarks.RangeOffset(start, end)
//	 // handle errors
//	 bbox, ok := spanMarks.BBox()
//	 // handle errors
type TextMark struct{

// Text is the extracted text.
Text string ;

// Original is the text in the PDF. It has not been decoded like `Text`.
Original string ;

// BBox is the bounding box of the text.
BBox _aa .PdfRectangle ;

// Font is the font the text was drawn with.
Font *_aa .PdfFont ;

// FontSize is the font size the text was drawn with.
FontSize float64 ;

// Offset is the offset of the start of TextMark.Text in the extracted text. If you do this
//   text, textMarks := pageText.Text(), pageText.Marks()
//   marks := textMarks.Elements()
// then marks[i].Offset is the offset of marks[i].Text in text.
Offset int ;

// Meta is set true for spaces and line breaks that we insert in the extracted text. We insert
// spaces (line breaks) when we see characters that are over a threshold horizontal (vertical)
//  distance  apart. See wordJoiner (lineJoiner) in PageText.computeViews().
Meta bool ;

// FillColor is the fill color of the text.
// The color is nil for spaces and line breaks (i.e. the Meta field is true).
FillColor _ed .Color ;

// StrokeColor is the stroke color of the text.
// The color is nil for spaces and line breaks (i.e. the Meta field is true).
StrokeColor _ed .Color ;

// Orientation is the text orientation
Orientation int ;

// DirectObject is the underlying PdfObject (Text Object) that represents the visible texts. This is introduced to get
// a simple access to the TextObject in case editing or replacment of some text is needed. E.g during redaction.
DirectObject _cg .PdfObject ;

// ObjString is a decoded string operand of a text-showing operator. It has the same value as `Text` attribute except
// when many glyphs are represented with the same Text Object that contains multiple length string operand in which case
// ObjString spans more than one character string that falls in different TextMark objects.
ObjString []string ;Tw float64 ;Th float64 ;Tc float64 ;Index int ;_ffcc bool ;_aadfbe *TextTable ;};func (_fggc rulingList )bbox ()_aa .PdfRectangle {var _cffg _aa .PdfRectangle ;if len (_fggc )==0{_g .Log .Error ("r\u0075\u006c\u0069\u006e\u0067\u004ci\u0073\u0074\u002e\u0062\u0062\u006f\u0078\u003a\u0020n\u006f\u0020\u0072u\u006ci\u006e\u0067\u0073");
return _aa .PdfRectangle {};};if _fggc [0]._cafff ==_feed {_cffg .Llx ,_cffg .Urx =_fggc .secMinMax ();_cffg .Lly ,_cffg .Ury =_fggc .primMinMax ();}else {_cffg .Llx ,_cffg .Urx =_fggc .primMinMax ();_cffg .Lly ,_cffg .Ury =_fggc .secMinMax ();};return _cffg ;
};func (_egfb *textObject )setWordSpacing (_bggf float64 ){if _egfb ==nil {return ;};_egfb ._abf ._bag =_bggf ;};type rectRuling struct{_abbd rulingKind ;_aeda markKind ;_ed .Color ;_aa .PdfRectangle ;};func _cbgd (_edcd []pathSection )rulingList {_agbaa (_edcd );
if _ccccd {_g .Log .Info ("\u006d\u0061k\u0065\u0053\u0074\u0072\u006f\u006b\u0065\u0052\u0075\u006c\u0069\u006e\u0067\u0073\u003a\u0020\u0025\u0064\u0020\u0073\u0074\u0072ok\u0065\u0073",len (_edcd ));};var _ebeed rulingList ;for _ ,_bdedd :=range _edcd {for _ ,_bcba :=range _bdedd ._cdbe {if len (_bcba ._eae )< 2{continue ;
};_fcgbf :=_bcba ._eae [0];for _ ,_edagaf :=range _bcba ._eae [1:]{if _eebbf ,_acagf :=_dbeef (_fcgbf ,_edagaf ,_bdedd .Color );_acagf {_ebeed =append (_ebeed ,_eebbf );};_fcgbf =_edagaf ;};};};if _ccccd {_g .Log .Info ("m\u0061\u006b\u0065\u0053tr\u006fk\u0065\u0052\u0075\u006c\u0069n\u0067\u0073\u003a\u0020\u0025\u0073",_ebeed );
};return _ebeed ;};func (_gaac paraList )writeText (_edga _da .Writer ){for _fgge ,_cfcfg :=range _gaac {if _cfcfg ._caaa {continue ;};_cfcfg .writeText (_edga );if _fgge !=len (_gaac )-1{if _agaec (_cfcfg ,_gaac [_fgge +1]){_edga .Write ([]byte ("\u0020"));
}else {_edga .Write ([]byte ("\u000a"));_edga .Write ([]byte ("\u000a"));};};};_edga .Write ([]byte ("\u000a"));_edga .Write ([]byte ("\u000a"));};

// ExtractPageText returns the text contents of `e` (an Extractor for a page) as a PageText.
// TODO(peterwilliams97): The stats complicate this function signature and aren't very useful.
//
//	Replace with a function like Extract() (*PageText, error)
func (_gcg *Extractor )ExtractPageText ()(*PageText ,int ,int ,error ){_faafb ,_adegg ,_cfgf ,_gccb :=_gcg .extractPageText (_gcg ._gcc ,_gcg ._cgf ,_dc .IdentityMatrix (),0,false );if _gccb !=nil &&_gccb !=_aa .ErrColorOutOfRange {return nil ,0,0,_gccb ;
};if _gcg ._fda !=nil {_faafb ._cfcf ._beda =_gcg ._fda .ExtractionMode ;};_faafb .computeViews ();_gccb =_bgcdd (_faafb );if _gccb !=nil {return nil ,0,0,_gccb ;};if _gcg ._fda !=nil {if _gcg ._fda .ApplyCropBox &&_gcg ._ccb !=nil {_faafb .ApplyArea (*_gcg ._ccb );
};_faafb ._cfcf ._cfdd =_gcg ._fda .DisableDocumentTags ;};return _faafb ,_adegg ,_cfgf ,nil ;};func (_cfdaf *textPara )writeText (_gcgg _da .Writer ){if _cfdaf ._eddc ==nil {_cfdaf .writeCellText (_gcgg );return ;};for _bdec :=0;_bdec < _cfdaf ._eddc ._gacfc ;
_bdec ++{for _fdda :=0;_fdda < _cfdaf ._eddc ._ebdg ;_fdda ++{_cffcf :=_cfdaf ._eddc .get (_fdda ,_bdec );if _cffcf ==nil {_gcgg .Write ([]byte ("\u0009"));}else {_gefd (_cffcf ._fdfb );_cffcf .writeCellText (_gcgg );};_gcgg .Write ([]byte ("\u0020"));
};if _bdec < _cfdaf ._eddc ._gacfc -1{_gcgg .Write ([]byte ("\u000a"));};};};func (_bbdcg *wordBag )minDepth ()float64 {return _bbdcg ._dagf -(_bbdcg .Ury -_bbdcg ._abcf )};func _agaec (_eegd ,_ecae *textPara )bool {if _eegd ._caaa ||_ecae ._caaa {return true ;
};return _bbage (_eegd .depth ()-_ecae .depth ());};func _gfec (_bgdbb []compositeCell )[]float64 {var _cedfb []*textLine ;_ecfdf :=0;for _ ,_egggc :=range _bgdbb {_ecfdf +=len (_egggc .paraList );_cedfb =append (_cedfb ,_egggc .lines ()...);};_f .Slice (_cedfb ,func (_deaa ,_ebcdc int )bool {_bffe ,_agfg :=_cedfb [_deaa ],_cedfb [_ebcdc ];
_gbbdd ,_ddgc :=_bffe ._agcc ,_agfg ._agcc ;if !_bbage (_gbbdd -_ddgc ){return _gbbdd < _ddgc ;};return _bffe .Llx < _agfg .Llx ;});if _bffae {_ad .Printf ("\u0020\u0020\u0020 r\u006f\u0077\u0042\u006f\u0072\u0064\u0065\u0072\u0073:\u0020%\u0064 \u0070a\u0072\u0061\u0073\u0020\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u000a",_ecfdf ,len (_cedfb ));
for _bedec ,_abebg :=range _cedfb {_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_bedec ,_abebg );};};var _ggfbf []float64 ;_ebae :=_cedfb [0];var _fafd [][]*textLine ;_cdadc :=[]*textLine {_ebae };for _fgdb ,_gecab :=range _cedfb [1:]{if _gecab .Ury < _ebae .Lly {_cgfd :=0.5*(_gecab .Ury +_ebae .Lly );
if _bffae {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u003c\u0020\u0025\u0036.\u0032f\u0020\u0062\u006f\u0072\u0064\u0065\u0072\u003d\u0025\u0036\u002e\u0032\u0066\u000a"+"\u0009\u0020\u0071\u003d\u0025\u0073\u000a\u0009\u0020p\u003d\u0025\u0073\u000a",_fgdb ,_gecab .Ury ,_ebae .Lly ,_cgfd ,_ebae ,_gecab );
};_ggfbf =append (_ggfbf ,_cgfd );_fafd =append (_fafd ,_cdadc );_cdadc =nil ;};_cdadc =append (_cdadc ,_gecab );if _gecab .Lly < _ebae .Lly {_ebae =_gecab ;};};if len (_cdadc )> 0{_fafd =append (_fafd ,_cdadc );};if _bffae {_ad .Printf (" \u0020\u0020\u0020\u0020\u0020\u0020 \u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064o\u0072\u0073\u003d%\u0036.\u0032\u0066\u000a",_ggfbf );
};if _bffae {_g .Log .Info ("\u0072\u006f\u0077\u003d\u0025\u0064",len (_bgdbb ));for _bcbd ,_dfcbg :=range _bgdbb {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_bcbd ,_dfcbg );};_g .Log .Info ("\u0067r\u006f\u0075\u0070\u0073\u003d\u0025d",len (_fafd ));
for _cgaf ,_eacagc :=range _fafd {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0064\u000a",_cgaf ,len (_eacagc ));for _fddc ,_afadb :=range _eacagc {_ad .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_fddc ,_afadb );};};};_gcad :=true ;
for _dgega ,_dddfc :=range _fafd {_dbadb :=true ;for _ddff ,_gafg :=range _bgdbb {if _bffae {_ad .Printf ("\u0020\u0020\u0020\u007e\u007e\u007e\u0067\u0072\u006f\u0075\u0070\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u0020\u0063\u0065\u006cl\u0020\u0025\u0064\u0020\u006ff\u0020\u0025d\u0020\u0025\u0073\u000a",_dgega ,len (_fafd ),_ddff ,len (_bgdbb ),_gafg );
};if !_gafg .hasLines (_dddfc ){if _bffae {_ad .Printf ("\u0020\u0020\u0020\u0021\u0021\u0021\u0067\u0072\u006f\u0075\u0070\u0020\u0025d\u0020\u006f\u0066\u0020\u0025\u0064 \u0063\u0065\u006c\u006c\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064 \u004f\u0055\u0054\u000a",_dgega ,len (_fafd ),_ddff ,len (_bgdbb ));
};_dbadb =false ;break ;};};if !_dbadb {_gcad =false ;break ;};};if !_gcad {if _bffae {_g .Log .Info ("\u0072\u006f\u0077\u0020\u0063o\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u0020\u0064\u006f\u006e\u0027\u0074 \u0073\u0070\u0061\u006e\u0020\u0061\u006c\u006c\u0020\u0063\u0065\u006c\u006c\u0073\u0020\u0069\u006e\u0020\u0072\u006f\u0077\u002e\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006eg");
};_ggfbf =nil ;};if _bffae &&_ggfbf !=nil {_ad .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u002a\u002a*\u0072\u006f\u0077\u0043\u006f\u0072\u0072i\u0064\u006f\u0072\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u000a",_ggfbf );};return _ggfbf ;
};func (_bbbe *TextMarkArray )getTextMarkAtOffset (_gggfc int )*TextMark {for _ ,_gfdd :=range _bbbe ._edgd {if _gfdd .Offset ==_gggfc {return &_gfdd ;};};return nil ;};func (_efbf *stateStack )push (_ggfb *textState ){_adaf :=*_ggfb ;*_efbf =append (*_efbf ,&_adaf )};
func _geba (_ffcce []float64 ,_gaaa ,_fbcdg float64 )[]float64 {_fdabg ,_ggbb :=_gaaa ,_fbcdg ;if _ggbb < _fdabg {_fdabg ,_ggbb =_ggbb ,_fdabg ;};_bbdcbd :=make ([]float64 ,0,len (_ffcce )+2);_bbdcbd =append (_bbdcbd ,_gaaa );for _ ,_baedd :=range _ffcce {if _baedd <=_fdabg {continue ;
}else if _baedd >=_ggbb {break ;};_bbdcbd =append (_bbdcbd ,_baedd );};_bbdcbd =append (_bbdcbd ,_fbcdg );return _bbdcbd ;};func _egba (_dcde []*textMark ,_abagg _aa .PdfRectangle ,_caef rulingList ,_dbce []gridTiling ,_ffdce bool )paraList {_g .Log .Trace ("\u006d\u0061\u006b\u0065\u0054\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u003a \u0025\u0064\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0073\u0020\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065=\u0025\u002e\u0032\u0066",len (_dcde ),_abagg );
if len (_dcde )==0{return nil ;};_gffc :=_eafee (_dcde ,_abagg ,false );if len (_gffc )==0{return nil ;};_caef .log ("\u006d\u0061\u006be\u0054\u0065\u0078\u0074\u0050\u0061\u0067\u0065");_gccd ,_cccbe :=_caef .vertsHorzs ();_fcffd :=_cefd (_gffc ,_abagg .Ury ,_gccd ,_cccbe );
_aefcg :=_gfaag (_fcffd ,_abagg .Ury ,_gccd ,_cccbe ,_ffdce );_aefcg =_edbce (_aefcg );_gedbe :=make (paraList ,0,len (_aefcg ));for _ ,_cecc :=range _aefcg {_fegf :=_cecc .arrangeText (_ffdce );if _fegf !=nil {_gedbe =append (_gedbe ,_fegf );};};if len (_gedbe )>=_cabaf {_gedbe =_gedbe .extractTables (_dbce );
};_gedbe .sortReadingOrder ();_gedbe .sortTopoOrder ();_gedbe .log ("\u0073\u006f\u0072te\u0064\u0020\u0069\u006e\u0020\u0072\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0064\u0065\u0072");return _gedbe ;};

// Font represents the font properties on a PDF page.
type Font struct{PdfFont *_aa .PdfFont ;

// FontName represents Font Name from font properties.
FontName string ;

// FontType represents Font Subtype entry in the font dictionary inside page resources.
// Examples : type0, Type1, MMType1, Type3, TrueType, CIDFont.
FontType string ;

// ToUnicode is true if font provides a `ToUnicode` mapping.
ToUnicode bool ;

// IsCID is true if underlying font is a composite font.
// Composite font is represented by a font dictionary whose Subtype is `Type0`
IsCID bool ;

// IsSimple is true if font is simple font.
// A simple font is limited to only 8 bit (255) character codes.
IsSimple bool ;

// FontData represents the raw data of the embedded font file.
// It can have format TrueType (TTF), PostScript Font (PFB) or Compact Font Format (CCF).
// FontData value can be indicates from `FontFile`, `FontFile2` or `FontFile3` inside Font Descriptor.
// At most, only one of `FontFile`, `FontFile2` or `FontFile3` will be FontData value.
FontData []byte ;

// FontFileName is a name representing the font. it has format:
// (Font Name) + (Font Type Extension), example: helvetica.ttf.
FontFileName string ;

// FontDescriptor represents metrics and other attributes inside font properties from PDF Structure (Font Descriptor).
FontDescriptor *_aa .PdfFontDescriptor ;};func (_bbb *imageExtractContext )processOperand (_fdg *_fc .ContentStreamOperation ,_gbcg _fc .GraphicsState ,_fbc *_aa .PdfPageResources )error {if _fdg .Operand =="\u0042\u0049"&&len (_fdg .Params )==1{_caa ,_geb :=_fdg .Params [0].(*_fc .ContentStreamInlineImage );
if !_geb {return nil ;};if _ddg ,_ffd :=_cg .GetBoolVal (_caa .ImageMask );_ffd {if _ddg &&!_bbb ._cag .IncludeInlineStencilMasks {return nil ;};};return _bbb .extractInlineImage (_caa ,_gbcg ,_fbc );}else if _fdg .Operand =="\u0044\u006f"&&len (_fdg .Params )==1{_fag ,_aff :=_cg .GetName (_fdg .Params [0]);
if !_aff {_g .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0079\u0070\u0065");return _ccc ;};_ ,_eab :=_fbc .GetXObjectByName (*_fag );switch _eab {case _aa .XObjectTypeImage :return _bbb .extractXObjectImage (_fag ,_gbcg ,_fbc );case _aa .XObjectTypeForm :return _bbb .extractFormImages (_fag ,_gbcg ,_fbc );
};}else if _bbb ._befd &&(_fdg .Operand =="\u0073\u0063\u006e"||_fdg .Operand =="\u0053\u0043\u004e")&&len (_fdg .Params )==1{_bdc ,_ggc :=_cg .GetName (_fdg .Params [0]);if !_ggc {_g .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0079\u0070\u0065");
return _ccc ;};_daf ,_ggc :=_fbc .GetPatternByName (*_bdc );if !_ggc {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0050\u0061\u0074\u0074\u0065\u0072n\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");return nil ;};if _daf .IsTiling (){_fed :=_daf .GetAsTilingPattern ();
_agf ,_dag :=_fed .GetContentStream ();if _dag !=nil {return _dag ;};_dag =_bbb .extractContentStreamImages (string (_agf ),_fed .Resources );if _dag !=nil {return _dag ;};};}else if (_fdg .Operand =="\u0063\u0073"||_fdg .Operand =="\u0043\u0053")&&len (_fdg .Params )>=1{_bbb ._befd =_fdg .Params [0].String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e";
};return nil ;};var _fffa =map[markKind ]string {_ccfaa :"\u0073\u0074\u0072\u006f\u006b\u0065",_cfcbf :"\u0066\u0069\u006c\u006c",_eggg :"\u0061u\u0067\u006d\u0065\u006e\u0074"};func _dgbc (_daec ,_bggff float64 )bool {return _ea .Abs (_daec -_bggff )<=_adag };
func (_gfdb *shapesState )lineTo (_efbff ,_cedcc float64 ){if _fdee {_g .Log .Info ("\u006c\u0069\u006eeT\u006f\u0028\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0020\u0070\u003d\u0025\u002e\u0032\u0066",_efbff ,_cedcc ,_gfdb .devicePoint (_efbff ,_cedcc ));
};_gfdb .addPoint (_efbff ,_cedcc );};func (_fagg *subpath )isQuadrilateral ()bool {if len (_fagg ._eae )< 4||len (_fagg ._eae )> 5{return false ;};if len (_fagg ._eae )==5{_dbege :=_fagg ._eae [0];_gdfg :=_fagg ._eae [4];if _dbege .X !=_gdfg .X ||_dbege .Y !=_gdfg .Y {return false ;
};};return true ;};func _ecegf (_cecd _aa .PdfRectangle )*ruling {return &ruling {_cafff :_feed ,_eced :_cecd .Lly ,_decc :_cecd .Llx ,_gcda :_cecd .Urx };};

// NewFromContents creates a new extractor from contents and page resources.
func NewFromContents (contents string ,resources *_aa .PdfPageResources )(*Extractor ,error ){const _aea ="\u0065x\u0074\u0072\u0061\u0063t\u006f\u0072\u002e\u004e\u0065w\u0046r\u006fm\u0043\u006f\u006e\u0074\u0065\u006e\u0074s";_bab :=&Extractor {_gcc :contents ,_cgf :resources ,_feaa :map[string ]fontEntry {},_dd :map[string ]textResult {}};
_ec .TrackUse (_aea );return _bab ,nil ;};func _cgbd (_gbfa []*textLine ,_effc string ,_cafb []*list )*list {return &list {_feff :_gbfa ,_ccae :_effc ,_bfaf :_cafb };};func (_aeea paraList )topoOrder ()[]int {if _gacf {_g .Log .Info ("\u0074\u006f\u0070\u006f\u004f\u0072\u0064\u0065\u0072\u003a");
};_aagd :=len (_aeea );_fgdd :=make ([]bool ,_aagd );_gaeaa :=make ([]int ,0,_aagd );_edaga :=_aeea .llyOrdering ();var _ggdf func (_eccfg int );_ggdf =func (_dbcf int ){_fgdd [_dbcf ]=true ;for _fbcad :=0;_fbcad < _aagd ;_fbcad ++{if !_fgdd [_fbcad ]{if _aeea .readBefore (_edaga ,_dbcf ,_fbcad ){_ggdf (_fbcad );
};};};_gaeaa =append (_gaeaa ,_dbcf );};for _cdcda :=0;_cdcda < _aagd ;_cdcda ++{if !_fgdd [_cdcda ]{_ggdf (_cdcda );};};return _aeeb (_gaeaa );};func (_dfcgb *wordBag )firstReadingIndex (_agfb int )int {_geec :=_dfcgb .firstWord (_agfb )._cacfc ;_bdfe :=float64 (_agfb +1)*_bbbd ;
_caea :=_bdfe +_fefd *_geec ;_geca :=_agfb ;for _ ,_gcbg :=range _dfcgb .depthBand (_bdfe ,_caea ){if _ecac (_dfcgb .firstWord (_gcbg ),_dfcgb .firstWord (_geca ))< 0{_geca =_gcbg ;};};return _geca ;};func _egef (_dfgg []*textLine ,_egg string )string {var _ccef _add .Builder ;
_ecaf :=0.0;for _gggd ,_ecfa :=range _dfgg {_ggaba :=_ecfa .text ();_cebe :=_ecfa ._agcc ;if _gggd < len (_dfgg )-1{_ecaf =_dfgg [_gggd +1]._agcc ;}else {_ecaf =0.0;};_ccef .WriteString (_egg );_ccef .WriteString (_ggaba );if _ecaf !=_cebe {_ccef .WriteString ("\u000a");
}else {_ccef .WriteString ("\u0020");};};return _ccef .String ();};type textResult struct{_affg PageText ;_aedb int ;_fgcb int ;};func _gbdbg (_bgbcb ,_dgca float64 )string {_cccgf :=!_bbage (_bgbcb -_dgca );if _cccgf {return "\u000a";};return "\u0020";
};

// String returns a description of `state`.
func (_bfaba *textState )String ()string {_egde :="\u005bN\u004f\u0054\u0020\u0053\u0045\u0054]";if _bfaba ._ebca !=nil {_egde =_bfaba ._ebca .BaseFont ();};return _ad .Sprintf ("\u0074\u0063\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0077\u003d\u0025\u002e\u0032\u0066 \u0074f\u0073\u003d\u0025\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u003d\u0025\u0071",_bfaba ._debe ,_bfaba ._bag ,_bfaba ._dced ,_egde );
};func (_ecbg rulingList )augmentGrid ()(rulingList ,rulingList ){_dbbdg ,_fggd :=_ecbg .vertsHorzs ();if len (_dbbdg )==0||len (_fggd )==0{return _dbbdg ,_fggd ;};_fdcbe ,_faca :=_dbbdg ,_fggd ;_efeaf :=_dbbdg .bbox ();_fffg :=_fggd .bbox ();if _ccccd {_g .Log .Info ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0047\u0072\u0069\u0064\u003a\u0020b\u0062\u006f\u0078\u0056\u003d\u0025\u0036\u002e\u0032\u0066",_efeaf );
_g .Log .Info ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0047\u0072\u0069\u0064\u003a\u0020b\u0062\u006f\u0078\u0048\u003d\u0025\u0036\u002e\u0032\u0066",_fffg );};var _eddda ,_cebeg ,_cbbb ,_gfcce *ruling ;if _fffg .Llx < _efeaf .Llx -_adag {_eddda =&ruling {_bcgbe :_eggg ,_cafff :_cggd ,_eced :_fffg .Llx ,_decc :_efeaf .Lly ,_gcda :_efeaf .Ury };
_dbbdg =append (rulingList {_eddda },_dbbdg ...);};if _fffg .Urx > _efeaf .Urx +_adag {_cebeg =&ruling {_bcgbe :_eggg ,_cafff :_cggd ,_eced :_fffg .Urx ,_decc :_efeaf .Lly ,_gcda :_efeaf .Ury };_dbbdg =append (_dbbdg ,_cebeg );};if _efeaf .Lly < _fffg .Lly -_adag {_cbbb =&ruling {_bcgbe :_eggg ,_cafff :_feed ,_eced :_efeaf .Lly ,_decc :_fffg .Llx ,_gcda :_fffg .Urx };
_fggd =append (rulingList {_cbbb },_fggd ...);};if _efeaf .Ury > _fffg .Ury +_adag {_gfcce =&ruling {_bcgbe :_eggg ,_cafff :_feed ,_eced :_efeaf .Ury ,_decc :_fffg .Llx ,_gcda :_fffg .Urx };_fggd =append (_fggd ,_gfcce );};if len (_dbbdg )+len (_fggd )==len (_ecbg ){return _fdcbe ,_faca ;
};_bgeae :=append (_dbbdg ,_fggd ...);_ecbg .log ("u\u006e\u0061\u0075\u0067\u006d\u0065\u006e\u0074\u0065\u0064");_bgeae .log ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0065d");return _dbbdg ,_fggd ;};

// ToText returns the page text as a single string.
// Deprecated: This function is deprecated and will be removed in a future major version. Please use
// Text() instead.
func (_dbgg PageText )ToText ()string {return _dbgg .Text ()};func (_ccaf *textLine )markWordBoundaries (){_eccf :=_ebbb *_ccaf ._cadd ;for _gaad ,_cfdfc :=range _ccaf ._dbcg [1:]{if _fbg (_cfdfc ,_ccaf ._dbcg [_gaad ])>=_eccf {_cfdfc ._ccdbc =true ;};
};};func (_dfbf *textTable )reduce ()*textTable {_bdgd :=make ([]int ,0,_dfbf ._gacfc );_ccbeb :=make ([]int ,0,_dfbf ._ebdg );for _fgbg :=0;_fgbg < _dfbf ._gacfc ;_fgbg ++{if !_dfbf .emptyCompositeRow (_fgbg ){_bdgd =append (_bdgd ,_fgbg );};};for _fgaaa :=0;
_fgaaa < _dfbf ._ebdg ;_fgaaa ++{if !_dfbf .emptyCompositeColumn (_fgaaa ){_ccbeb =append (_ccbeb ,_fgaaa );};};if len (_bdgd )==_dfbf ._gacfc &&len (_ccbeb )==_dfbf ._ebdg {return _dfbf ;};_ebda :=textTable {_ccfad :_dfbf ._ccfad ,_ebdg :len (_ccbeb ),_gacfc :len (_bdgd ),_fdgeg :make (map[uint64 ]*textPara ,len (_ccbeb )*len (_bdgd ))};
if _bffae {_g .Log .Info ("\u0072\u0065\u0064\u0075ce\u003a\u0020\u0025\u0064\u0078\u0025\u0064\u0020\u002d\u003e\u0020\u0025\u0064\u0078%\u0064",_dfbf ._ebdg ,_dfbf ._gacfc ,len (_ccbeb ),len (_bdgd ));_g .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0043\u006f\u006c\u0073\u003a\u0020\u0025\u002b\u0076",_ccbeb );
_g .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0052\u006f\u0077\u0073\u003a\u0020\u0025\u002b\u0076",_bdgd );};for _fbcfa ,_bege :=range _bdgd {for _ffdcd ,_faeaa :=range _ccbeb {_bfggf ,_cgbfb :=_dfbf .getComposite (_faeaa ,_bege );if _bfggf ==nil {continue ;
};if _bffae {_ad .Printf ("\u0020 \u0025\u0032\u0064\u002c \u0025\u0032\u0064\u0020\u0028%\u0032d\u002c \u0025\u0032\u0064\u0029\u0020\u0025\u0071\n",_ffdcd ,_fbcfa ,_faeaa ,_bege ,_ecegb (_bfggf .merge ().text (),50));};_ebda .putComposite (_ffdcd ,_fbcfa ,_bfggf ,_cgbfb );
};};return &_ebda ;};func _babc (_bbac string )bool {if _bc .RuneCountInString (_bbac )< _afgbd {return false ;};_ggaf ,_facg :=_bc .DecodeLastRuneInString (_bbac );if _facg <=0||!_c .Is (_c .Hyphen ,_ggaf ){return false ;};_ggaf ,_facg =_bc .DecodeLastRuneInString (_bbac [:len (_bbac )-_facg ]);
return _facg > 0&&!_c .IsSpace (_ggaf );};func _eeabd (_dcdfe []*textLine ,_cddf map[float64 ][]*textLine )[]*list {_dbga :=_eeaa (_cddf );_eecea :=[]*list {};if len (_dbga )==0{return _eecea ;};_ecea :=_dbga [0];_gaefa :=1;_bgbc :=_cddf [_ecea ];for _fdgc ,_bda :=range _bgbc {var _bfac float64 ;
_gfaaf :=[]*list {};_acaf :=_bda ._agcc ;_agca :=-1.0;if _fdgc < len (_bgbc )-1{_agca =_bgbc [_fdgc +1]._agcc ;};if _gaefa < len (_dbga ){_gfaaf =_efba (_dcdfe ,_cddf ,_dbga ,_gaefa ,_acaf ,_agca );};_bfac =_agca ;if len (_gfaaf )> 0{_fcbe :=_gfaaf [0];
if len (_fcbe ._feff )> 0{_bfac =_fcbe ._feff [0]._agcc ;};};_baeaa :=[]*textLine {_bda };_adfd :=_cdca (_bda ,_dcdfe ,_acaf ,_bfac );_baeaa =append (_baeaa ,_adfd ...);_fbgd :=_cgbd (_baeaa ,"\u0062\u0075\u006c\u006c\u0065\u0074",_gfaaf );_fbgd ._cdfd =_egef (_baeaa ,"");
_eecea =append (_eecea ,_fbgd );};return _eecea ;};

// String returns a human readable description of `vecs`.
func (_fcbae rulingList )String ()string {if len (_fcbae )==0{return "\u007b \u0045\u004d\u0050\u0054\u0059\u0020}";};_ddbd ,_dfac :=_fcbae .vertsHorzs ();_aaga :=len (_ddbd );_dagc :=len (_dfac );if _aaga ==0||_dagc ==0{return _ad .Sprintf ("\u007b%\u0064\u0020\u0078\u0020\u0025\u0064}",_aaga ,_dagc );
};_accdc :=_aa .PdfRectangle {Llx :_ddbd [0]._eced ,Urx :_ddbd [_aaga -1]._eced ,Lly :_dfac [_dagc -1]._eced ,Ury :_dfac [0]._eced };return _ad .Sprintf ("\u007b\u0025d\u0020\u0078\u0020%\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u007d",_aaga ,_dagc ,_accdc );
};

// ExtractFonts returns all font information from the page extractor, including
// font name, font type, the raw data of the embedded font file (if embedded), font descriptor and more.
//
// The argument `previousPageFonts` is used when trying to build a complete font catalog for multiple pages or the entire document.
// The entries from `previousPageFonts` are added to the returned result unless already included in the page, i.e. no duplicate entries.
//
// NOTE: If previousPageFonts is nil, all fonts from the page will be returned. Use it when building up a full list of fonts for a document or page range.
func (_bfc *Extractor )ExtractFonts (previousPageFonts *PageFonts )(*PageFonts ,error ){_cfc :=PageFonts {};_debc :=_cfc .extractPageResourcesToFont (_bfc ._cgf );if _debc !=nil {return nil ,_debc ;};if previousPageFonts !=nil {for _ ,_edb :=range previousPageFonts .Fonts {if !_ccg (_cfc .Fonts ,_edb .FontName ){_cfc .Fonts =append (_cfc .Fonts ,_edb );
};};};return &PageFonts {Fonts :_cfc .Fonts },nil ;};func _deecf (_ggaad []*textMark ,_gaegc _aa .PdfRectangle )*textWord {_deaf :=_ggaad [0].PdfRectangle ;_feebe :=_ggaad [0]._fadfd ;for _ ,_debee :=range _ggaad [1:]{_deaf =_bdda (_deaf ,_debee .PdfRectangle );
if _debee ._fadfd > _feebe {_feebe =_debee ._fadfd ;};};return &textWord {PdfRectangle :_deaf ,_fcacg :_ggaad ,_abace :_gaegc .Ury -_deaf .Lly ,_cacfc :_feebe };};const (_fdag =1.0e-6;_fabad =1.0e-4;_fgaed =10;_bbbd =6;_dfff =0.5;_gggb =0.12;_aebbf =0.19;
_edabd =0.04;_abfea =1.0;_fefg =0.04;_dfeb =12;_ffaa =0.4;_eaca =0.7;_ccdag =1.0;_fddgg =0.1;_dbcc =1.4;_gfcb =0.46;_ebbb =0.02;_cegde =0.2;_ddfa =0.5;_afgbd =4;_fefd =4.0;_cabaf =6;_bged =0.3;_eaeb =0.01;_fddggf =0.02;_fagd =2;_ecbf =2;_gdea =500;_agaf =4.0;
_agefe =0.05;_fafb =0.1;_adag =2.0;_cafa =2.0;_aaeeb =1.5;_acecg =3.0;_gcbd =0.25;);func (_bcgc *textObject )moveText (_gddf ,_gdde float64 ){_bcgc .moveLP (_gddf ,_gdde )};func (_bbbaf *shapesState )devicePoint (_gdc ,_eedd float64 )_dc .Point {_eaac :=_bbbaf ._deeb .Mult (_bbbaf ._defg );
_gdc ,_eedd =_eaac .Transform (_gdc ,_eedd );return _dc .NewPoint (_gdc ,_eedd );};type intSet map[int ]struct{};func (_edbf *textObject )setTextRenderMode (_eed int ){if _edbf ==nil {return ;};_edbf ._abf ._cbffg =RenderMode (_eed );};

// Options extractor options.
type Options struct{

// DisableDocumentTags specifies whether to use the document tags during list extraction.
DisableDocumentTags bool ;

// ApplyCropBox will extract page text based on page cropbox if set to `true`.
ApplyCropBox bool ;

// Text extraction mode, default is ExtractionModeLayout.
ExtractionMode ExtractionMode ;

// IncludeAnnotations specifies whether to include annotations in the extraction process, default value is `false`.
IncludeAnnotations bool ;

// RelaxedMode specifies whether to use relaxed mode for processing the objects,
// If enabled UniPDF will automatically try to fix invalid parameters length and value.
// Default is `false`.
RelaxedMode bool ;};var _dagfgb *_bcc .Regexp =_bcc .MustCompile (_edfcd +"\u007c"+_feagc );func (_gbca *wordBag )getDepthIdx (_gabc float64 )int {_cagca :=_gbca .depthIndexes ();_caff :=_dadf (_gabc );if _caff < _cagca [0]{return _cagca [0];};if _caff > _cagca [len (_cagca )-1]{return _cagca [len (_cagca )-1];
};return _caff ;};type wordBag struct{_aa .PdfRectangle ;_abcf float64 ;_aada ,_bcdc rulingList ;_dagf float64 ;_cdaf map[int ][]*textWord ;};func _bbdba (_dbeg []TextMark ,_defc *TextTable )[]TextMark {var _gccg []TextMark ;for _ ,_fgca :=range _dbeg {_fgca ._ffcc =true ;
_fgca ._aadfbe =_defc ;_gccg =append (_gccg ,_fgca );};return _gccg ;};

// WriteToFile writes the edited content to `outputPath`.
func (_cgee *Editor )WriteToFile (outputPath string )error {_gag ,_dfedc :=_cgee ._fedd .ToWriter (nil );if _dfedc !=nil {return _ad .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0074\u006f\u0020c\u006f\u006e\u0076\u0065\u0072\u0074\u0020t\u006f\u0020\u0077\u0072\u0069\u0074\u0065\u0072\u0020\u0025\u0076",_dfedc );
};_gag .WriteToFile (outputPath );return nil ;};func _acfcg (_edbcg string )bool {for _ ,_gcdc :=range _edbcg {if !_c .IsSpace (_gcdc ){return false ;};};return true ;};func (_edbb *ruling )equals (_fgbbb *ruling )bool {return _edbb ._cafff ==_fgbbb ._cafff &&_dgbc (_edbb ._eced ,_fgbbb ._eced )&&_dgbc (_edbb ._decc ,_fgbbb ._decc )&&_dgbc (_edbb ._gcda ,_fgbbb ._gcda );
};func _cgaa (_ebba ,_bgeee _dc .Point )rulingKind {_dbag :=_ea .Abs (_ebba .X -_bgeee .X );_dffc :=_ea .Abs (_ebba .Y -_bgeee .Y );return _feede (_dbag ,_dffc ,_agaf );};func (_agbcf *textLine )appendWord (_addfa *textWord ){_agbcf ._dbcg =append (_agbcf ._dbcg ,_addfa );
_agbcf .PdfRectangle =_bdda (_agbcf .PdfRectangle ,_addfa .PdfRectangle );if _addfa ._cacfc > _agbcf ._cadd {_agbcf ._cadd =_addfa ._cacfc ;};if _addfa ._abace > _agbcf ._agcc {_agbcf ._agcc =_addfa ._abace ;};};const (RenderModeStroke RenderMode =1<<iota ;
RenderModeFill ;RenderModeClip ;);func _cfeaf (_fedee ,_gbbb _ff .Image )_ff .Image {_gdeae ,_adcecb :=_gbbb .Bounds ().Size (),_fedee .Bounds ().Size ();_fdcgd ,_bbgfg :=_gdeae .X ,_gdeae .Y ;if _adcecb .X > _fdcgd {_fdcgd =_adcecb .X ;};if _adcecb .Y > _bbgfg {_bbgfg =_adcecb .Y ;
};_adee :=_ff .Rect (0,0,_fdcgd ,_bbgfg );if _gdeae .X !=_fdcgd ||_gdeae .Y !=_bbgfg {_bafg :=_ff .NewRGBA (_adee );_a .BiLinear .Scale (_bafg ,_adee ,_fedee ,_gbbb .Bounds (),_a .Over ,nil );_gbbb =_bafg ;};if _adcecb .X !=_fdcgd ||_adcecb .Y !=_bbgfg {_fdad :=_ff .NewRGBA (_adee );
_a .BiLinear .Scale (_fdad ,_adee ,_fedee ,_fedee .Bounds (),_a .Over ,nil );_fedee =_fdad ;};_ccege :=_ff .NewRGBA (_adee );_a .DrawMask (_ccege ,_adee ,_fedee ,_ff .Point {},_gbbb ,_ff .Point {},_a .Over );return _ccege ;};func _cgdb (_cedc *_cg .PdfObjectString ,_befc string ,_efc *_aa .PdfFont ){_efeg ,_bdf :=_efc .StringToCharcodeBytes (_befc );
if _bdf !=0{_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0073\u006fm\u0065\u0020\u0072un\u0065\u0073\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0065d\u002e\u000a\u0009\u0025\u0073\u0020\u002d\u003e \u0025\u0076",_befc ,_efeg );
};_gaae :=_cg .MakeStringFromBytes (_efeg );*_cedc =*_gaae ;};func (_ffgd paraList )lines ()[]*textLine {var _gecef []*textLine ;for _ ,_dbdce :=range _ffgd {_gecef =append (_gecef ,_dbdce ._fdfb ...);};return _gecef ;};func _afeg (_debg ,_begbb ,_cfec ,_gggab *textPara )*textTable {_bgadb :=&textTable {_ebdg :2,_gacfc :2,_fdgeg :make (map[uint64 ]*textPara ,4)};
_bgadb .put (0,0,_debg );_bgadb .put (1,0,_begbb );_bgadb .put (0,1,_cfec );_bgadb .put (1,1,_gggab );return _bgadb ;};func _daca (_bbeg map[int ]intSet )[]int {_dagg :=make ([]int ,0,len (_bbeg ));for _egge :=range _bbeg {_dagg =append (_dagg ,_egge );
};_f .Ints (_dagg );return _dagg ;};func (_eggag *textTable )logComposite (_aacdd string ){if !_bffae {return ;};_g .Log .Info ("\u007e~\u007eP\u0061\u0072\u0061\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0073",_eggag ._ebdg ,_eggag ._gacfc ,_aacdd );
_ad .Printf ("\u0025\u0035\u0073 \u007c","");for _cabga :=0;_cabga < _eggag ._ebdg ;_cabga ++{_ad .Printf ("\u0025\u0033\u0064 \u007c",_cabga );};_ad .Println ("");_ad .Printf ("\u0025\u0035\u0073 \u002b","");for _gfed :=0;_gfed < _eggag ._ebdg ;_gfed ++{_ad .Printf ("\u0025\u0033\u0073 \u002b","\u002d\u002d\u002d");
};_ad .Println ("");for _ebbg :=0;_ebbg < _eggag ._gacfc ;_ebbg ++{_ad .Printf ("\u0025\u0035\u0064 \u007c",_ebbg );for _cbbaf :=0;_cbbaf < _eggag ._ebdg ;_cbbaf ++{_ecdcc ,_ :=_eggag ._efcbeb [_cggdf (_cbbaf ,_ebbg )].parasBBox ();_ad .Printf ("\u0025\u0033\u0064 \u007c",len (_ecdcc ));
};_ad .Println ("");};_g .Log .Info ("\u007e~\u007eT\u0065\u0078\u0074\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0073",_eggag ._ebdg ,_eggag ._gacfc ,_aacdd );_ad .Printf ("\u0025\u0035\u0073 \u007c","");for _cacba :=0;_cacba < _eggag ._ebdg ;
_cacba ++{_ad .Printf ("\u0025\u0031\u0032\u0064\u0020\u007c",_cacba );};_ad .Println ("");_ad .Printf ("\u0025\u0035\u0073 \u002b","");for _bgcda :=0;_bgcda < _eggag ._ebdg ;_bgcda ++{_ad .Print ("\u002d\u002d\u002d\u002d\u002d\u002d\u002d\u002d\u002d-\u002d\u002d\u002d\u002b");
};_ad .Println ("");for _cecfa :=0;_cecfa < _eggag ._gacfc ;_cecfa ++{_ad .Printf ("\u0025\u0035\u0064 \u007c",_cecfa );for _faafca :=0;_faafca < _eggag ._ebdg ;_faafca ++{_fgade ,_ :=_eggag ._efcbeb [_cggdf (_faafca ,_cecfa )].parasBBox ();_afbafe :="";
_agdd :=_fgade .merge ();if _agdd !=nil {_afbafe =_agdd .text ();};_afbafe =_ad .Sprintf ("\u0025\u0071",_ecegb (_afbafe ,12));_afbafe =_afbafe [1:len (_afbafe )-1];_ad .Printf ("\u0025\u0031\u0032\u0073\u0020\u007c",_afbafe );};_ad .Println ("");};};func (_baefg lineRuling )asRuling ()(*ruling ,bool ){_bfdc :=ruling {_cafff :_baefg ._fbfg ,Color :_baefg .Color ,_bcgbe :_ccfaa };
switch _baefg ._fbfg {case _cggd :_bfdc ._eced =_baefg .xMean ();_bfdc ._decc =_ea .Min (_baefg ._cfdad .Y ,_baefg ._bddd .Y );_bfdc ._gcda =_ea .Max (_baefg ._cfdad .Y ,_baefg ._bddd .Y );case _feed :_bfdc ._eced =_baefg .yMean ();_bfdc ._decc =_ea .Min (_baefg ._cfdad .X ,_baefg ._bddd .X );
_bfdc ._gcda =_ea .Max (_baefg ._cfdad .X ,_baefg ._bddd .X );default:_g .Log .Error ("\u0062\u0061\u0064\u0020pr\u0069\u006d\u0061\u0072\u0079\u0020\u006b\u0069\u006e\u0064\u003d\u0025\u0064",_baefg ._fbfg );return nil ,false ;};return &_bfdc ,true ;};
func (_addbc *stateStack )size ()int {return len (*_addbc )};func _bdda (_aabb ,_gfgcf _aa .PdfRectangle )_aa .PdfRectangle {return _aa .PdfRectangle {Llx :_ea .Min (_aabb .Llx ,_gfgcf .Llx ),Lly :_ea .Min (_aabb .Lly ,_gfgcf .Lly ),Urx :_ea .Max (_aabb .Urx ,_gfgcf .Urx ),Ury :_ea .Max (_aabb .Ury ,_gfgcf .Ury )};
};func _bbba (_cega []*TextMarkArray ,_fcfg ,_eee string )(string ,error ){_dfeeb :=0;for _ ,_aef :=range _cega {_geg :=_aef ._edgd [0].DirectObject ;if _geg ==nil {continue ;};_gbbf :=_geg .String ();if len (_gbbf )> 1{_gcba :=_gaga (_aef ,&_fcfg ,&_dfeeb ,_eee );
if _gcba !=nil {return _fcfg ,_gcba ;};}else if len (_gbbf )==1{_adgb :=_ccfd (_aef ,&_fcfg ,&_dfeeb ,_eee );if _adgb !=nil {return _fcfg ,_adgb ;};};};return _fcfg ,nil ;};func (_ecfdd *textPara )text ()string {_aeef :=new (_bb .Buffer );_ecfdd .writeText (_aeef );
return _aeef .String ();};

// ExtractTextWithStats works like ExtractText but returns the number of characters in the output
// (`numChars`) and the number of characters that were not decoded (`numMisses`).
func (_bbc *Extractor )ExtractTextWithStats ()(_dfdd string ,_efee int ,_gggf int ,_dfae error ){_edeg ,_efee ,_gggf ,_dfae :=_bbc .ExtractPageText ();if _dfae !=nil {return "",_efee ,_gggf ,_dfae ;};return _edeg .Text (),_efee ,_gggf ,nil ;};func (_bagfa *textTable )putComposite (_bbecc ,_aegbf int ,_gdcec paraList ,_bdccb _aa .PdfRectangle ){if len (_gdcec )==0{_g .Log .Error ("\u0074\u0065xt\u0054\u0061\u0062l\u0065\u0029\u0020\u0070utC\u006fmp\u006f\u0073\u0069\u0074\u0065\u003a\u0020em\u0070\u0074\u0079\u0020\u0070\u0061\u0072a\u0073");
return ;};_cgae :=compositeCell {PdfRectangle :_bdccb ,paraList :_gdcec };if _bffae {_ad .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0070\u0075\u0074\u0043\u006f\u006d\u0070o\u0073i\u0074\u0065\u0028\u0025\u0064\u002c\u0025\u0064\u0029\u003c\u002d\u0025\u0073\u000a",_bbecc ,_aegbf ,_cgae .String ());
};_cgae .updateBBox ();_bagfa ._efcbeb [_cggdf (_bbecc ,_aegbf )]=_cgae ;};func (_aadg *wordBag )highestWord (_gebf int ,_ecde ,_egegb float64 )*textWord {for _ ,_afcf :=range _aadg ._cdaf [_gebf ]{if _ecde <=_afcf ._abace &&_afcf ._abace <=_egegb {return _afcf ;
};};return nil ;};func (_ebcff *textPara )writeCellText (_effcd _da .Writer ){for _afce ,_fcffg :=range _ebcff ._fdfb {_caad :=_fcffg .text ();_faaa :=_cgcg &&_fcffg .endsInHyphen ()&&_afce !=len (_ebcff ._fdfb )-1;if _faaa {_caad =_gecd (_caad );};_effcd .Write ([]byte (_caad ));
if !(_faaa ||_afce ==len (_ebcff ._fdfb )-1){_effcd .Write ([]byte (_gbdbg (_fcffg ._agcc ,_ebcff ._fdfb [_afce +1]._agcc )));};};};func (_cdbd *wordBag )applyRemovals (_afca map[int ]map[*textWord ]struct{}){for _bcbg ,_dgfec :=range _afca {if len (_dgfec )==0{continue ;
};_edfb :=_cdbd ._cdaf [_bcbg ];_gfag :=len (_edfb )-len (_dgfec );if _gfag ==0{delete (_cdbd ._cdaf ,_bcbg );continue ;};_daee :=make ([]*textWord ,_gfag );_abab :=0;for _ ,_addac :=range _edfb {if _ ,_eedg :=_dgfec [_addac ];!_eedg {_daee [_abab ]=_addac ;
_abab ++;};};_cdbd ._cdaf [_bcbg ]=_daee ;};};func (_bfge *shapesState )closePath (){if _bfge ._geee {_bfge ._agdg =append (_bfge ._agdg ,_cgfa (_bfge ._cfddfa ));_bfge ._geee =false ;}else if len (_bfge ._agdg )==0{if _fdee {_g .Log .Debug ("\u0063\u006c\u006f\u0073eP\u0061\u0074\u0068\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f\u0020\u0070\u0061t\u0068");
};_bfge ._geee =false ;return ;};_bfge ._agdg [len (_bfge ._agdg )-1].close ();if _fdee {_g .Log .Info ("\u0063\u006c\u006f\u0073\u0065\u0050\u0061\u0074\u0068\u003a\u0020\u0025\u0073",_bfge );};};func _cfgg (_daedf ,_dbdcec _dc .Point )rulingKind {_cgdd :=_ea .Abs (_daedf .X -_dbdcec .X );
_fdcg :=_ea .Abs (_daedf .Y -_dbdcec .Y );return _feede (_cgdd ,_fdcg ,_agefe );};func (_ebea *imageExtractContext )extractXObjectImage (_fgd *_cg .PdfObjectName ,_ggf _fc .GraphicsState ,_ede *_aa .PdfPageResources )error {_dcca ,_ :=_ede .GetXObjectByName (*_fgd );
if _dcca ==nil {return nil ;};_bcg ,_dfb :=_ebea ._gafab [_dcca ];if !_dfb {_bff ,_gbb :=_ede .GetXObjectImageByName (*_fgd );if _gbb !=nil {return _gbb ;};if _bff ==nil {return nil ;};_deg ,_gbb :=_bff .ToImage ();if _gbb !=nil {return _gbb ;};var _bfe _ff .Image ;
if _bff .Mask !=nil {if _bfe ,_gbb =_dbebb (_bff .Mask ,_ed .Opaque );_gbb !=nil {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a \u0063\u006f\u0075\u006c\u0064 \u006eo\u0074\u0020\u0067\u0065\u0074\u0020\u0065\u0078\u0070\u006c\u0069\u0063\u0069\u0074\u0020\u0069\u006d\u0061\u0067e\u0020\u006d\u0061\u0073\u006b\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e");
};}else if _bff .SMask !=nil {_bfe ,_gbb =_gbcb (_bff .SMask ,_ed .Opaque );if _gbb !=nil {_g .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0066\u0074\u0020\u0069\u006da\u0067e\u0020\u006d\u0061\u0073k\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
};};if _bfe !=nil {_afb ,_ddd :=_deg .ToGoImage ();if _ddd !=nil {return _ddd ;};_afb =_cfeaf (_afb ,_bfe );switch _bff .ColorSpace .String (){case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0049n\u0064\u0065\u0078\u0065\u0064":_deg ,_ddd =_aa .ImageHandling .NewGrayImageFromGoImage (_afb );
if _ddd !=nil {return _ddd ;};default:_deg ,_ddd =_aa .ImageHandling .NewImageFromGoImage (_afb );if _ddd !=nil {return _ddd ;};};};_bcg =&cachedImage {_gaa :_deg ,_ded :_bff .ColorSpace };_ebea ._gafab [_dcca ]=_bcg ;};_gcb :=_bcg ._gaa ;_ege :=_bcg ._ded ;
_fcae ,_agdc :=_ege .ImageToRGB (*_gcb );if _agdc !=nil {return _agdc ;};_g .Log .Debug ("@\u0044\u006f\u0020\u0043\u0054\u004d\u003a\u0020\u0025\u0073",_ggf .CTM .String ());_dga :=ImageMark {Image :&_fcae ,Width :_ggf .CTM .ScalingFactorX (),Height :_ggf .CTM .ScalingFactorY (),Angle :_ggf .CTM .Angle ()};
_dga .X ,_dga .Y =_ggf .CTM .Translation ();_ebea ._fbb =append (_ebea ._fbb ,_dga );_ebea ._dbg ++;return nil ;};

// String returns a human readable description of `s`.
func (_fdbde intSet )String ()string {var _afbee []int ;for _eaefb :=range _fdbde {if _fdbde .has (_eaefb ){_afbee =append (_afbee ,_eaefb );};};_f .Ints (_afbee );return _ad .Sprintf ("\u0025\u002b\u0076",_afbee );};const (_befa markKind =iota ;_ccfaa ;
_cfcbf ;_eggg ;);func _cfg (_bffa *TextMarkArray ,_cabd int )int {_dfee :=_bffa .Elements ();_bcb :=_cabd -1;_ada :=_cabd +1;_adeg :=-1;if _bcb >=0{_bde :=_dfee [_bcb ];_ced :=_bde .ObjString ;_fdab :=len (_ced );_cac :=_bde .Index ;if _cac +1< _fdab {return _bcb ;
};};if _ada < len (_dfee ){_ebeae :=_dfee [_ada ];_dee :=_ebeae .ObjString ;if _dee [0]!=_ebeae .Text {return _ada ;};};if _adeg ==-1&&_dfee [_cabd ].Text =="\u0020"{return _bcb ;};return _adeg ;};func (_bafd compositeCell )split (_gdbcf ,_efbfa []float64 )*textTable {_dffd :=len (_gdbcf )+1;
_fcead :=len (_efbfa )+1;if _bffae {_g .Log .Info ("\u0063\u006f\u006d\u0070\u006f\u0073\u0069t\u0065\u0043\u0065l\u006c\u002e\u0073\u0070l\u0069\u0074\u003a\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u000a\u0009\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u003d\u0025\u0073\u000a"+"\u0009\u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073=\u0025\u0036\u002e\u0032\u0066\u000a\t\u0063\u006f\u006c\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u003d%\u0036\u002e\u0032\u0066",_fcead ,_dffd ,_bafd ,_gdbcf ,_efbfa );
_ad .Printf ("\u0020\u0020\u0020\u0020\u0025\u0064\u0020\u0070\u0061\u0072\u0061\u0073\u000a",len (_bafd .paraList ));for _eecbb ,_agcba :=range _bafd .paraList {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_eecbb ,_agcba .String ());
};_ad .Printf ("\u0020\u0020\u0020\u0020\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u000a",len (_bafd .lines ()));for _ddbg ,_gbfbd :=range _bafd .lines (){_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ddbg ,_gbfbd );};};_gdbcf =_geba (_gdbcf ,_bafd .Ury ,_bafd .Lly );
_efbfa =_geba (_efbfa ,_bafd .Llx ,_bafd .Urx );_dccba :=make (map[uint64 ]*textPara ,_fcead *_dffd );_fcba :=textTable {_ebdg :_fcead ,_gacfc :_dffd ,_fdgeg :_dccba };_egfg :=_bafd .paraList ;_f .Slice (_egfg ,func (_bage ,_gbda int )bool {_bfafg ,_beea :=_egfg [_bage ],_egfg [_gbda ];
_bgca ,_ddac :=_bfafg .Lly ,_beea .Lly ;if _bgca !=_ddac {return _bgca < _ddac ;};return _bfafg .Llx < _beea .Llx ;});_bcde :=make (map[uint64 ]_aa .PdfRectangle ,_fcead *_dffd );for _bccd ,_afbaf :=range _gdbcf [1:]{_adfgf :=_gdbcf [_bccd ];for _acab ,_ffbe :=range _efbfa [1:]{_gggdc :=_efbfa [_acab ];
_bcde [_cggdf (_acab ,_bccd )]=_aa .PdfRectangle {Llx :_gggdc ,Urx :_ffbe ,Lly :_afbaf ,Ury :_adfgf };};};if _bffae {_g .Log .Info ("\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0043\u0065l\u006c\u002e\u0073\u0070\u006c\u0069\u0074\u003a\u0020\u0072e\u0063\u0074\u0073");
_ad .Printf ("\u0020\u0020\u0020\u0020");for _dgdab :=0;_dgdab < _fcead ;_dgdab ++{_ad .Printf ("\u0025\u0033\u0030\u0064\u002c\u0020",_dgdab );};_ad .Println ();for _egdf :=0;_egdf < _dffd ;_egdf ++{_ad .Printf ("\u0020\u0020\u0025\u0032\u0064\u003a",_egdf );
for _gfcf :=0;_gfcf < _fcead ;_gfcf ++{_ad .Printf ("\u00256\u002e\u0032\u0066\u002c\u0020",_bcde [_cggdf (_gfcf ,_egdf )]);};_ad .Println ();};};_gegg :=func (_cbgc *textLine )(int ,int ){for _ggcc :=0;_ggcc < _dffd ;_ggcc ++{for _ebce :=0;_ebce < _fcead ;
_ebce ++{if _agaea (_bcde [_cggdf (_ebce ,_ggcc )],_cbgc .PdfRectangle ){return _ebce ,_ggcc ;};};};return -1,-1;};_fcgb :=make (map[uint64 ][]*textLine ,_fcead *_dffd );for _ ,_cbcd :=range _egfg .lines (){_efcd ,_fbaa :=_gegg (_cbcd );if _efcd < 0{continue ;
};_fcgb [_cggdf (_efcd ,_fbaa )]=append (_fcgb [_cggdf (_efcd ,_fbaa )],_cbcd );};for _ecce :=0;_ecce < len (_gdbcf )-1;_ecce ++{_abea :=_gdbcf [_ecce ];_baad :=_gdbcf [_ecce +1];for _aceg :=0;_aceg < len (_efbfa )-1;_aceg ++{_dgdac :=_efbfa [_aceg ];_eecec :=_efbfa [_aceg +1];
_acba :=_aa .PdfRectangle {Llx :_dgdac ,Urx :_eecec ,Lly :_baad ,Ury :_abea };_efeb :=_fcgb [_cggdf (_aceg ,_ecce )];if len (_efeb )==0{continue ;};_affeb :=_bgdca (_acba ,_efeb );_fcba .put (_aceg ,_ecce ,_affeb );};};return &_fcba ;};func (_bdga paraList )llyRange (_cfbbd []int ,_afaf ,_febbf float64 )[]int {_aeec :=len (_bdga );
if _febbf < _bdga [_cfbbd [0]].Lly ||_afaf > _bdga [_cfbbd [_aeec -1]].Lly {return nil ;};_abbe :=_f .Search (_aeec ,func (_fbde int )bool {return _bdga [_cfbbd [_fbde ]].Lly >=_afaf });_dfge :=_f .Search (_aeec ,func (_dbeff int )bool {return _bdga [_cfbbd [_dbeff ]].Lly > _febbf });
return _cfbbd [_abbe :_dfge ];};func (_cfdb *textTable )emptyCompositeColumn (_aafc int )bool {for _dede :=0;_dede < _cfdb ._gacfc ;_dede ++{if _efedf ,_cfgc :=_cfdb ._efcbeb [_cggdf (_aafc ,_dede )];_cfgc {if len (_efedf .paraList )> 0{return false ;};
};};return true ;};func (_fgbbbd *textTable )depth ()float64 {_debca :=1e10;for _aefd :=0;_aefd < _fgbbbd ._ebdg ;_aefd ++{_gaefd :=_fgbbbd .get (_aefd ,0);if _gaefd ==nil ||_gaefd ._caaa {continue ;};_debca =_ea .Min (_debca ,_gaefd .depth ());};return _debca ;
};

// NewEditor returns a new Editor object
func NewEditor (reader *_aa .PdfReader )*Editor {return &Editor {_fedd :reader }};func (_feeaa *ruling )alignsSec (_agbea *ruling )bool {const _fegdc =_cafa ****;return _feeaa ._decc -_fegdc <=_agbea ._gcda &&_agbea ._decc -_fegdc <=_feeaa ._gcda ;};func (_gcfgd paraList )findTextTables ()[]*textTable {var _cadgc []*textTable ;
for _ ,_agce :=range _gcfgd {if _agce .taken ()||_agce .Width ()==0{continue ;};_cadcd :=_agce .isAtom ();if _cadcd ==nil {continue ;};_cadcd .growTable ();if _cadcd ._ebdg *_cadcd ._gacfc < _cabaf {continue ;};_cadcd .markCells ();_cadcd .log ("\u0067\u0072\u006fw\u006e");
_cadgc =append (_cadgc ,_cadcd );};return _cadgc ;};func _dbgbd (_agdgd map[int ][]float64 ){if len (_agdgd )<=1{return ;};_bbgga :=_geea (_agdgd );if _bffae {_g .Log .Info ("\u0066i\u0078C\u0065\u006c\u006c\u0073\u003a \u006b\u0065y\u0073\u003d\u0025\u002b\u0076",_bbgga );
};var _dfcae ,_cafcc int ;for _dfcae ,_cafcc =range _bbgga {if _agdgd [_cafcc ]!=nil {break ;};};for _fcdc ,_ggdc :=range _bbgga [_dfcae :]{_bgdbg :=_agdgd [_ggdc ];if _bgdbg ==nil {continue ;};if _bffae {_ad .Printf ("\u0025\u0034\u0064\u003a\u0020\u006b\u0030\u003d\u0025\u0064\u0020\u006b1\u003d\u0025\u0064\u000a",_dfcae +_fcdc ,_cafcc ,_ggdc );
};_gbbdg :=_agdgd [_ggdc ];if _gbbdg [len (_gbbdg )-1]> _bgdbg [0]{_gbbdg [len (_gbbdg )-1]=_bgdbg [0];_agdgd [_cafcc ]=_gbbdg ;};_cafcc =_ggdc ;};};var _abc =TextMark {Text :"\u005b\u0058\u005d",Original :"\u0020",Meta :true ,FillColor :_ed .White ,StrokeColor :_ed .White };
func (_ggggg *textObject )nextLine (){_ggggg .moveLP (0,-_ggggg ._abf ._dcfb )};func (_eceae *textTable )log (_ddgb string ){if !_bffae {return ;};_g .Log .Info ("~\u007e\u007e\u0020\u0025\u0073\u003a \u0025\u0064\u0020\u0078\u0020\u0025d\u0020\u0067\u0072\u0069\u0064\u003d\u0025t\u000a\u0020\u0020\u0020\u0020\u0020\u0020\u0025\u0036\u002e2\u0066",_ddgb ,_eceae ._ebdg ,_eceae ._gacfc ,_eceae ._ccfad ,_eceae .PdfRectangle );
for _eebf :=0;_eebf < _eceae ._gacfc ;_eebf ++{for _afcc :=0;_afcc < _eceae ._ebdg ;_afcc ++{_fgeg :=_eceae .get (_afcc ,_eebf );if _fgeg ==nil {continue ;};_ad .Printf ("%\u0034\u0064\u0020\u00252d\u003a \u0025\u0036\u002e\u0032\u0066 \u0025\u0071\u0020\u0025\u0064\u000a",_afcc ,_eebf ,_fgeg .PdfRectangle ,_ecegb (_fgeg .text (),50),_bc .RuneCountInString (_fgeg .text ()));
};};};

// Text gets the extracted text contained in `l`.
func (_eefa *list )Text ()string {_dabe :=&_add .Builder {};_ggae :="";_bbdec (_eefa ,_dabe ,&_ggae );return _dabe .String ();};var _bggb =[]string {"\u2756","\u27a2","\u2713","\u2022","\uf0a7","\u25a1","\u2212","\u25a0","\u25aa","\u006f"};func _dcdg (_abge []*_aa .KValue ,_bee map[int ][]*textLine ,_gbga _cg .PdfObject )[]*list {_dfedcg :=[]*list {};
for _ ,_agfbb :=range _abge {_cece :=_agfbb .GetKDict ();_ddad :=_cece .GetChildren ();_gfbf :=[]*textLine {};_bbgeb :=[]*list {};_cdafe :=_cece .S .(*_cg .PdfObjectName ).String ();_fbdg :=_ddad [0];_ggcbe :=_fbdg .GetMCID ();if len (_ddad )==1&&_ggcbe !=nil &&*_ggcbe !=-1{if _cece .Pg ==_gbga {_gfbf =_bee [*_ggcbe ];
};}else {_bbgeb =_dcdg (_ddad ,_bee ,_gbga );};_agda :=_cgbd (_gfbf ,_cdafe ,_bbgeb );_dfedcg =append (_dfedcg ,_agda );};return _dfedcg ;};func (_aceea rulingList )comp (_dgcdf ,_cbda int )bool {_ggegb ,_ddfcc :=_aceea [_dgcdf ],_aceea [_cbda ];_ebcc ,_fega :=_ggegb ._cafff ,_ddfcc ._cafff ;
if _ebcc !=_fega {return _ebcc > _fega ;};if _ebcc ==_bccgg {return false ;};_dfaea :=func (_bdfee bool )bool {if _ebcc ==_feed {return _bdfee ;};return !_bdfee ;};_addfad ,_egbea :=_ggegb ._eced ,_ddfcc ._eced ;if _addfad !=_egbea {return _dfaea (_addfad > _egbea );
};_addfad ,_egbea =_ggegb ._decc ,_ddfcc ._decc ;if _addfad !=_egbea {return _dfaea (_addfad < _egbea );};return _dfaea (_ggegb ._gcda < _ddfcc ._gcda );};func _cgeed (_ggaa ,_bbffa _aa .PdfRectangle )(_aa .PdfRectangle ,bool ){if !_afgf (_ggaa ,_bbffa ){return _aa .PdfRectangle {},false ;
};return _aa .PdfRectangle {Llx :_ea .Max (_ggaa .Llx ,_bbffa .Llx ),Urx :_ea .Min (_ggaa .Urx ,_bbffa .Urx ),Lly :_ea .Max (_ggaa .Lly ,_bbffa .Lly ),Ury :_ea .Min (_ggaa .Ury ,_bbffa .Ury )},true ;};func (_ceaf *wordBag )depthRange (_gbad ,_eabe int )[]int {var _cbge []int ;
for _edgf :=range _ceaf ._cdaf {if _gbad <=_edgf &&_edgf <=_eabe {_cbge =append (_cbge ,_edgf );};};if len (_cbge )==0{return nil ;};_f .Ints (_cbge );return _cbge ;};func (_bagg *shapesState )newSubPath (){_bagg .clearPath ();if _fdee {_g .Log .Info ("\u006e\u0065\u0077\u0053\u0075\u0062\u0050\u0061\u0074h\u003a\u0020\u0025\u0073",_bagg );
};};func (_edfa *wordBag )depthIndexes ()[]int {if len (_edfa ._cdaf )==0{return nil ;};_edega :=make ([]int ,len (_edfa ._cdaf ));_fggb :=0;for _bagb :=range _edfa ._cdaf {_edega [_fggb ]=_bagb ;_fggb ++;};_f .Ints (_edega );return _edega ;};func (_ggcf *textTable )isExportable ()bool {if _ggcf ._ccfad {return true ;
};_gcdd :=func (_bcaecg int )bool {_dbbbb :=_ggcf .get (0,_bcaecg );if _dbbbb ==nil {return false ;};_dgag :=_dbbbb .text ();_agafg :=_bc .RuneCountInString (_dgag );_eddgd :=_dabgd .MatchString (_dgag );return _agafg <=1||_eddgd ;};for _eeeb :=0;_eeeb < _ggcf ._gacfc ;
_eeeb ++{if !_gcdd (_eeeb ){return true ;};};return false ;};

// ImageMark represents an image drawn on a page and its position in device coordinates.
// All coordinates are in device coordinates.
type ImageMark struct{Image *_aa .Image ;

// Dimensions of the image as displayed in the PDF.
Width float64 ;Height float64 ;

// Position of the image in PDF coordinates (lower left corner).
X float64 ;Y float64 ;

// Angle in degrees, if rotated.
Angle float64 ;};func (_dgcf rulingList )toTilings ()(rulingList ,[]gridTiling ){_dgcf .log ("\u0074o\u0054\u0069\u006c\u0069\u006e\u0067s");if len (_dgcf )==0{return nil ,nil ;};_dgcf =_dgcf .tidied ("\u0061\u006c\u006c");_dgcf .log ("\u0074\u0069\u0064\u0069\u0065\u0064");
_agba :=_dgcf .toGrids ();_aabcg :=make ([]gridTiling ,len (_agba ));for _baafc ,_fdbc :=range _agba {_aabcg [_baafc ]=_fdbc .asTiling ();};return _dgcf ,_aabcg ;};

// String returns a description of `p`.
func (_bdac *textPara )String ()string {if _bdac ._caaa {return _ad .Sprintf ("\u0025\u0036\u002e\u0032\u0066\u0020\u005b\u0045\u004d\u0050\u0054\u0059\u005d",_bdac .PdfRectangle );};_fface :="";if _bdac ._eddc !=nil {_fface =_ad .Sprintf ("\u005b\u0025\u0064\u0078\u0025\u0064\u005d\u0020",_bdac ._eddc ._ebdg ,_bdac ._eddc ._gacfc );
};return _ad .Sprintf ("\u0025\u0036\u002e\u0032f \u0025\u0073\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u0020\u0025\u0071",_bdac .PdfRectangle ,_fface ,len (_bdac ._fdfb ),_ecegb (_bdac .text (),50));};func (_eeadf *textWord )addDiacritic (_dbac string ){_bbece :=_eeadf ._fcacg [len (_eeadf ._fcacg )-1];
_bbece ._gacc +=_dbac ;_bbece ._gacc =_b .NFKC .String (_bbece ._gacc );};func (_cagc *shapesState )cubicTo (_ecbc ,_adb ,_cegd ,_faafc ,_ebeg ,_geeeg float64 ){if _fdee {_g .Log .Info ("\u0063\u0075\u0062\u0069\u0063\u0054\u006f\u003a");};_cagc .addPoint (_ebeg ,_geeeg );
};func (_gecc paraList )tables ()[]TextTable {var _afge []TextTable ;if _bffae {_g .Log .Info ("\u0070\u0061\u0072\u0061\u0073\u002e\u0074\u0061\u0062\u006c\u0065\u0073\u003a");};for _ ,_ccagg :=range _gecc {_gcge :=_ccagg ._eddc ;if _gcge !=nil &&_gcge .isExportable (){_afge =append (_afge ,_gcge .toTextTable ());
};};return _afge ;};func (_ddgg *textObject )setCharSpacing (_fbcf float64 ){if _ddgg ==nil {return ;};_ddgg ._abf ._debe =_fbcf ;if _bfga {_g .Log .Info ("\u0073\u0065t\u0043\u0068\u0061\u0072\u0053\u0070\u0061\u0063\u0069\u006e\u0067\u003a\u0020\u0025\u002e\u0032\u0066\u0020\u0073\u0074\u0061\u0074e=\u0025\u0073",_fbcf ,_ddgg ._abf .String ());
};};func _dfc (_fec []string ,_bfa int ,_fa int ){for _af ,_aeb :=_bfa ,_fa -1;_af < _aeb ;_af ,_aeb =_af +1,_aeb -1{_agb :=_fec [_af ];_fec [_af ]=_fec [_aeb ];_fec [_aeb ]=_agb ;};};type list struct{_feff []*textLine ;_ccae string ;_bfaf []*list ;_cdfd string ;
};func _ggba (_gece byte )bool {for _ ,_ceae :=range _bggb {if []byte (_ceae )[0]==_gece {return true ;};};return false ;};