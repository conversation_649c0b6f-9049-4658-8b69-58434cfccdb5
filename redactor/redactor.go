//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package redactor ;import (_a "errors";_f "fmt";_b "github.com/unidoc/unipdf/v4/common";_cd "github.com/unidoc/unipdf/v4/contentstream";_ac "github.com/unidoc/unipdf/v4/core";_aa "github.com/unidoc/unipdf/v4/creator";_adc "github.com/unidoc/unipdf/v4/extractor";
_g "github.com/unidoc/unipdf/v4/model";_c "io";_d "regexp";_ee "sort";_ad "strings";);func _ega (_aged []int ,_feac *_adc .TextMarkArray ,_gbc string )(*_adc .TextMarkArray ,matchedBBox ,error ){_dff :=matchedBBox {};_dacf :=_aged [0];_faf :=_aged [1];
_fagc :=len (_gbc )-len (_ad .TrimLeft (_gbc ,"\u0020"));_bdcg :=len (_gbc )-len (_ad .TrimRight (_gbc ,"\u0020\u000a"));_dacf =_dacf +_fagc ;_faf =_faf -_bdcg ;_gbc =_ad .Trim (_gbc ,"\u0020\u000a");_gaee ,_gecg :=_feac .RangeOffset (_dacf ,_faf );if _gecg !=nil {return nil ,_dff ,_gecg ;
};_bdgd ,_bec :=_gaee .BBox ();if !_bec {return nil ,_dff ,_f .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_gaee );
};_dff =matchedBBox {_gfd :_gbc ,_eaac :_bdgd };return _gaee ,_dff ,nil ;};type localSpanMarks struct{_cggf *_adc .TextMarkArray ;_agf int ;_feab string ;};func _facc (_gbaf *_adc .TextMarkArray ,_fceb int ,_geg int )int {_begf :=_gbaf .Elements ();_acgf :=_fceb -1;
_bcfc :=_fceb +1;_befgd :=-1;if _acgf >=0{_gadf :=_begf [_acgf ];_aaa :=_gadf .ObjString ;_dccc :=len (_aaa );_efb :=_gadf .Index ;if _efb +1< _dccc {_befgd =_acgf ;return _befgd ;};};if _bcfc < len (_begf ){_bea :=_begf [_bcfc ];_daee :=_bea .ObjString ;
if _daee [0]!=_bea .Text {_befgd =_bcfc ;return _befgd ;};};return _befgd ;};func _cb (_df *_adc .TextMarkArray )int {_baa :=0;_gb :=_df .Elements ();if _gb [0].Text =="\u0020"{_baa ++;};if _gb [_df .Len ()-1].Text =="\u0020"{_baa ++;};return _baa ;};func _ecb (_edg localSpanMarks ,_eca *_adc .TextMarkArray ,_agd *_g .PdfFont ,_cgg ,_fea string )([]_ac .PdfObject ,error ){_dfe :=_bce (_eca );
Tj ,_gga :=_gdc (_eca );if _gga !=nil {return nil ,_gga ;};_bag :=len (_cgg );_cgga :=len (_dfe );_cfa :=-1;_aga :=_ac .MakeFloat (Tj );if _dfe !=_fea {_fgae :=_edg ._agf ;if _fgae ==0{_cfa =_ad .LastIndex (_cgg ,_dfe );}else {_cfa =_ad .Index (_cgg ,_dfe );
};}else {_cfa =_ad .Index (_cgg ,_dfe );};_aaf :=_cfa +_cgga ;_bae :=[]_ac .PdfObject {};if _cfa ==0&&_aaf ==_bag {_bae =append (_bae ,_aga );}else if _cfa ==0&&_aaf < _bag {_ecbb :=_fcb (_cgg [_aaf :],_agd );_adb :=_ac .MakeStringFromBytes (_ecbb );_bae =append (_bae ,_aga ,_adb );
}else if _cfa > 0&&_aaf >=_bag {_bdc :=_fcb (_cgg [:_cfa ],_agd );_adba :=_ac .MakeStringFromBytes (_bdc );_bae =append (_bae ,_adba ,_aga );}else if _cfa > 0&&_aaf < _bag {_gda :=_fcb (_cgg [:_cfa ],_agd );_gfc :=_fcb (_cgg [_aaf :],_agd );_eefd :=_ac .MakeStringFromBytes (_gda );
_ead :=_ac .MakeString (string (_gfc ));_bae =append (_bae ,_eefd ,_aga ,_ead );};return _bae ,nil ;};func _ffg (_fg *_g .PdfFont ,_ca _adc .TextMark )float64 {_dbc :=0.001;_dca :=_ca .Th /100;if _fg .Subtype ()=="\u0054\u0079\u0070e\u0033"{_dbc =1;};_ffgb ,_fege :=_fg .GetRuneMetrics (' ');
if !_fege {_ffgb ,_fege =_fg .GetCharMetrics (32);};if !_fege {_ffgb ,_ =_g .DefaultFont ().GetRuneMetrics (' ');};_ebe :=_dbc *((_ffgb .Wx *_ca .FontSize +_ca .Tc +_ca .Tw )/_dca );return _ebe ;};func _gdc (_dgd *_adc .TextMarkArray )(float64 ,error ){_dbce ,_fff :=_dgd .BBox ();
if !_fff {return 0.0,_f .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_dgd );
};_cfe :=_cb (_dgd );_bee :=0.0;_ ,_gee :=_fag (_dgd );_ded :=_dgd .Elements ()[_gee ];_afb :=_ded .Font ;if _cfe > 0{_bee =_ffg (_afb ,_ded );};_ggc :=(_dbce .Urx -_dbce .Llx );_ggc =_ggc +_bee *float64 (_cfe );Tj :=_bdf (_ggc ,_ded .FontSize ,_ded .Th );
return Tj ,nil ;};func _eb (_fd *_cd .ContentStreamOperations ,_fa map[_ac .PdfObject ][]localSpanMarks )error {for _fdf ,_fac :=range _fa {if _fdf ==nil {continue ;};_cc ,_bf ,_dc :=_efc (_fd ,_fdf );if !_dc {_b .Log .Debug ("Pd\u0066\u004fb\u006a\u0065\u0063\u0074\u0020\u0025\u0073\u006e\u006ft\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0073\u0069\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0073\u0074r\u0065a\u006d\u0020\u006f\u0070\u0065\u0072\u0061\u0074i\u006fn\u0020\u0025s",_fdf ,_fd );
return nil ;};if _cc .Operand =="\u0054\u006a"{_ba :=_edf (_cc ,_fdf ,_fac );if _ba !=nil {return _ba ;};}else if _cc .Operand =="\u0054\u004a"{_ed :=_gbb (_cc ,_fdf ,_fac );if _ed !=nil {return _ed ;};}else if _cc .Operand =="\u0027"||_cc .Operand =="\u0022"{_dea :=_ea (_fd ,_cc .Operand ,_bf );
if _dea !=nil {return _dea ;};_dea =_edf (_cc ,_fdf ,_fac );if _dea !=nil {return _dea ;};};};return nil ;};func (_ccc *Redactor )redactPage (_cccc *_cd .ContentStreamOperations ,_eff *_g .PdfPageResources )([]matchedBBox ,*_cd .ContentStreamOperations ,error ){_ggaf ,_afa :=_adc .NewFromContents (_cccc .String (),_eff );
if _afa !=nil {return nil ,nil ,_afa ;};_dcc ,_ ,_ ,_afa :=_ggaf .ExtractPageText ();if _afa !=nil {return nil ,nil ,_afa ;};_cccc =_dcc .GetContentStreamOps ();_gaef :=_dcc .Marks ();_efgb :=_dcc .Text ();_efgb ,_cddf :=_caef (_efgb );_gdea :=[]matchedBBox {};
_bgad :=make (map[_ac .PdfObject ][]localSpanMarks );_dccg :=[]*targetMap {};for _ ,_daa :=range _ccc ._bgda .Terms {_beb ,_fde :=_fcee (_daa );if _fde !=nil {return nil ,nil ,_fde ;};_ecge ,_fde :=_beb .match (_efgb );if _fde !=nil {return nil ,nil ,_fde ;
};_ecge =_dffg (_ecge ,_cddf );_feb :=_baf (_ecge );_dccg =append (_dccg ,_feb ...);};_babe (_dccg );for _ ,_bcc :=range _dccg {_bddef :=_bcc ._bga ;_faa :=_bcc ._fdcd ;_gea :=[]matchedBBox {};for _ ,_gcfb :=range _faa {_dacg ,_aecb ,_ecdd :=_ega (_gcfb ,_gaef ,_bddef );
if _ecdd !=nil {return nil ,nil ,_ecdd ;};_bgaef :=_afad (_dacg );for _agdb ,_cbb :=range _bgaef {_fdg :=localSpanMarks {_cggf :_cbb ,_agf :_agdb ,_feab :_bddef };_edc ,_ :=_fag (_cbb );if _dcfa ,_ebb :=_bgad [_edc ];_ebb {_bgad [_edc ]=append (_dcfa ,_fdg );
}else {_bgad [_edc ]=[]localSpanMarks {_fdg };};};_gea =append (_gea ,_aecb );};_gdea =append (_gdea ,_gea ...);};_afa =_eb (_cccc ,_bgad );if _afa !=nil {return nil ,nil ,_afa ;};return _gdea ,_cccc ,nil ;};func _bce (_fec *_adc .TextMarkArray )string {_ggb :="";
for _ ,_bde :=range _fec .Elements (){_ggb +=_bde .Text ;};return _ggb ;};func _aded (_bcf string ,_eac []localSpanMarks )([]placeHolders ,error ){_fbe :="";_aba :=[]placeHolders {};for _fbab ,_eeb :=range _eac {_bdga :=_eeb ._cggf ;_adcg :=_eeb ._feab ;
_fef :=_bce (_bdga );_faga ,_gfb :=_gdc (_bdga );if _gfb !=nil {return nil ,_gfb ;};if _fef !=_fbe {var _aabdc []int ;if _fbab ==0&&_adcg !=_fef {_dgg :=_ad .Index (_bcf ,_fef );_aabdc =[]int {_dgg };}else if _fbab ==len (_eac )-1{_eda :=_ad .LastIndex (_bcf ,_fef );
_aabdc =[]int {_eda };}else {_aabdc =_gaf (_bcf ,_fef );};_ffga :=placeHolders {_af :_aabdc ,_aag :_fef ,_be :_faga };_aba =append (_aba ,_ffga );};_fbe =_fef ;};return _aba ,nil ;};func _ea (_ccd *_cd .ContentStreamOperations ,_cf string ,_ae int )error {_gd :=_cd .ContentStreamOperations {};
var _eeg _cd .ContentStreamOperation ;for _aac ,_fdc :=range *_ccd {if _aac ==_ae {if _cf =="\u0027"{_aea :=_cd .ContentStreamOperation {Operand :"\u0054\u002a"};_gd =append (_gd ,&_aea );_eeg .Params =_fdc .Params ;_eeg .Operand ="\u0054\u006a";_gd =append (_gd ,&_eeg );
}else if _cf =="\u0022"{_gc :=_fdc .Params [:2];Tc ,Tw :=_gc [0],_gc [1];_bd :=_cd .ContentStreamOperation {Params :[]_ac .PdfObject {Tc },Operand :"\u0054\u0063"};_gd =append (_gd ,&_bd );_bd =_cd .ContentStreamOperation {Params :[]_ac .PdfObject {Tw },Operand :"\u0054\u0077"};
_gd =append (_gd ,&_bd );_eeg .Params =[]_ac .PdfObject {_fdc .Params [2]};_eeg .Operand ="\u0054\u006a";_gd =append (_gd ,&_eeg );};};_gd =append (_gd ,_fdc );};*_ccd =_gd ;return nil ;};

// Redactor represents a Redactor object.
type Redactor struct{_cae *_g .PdfReader ;_bgda *RedactionOptions ;_agg *_aa .Creator ;_adde *RectangleProps ;};func (_dec *regexMatcher )match (_beeb string )([]*matchedIndex ,error ){_gef :=_dec ._ddfe .Pattern ;if _gef ==nil {return nil ,_a .New ("\u006e\u006f\u0020\u0070at\u0074\u0065\u0072\u006e\u0020\u0063\u006f\u006d\u0070\u0069\u006c\u0065\u0064");
};var (_aadf =_gef .FindAllStringIndex (_beeb ,-1);_eafd []*matchedIndex ;);for _ ,_aaaf :=range _aadf {_eafd =append (_eafd ,&matchedIndex {_dace :_aaaf [0],_fbag :_aaaf [1],_eee :_beeb [_aaaf [0]:_aaaf [1]]});};return _eafd ,nil ;};

// Redact executes the redact operation on a pdf file and updates the content streams of all pages of the file.
func (_fbac *Redactor )Redact ()error {_aabc ,_bcfe :=_fbac ._cae .GetNumPages ();if _bcfe !=nil {return _f .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064 \u0074\u006f\u0020\u0067\u0065\u0074\u0020\u0074\u0068\u0065\u0020\u006e\u0075m\u0062\u0065\u0072\u0020\u006f\u0066\u0020P\u0061\u0067\u0065\u0073");
};_dddb :=_fbac ._adde .FillColor ;_dggc :=_fbac ._adde .BorderWidth ;_bddb :=_fbac ._adde .FillOpacity ;for _bgae :=1;_bgae <=_aabc ;_bgae ++{_afe ,_ecd :=_fbac ._cae .GetPage (_bgae );if _ecd !=nil {return _ecd ;};_dcfc ,_ecd :=_adc .New (_afe );if _ecd !=nil {return _ecd ;
};_ggg ,_ ,_ ,_ecd :=_dcfc .ExtractPageText ();if _ecd !=nil {return _ecd ;};_ddc :=_ggg .GetContentStreamOps ();_dedd ,_fffb ,_ecd :=_fbac .redactPage (_ddc ,_afe .Resources );if _fffb ==nil {_b .Log .Info ("N\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0066\u006f\u0072\u0020t\u0068\u0065\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065d \u0070\u0061\u0074t\u0061r\u006e\u002e");
_fffb =_ddc ;};_bcd :=_cd .ContentStreamOperation {Operand :"\u006e"};*_fffb =append (*_fffb ,&_bcd );_afe .SetContentStreams ([]string {_fffb .String ()},_ac .NewFlateEncoder ());if _ecd !=nil {return _ecd ;};_gba ,_ecd :=_afe .GetMediaBox ();if _ecd !=nil {return _ecd ;
};if _afe .MediaBox ==nil {_afe .MediaBox =_gba ;};if _fadd :=_fbac ._agg .AddPage (_afe );_fadd !=nil {return _fadd ;};_ee .Slice (_dedd ,func (_ede ,_dcd int )bool {return _dedd [_ede ]._gfd < _dedd [_dcd ]._gfd });_dab :=_gba .Ury ;for _ ,_ggdf :=range _dedd {_dbg :=_ggdf ._eaac ;
_aecc :=_fbac ._agg .NewRectangle (_dbg .Llx ,_dab -_dbg .Lly ,_dbg .Urx -_dbg .Llx ,-(_dbg .Ury -_dbg .Lly ));_aecc .SetFillColor (_dddb );_aecc .SetBorderWidth (_dggc );_aecc .SetFillOpacity (_bddb );if _afg :=_fbac ._agg .Draw (_aecc );_afg !=nil {return nil ;
};};};_fbac ._agg .SetOutlineTree (_fbac ._cae .GetOutlineTree ());return nil ;};

// WriteToFile writes the redacted document to file specified by `outputPath`.
func (_fefe *Redactor )WriteToFile (outputPath string )error {if _agge :=_fefe ._agg .WriteToFile (outputPath );_agge !=nil {return _f .Errorf ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0074o\u0020\u0077\u0072\u0069\u0074\u0065\u0020t\u0068\u0065\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020\u0066\u0069\u006c\u0065");
};return nil ;};func _efc (_bdcd *_cd .ContentStreamOperations ,PdfObj _ac .PdfObject )(*_cd .ContentStreamOperation ,int ,bool ){for _dde ,_bagg :=range *_bdcd {_bbc :=_bagg .Operand ;if _bbc =="\u0054\u006a"{_fad :=_ac .TraceToDirectObject (_bagg .Params [0]);
if _fad ==PdfObj {return _bagg ,_dde ,true ;};}else if _bbc =="\u0054\u004a"{_ccg ,_dcfe :=_ac .GetArray (_bagg .Params [0]);if !_dcfe {return nil ,_dde ,_dcfe ;};for _ ,_fdd :=range _ccg .Elements (){if _fdd ==PdfObj {return _bagg ,_dde ,true ;};};}else if _bbc =="\u0022"{_add :=_ac .TraceToDirectObject (_bagg .Params [2]);
if _add ==PdfObj {return _bagg ,_dde ,true ;};}else if _bbc =="\u0027"{_bage :=_ac .TraceToDirectObject (_bagg .Params [0]);if _bage ==PdfObj {return _bagg ,_dde ,true ;};};};return nil ,-1,false ;};func _gaf (_efg ,_cbd string )[]int {if len (_cbd )==0{return nil ;
};var _aace []int ;for _gec :=0;_gec < len (_efg );{_efac :=_ad .Index (_efg [_gec :],_cbd );if _efac < 0{return _aace ;};_aace =append (_aace ,_gec +_efac );_gec +=_efac +len (_cbd );};return _aace ;};

// RedactRectanglePropsNew return a new pointer to a default RectangleProps object.
func RedactRectanglePropsNew ()*RectangleProps {return &RectangleProps {FillColor :_aa .ColorBlack ,BorderWidth :0.0,FillOpacity :1.0};};func _cca (_dfb ,_bddbe targetMap )(bool ,[]int ){_fcef :=_ad .Contains (_dfb ._bga ,_bddbe ._bga );var _ebbd []int ;
for _ ,_dgc :=range _dfb ._fdcd {for _fddg ,_acga :=range _bddbe ._fdcd {if _acga [0]>=_dgc [0]&&_acga [1]<=_dgc [1]{_ebbd =append (_ebbd ,_fddg );};};};return _fcef ,_ebbd ;};func _abag (_abg int ,_efce []int )bool {for _ ,_adbb :=range _efce {if _adbb ==_abg {return true ;
};};return false ;};

// New instantiates a Redactor object with given PdfReader and `regex` pattern.
func New (reader *_g .PdfReader ,opts *RedactionOptions ,rectProps *RectangleProps )*Redactor {if rectProps ==nil {rectProps =RedactRectanglePropsNew ();};return &Redactor {_cae :reader ,_bgda :opts ,_agg :_aa .New (),_adde :rectProps };};type replacement struct{_de string ;
_cdd float64 ;_eea int ;};type regexMatcher struct{_ddfe RedactionTerm };func _ffe (_bgf *targetMap ,_cgf []int ){var _bfb [][]int ;for _gfaa ,_gdcc :=range _bgf ._fdcd {if _abag (_gfaa ,_cgf ){continue ;};_bfb =append (_bfb ,_gdcc );};_bgf ._fdcd =_bfb ;
};

// RectangleProps defines properties of the redaction rectangle to be drawn.
type RectangleProps struct{FillColor _aa .Color ;BorderWidth float64 ;FillOpacity float64 ;};

// Write writes the content of `re.creator` to writer of type io.Writer interface.
func (_ffgf *Redactor )Write (writer _c .Writer )error {return _ffgf ._agg .Write (writer )};func _dbgd (_abb *matchedIndex ,_ced [][]int )[]*matchedIndex {_bfg :=[]*matchedIndex {};_gbf :=_abb ._dace ;_geea :=_gbf ;_geeb :=_abb ._eee ;_dgec :=0;for _ ,_cggb :=range _ced {_gca :=_cggb [0]-_gbf ;
if _dgec >=_gca {continue ;};_dcaf :=_geeb [_dgec :_gca ];_fcbe :=&matchedIndex {_eee :_dcaf ,_dace :_geea ,_fbag :_cggb [0]};if len (_ad .TrimSpace (_dcaf ))!=0{_bfg =append (_bfg ,_fcbe );};_dgec =_cggb [1]-_gbf ;_geea =_gbf +_dgec ;};_gcc :=_geeb [_dgec :];
_egg :=&matchedIndex {_eee :_gcc ,_dace :_geea ,_fbag :_abb ._fbag };if len (_ad .TrimSpace (_gcc ))!=0{_bfg =append (_bfg ,_egg );};return _bfg ;};func _afad (_aacf *_adc .TextMarkArray )[]*_adc .TextMarkArray {_dbb :=_aacf .Elements ();_bdfg :=len (_dbb );
var _dgf _ac .PdfObject ;_edaf :=[]*_adc .TextMarkArray {};_dbf :=&_adc .TextMarkArray {};_gaa :=-1;for _bddg ,_gbge :=range _dbb {_egbge :=_gbge .DirectObject ;_gaa =_gbge .Index ;if _egbge ==nil {_faff :=_facc (_aacf ,_bddg ,_gaa );if _dgf !=nil {if _faff ==-1||_faff > _bddg {_edaf =append (_edaf ,_dbf );
_dbf =&_adc .TextMarkArray {};};};}else if _egbge !=nil &&_dgf ==nil {if _gaa ==0&&_bddg > 0{_edaf =append (_edaf ,_dbf );_dbf =&_adc .TextMarkArray {};};}else if _egbge !=nil &&_dgf !=nil {if _egbge !=_dgf {_edaf =append (_edaf ,_dbf );_dbf =&_adc .TextMarkArray {};
};};_dgf =_egbge ;_dbf .Append (_gbge );if _bddg ==(_bdfg -1){_edaf =append (_edaf ,_dbf );};};return _edaf ;};func _fcee (_cda RedactionTerm )(*regexMatcher ,error ){return &regexMatcher {_ddfe :_cda },nil };func _ffgd (_gfa []placeHolders )[]replacement {_ecg :=[]replacement {};
for _ ,_ecgb :=range _gfa {_fgd :=_ecgb ._af ;_aeb :=_ecgb ._aag ;_cab :=_ecgb ._be ;for _ ,_fgb :=range _fgd {_aef :=replacement {_de :_aeb ,_cdd :_cab ,_eea :_fgb };_ecg =append (_ecg ,_aef );};};_ee .Slice (_ecg ,func (_eaa ,_befg int )bool {return _ecg [_eaa ]._eea < _ecg [_befg ]._eea });
return _ecg ;};func _ggd (_bbac string ,_dcae []replacement ,_egf *_g .PdfFont )[]_ac .PdfObject {_egc :=[]_ac .PdfObject {};_dga :=0;_ffbb :=_bbac ;for _abd ,_fcc :=range _dcae {_fdb :=_fcc ._eea ;_bdge :=_fcc ._cdd ;_ge :=_fcc ._de ;_abdg :=_ac .MakeFloat (_bdge );
if _dga > _fdb ||_fdb ==-1{continue ;};_eecb :=_bbac [_dga :_fdb ];_bdde :=_fcb (_eecb ,_egf );_aebg :=_ac .MakeStringFromBytes (_bdde );_egc =append (_egc ,_aebg );_egc =append (_egc ,_abdg );_deg :=_fdb +len (_ge );_ffbb =_bbac [_deg :];_dga =_deg ;if _abd ==len (_dcae )-1{_bdde =_fcb (_ffbb ,_egf );
_aebg =_ac .MakeStringFromBytes (_bdde );_egc =append (_egc ,_aebg );};};return _egc ;};

// RedactionTerm holds the regexp pattern and the replacement string for the redaction process.
type RedactionTerm struct{Pattern *_d .Regexp ;};func _bge (_egb []localSpanMarks )(map[string ][]localSpanMarks ,[]string ){_dac :=make (map[string ][]localSpanMarks );_eec :=[]string {};for _ ,_ffb :=range _egb {_bbf :=_ffb ._feab ;if _bc ,_bed :=_dac [_bbf ];
_bed {_dac [_bbf ]=append (_bc ,_ffb );}else {_dac [_bbf ]=[]localSpanMarks {_ffb };_eec =append (_eec ,_bbf );};};return _dac ,_eec ;};type matchedBBox struct{_eaac _g .PdfRectangle ;_gfd string ;};func _dffg (_dfd []*matchedIndex ,_afaa [][]int )[]*matchedIndex {_gdg :=[]*matchedIndex {};
for _ ,_ddb :=range _dfd {_edd ,_gbgaf :=_ffc (_ddb ,_afaa );if _edd {_gead :=_dbgd (_ddb ,_gbgaf );_gdg =append (_gdg ,_gead ...);}else {_gdg =append (_gdg ,_ddb );};};return _gdg ;};type placeHolders struct{_af []int ;_aag string ;_be float64 ;};func _gbb (_da *_cd .ContentStreamOperation ,_gde _ac .PdfObject ,_gf []localSpanMarks )error {_fbf ,_aab :=_ac .GetArray (_da .Params [0]);
_bac :=[]_ac .PdfObject {};if !_aab {_b .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0054\u004a\u0020\u006f\u0070\u003d\u0025s\u0020G\u0065t\u0041r\u0072\u0061\u0079\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_da );return _f .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_da );
};_cdb ,_fe :=_bge (_gf );if len (_fe )==1{_eaf :=_fe [0];_efd :=_cdb [_eaf ];if len (_efd )==1{_bb :=_efd [0];_cdbd :=_bb ._cggf ;_ga :=_egbf (_cdbd );_fdff ,_acd :=_fce (_gde ,_ga );if _acd !=nil {return _acd ;};_ag ,_acd :=_ecb (_bb ,_cdbd ,_ga ,_fdff ,_eaf );
if _acd !=nil {return _acd ;};for _ ,_eg :=range _fbf .Elements (){if _eg ==_gde {_bac =append (_bac ,_ag ...);}else {_bac =append (_bac ,_eg );};};}else {_feg :=_efd [0]._cggf ;_eef :=_egbf (_feg );_bdd ,_bdg :=_fce (_gde ,_eef );if _bdg !=nil {return _bdg ;
};_gbbe ,_bdg :=_aded (_bdd ,_efd );if _bdg !=nil {return _bdg ;};_bbg :=_ffgd (_gbbe );_gae :=_ggd (_bdd ,_bbg ,_eef );for _ ,_ff :=range _fbf .Elements (){if _ff ==_gde {_bac =append (_bac ,_gae ...);}else {_bac =append (_bac ,_ff );};};};_da .Params [0]=_ac .MakeArray (_bac ...);
}else if len (_fe )> 1{_ace :=_gf [0];_ffa :=_ace ._cggf ;_ ,_eed :=_fag (_ffa );_cg :=_ffa .Elements ()[_eed ];_bgd :=_cg .Font ;_fae ,_db :=_fce (_gde ,_bgd );if _db !=nil {return _db ;};_cfb ,_db :=_aded (_fae ,_gf );if _db !=nil {return _db ;};_bff :=_ffgd (_cfb );
_ce :=_ggd (_fae ,_bff ,_bgd );for _ ,_dd :=range _fbf .Elements (){if _dd ==_gde {_bac =append (_bac ,_ce ...);}else {_bac =append (_bac ,_dd );};};_da .Params [0]=_ac .MakeArray (_bac ...);};return nil ;};func _fcb (_fbd string ,_dcf *_g .PdfFont )[]byte {_facg ,_ade :=_dcf .StringToCharcodeBytes (_fbd );
if _ade !=0{_b .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0073\u006fm\u0065\u0020\u0072un\u0065\u0073\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0065d\u002e\u000a\u0009\u0025\u0073\u0020\u002d\u003e \u0025\u0076",_fbd ,_facg );
};return _facg ;};func _bdf (_cce ,_dgab ,_bdca float64 )float64 {_bdca =_bdca /100;_aec :=(-1000*_cce )/(_dgab *_bdca );return _aec ;};func _egbf (_bba *_adc .TextMarkArray )*_g .PdfFont {_ ,_ddf :=_fag (_bba );_acef :=_bba .Elements ()[_ddf ];_fbc :=_acef .Font ;
return _fbc ;};type targetMap struct{_bga string ;_fdcd [][]int ;};func _baf (_baag []*matchedIndex )[]*targetMap {_dacgc :=make (map[string ][][]int );_abgf :=[]*targetMap {};for _ ,_cac :=range _baag {_edfc :=_cac ._eee ;_bfba :=[]int {_cac ._dace ,_cac ._fbag };
if _bbgc ,_gbad :=_dacgc [_edfc ];_gbad {_dacgc [_edfc ]=append (_bbgc ,_bfba );}else {_dacgc [_edfc ]=[][]int {_bfba };};};for _ecbbf ,_bfd :=range _dacgc {_fbca :=&targetMap {_bga :_ecbbf ,_fdcd :_bfd };_abgf =append (_abgf ,_fbca );};return _abgf ;};
func _edf (_age *_cd .ContentStreamOperation ,_bbe _ac .PdfObject ,_cag []localSpanMarks )error {var _gg *_ac .PdfObjectArray ;_fca ,_afd :=_bge (_cag );if len (_afd )==1{_ddg :=_afd [0];_bgef :=_fca [_ddg ];if len (_bgef )==1{_dae :=_bgef [0];_gcf :=_dae ._cggf ;
_efa :=_egbf (_gcf );_aacd ,_gab :=_fce (_bbe ,_efa );if _gab !=nil {return _gab ;};_aabd ,_gab :=_ecb (_dae ,_gcf ,_efa ,_aacd ,_ddg );if _gab !=nil {return _gab ;};_gg =_ac .MakeArray (_aabd ...);}else {_fga :=_bgef [0]._cggf ;_fgaa :=_egbf (_fga );_bca ,_cga :=_fce (_bbe ,_fgaa );
if _cga !=nil {return _cga ;};_gbg ,_cga :=_aded (_bca ,_bgef );if _cga !=nil {return _cga ;};_acda :=_ffgd (_gbg );_fba :=_ggd (_bca ,_acda ,_fgaa );_gg =_ac .MakeArray (_fba ...);};}else if len (_afd )> 1{_acg :=_cag [0];_dee :=_acg ._cggf ;_ ,_acgd :=_fag (_dee );
_abe :=_dee .Elements ()[_acgd ];_bef :=_abe .Font ;_fbdc ,_dcg :=_fce (_bbe ,_bef );if _dcg !=nil {return _dcg ;};_bedf ,_dcg :=_aded (_fbdc ,_cag );if _dcg !=nil {return _dcg ;};_ec :=_ffgd (_bedf );_bfc :=_ggd (_fbdc ,_ec ,_bef );_gg =_ac .MakeArray (_bfc ...);
};_age .Params [0]=_gg ;_age .Operand ="\u0054\u004a";return nil ;};type matchedIndex struct{_dace int ;_fbag int ;_eee string ;};func _babe (_cccg []*targetMap ){for _bad ,_efdd :=range _cccg {for _adf ,_geag :=range _cccg {if _bad !=_adf {_afdb ,_bacc :=_cca (*_efdd ,*_geag );
if _afdb {_ffe (_geag ,_bacc );};};};};};func _fag (_afc *_adc .TextMarkArray )(_ac .PdfObject ,int ){var _ef _ac .PdfObject ;_fb :=-1;for _acc ,_ab :=range _afc .Elements (){_ef =_ab .DirectObject ;_fb =_acc ;if _ef !=nil {break ;};};return _ef ,_fb ;
};

// RedactionOptions is a collection of RedactionTerm objects.
type RedactionOptions struct{Terms []RedactionTerm ;};func _ffc (_ecgbg *matchedIndex ,_cdg [][]int )(bool ,[][]int ){_eccb :=[][]int {};for _ ,_cagg :=range _cdg {if _ecgbg ._dace < _cagg [0]&&_ecgbg ._fbag > _cagg [1]{_eccb =append (_eccb ,_cagg );};
};return len (_eccb )> 0,_eccb ;};func _fce (_geec _ac .PdfObject ,_bab *_g .PdfFont )(string ,error ){_gbga ,_ada :=_ac .GetStringBytes (_geec );if !_ada {return "",_ac .ErrTypeError ;};_ecgg :=_bab .BytesToCharcodes (_gbga );_ebg ,_ddd ,_dacc :=_bab .CharcodesToStrings (_ecgg ,"");
if _dacc > 0{_b .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_ddd ,_dacc );};_eeff :=_ad .Join (_ebg ,"");return _eeff ,nil ;
};func _caef (_ccde string )(string ,[][]int ){_ccag :=_d .MustCompile ("\u005c\u006e");_dcfb :=_ccag .FindAllStringIndex (_ccde ,-1);_ccef :=_ccag .ReplaceAllString (_ccde ,"\u0020");return _ccef ,_dcfb ;};