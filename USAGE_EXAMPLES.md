# UniPDF Usage Examples (License-Free Version)

This document provides comprehensive examples for using the license-free version of UniPDF.

## Table of Contents

1. [Basic PDF Creation](#basic-pdf-creation)
2. [Reading PDF Files](#reading-pdf-files)
3. [Text Extraction](#text-extraction)
4. [Image Operations](#image-operations)
5. [Form Handling](#form-handling)
6. [PDF Manipulation](#pdf-manipulation)
7. [Security Operations](#security-operations)

## Basic PDF Creation

### Simple Text Document

```go
package main

import (
    "github.com/unidoc/unipdf/v4/creator"
)

func main() {
    c := creator.New()
    c.NewPage()
    
    // Add title
    title := c.NewParagraph("My First PDF")
    title.SetFontSize(24)
    title.SetColor(creator.ColorRGBFromHex("#2E86AB"))
    c.Draw(title)
    
    // Add content
    content := c.NewParagraph("This PDF was created without any license restrictions!")
    content.SetFontSize(12)
    content.SetMargins(0, 0, 20, 0)
    c.Draw(content)
    
    // Save
    c.WriteToFile("simple.pdf")
}
```

### PDF with Images

```go
package main

import (
    "github.com/unidoc/unipdf/v4/creator"
)

func main() {
    c := creator.New()
    c.NewPage()
    
    // Add image
    img, err := c.NewImageFromFile("logo.png")
    if err != nil {
        panic(err)
    }
    img.ScaleToWidth(200)
    c.Draw(img)
    
    // Add text below image
    text := c.NewParagraph("Image added successfully!")
    c.Draw(text)
    
    c.WriteToFile("with_image.pdf")
}
```

## Reading PDF Files

### Basic PDF Reading

```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // Open file
    f, err := os.Open("input.pdf")
    if err != nil {
        panic(err)
    }
    defer f.Close()
    
    // Create reader
    reader, err := model.NewPdfReader(f)
    if err != nil {
        panic(err)
    }
    
    // Get info
    numPages, _ := reader.GetNumPages()
    fmt.Printf("Number of pages: %d\n", numPages)
    
    // Check if encrypted
    isEncrypted, _ := reader.IsEncrypted()
    fmt.Printf("Is encrypted: %v\n", isEncrypted)
}
```

## Text Extraction

### Extract All Text

```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/extractor"
    "github.com/unidoc/unipdf/v4/model"
)

func extractAllText(filename string) error {
    f, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer f.Close()
    
    reader, err := model.NewPdfReader(f)
    if err != nil {
        return err
    }
    
    numPages, err := reader.GetNumPages()
    if err != nil {
        return err
    }
    
    for i := 1; i <= numPages; i++ {
        page, err := reader.GetPage(i)
        if err != nil {
            continue
        }
        
        ex, err := extractor.New(page)
        if err != nil {
            continue
        }
        
        text, err := ex.ExtractText()
        if err != nil {
            continue
        }
        
        fmt.Printf("Page %d:\n%s\n\n", i, text)
    }
    
    return nil
}

func main() {
    extractAllText("document.pdf")
}
```

## Image Operations

### Extract Images

```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/extractor"
    "github.com/unidoc/unipdf/v4/model"
)

func extractImages(filename string) error {
    f, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer f.Close()
    
    reader, err := model.NewPdfReader(f)
    if err != nil {
        return err
    }
    
    numPages, _ := reader.GetNumPages()
    
    for i := 1; i <= numPages; i++ {
        page, err := reader.GetPage(i)
        if err != nil {
            continue
        }
        
        ex, err := extractor.New(page)
        if err != nil {
            continue
        }
        
        images, err := ex.ExtractPageImages(nil)
        if err != nil {
            continue
        }
        
        for j, img := range images.Images {
            filename := fmt.Sprintf("page_%d_image_%d.jpg", i, j)
            img.Image.WriteToFile(filename)
            fmt.Printf("Extracted: %s\n", filename)
        }
    }
    
    return nil
}

func main() {
    extractImages("document.pdf")
}
```

## PDF Manipulation

### Merge PDFs

```go
package main

import (
    "os"
    "github.com/unidoc/unipdf/v4/model"
)

func mergePDFs(outputPath string, inputPaths ...string) error {
    writer := model.NewPdfWriter()
    
    for _, inputPath := range inputPaths {
        f, err := os.Open(inputPath)
        if err != nil {
            continue
        }
        
        reader, err := model.NewPdfReader(f)
        if err != nil {
            f.Close()
            continue
        }
        
        numPages, _ := reader.GetNumPages()
        for i := 1; i <= numPages; i++ {
            page, err := reader.GetPage(i)
            if err != nil {
                continue
            }
            writer.AddPage(page)
        }
        
        f.Close()
    }
    
    // Write output
    outFile, err := os.Create(outputPath)
    if err != nil {
        return err
    }
    defer outFile.Close()
    
    return writer.Write(outFile)
}

func main() {
    mergePDFs("merged.pdf", "doc1.pdf", "doc2.pdf", "doc3.pdf")
}
```

### Split PDF

```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/model"
)

func splitPDF(inputPath string) error {
    f, err := os.Open(inputPath)
    if err != nil {
        return err
    }
    defer f.Close()
    
    reader, err := model.NewPdfReader(f)
    if err != nil {
        return err
    }
    
    numPages, _ := reader.GetNumPages()
    
    for i := 1; i <= numPages; i++ {
        writer := model.NewPdfWriter()
        
        page, err := reader.GetPage(i)
        if err != nil {
            continue
        }
        
        writer.AddPage(page)
        
        outputPath := fmt.Sprintf("page_%d.pdf", i)
        outFile, err := os.Create(outputPath)
        if err != nil {
            continue
        }
        
        writer.Write(outFile)
        outFile.Close()
        
        fmt.Printf("Created: %s\n", outputPath)
    }
    
    return nil
}

func main() {
    splitPDF("document.pdf")
}
```

## Security Operations

### Remove Password Protection

```go
package main

import (
    "os"
    "github.com/unidoc/unipdf/v4/model"
)

func removePassword(inputPath, outputPath, password string) error {
    f, err := os.Open(inputPath)
    if err != nil {
        return err
    }
    defer f.Close()
    
    reader, err := model.NewPdfReader(f)
    if err != nil {
        return err
    }
    
    // Decrypt if needed
    isEncrypted, _ := reader.IsEncrypted()
    if isEncrypted {
        auth, err := reader.Decrypt([]byte(password))
        if err != nil {
            return err
        }
        if !auth {
            return fmt.Errorf("invalid password")
        }
    }
    
    // Create new writer without encryption
    writer := model.NewPdfWriter()
    
    numPages, _ := reader.GetNumPages()
    for i := 1; i <= numPages; i++ {
        page, err := reader.GetPage(i)
        if err != nil {
            continue
        }
        writer.AddPage(page)
    }
    
    // Write unencrypted PDF
    outFile, err := os.Create(outputPath)
    if err != nil {
        return err
    }
    defer outFile.Close()
    
    return writer.Write(outFile)
}

func main() {
    removePassword("encrypted.pdf", "decrypted.pdf", "password123")
}
```

## Notes

- **No License Required**: All examples work without any license keys
- **Full Functionality**: All features are available without restrictions
- **No Network Calls**: Everything works offline
- **Production Ready**: Same performance as the original library

For more examples, visit: https://github.com/unidoc/unipdf-examples
