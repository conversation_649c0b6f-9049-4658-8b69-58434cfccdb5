//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package transform ;import (_d "fmt";_de "github.com/unidoc/unipdf/v4/common";_ed "math";);func RotationMatrix (angle float64 )Matrix {_a :=_ed .Cos (angle );_ab :=_ed .Sin (angle );return NewMatrix (_a ,_ab ,-_ab ,_a ,0,0);};func (_cg *Matrix )Concat (b Matrix ){*_cg =Matrix {b [0]*_cg [0]+b [1]*_cg [3],b [0]*_cg [1]+b [1]*_cg [4],0,b [3]*_cg [0]+b [4]*_cg [3],b [3]*_cg [1]+b [4]*_cg [4],0,b [6]*_cg [0]+b [7]*_cg [3]+_cg [6],b [6]*_cg [1]+b [7]*_cg [4]+_cg [7],1};
_cg .clampRange ();};func NewMatrix (a ,b ,c ,d ,tx ,ty float64 )Matrix {_ec :=Matrix {a ,b ,0,c ,d ,0,tx ,ty ,1};_ec .clampRange ();return _ec ;};func (_gb Matrix )Singular ()bool {return _ed .Abs (_gb [0]*_gb [4]-_gb [1]*_gb [3])< _ca };func (_gf Point )String ()string {return _d .Sprintf ("(\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0029",_gf .X ,_gf .Y );
};func (_b Matrix )Translate (tx ,ty float64 )Matrix {return _b .Mult (TranslationMatrix (tx ,ty ))};func (_ecdb Point )Distance (b Point )float64 {return _ed .Hypot (_ecdb .X -b .X ,_ecdb .Y -b .Y )};func (_dfg Matrix )Transform (x ,y float64 )(float64 ,float64 ){_bg :=x *_dfg [0]+y *_dfg [3]+_dfg [6];
_ecc :=x *_dfg [1]+y *_dfg [4]+_dfg [7];return _bg ,_ecc ;};func ShearMatrix (x ,y float64 )Matrix {return NewMatrix (1,y ,x ,1,0,0)};func (_ce Matrix )Angle ()float64 {_bf :=_ed .Atan2 (-_ce [1],_ce [0]);if _bf < 0.0{_bf +=2*_ed .Pi ;};return _bf /_ed .Pi *180.0;
};func (_gbc Matrix )ScalingFactorX ()float64 {return _ed .Hypot (_gbc [0],_gbc [1])};func IdentityMatrix ()Matrix {return NewMatrix (1,0,0,1,0,0)};func (_ae *Matrix )Set (a ,b ,c ,d ,tx ,ty float64 ){_ae [0],_ae [1]=a ,b ;_ae [3],_ae [4]=c ,d ;_ae [6],_ae [7]=tx ,ty ;
_ae .clampRange ();};type Matrix [9]float64 ;func (_ac Matrix )Scale (xScale ,yScale float64 )Matrix {return _ac .Mult (ScaleMatrix (xScale ,yScale ))};type Point struct{X float64 ;Y float64 ;};func (_ef *Matrix )Shear (x ,y float64 ){_ef .Concat (ShearMatrix (x ,y ))};
func NewPoint (x ,y float64 )Point {return Point {X :x ,Y :y }};const _ca =1e-10;const _ge =1e-6;func (_cef Matrix )Unrealistic ()bool {_dfb ,_fc ,_fce ,_ba :=_ed .Abs (_cef [0]),_ed .Abs (_cef [1]),_ed .Abs (_cef [3]),_ed .Abs (_cef [4]);_cee :=_dfb > _ge &&_ba > _ge ;
_efg :=_fc > _ge &&_fce > _ge ;return !(_cee ||_efg );};func (_c Matrix )Rotate (theta float64 )Matrix {return _c .Mult (RotationMatrix (theta ))};func NewMatrixFromTransforms (xScale ,yScale ,theta ,tx ,ty float64 )Matrix {return IdentityMatrix ().Scale (xScale ,yScale ).Rotate (theta ).Translate (tx ,ty );
};func (_gg Matrix )Mult (b Matrix )Matrix {_gg .Concat (b );return _gg };func (_bgd Matrix )Inverse ()(Matrix ,bool ){_eb ,_gga :=_bgd [0],_bgd [1];_dc ,_fab :=_bgd [3],_bgd [4];_cd ,_fbd :=_bgd [6],_bgd [7];_ag :=_eb *_fab -_gga *_dc ;if _ed .Abs (_ag )< _cda {return Matrix {},false ;
};_cac ,_abd :=_fab /_ag ,-_gga /_ag ;_cf ,_eg :=-_dc /_ag ,_eb /_ag ;_egf :=-(_cac *_cd +_cf *_fbd );_cb :=-(_abd *_cd +_eg *_fbd );return NewMatrix (_cac ,_abd ,_cf ,_eg ,_egf ,_cb ),true ;};const _cda =1.0e-6;func (_cge Matrix )Translation ()(float64 ,float64 ){return _cge [6],_cge [7]};
func (_cgd Point )Displace (delta Point )Point {return Point {_cgd .X +delta .X ,_cgd .Y +delta .Y }};func (_ceg *Point )transformByMatrix (_fbg Matrix ){_ceg .X ,_ceg .Y =_fbg .Transform (_ceg .X ,_ceg .Y )};func ScaleMatrix (x ,y float64 )Matrix {return NewMatrix (x ,0,0,y ,0,0)};
func (_egd Point )Rotate (theta float64 )Point {_dd :=_ed .Hypot (_egd .X ,_egd .Y );_eccg :=_ed .Atan2 (_egd .Y ,_egd .X );_be ,_ace :=_ed .Sincos (_eccg +theta /180.0*_ed .Pi );return Point {_dd *_ace ,_dd *_be };};func (_ad Point )Interpolate (b Point ,t float64 )Point {return Point {X :(1-t )*_ad .X +t *b .X ,Y :(1-t )*_ad .Y +t *b .Y };
};func (_ecd Matrix )ScalingFactorY ()float64 {return _ed .Hypot (_ecd [3],_ecd [4])};func (_cfd *Matrix )clampRange (){for _dg ,_eba :=range _cfd {if _eba > _ece {_de .Log .Debug ("\u0043L\u0041M\u0050\u003a\u0020\u0025\u0067\u0020\u002d\u003e\u0020\u0025\u0067",_eba ,_ece );
_cfd [_dg ]=_ece ;}else if _eba < -_ece {_de .Log .Debug ("\u0043L\u0041M\u0050\u003a\u0020\u0025\u0067\u0020\u002d\u003e\u0020\u0025\u0067",_eba ,-_ece );_cfd [_dg ]=-_ece ;};};};func (_ff Matrix )Identity ()bool {return _ff [0]==1&&_ff [1]==0&&_ff [2]==0&&_ff [3]==0&&_ff [4]==1&&_ff [5]==0&&_ff [6]==0&&_ff [7]==0&&_ff [8]==1;
};const _ece =1e9;func (_fbe *Point )Transform (a ,b ,c ,d ,tx ,ty float64 ){_edef :=NewMatrix (a ,b ,c ,d ,tx ,ty );_fbe .transformByMatrix (_edef );};func (_fba *Point )Set (x ,y float64 ){_fba .X ,_fba .Y =x ,y };func TranslationMatrix (tx ,ty float64 )Matrix {return NewMatrix (1,0,0,1,tx ,ty )};
func (_f Matrix )Round (precision float64 )Matrix {for _g :=range _f {_f [_g ]=_ed .Round (_f [_g ]/precision )*precision ;};return _f ;};func (_fb Matrix )String ()string {_fa ,_ded ,_fae ,_ede ,_ea ,_df :=_fb [0],_fb [1],_fb [3],_fb [4],_fb [6],_fb [7];
return _d .Sprintf ("\u005b\u00257\u002e\u0034\u0066\u002c%\u0037\u002e4\u0066\u002c\u0025\u0037\u002e\u0034\u0066\u002c%\u0037\u002e\u0034\u0066\u003a\u0025\u0037\u002e\u0034\u0066\u002c\u00257\u002e\u0034\u0066\u005d",_fa ,_ded ,_fae ,_ede ,_ea ,_df );
};func (_bd *Matrix )Clone ()Matrix {return NewMatrix (_bd [0],_bd [1],_bd [3],_bd [4],_bd [6],_bd [7])};