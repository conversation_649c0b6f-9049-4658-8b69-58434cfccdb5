//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package ccittfax ;import (_e "errors";_f "github.com/unidoc/unipdf/v4/internal/bitwise";_dg "io";_g "math";);func _eff (_adb ,_cdgd []byte ,_aag int ,_cgb bool )int {_cbade :=_edgbg (_cdgd ,_aag );if _cbade < len (_cdgd )&&(_aag ==-1&&_cdgd [_cbade ]==_eac ||_aag >=0&&_aag < len (_adb )&&_adb [_aag ]==_cdgd [_cbade ]||_aag >=len (_adb )&&_cgb &&_cdgd [_cbade ]==_eac ||_aag >=len (_adb )&&!_cgb &&_cdgd [_cbade ]==_bedb ){_cbade =_edgbg (_cdgd ,_cbade );
};return _cbade ;};func init (){_a =&treeNode {_bff :true ,_bggb :_c };_ad =&treeNode {_bggb :_ef ,_efbeg :_a };_ad ._dfbe =_ad ;_fd =&tree {_gaf :&treeNode {}};if _gb :=_fd .fillWithNode (12,0,_ad );_gb !=nil {panic (_gb .Error ());};if _ac :=_fd .fillWithNode (12,1,_a );
_ac !=nil {panic (_ac .Error ());};_ed =&tree {_gaf :&treeNode {}};for _dd :=0;_dd < len (_ff );_dd ++{for _bc :=0;_bc < len (_ff [_dd ]);_bc ++{if _cc :=_ed .fill (_dd +2,int (_ff [_dd ][_bc ]),int (_gf [_dd ][_bc ]));_cc !=nil {panic (_cc .Error ());
};};};if _db :=_ed .fillWithNode (12,0,_ad );_db !=nil {panic (_db .Error ());};if _bee :=_ed .fillWithNode (12,1,_a );_bee !=nil {panic (_bee .Error ());};_aa =&tree {_gaf :&treeNode {}};for _ccc :=0;_ccc < len (_ea );_ccc ++{for _df :=0;_df < len (_ea [_ccc ]);
_df ++{if _de :=_aa .fill (_ccc +4,int (_ea [_ccc ][_df ]),int (_eab [_ccc ][_df ]));_de !=nil {panic (_de .Error ());};};};if _eb :=_aa .fillWithNode (12,0,_ad );_eb !=nil {panic (_eb .Error ());};if _bed :=_aa .fillWithNode (12,1,_a );_bed !=nil {panic (_bed .Error ());
};_gg =&tree {_gaf :&treeNode {}};if _fe :=_gg .fill (4,1,_gc );_fe !=nil {panic (_fe .Error ());};if _efb :=_gg .fill (3,1,_b );_efb !=nil {panic (_efb .Error ());};if _af :=_gg .fill (1,1,0);_af !=nil {panic (_af .Error ());};if _fa :=_gg .fill (3,3,1);
_fa !=nil {panic (_fa .Error ());};if _gd :=_gg .fill (6,3,2);_gd !=nil {panic (_gd .Error ());};if _afg :=_gg .fill (7,3,3);_afg !=nil {panic (_afg .Error ());};if _ebg :=_gg .fill (3,2,-1);_ebg !=nil {panic (_ebg .Error ());};if _fc :=_gg .fill (6,2,-2);
_fc !=nil {panic (_fc .Error ());};if _cd :=_gg .fill (7,2,-3);_cd !=nil {panic (_cd .Error ());};};func _efbe (_bf int )([]byte ,int ){var _bac []byte ;for _bbaf :=0;_bbaf < 2;_bbaf ++{_bac ,_bf =_dbb (_bac ,_bf ,_eg );};return _bac ,_bf %8;};func (_cac *Decoder )fetch ()error {if _cac ._dgf ==-1{return nil ;
};if _cac ._ca < _cac ._dgf {return nil ;};_cac ._dgf =0;_cfb :=_cac .decodeRow ();if _cfb !=nil {if !_e .Is (_cfb ,_dg .EOF ){return _cfb ;};if _cac ._dgf !=0{return _cfb ;};_cac ._dgf =-1;};_cac ._ca =0;return nil ;};var _ff =[...][]uint16 {{0x2,0x3},{0x2,0x3},{0x2,0x3},{0x3},{0x4,0x5},{0x4,0x5,0x7},{0x4,0x7},{0x18},{0x17,0x18,0x37,0x8,0xf},{0x17,0x18,0x28,0x37,0x67,0x68,0x6c,0x8,0xc,0xd},{0x12,0x13,0x14,0x15,0x16,0x17,0x1c,0x1d,0x1e,0x1f,0x24,0x27,0x28,0x2b,0x2c,0x33,0x34,0x35,0x37,0x38,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x5b,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0xc8,0xc9,0xca,0xcb,0xcc,0xcd,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xda,0xdb},{0x4a,0x4b,0x4c,0x4d,0x52,0x53,0x54,0x55,0x5a,0x5b,0x64,0x65,0x6c,0x6d,0x72,0x73,0x74,0x75,0x76,0x77}};
type tree struct{_gaf *treeNode };func _fbb (_cdd []byte ,_eaee int )([]byte ,int ){return _dbb (_cdd ,_eaee ,_fef )};func (_ce *Decoder )looseFetchEOL ()(bool ,error ){_ccca ,_ebf :=_ce ._bcg .ReadBits (12);if _ebf !=nil {return false ,_ebf ;};switch _ccca {case 0x1:return true ,nil ;
case 0x0:for {_gec ,_ba :=_ce ._bcg .ReadBool ();if _ba !=nil {return false ,_ba ;};if _gec {return true ,nil ;};};default:return false ,nil ;};};func init (){_ebb =make (map[int ]code );_ebb [0]=code {Code :13<<8|3<<6,BitsWritten :10};_ebb [1]=code {Code :2<<(5+8),BitsWritten :3};
_ebb [2]=code {Code :3<<(6+8),BitsWritten :2};_ebb [3]=code {Code :2<<(6+8),BitsWritten :2};_ebb [4]=code {Code :3<<(5+8),BitsWritten :3};_ebb [5]=code {Code :3<<(4+8),BitsWritten :4};_ebb [6]=code {Code :2<<(4+8),BitsWritten :4};_ebb [7]=code {Code :3<<(3+8),BitsWritten :5};
_ebb [8]=code {Code :5<<(2+8),BitsWritten :6};_ebb [9]=code {Code :4<<(2+8),BitsWritten :6};_ebb [10]=code {Code :4<<(1+8),BitsWritten :7};_ebb [11]=code {Code :5<<(1+8),BitsWritten :7};_ebb [12]=code {Code :7<<(1+8),BitsWritten :7};_ebb [13]=code {Code :4<<8,BitsWritten :8};
_ebb [14]=code {Code :7<<8,BitsWritten :8};_ebb [15]=code {Code :12<<8,BitsWritten :9};_ebb [16]=code {Code :5<<8|3<<6,BitsWritten :10};_ebb [17]=code {Code :6<<8,BitsWritten :10};_ebb [18]=code {Code :2<<8,BitsWritten :10};_ebb [19]=code {Code :12<<8|7<<5,BitsWritten :11};
_ebb [20]=code {Code :13<<8,BitsWritten :11};_ebb [21]=code {Code :13<<8|4<<5,BitsWritten :11};_ebb [22]=code {Code :6<<8|7<<5,BitsWritten :11};_ebb [23]=code {Code :5<<8,BitsWritten :11};_ebb [24]=code {Code :2<<8|7<<5,BitsWritten :11};_ebb [25]=code {Code :3<<8,BitsWritten :11};
_ebb [26]=code {Code :12<<8|10<<4,BitsWritten :12};_ebb [27]=code {Code :12<<8|11<<4,BitsWritten :12};_ebb [28]=code {Code :12<<8|12<<4,BitsWritten :12};_ebb [29]=code {Code :12<<8|13<<4,BitsWritten :12};_ebb [30]=code {Code :6<<8|8<<4,BitsWritten :12};
_ebb [31]=code {Code :6<<8|9<<4,BitsWritten :12};_ebb [32]=code {Code :6<<8|10<<4,BitsWritten :12};_ebb [33]=code {Code :6<<8|11<<4,BitsWritten :12};_ebb [34]=code {Code :13<<8|2<<4,BitsWritten :12};_ebb [35]=code {Code :13<<8|3<<4,BitsWritten :12};_ebb [36]=code {Code :13<<8|4<<4,BitsWritten :12};
_ebb [37]=code {Code :13<<8|5<<4,BitsWritten :12};_ebb [38]=code {Code :13<<8|6<<4,BitsWritten :12};_ebb [39]=code {Code :13<<8|7<<4,BitsWritten :12};_ebb [40]=code {Code :6<<8|12<<4,BitsWritten :12};_ebb [41]=code {Code :6<<8|13<<4,BitsWritten :12};_ebb [42]=code {Code :13<<8|10<<4,BitsWritten :12};
_ebb [43]=code {Code :13<<8|11<<4,BitsWritten :12};_ebb [44]=code {Code :5<<8|4<<4,BitsWritten :12};_ebb [45]=code {Code :5<<8|5<<4,BitsWritten :12};_ebb [46]=code {Code :5<<8|6<<4,BitsWritten :12};_ebb [47]=code {Code :5<<8|7<<4,BitsWritten :12};_ebb [48]=code {Code :6<<8|4<<4,BitsWritten :12};
_ebb [49]=code {Code :6<<8|5<<4,BitsWritten :12};_ebb [50]=code {Code :5<<8|2<<4,BitsWritten :12};_ebb [51]=code {Code :5<<8|3<<4,BitsWritten :12};_ebb [52]=code {Code :2<<8|4<<4,BitsWritten :12};_ebb [53]=code {Code :3<<8|7<<4,BitsWritten :12};_ebb [54]=code {Code :3<<8|8<<4,BitsWritten :12};
_ebb [55]=code {Code :2<<8|7<<4,BitsWritten :12};_ebb [56]=code {Code :2<<8|8<<4,BitsWritten :12};_ebb [57]=code {Code :5<<8|8<<4,BitsWritten :12};_ebb [58]=code {Code :5<<8|9<<4,BitsWritten :12};_ebb [59]=code {Code :2<<8|11<<4,BitsWritten :12};_ebb [60]=code {Code :2<<8|12<<4,BitsWritten :12};
_ebb [61]=code {Code :5<<8|10<<4,BitsWritten :12};_ebb [62]=code {Code :6<<8|6<<4,BitsWritten :12};_ebb [63]=code {Code :6<<8|7<<4,BitsWritten :12};_dgg =make (map[int ]code );_dgg [0]=code {Code :53<<8,BitsWritten :8};_dgg [1]=code {Code :7<<(2+8),BitsWritten :6};
_dgg [2]=code {Code :7<<(4+8),BitsWritten :4};_dgg [3]=code {Code :8<<(4+8),BitsWritten :4};_dgg [4]=code {Code :11<<(4+8),BitsWritten :4};_dgg [5]=code {Code :12<<(4+8),BitsWritten :4};_dgg [6]=code {Code :14<<(4+8),BitsWritten :4};_dgg [7]=code {Code :15<<(4+8),BitsWritten :4};
_dgg [8]=code {Code :19<<(3+8),BitsWritten :5};_dgg [9]=code {Code :20<<(3+8),BitsWritten :5};_dgg [10]=code {Code :7<<(3+8),BitsWritten :5};_dgg [11]=code {Code :8<<(3+8),BitsWritten :5};_dgg [12]=code {Code :8<<(2+8),BitsWritten :6};_dgg [13]=code {Code :3<<(2+8),BitsWritten :6};
_dgg [14]=code {Code :52<<(2+8),BitsWritten :6};_dgg [15]=code {Code :53<<(2+8),BitsWritten :6};_dgg [16]=code {Code :42<<(2+8),BitsWritten :6};_dgg [17]=code {Code :43<<(2+8),BitsWritten :6};_dgg [18]=code {Code :39<<(1+8),BitsWritten :7};_dgg [19]=code {Code :12<<(1+8),BitsWritten :7};
_dgg [20]=code {Code :8<<(1+8),BitsWritten :7};_dgg [21]=code {Code :23<<(1+8),BitsWritten :7};_dgg [22]=code {Code :3<<(1+8),BitsWritten :7};_dgg [23]=code {Code :4<<(1+8),BitsWritten :7};_dgg [24]=code {Code :40<<(1+8),BitsWritten :7};_dgg [25]=code {Code :43<<(1+8),BitsWritten :7};
_dgg [26]=code {Code :19<<(1+8),BitsWritten :7};_dgg [27]=code {Code :36<<(1+8),BitsWritten :7};_dgg [28]=code {Code :24<<(1+8),BitsWritten :7};_dgg [29]=code {Code :2<<8,BitsWritten :8};_dgg [30]=code {Code :3<<8,BitsWritten :8};_dgg [31]=code {Code :26<<8,BitsWritten :8};
_dgg [32]=code {Code :27<<8,BitsWritten :8};_dgg [33]=code {Code :18<<8,BitsWritten :8};_dgg [34]=code {Code :19<<8,BitsWritten :8};_dgg [35]=code {Code :20<<8,BitsWritten :8};_dgg [36]=code {Code :21<<8,BitsWritten :8};_dgg [37]=code {Code :22<<8,BitsWritten :8};
_dgg [38]=code {Code :23<<8,BitsWritten :8};_dgg [39]=code {Code :40<<8,BitsWritten :8};_dgg [40]=code {Code :41<<8,BitsWritten :8};_dgg [41]=code {Code :42<<8,BitsWritten :8};_dgg [42]=code {Code :43<<8,BitsWritten :8};_dgg [43]=code {Code :44<<8,BitsWritten :8};
_dgg [44]=code {Code :45<<8,BitsWritten :8};_dgg [45]=code {Code :4<<8,BitsWritten :8};_dgg [46]=code {Code :5<<8,BitsWritten :8};_dgg [47]=code {Code :10<<8,BitsWritten :8};_dgg [48]=code {Code :11<<8,BitsWritten :8};_dgg [49]=code {Code :82<<8,BitsWritten :8};
_dgg [50]=code {Code :83<<8,BitsWritten :8};_dgg [51]=code {Code :84<<8,BitsWritten :8};_dgg [52]=code {Code :85<<8,BitsWritten :8};_dgg [53]=code {Code :36<<8,BitsWritten :8};_dgg [54]=code {Code :37<<8,BitsWritten :8};_dgg [55]=code {Code :88<<8,BitsWritten :8};
_dgg [56]=code {Code :89<<8,BitsWritten :8};_dgg [57]=code {Code :90<<8,BitsWritten :8};_dgg [58]=code {Code :91<<8,BitsWritten :8};_dgg [59]=code {Code :74<<8,BitsWritten :8};_dgg [60]=code {Code :75<<8,BitsWritten :8};_dgg [61]=code {Code :50<<8,BitsWritten :8};
_dgg [62]=code {Code :51<<8,BitsWritten :8};_dgg [63]=code {Code :52<<8,BitsWritten :8};_fdb =make (map[int ]code );_fdb [64]=code {Code :3<<8|3<<6,BitsWritten :10};_fdb [128]=code {Code :12<<8|8<<4,BitsWritten :12};_fdb [192]=code {Code :12<<8|9<<4,BitsWritten :12};
_fdb [256]=code {Code :5<<8|11<<4,BitsWritten :12};_fdb [320]=code {Code :3<<8|3<<4,BitsWritten :12};_fdb [384]=code {Code :3<<8|4<<4,BitsWritten :12};_fdb [448]=code {Code :3<<8|5<<4,BitsWritten :12};_fdb [512]=code {Code :3<<8|12<<3,BitsWritten :13};
_fdb [576]=code {Code :3<<8|13<<3,BitsWritten :13};_fdb [640]=code {Code :2<<8|10<<3,BitsWritten :13};_fdb [704]=code {Code :2<<8|11<<3,BitsWritten :13};_fdb [768]=code {Code :2<<8|12<<3,BitsWritten :13};_fdb [832]=code {Code :2<<8|13<<3,BitsWritten :13};
_fdb [896]=code {Code :3<<8|18<<3,BitsWritten :13};_fdb [960]=code {Code :3<<8|19<<3,BitsWritten :13};_fdb [1024]=code {Code :3<<8|20<<3,BitsWritten :13};_fdb [1088]=code {Code :3<<8|21<<3,BitsWritten :13};_fdb [1152]=code {Code :3<<8|22<<3,BitsWritten :13};
_fdb [1216]=code {Code :119<<3,BitsWritten :13};_fdb [1280]=code {Code :2<<8|18<<3,BitsWritten :13};_fdb [1344]=code {Code :2<<8|19<<3,BitsWritten :13};_fdb [1408]=code {Code :2<<8|20<<3,BitsWritten :13};_fdb [1472]=code {Code :2<<8|21<<3,BitsWritten :13};
_fdb [1536]=code {Code :2<<8|26<<3,BitsWritten :13};_fdb [1600]=code {Code :2<<8|27<<3,BitsWritten :13};_fdb [1664]=code {Code :3<<8|4<<3,BitsWritten :13};_fdb [1728]=code {Code :3<<8|5<<3,BitsWritten :13};_ebc =make (map[int ]code );_ebc [64]=code {Code :27<<(3+8),BitsWritten :5};
_ebc [128]=code {Code :18<<(3+8),BitsWritten :5};_ebc [192]=code {Code :23<<(2+8),BitsWritten :6};_ebc [256]=code {Code :55<<(1+8),BitsWritten :7};_ebc [320]=code {Code :54<<8,BitsWritten :8};_ebc [384]=code {Code :55<<8,BitsWritten :8};_ebc [448]=code {Code :100<<8,BitsWritten :8};
_ebc [512]=code {Code :101<<8,BitsWritten :8};_ebc [576]=code {Code :104<<8,BitsWritten :8};_ebc [640]=code {Code :103<<8,BitsWritten :8};_ebc [704]=code {Code :102<<8,BitsWritten :9};_ebc [768]=code {Code :102<<8|1<<7,BitsWritten :9};_ebc [832]=code {Code :105<<8,BitsWritten :9};
_ebc [896]=code {Code :105<<8|1<<7,BitsWritten :9};_ebc [960]=code {Code :106<<8,BitsWritten :9};_ebc [1024]=code {Code :106<<8|1<<7,BitsWritten :9};_ebc [1088]=code {Code :107<<8,BitsWritten :9};_ebc [1152]=code {Code :107<<8|1<<7,BitsWritten :9};_ebc [1216]=code {Code :108<<8,BitsWritten :9};
_ebc [1280]=code {Code :108<<8|1<<7,BitsWritten :9};_ebc [1344]=code {Code :109<<8,BitsWritten :9};_ebc [1408]=code {Code :109<<8|1<<7,BitsWritten :9};_ebc [1472]=code {Code :76<<8,BitsWritten :9};_ebc [1536]=code {Code :76<<8|1<<7,BitsWritten :9};_ebc [1600]=code {Code :77<<8,BitsWritten :9};
_ebc [1664]=code {Code :24<<(2+8),BitsWritten :6};_ebc [1728]=code {Code :77<<8|1<<7,BitsWritten :9};_faf =make (map[int ]code );_faf [1792]=code {Code :1<<8,BitsWritten :11};_faf [1856]=code {Code :1<<8|4<<5,BitsWritten :11};_faf [1920]=code {Code :1<<8|5<<5,BitsWritten :11};
_faf [1984]=code {Code :1<<8|2<<4,BitsWritten :12};_faf [2048]=code {Code :1<<8|3<<4,BitsWritten :12};_faf [2112]=code {Code :1<<8|4<<4,BitsWritten :12};_faf [2176]=code {Code :1<<8|5<<4,BitsWritten :12};_faf [2240]=code {Code :1<<8|6<<4,BitsWritten :12};
_faf [2304]=code {Code :1<<8|7<<4,BitsWritten :12};_faf [2368]=code {Code :1<<8|12<<4,BitsWritten :12};_faf [2432]=code {Code :1<<8|13<<4,BitsWritten :12};_faf [2496]=code {Code :1<<8|14<<4,BitsWritten :12};_faf [2560]=code {Code :1<<8|15<<4,BitsWritten :12};
_eaa =make (map[int ]byte );_eaa [0]=0xFF;_eaa [1]=0xFE;_eaa [2]=0xFC;_eaa [3]=0xF8;_eaa [4]=0xF0;_eaa [5]=0xE0;_eaa [6]=0xC0;_eaa [7]=0x80;_eaa [8]=0x00;};var (_a *treeNode ;_ad *treeNode ;_ed *tree ;_aa *tree ;_fd *tree ;_gg *tree ;_c =-2000;_ef =-1000;
_gc =-3000;_b =-4000;);type DecodeOptions struct{Columns int ;Rows int ;K int ;EncodedByteAligned bool ;BlackIsOne bool ;EndOfBlock bool ;EndOfLine bool ;DamagedRowsBeforeError int ;};const (_ tiffType =iota ;_cff ;_gbe ;_dc ;);func _cee (_bfg int ,_ggf bool )(code ,int ,bool ){if _bfg < 64{if _ggf {return _dgg [_bfg ],0,true ;
};return _ebb [_bfg ],0,true ;};_ddd :=_bfg /64;if _ddd > 40{return _faf [2560],_bfg -2560,false ;};if _ddd > 27{return _faf [_ddd *64],_bfg -_ddd *64,false ;};if _ggf {return _ebc [_ddd *64],_bfg -_ddd *64,false ;};return _fdb [_ddd *64],_bfg -_ddd *64,false ;
};func (_cadg *Decoder )tryFetchEOL ()(bool ,error ){_gdg ,_cbad :=_cadg ._bcg .ReadBits (12);if _cbad !=nil {return false ,_cbad ;};return _gdg ==0x1,nil ;};var (_ebb map[int ]code ;_dgg map[int ]code ;_fdb map[int ]code ;_ebc map[int ]code ;_faf map[int ]code ;
_eaa map[int ]byte ;_eg =code {Code :1<<4,BitsWritten :12};_ec =code {Code :3<<3,BitsWritten :13};_fab =code {Code :2<<3,BitsWritten :13};_fef =code {Code :1<<12,BitsWritten :4};_efd =code {Code :1<<13,BitsWritten :3};_efe =code {Code :1<<15,BitsWritten :1};
_bg =code {Code :3<<13,BitsWritten :3};_dff =code {Code :3<<10,BitsWritten :6};_cg =code {Code :3<<9,BitsWritten :7};_cca =code {Code :2<<13,BitsWritten :3};_cf =code {Code :2<<10,BitsWritten :6};_fed =code {Code :2<<9,BitsWritten :7};);type treeNode struct{_dfbe *treeNode ;
_efbeg *treeNode ;_bggb int ;_acc bool ;_bff bool ;};func _gdd (_feg ,_agf []byte ,_geg int )int {_ede :=_edgbg (_agf ,_geg );if _ede < len (_agf )&&(_geg ==-1&&_agf [_ede ]==_eac ||_geg >=0&&_geg < len (_feg )&&_feg [_geg ]==_agf [_ede ]||_geg >=len (_feg )&&_feg [_geg -1]!=_agf [_ede ]){_ede =_edgbg (_agf ,_ede );
};return _ede ;};func (_gga *Decoder )tryFetchEOL1 ()(bool ,error ){_fdee ,_fad :=_gga ._bcg .ReadBits (13);if _fad !=nil {return false ,_fad ;};return _fdee ==0x3,nil ;};func (_gge *Decoder )decoderRowType41D ()error {if _gge ._eca {_gge ._bcg .Align ();
};_gge ._bcg .Mark ();var (_ccgg bool ;_cdgg error ;);if _gge ._fcd {_ccgg ,_cdgg =_gge .tryFetchEOL ();if _cdgg !=nil {return _cdgg ;};if !_ccgg {return _ege ;};}else {_ccgg ,_cdgg =_gge .looseFetchEOL ();if _cdgg !=nil {return _cdgg ;};};if !_ccgg {_gge ._bcg .Reset ();
};if _ccgg &&_gge ._cba {_gge ._bcg .Mark ();for _gef :=0;_gef < 5;_gef ++{_ccgg ,_cdgg =_gge .tryFetchEOL ();if _cdgg !=nil {if _e .Is (_cdgg ,_dg .EOF ){if _gef ==0{break ;};return _dda ;};};if _ccgg {continue ;};if _gef > 0{return _dda ;};break ;};if _ccgg {return _dg .EOF ;
};_gge ._bcg .Reset ();};if _cdgg =_gge .decode1D ();_cdgg !=nil {return _cdgg ;};return nil ;};func (_ggc *Decoder )decode2D ()error {_ggc ._acd =_ggc ._gcf ;_ggc ._dce ,_ggc ._ee =_ggc ._ee ,_ggc ._dce ;_abb :=true ;var (_eea bool ;_ecg int ;_edb error ;
);_ggc ._gcf =0;_bd :for _ecg < _ggc ._beea {_fafa :=_gg ._gaf ;for {_eea ,_edb =_ggc ._bcg .ReadBool ();if _edb !=nil {return _edb ;};_fafa =_fafa .walk (_eea );if _fafa ==nil {continue _bd ;};if !_fafa ._bff {continue ;};switch _fafa ._bggb {case _b :var _abc int ;
if _abb {_abc ,_edb =_ggc .decodeRun (_aa );}else {_abc ,_edb =_ggc .decodeRun (_ed );};if _edb !=nil {return _edb ;};_ecg +=_abc ;_ggc ._dce [_ggc ._gcf ]=_ecg ;_ggc ._gcf ++;if _abb {_abc ,_edb =_ggc .decodeRun (_ed );}else {_abc ,_edb =_ggc .decodeRun (_aa );
};if _edb !=nil {return _edb ;};_ecg +=_abc ;_ggc ._dce [_ggc ._gcf ]=_ecg ;_ggc ._gcf ++;case _gc :_bca :=_ggc .getNextChangingElement (_ecg ,_abb )+1;if _bca >=_ggc ._acd {_ecg =_ggc ._beea ;}else {_ecg =_ggc ._ee [_bca ];};default:_dcd :=_ggc .getNextChangingElement (_ecg ,_abb );
if _dcd >=_ggc ._acd ||_dcd ==-1{_ecg =_ggc ._beea +_fafa ._bggb ;}else {_ecg =_ggc ._ee [_dcd ]+_fafa ._bggb ;};_ggc ._dce [_ggc ._gcf ]=_ecg ;_ggc ._gcf ++;_abb =!_abb ;};continue _bd ;};};return nil ;};func _ffbc (_bfd ,_bef []byte ,_bae ,_bbd ,_edef int )([]byte ,int ,int ){_afea :=_edgbg (_bfd ,_edef );
_fddf :=_bbd >=0&&_bfd [_bbd ]==_eac ||_bbd ==-1;_bef ,_bae =_dbb (_bef ,_bae ,_efd );var _gcc int ;if _bbd > -1{_gcc =_edef -_bbd ;}else {_gcc =_edef -_bbd -1;};_bef ,_bae =_aef (_bef ,_bae ,_gcc ,_fddf );_fddf =!_fddf ;_agda :=_afea -_edef ;_bef ,_bae =_aef (_bef ,_bae ,_agda ,_fddf );
_bbd =_afea ;return _bef ,_bae ,_bbd ;};var (_dda =_e .New ("\u0063\u0063\u0069\u0074tf\u0061\u0078\u0020\u0063\u006f\u0072\u0072\u0075\u0070\u0074\u0065\u0064\u0020\u0052T\u0043");_ege =_e .New ("\u0063\u0063\u0069\u0074tf\u0061\u0078\u0020\u0045\u004f\u004c\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064");
);func _fecf (_dfb int )([]byte ,int ){var _deec []byte ;for _dgfb :=0;_dgfb < 6;_dgfb ++{_deec ,_dfb =_dbb (_deec ,_dfb ,_eg );};return _deec ,_dfb %8;};func (_gag *Encoder )Encode (pixels [][]byte )[]byte {if _gag .BlackIs1 {_eac =0;_bedb =1;}else {_eac =1;
_bedb =0;};if _gag .K ==0{return _gag .encodeG31D (pixels );};if _gag .K > 0{return _gag .encodeG32D (pixels );};if _gag .K < 0{return _gag .encodeG4 (pixels );};return nil ;};func (_ga *Decoder )decodeRowType6 ()error {if _ga ._eca {_ga ._bcg .Align ();
};if _ga ._cba {_ga ._bcg .Mark ();_gdc ,_afe :=_ga .tryFetchEOL ();if _afe !=nil {return _afe ;};if _gdc {_gdc ,_afe =_ga .tryFetchEOL ();if _afe !=nil {return _afe ;};if _gdc {return _dg .EOF ;};};_ga ._bcg .Reset ();};return _ga .decode2D ();};func (_cdg *Decoder )decodeRowType2 ()error {if _cdg ._eca {_cdg ._bcg .Align ();
};if _gee :=_cdg .decode1D ();_gee !=nil {return _gee ;};return nil ;};func _ggbf (_dcedb [][]byte )[][]byte {_cddd :=make ([]byte ,len (_dcedb [0]));for _gdb :=range _cddd {_cddd [_gdb ]=_eac ;};_dcedb =append (_dcedb ,[]byte {});for _dffed :=len (_dcedb )-1;
_dffed > 0;_dffed --{_dcedb [_dffed ]=_dcedb [_dffed -1];};_dcedb [0]=_cddd ;return _dcedb ;};func (_bec *Decoder )decode1D ()error {var (_beb int ;_afc error ;);_fdc :=true ;_bec ._gcf =0;for {var _add int ;if _fdc {_add ,_afc =_bec .decodeRun (_aa );
}else {_add ,_afc =_bec .decodeRun (_ed );};if _afc !=nil {return _afc ;};_beb +=_add ;_bec ._dce [_bec ._gcf ]=_beb ;_bec ._gcf ++;_fdc =!_fdc ;if _beb >=_bec ._beea {break ;};};return nil ;};func _dbb (_bacb []byte ,_gcef int ,_dbfb code )([]byte ,int ){_deecb :=0;
for _deecb < _dbfb .BitsWritten {_dagf :=_gcef /8;_ggfe :=_gcef %8;if _dagf >=len (_bacb ){_bacb =append (_bacb ,0);};_bgfd :=8-_ggfe ;_dcgg :=_dbfb .BitsWritten -_deecb ;if _bgfd > _dcgg {_bgfd =_dcgg ;};if _deecb < 8{_bacb [_dagf ]=_bacb [_dagf ]|byte (_dbfb .Code >>uint (8+_ggfe -_deecb ))&_eaa [8-_bgfd -_ggfe ];
}else {_bacb [_dagf ]=_bacb [_dagf ]|(byte (_dbfb .Code <<uint (_deecb -8))&_eaa [8-_bgfd ])>>uint (_ggfe );};_gcef +=_bgfd ;_deecb +=_bgfd ;};return _bacb ,_gcef ;};func (_becc *Encoder )encodeG32D (_gcd [][]byte )[]byte {var _dbe []byte ;var _cbc int ;
for _ffg :=0;_ffg < len (_gcd );_ffg +=_becc .K {if _becc .Rows > 0&&!_becc .EndOfBlock &&_ffg ==_becc .Rows {break ;};_abbd ,_fgc :=_daa (_gcd [_ffg ],_cbc ,_ec );_dbe =_becc .appendEncodedRow (_dbe ,_abbd ,_cbc );if _becc .EncodedByteAlign {_fgc =0;};
_cbc =_fgc ;for _edgb :=_ffg +1;_edgb < (_ffg +_becc .K )&&_edgb < len (_gcd );_edgb ++{if _becc .Rows > 0&&!_becc .EndOfBlock &&_edgb ==_becc .Rows {break ;};_fdec ,_dfg :=_dbb (nil ,_cbc ,_fab );var _afa ,_dca ,_dcfc int ;_agd :=-1;for _agd < len (_gcd [_edgb ]){_afa =_edgbg (_gcd [_edgb ],_agd );
_dca =_gdd (_gcd [_edgb ],_gcd [_edgb -1],_agd );_dcfc =_edgbg (_gcd [_edgb -1],_dca );if _dcfc < _afa {_fdec ,_dfg =_fbb (_fdec ,_dfg );_agd =_dcfc ;}else {if _g .Abs (float64 (_dca -_afa ))> 3{_fdec ,_dfg ,_agd =_ffbc (_gcd [_edgb ],_fdec ,_dfg ,_agd ,_afa );
}else {_fdec ,_dfg =_egb (_fdec ,_dfg ,_afa ,_dca );_agd =_afa ;};};};_dbe =_becc .appendEncodedRow (_dbe ,_fdec ,_cbc );if _becc .EncodedByteAlign {_dfg =0;};_cbc =_dfg %8;};};if _becc .EndOfBlock {_acfc ,_ :=_fcf (_cbc );_dbe =_becc .appendEncodedRow (_dbe ,_acfc ,_cbc );
};return _dbe ;};func (_dde *Encoder )appendEncodedRow (_dged ,_ggb []byte ,_efbeb int )[]byte {if len (_dged )> 0&&_efbeb !=0&&!_dde .EncodedByteAlign {_dged [len (_dged )-1]=_dged [len (_dged )-1]|_ggb [0];_dged =append (_dged ,_ggb [1:]...);}else {_dged =append (_dged ,_ggb ...);
};return _dged ;};func _fcf (_ead int )([]byte ,int ){var _bgg []byte ;for _bgdf :=0;_bgdf < 6;_bgdf ++{_bgg ,_ead =_dbb (_bgg ,_ead ,_ec );};return _bgg ,_ead %8;};func (_ae *Decoder )Read (in []byte )(int ,error ){if _ae ._ggd !=nil {return 0,_ae ._ggd ;
};_cdf :=len (in );var (_gfg int ;_ge int ;);for _cdf !=0{if _ae ._ca >=_ae ._dgf {if _cgf :=_ae .fetch ();_cgf !=nil {_ae ._ggd =_cgf ;return 0,_cgf ;};};if _ae ._dgf ==-1{return _gfg ,_dg .EOF ;};switch {case _cdf <=_ae ._dgf -_ae ._ca :_gcfe :=_ae ._faa [_ae ._ca :_ae ._ca +_cdf ];
for _ ,_fca :=range _gcfe {if !_ae ._dbg {_fca =^_fca ;};in [_ge ]=_fca ;_ge ++;};_gfg +=len (_gcfe );_ae ._ca +=len (_gcfe );return _gfg ,nil ;default:_fde :=_ae ._faa [_ae ._ca :];for _ ,_cfd :=range _fde {if !_ae ._dbg {_cfd =^_cfd ;};in [_ge ]=_cfd ;
_ge ++;};_gfg +=len (_fde );_ae ._ca +=len (_fde );_cdf -=len (_fde );};};return _gfg ,nil ;};func (_fceg *Decoder )getNextChangingElement (_dgb int ,_gbd bool )int {_dfa :=0;if !_gbd {_dfa =1;};_bdg :=int (uint32 (_fceg ._ebe )&0xFFFFFFFE)+_dfa ;if _bdg > 2{_bdg -=2;
};if _dgb ==0{return _bdg ;};for _gca :=_bdg ;_gca < _fceg ._acd ;_gca +=2{if _dgb < _fceg ._ee [_gca ]{_fceg ._ebe =_gca ;return _gca ;};};return -1;};func (_dge *Decoder )decodeG32D ()error {_dge ._acd =_dge ._gcf ;_dge ._dce ,_dge ._ee =_dge ._ee ,_dge ._dce ;
_fabb :=true ;var (_aad bool ;_dcee int ;_gde error ;);_dge ._gcf =0;_gcb :for _dcee < _dge ._beea {_ag :=_gg ._gaf ;for {_aad ,_gde =_dge ._bcg .ReadBool ();if _gde !=nil {return _gde ;};_ag =_ag .walk (_aad );if _ag ==nil {continue _gcb ;};if !_ag ._bff {continue ;
};switch _ag ._bggb {case _b :var _ecd int ;if _fabb {_ecd ,_gde =_dge .decodeRun (_aa );}else {_ecd ,_gde =_dge .decodeRun (_ed );};if _gde !=nil {return _gde ;};_dcee +=_ecd ;_dge ._dce [_dge ._gcf ]=_dcee ;_dge ._gcf ++;if _fabb {_ecd ,_gde =_dge .decodeRun (_ed );
}else {_ecd ,_gde =_dge .decodeRun (_aa );};if _gde !=nil {return _gde ;};_dcee +=_ecd ;_dge ._dce [_dge ._gcf ]=_dcee ;_dge ._gcf ++;case _gc :_age :=_dge .getNextChangingElement (_dcee ,_fabb )+1;if _age >=_dge ._acd {_dcee =_dge ._beea ;}else {_dcee =_dge ._ee [_age ];
};default:_caf :=_dge .getNextChangingElement (_dcee ,_fabb );if _caf >=_dge ._acd ||_caf ==-1{_dcee =_dge ._beea +_ag ._bggb ;}else {_dcee =_dge ._ee [_caf ]+_ag ._bggb ;};_dge ._dce [_dge ._gcf ]=_dcee ;_dge ._gcf ++;_fabb =!_fabb ;};continue _gcb ;};
};return nil ;};var _eab =[...][]uint16 {{2,3,4,5,6,7},{128,8,9,64,10,11},{192,1664,16,17,13,14,15,1,12},{26,21,28,27,18,24,25,22,256,23,20,19},{33,34,35,36,37,38,31,32,29,53,54,39,40,41,42,43,44,30,61,62,63,0,320,384,45,59,60,46,49,50,51,52,55,56,57,58,448,512,640,576,47,48},{1472,1536,1600,1728,704,768,832,896,960,1024,1088,1152,1216,1280,1344,1408},{},{1792,1856,1920},{1984,2048,2112,2176,2240,2304,2368,2432,2496,2560}};
func (_bdc *treeNode )set (_acdg bool ,_bcgf *treeNode ){if !_acdg {_bdc ._dfbe =_bcgf ;}else {_bdc ._efbeg =_bcgf ;};};func (_aac *Encoder )encodeG31D (_gba [][]byte )[]byte {var _dcg []byte ;_aega :=0;for _ddb :=range _gba {if _aac .Rows > 0&&!_aac .EndOfBlock &&_ddb ==_aac .Rows {break ;
};_adg ,_fdbd :=_daa (_gba [_ddb ],_aega ,_eg );_dcg =_aac .appendEncodedRow (_dcg ,_adg ,_aega );if _aac .EncodedByteAlign {_fdbd =0;};_aega =_fdbd ;};if _aac .EndOfBlock {_bgb ,_ :=_fecf (_aega );_dcg =_aac .appendEncodedRow (_dcg ,_bgb ,_aega );};return _dcg ;
};func _daa (_eae []byte ,_ecf int ,_ebgf code )([]byte ,int ){_dffg :=true ;var _def []byte ;_def ,_ecf =_dbb (nil ,_ecf ,_ebgf );_cfe :=0;var _ffe int ;for _cfe < len (_eae ){_ffe ,_cfe =_dced (_eae ,_dffg ,_cfe );_def ,_ecf =_aef (_def ,_ecf ,_ffe ,_dffg );
_dffg =!_dffg ;};return _def ,_ecf %8;};func (_fce tiffType )String ()string {switch _fce {case _cff :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u004d\u006f\u0064i\u0066\u0069\u0065\u0064\u0048\u0075\u0066\u0066\u006d\u0061n\u0052\u006c\u0065";
case _gbe :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u0054\u0034";case _dc :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u0054\u0036";default:return "\u0075n\u0064\u0065\u0066\u0069\u006e\u0065d";};};func (_dga *Decoder )decodeRun (_cad *tree )(int ,error ){var _fg int ;
_da :=_cad ._gaf ;for {_bgd ,_gce :=_dga ._bcg .ReadBool ();if _gce !=nil {return 0,_gce ;};_da =_da .walk (_bgd );if _da ==nil {return 0,_e .New ("\u0075\u006e\u006bno\u0077\u006e\u0020\u0063\u006f\u0064\u0065\u0020\u0069n\u0020H\u0075f\u0066m\u0061\u006e\u0020\u0052\u004c\u0045\u0020\u0073\u0074\u0072\u0065\u0061\u006d");
};if _da ._bff {_fg +=_da ._bggb ;switch {case _da ._bggb >=64:_da =_cad ._gaf ;case _da ._bggb >=0:return _fg ,nil ;default:return _dga ._beea ,nil ;};};};};type Encoder struct{K int ;EndOfLine bool ;EncodedByteAlign bool ;Columns int ;Rows int ;EndOfBlock bool ;
BlackIs1 bool ;DamagedRowsBeforeError int ;};type Decoder struct{_beea int ;_dcf int ;_ffa int ;_faa []byte ;_fb int ;_fda bool ;_cgc bool ;_efc bool ;_dbg bool ;_fcd bool ;_cba bool ;_eca bool ;_dgf int ;_ca int ;_ee []int ;_dce []int ;_acd int ;_gcf int ;
_ccd int ;_ebe int ;_bcg *_f .Reader ;_dffc tiffType ;_ggd error ;};func _dced (_gcae []byte ,_dag bool ,_fdd int )(int ,int ){_efef :=0;for _fdd < len (_gcae ){if _dag {if _gcae [_fdd ]!=_eac {break ;};}else {if _gcae [_fdd ]!=_bedb {break ;};};_efef ++;
_fdd ++;};return _efef ,_fdd ;};func (_cbb *Decoder )tryFetchRTC2D ()(_aeg error ){_cbb ._bcg .Mark ();var _dfc bool ;for _fgd :=0;_fgd < 5;_fgd ++{_dfc ,_aeg =_cbb .tryFetchEOL1 ();if _aeg !=nil {if _e .Is (_aeg ,_dg .EOF ){if _fgd ==0{break ;};return _dda ;
};};if _dfc {continue ;};if _fgd > 0{return _dda ;};break ;};if _dfc {return _dg .EOF ;};_cbb ._bcg .Reset ();return _aeg ;};func (_debb *tree )fillWithNode (_cafbg ,_dffedb int ,_efee *treeNode )error {_dcfd :=_debb ._gaf ;for _bgc :=0;_bgc < _cafbg ;
_bgc ++{_ecb :=uint (_cafbg -1-_bgc );_ebgfb :=((_dffedb >>_ecb )&1)!=0;_aaa :=_dcfd .walk (_ebgfb );if _aaa !=nil {if _aaa ._bff {return _e .New ("\u006e\u006f\u0064\u0065\u0020\u0069\u0073\u0020\u006c\u0065\u0061\u0066\u002c\u0020\u006eo\u0020o\u0074\u0068\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067");
};_dcfd =_aaa ;continue ;};if _bgc ==_cafbg -1{_aaa =_efee ;}else {_aaa =&treeNode {};};if _dffedb ==0{_aaa ._acc =true ;};_dcfd .set (_ebgfb ,_aaa );_dcfd =_aaa ;};return nil ;};func _egb (_abeb []byte ,_agg ,_fgg ,_efa int )([]byte ,int ){_fbd :=_gff (_fgg ,_efa );
_abeb ,_agg =_dbb (_abeb ,_agg ,_fbd );return _abeb ,_agg ;};func _edgbg (_efg []byte ,_gbg int )int {if _gbg >=len (_efg ){return _gbg ;};if _gbg < -1{_gbg =-1;};var _ccea byte ;if _gbg > -1{_ccea =_efg [_gbg ];}else {_ccea =_eac ;};_abbe :=_gbg +1;for _abbe < len (_efg ){if _efg [_abbe ]!=_ccea {break ;
};_abbe ++;};return _abbe ;};type code struct{Code uint16 ;BitsWritten int ;};var _ea =[...][]uint16 {{0x7,0x8,0xb,0xc,0xe,0xf},{0x12,0x13,0x14,0x1b,0x7,0x8},{0x17,0x18,0x2a,0x2b,0x3,0x34,0x35,0x7,0x8},{0x13,0x17,0x18,0x24,0x27,0x28,0x2b,0x3,0x37,0x4,0x8,0xc},{0x12,0x13,0x14,0x15,0x16,0x17,0x1a,0x1b,0x2,0x24,0x25,0x28,0x29,0x2a,0x2b,0x2c,0x2d,0x3,0x32,0x33,0x34,0x35,0x36,0x37,0x4,0x4a,0x4b,0x5,0x52,0x53,0x54,0x55,0x58,0x59,0x5a,0x5b,0x64,0x65,0x67,0x68,0xa,0xb},{0x98,0x99,0x9a,0x9b,0xcc,0xcd,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb},{},{0x8,0xc,0xd},{0x12,0x13,0x14,0x15,0x16,0x17,0x1c,0x1d,0x1e,0x1f}};
func _gff (_dffe ,_cgd int )code {var _cgcd code ;switch _cgd -_dffe {case -1:_cgcd =_bg ;case -2:_cgcd =_dff ;case -3:_cgcd =_cg ;case 0:_cgcd =_efe ;case 1:_cgcd =_cca ;case 2:_cgcd =_cf ;case 3:_cgcd =_fed ;};return _cgcd ;};func NewDecoder (data []byte ,options DecodeOptions )(*Decoder ,error ){_ccg :=&Decoder {_bcg :_f .NewReader (data ),_beea :options .Columns ,_dcf :options .Rows ,_fb :options .DamagedRowsBeforeError ,_faa :make ([]byte ,(options .Columns +7)/8),_ee :make ([]int ,options .Columns +2),_dce :make ([]int ,options .Columns +2),_eca :options .EncodedByteAligned ,_dbg :options .BlackIsOne ,_fcd :options .EndOfLine ,_cba :options .EndOfBlock };
switch {case options .K ==0:_ccg ._dffc =_gbe ;if len (data )< 20{return nil ,_e .New ("\u0074o\u006f\u0020\u0073\u0068o\u0072\u0074\u0020\u0063\u0063i\u0074t\u0066a\u0078\u0020\u0073\u0074\u0072\u0065\u0061m");};_dbf :=data [:20];if _dbf [0]!=0||(_dbf [1]>>4!=1&&_dbf [1]!=1){_ccg ._dffc =_cff ;
_cdb :=(uint16 (_dbf [0])<<8+uint16 (_dbf [1]&0xff))>>4;for _ab :=12;_ab < 160;_ab ++{_cdb =(_cdb <<1)+uint16 ((_dbf [_ab /8]>>uint16 (7-(_ab %8)))&0x01);if _cdb &0xfff==1{_ccg ._dffc =_gbe ;break ;};};};case options .K < 0:_ccg ._dffc =_dc ;case options .K > 0:_ccg ._dffc =_gbe ;
_ccg ._fda =true ;};switch _ccg ._dffc {case _cff ,_gbe ,_dc :default:return nil ,_e .New ("\u0075\u006ek\u006e\u006f\u0077\u006e\u0020\u0063\u0063\u0069\u0074\u0074\u0066\u0061\u0078\u002e\u0044\u0065\u0063\u006f\u0064\u0065\u0072\u0020ty\u0070\u0065");
};return _ccg ,nil ;};func (_bb *Decoder )decodeRow ()(_bga error ){if !_bb ._cba &&_bb ._dcf > 0&&_bb ._dcf ==_bb ._ffa {return _dg .EOF ;};switch _bb ._dffc {case _cff :_bga =_bb .decodeRowType2 ();case _gbe :_bga =_bb .decodeRowType4 ();case _dc :_bga =_bb .decodeRowType6 ();
};if _bga !=nil {return _bga ;};_dee :=0;_bgf :=true ;_bb ._ebe =0;for _eba :=0;_eba < _bb ._gcf ;_eba ++{_fbc :=_bb ._beea ;if _eba !=_bb ._gcf {_fbc =_bb ._dce [_eba ];};if _fbc > _bb ._beea {_fbc =_bb ._beea ;};_gdf :=_dee /8;for _dee %8!=0&&_fbc -_dee > 0{var _gbb byte ;
if !_bgf {_gbb =1<<uint (7-(_dee %8));};_bb ._faa [_gdf ]|=_gbb ;_dee ++;};if _dee %8==0{_gdf =_dee /8;var _acf byte ;if !_bgf {_acf =0xff;};for _fbc -_dee > 7{_bb ._faa [_gdf ]=_acf ;_dee +=8;_gdf ++;};};for _fbc -_dee > 0{if _dee %8==0{_bb ._faa [_gdf ]=0;
};var _ffb byte ;if !_bgf {_ffb =1<<uint (7-(_dee %8));};_bb ._faa [_gdf ]|=_ffb ;_dee ++;};_bgf =!_bgf ;};if _dee !=_bb ._beea {return _e .New ("\u0073\u0075\u006d\u0020\u006f\u0066 \u0072\u0075\u006e\u002d\u006c\u0065\u006e\u0067\u0074\u0068\u0073\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074 \u0065\u0071\u0075\u0061\u006c\u0020\u0073\u0063\u0061\u006e\u0020\u006c\u0069\u006ee\u0020w\u0069\u0064\u0074\u0068");
};_bb ._dgf =(_dee +7)/8;_bb ._ffa ++;return nil ;};func _aef (_ggdc []byte ,_cafb int ,_efege int ,_ceg bool )([]byte ,int ){var (_fcg code ;_gfb bool ;);for !_gfb {_fcg ,_efege ,_gfb =_cee (_efege ,_ceg );_ggdc ,_cafb =_dbb (_ggdc ,_cafb ,_fcg );};return _ggdc ,_cafb ;
};func (_cda *treeNode )walk (_gfff bool )*treeNode {if _gfff {return _cda ._efbeg ;};return _cda ._dfbe ;};var (_eac byte =1;_bedb byte =0;);func (_afb *Decoder )decodeRowType4 ()error {if !_afb ._fda {return _afb .decoderRowType41D ();};if _afb ._eca {_afb ._bcg .Align ();
};_afb ._bcg .Mark ();_gcff ,_cge :=_afb .tryFetchEOL ();if _cge !=nil {return _cge ;};if !_gcff &&_afb ._fcd {_afb ._ccd ++;if _afb ._ccd > _afb ._fb {return _ege ;};_afb ._bcg .Reset ();};if !_gcff {_afb ._bcg .Reset ();};_cccb ,_cge :=_afb ._bcg .ReadBool ();
if _cge !=nil {return _cge ;};if _cccb {if _gcff &&_afb ._cba {if _cge =_afb .tryFetchRTC2D ();_cge !=nil {return _cge ;};};_cge =_afb .decode1D ();}else {_cge =_afb .decode2D ();};if _cge !=nil {return _cge ;};return nil ;};var _gf =[...][]uint16 {{3,2},{1,4},{6,5},{7},{9,8},{10,11,12},{13,14},{15},{16,17,0,18,64},{24,25,23,22,19,20,21,1792,1856,1920},{1984,2048,2112,2176,2240,2304,2368,2432,2496,2560,52,55,56,59,60,320,384,448,53,54,50,51,44,45,46,47,57,58,61,256,48,49,62,63,30,31,32,33,40,41,128,192,26,27,28,29,34,35,36,37,38,39,42,43},{640,704,768,832,1280,1344,1408,1472,1536,1600,1664,1728,512,576,896,960,1024,1088,1152,1216}};
type tiffType int ;func (_eed *Encoder )encodeG4 (_ecdc [][]byte )[]byte {_ecc :=make ([][]byte ,len (_ecdc ));copy (_ecc ,_ecdc );_ecc =_ggbf (_ecc );var _fge []byte ;var _cbe int ;for _bba :=1;_bba < len (_ecc );_bba ++{if _eed .Rows > 0&&!_eed .EndOfBlock &&_bba ==(_eed .Rows +1){break ;
};var _abe []byte ;var _ged ,_cce ,_cgfg int ;_edgc :=_cbe ;_efeg :=-1;for _efeg < len (_ecc [_bba ]){_ged =_edgbg (_ecc [_bba ],_efeg );_cce =_gdd (_ecc [_bba ],_ecc [_bba -1],_efeg );_cgfg =_edgbg (_ecc [_bba -1],_cce );if _cgfg < _ged {_abe ,_edgc =_dbb (_abe ,_edgc ,_fef );
_efeg =_cgfg ;}else {if _g .Abs (float64 (_cce -_ged ))> 3{_abe ,_edgc ,_efeg =_ffbc (_ecc [_bba ],_abe ,_edgc ,_efeg ,_ged );}else {_abe ,_edgc =_egb (_abe ,_edgc ,_ged ,_cce );_efeg =_ged ;};};};_fge =_eed .appendEncodedRow (_fge ,_abe ,_cbe );if _eed .EncodedByteAlign {_edgc =0;
};_cbe =_edgc %8;};if _eed .EndOfBlock {_aca ,_ :=_efbe (_cbe );_fge =_eed .appendEncodedRow (_fge ,_aca ,_cbe );};return _fge ;};func (_cbcc *tree )fill (_fbe ,_cag ,_eeab int )error {_fbf :=_cbcc ._gaf ;for _cea :=0;_cea < _fbe ;_cea ++{_bfgg :=_fbe -1-_cea ;
_fbed :=((_cag >>uint (_bfgg ))&1)!=0;_fae :=_fbf .walk (_fbed );if _fae !=nil {if _fae ._bff {return _e .New ("\u006e\u006f\u0064\u0065\u0020\u0069\u0073\u0020\u006c\u0065\u0061\u0066\u002c\u0020\u006eo\u0020o\u0074\u0068\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067");
};_fbf =_fae ;continue ;};_fae =&treeNode {};if _cea ==_fbe -1{_fae ._bggb =_eeab ;_fae ._bff =true ;};if _cag ==0{_fae ._acc =true ;};_fbf .set (_fbed ,_fae );_fbf =_fae ;};return nil ;};