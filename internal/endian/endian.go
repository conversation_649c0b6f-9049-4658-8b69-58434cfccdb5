//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package endian ;import (_a "encoding/binary";_g "unsafe";);func IsLittle ()bool {return !_ag };func init (){const _agf =int (_g .Sizeof (0));_gc :=1;_af :=(*[_agf ]byte )(_g .Pointer (&_gc ));if _af [0]==0{_ag =true ;ByteOrder =_a .BigEndian ;}else {ByteOrder =_a .LittleEndian ;
};};func IsBig ()bool {return _ag };var (ByteOrder _a .ByteOrder ;_ag bool ;);