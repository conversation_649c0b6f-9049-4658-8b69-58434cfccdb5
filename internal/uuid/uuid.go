//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package uuid ;import (_g "crypto/rand";_f "encoding/hex";_b "io";);func NewUUID ()(UUID ,error ){var uuid UUID ;_ ,_df :=_b .ReadFull (_fc ,uuid [:]);if _df !=nil {return _ba ,_df ;};uuid [6]=(uuid [6]&0x0f)|0x40;uuid [8]=(uuid [8]&0x3f)|0x80;return uuid ,nil ;
};var _ba UUID ;var _fc =_g .Reader ;type UUID [16]byte ;func _gc (_eg []byte ,_eb UUID ){_f .Encode (_eg ,_eb [:4]);_eg [8]='-';_f .Encode (_eg [9:13],_eb [4:6]);_eg [13]='-';_f .Encode (_eg [14:18],_eb [6:8]);_eg [18]='-';_f .Encode (_eg [19:23],_eb [8:10]);
_eg [23]='-';_f .Encode (_eg [24:],_eb [10:]);};func MustUUID ()UUID {uuid ,_ag :=NewUUID ();if _ag !=nil {panic (_ag );};return uuid ;};func (_bc UUID )String ()string {var _gg [36]byte ;_gc (_gg [:],_bc );return string (_gg [:])};var Nil =_ba ;