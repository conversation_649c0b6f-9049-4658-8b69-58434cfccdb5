//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package imageutil ;import (_f "encoding/binary";_d "errors";_ag "fmt";_bc "github.com/unidoc/unipdf/v4/common";_dg "github.com/unidoc/unipdf/v4/internal/bitwise";_dc "image";_e "image/color";_ec "image/draw";_a "math";);func (_eedd *Gray8 )SetGray (x ,y int ,g _e .Gray ){_eaf :=y *_eedd .BytesPerLine +x ;
if _eaf > len (_eedd .Data )-1{return ;};_eedd .Data [_eaf ]=g .Y ;};func _cbd (_cc<PERSON> ,_ccee <PERSON> ,_gdfa _dc .Rectangle ){for _gae :=0;_gae < _gdfa .Max .X ;_gae ++{for _gge :=0;_gge < _gdfa .Max .Y ;_gge ++{_gbeg :=_ccce .GrayAt (_gae ,_gge );_ccee .SetCMYK (_gae ,_gge ,_babc (_gbeg ));
};};};var _ _dc .Image =&Monochrome {};func _abbd (_fdcf _e .Gray ,_cdfg monochromeModel )_e .Gray {if _fdcf .Y > uint8 (_cdfg ){return _e .Gray {Y :_a .MaxUint8 };};return _e .Gray {};};func (_gdbb *Monochrome )clearBit (_gcag ,_fffc int ){_gdbb .Data [_gcag ]&=^(0x80>>uint (_fffc &7))};
func _ebf (_fcbfe _e .Color )_e .Color {_ccde :=_e .GrayModel .Convert (_fcbfe ).(_e .Gray );return _cffe (_ccde );};type RasterOperator int ;func _bff (_dba ,_fdeb int ,_fca []byte )*Monochrome {_feef :=_cbb (_dba ,_fdeb );_feef .Data =_fca ;return _feef ;
};func (_fcec *Gray8 )Validate ()error {if len (_fcec .Data )!=_fcec .Height *_fcec .BytesPerLine {return ErrInvalidImage ;};return nil ;};func _bdd (_acg *Monochrome ,_ffe ,_cgg int )(*Monochrome ,error ){if _acg ==nil {return nil ,_d .New ("\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _ffe <=0||_cgg <=0{return nil ,_d .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0063\u0061l\u0065\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020<\u003d\u0020\u0030");};if _ffe ==_cgg {if _ffe ==1{return _acg .copy (),nil ;};
if _ffe ==2||_ffe ==4||_ffe ==8{_cfe ,_gfa :=_bb (_acg ,_ffe );if _gfa !=nil {return nil ,_gfa ;};return _cfe ,nil ;};};_faa :=_ffe *_acg .Width ;_dd :=_cgg *_acg .Height ;_gg :=_cbb (_faa ,_dd );_fdbd :=_gg .BytesPerLine ;var (_cgd ,_dfdd ,_ebc ,_bfg ,_fc int ;
_da byte ;_aa error ;);for _dfdd =0;_dfdd < _acg .Height ;_dfdd ++{_cgd =_cgg *_dfdd *_fdbd ;for _ebc =0;_ebc < _acg .Width ;_ebc ++{if _dcc :=_acg .getBitAt (_ebc ,_dfdd );_dcc {_fc =_ffe *_ebc ;for _bfg =0;_bfg < _ffe ;_bfg ++{_gg .setIndexedBit (_cgd *8+_fc +_bfg );
};};};for _bfg =1;_bfg < _cgg ;_bfg ++{_afb :=_cgd +_bfg *_fdbd ;for _bda :=0;_bda < _fdbd ;_bda ++{if _da ,_aa =_gg .getByte (_cgd +_bda );_aa !=nil {return nil ,_aa ;};if _aa =_gg .setByte (_afb +_bda ,_da );_aa !=nil {return nil ,_aa ;};};};};return _gg ,nil ;
};func (_agcbg *ImageBase )setEightBytes (_fbaee int ,_eafc uint64 )error {_dde :=_agcbg .BytesPerLine -(_fbaee %_agcbg .BytesPerLine );if _agcbg .BytesPerLine !=_agcbg .Width >>3{_dde --;};if _dde >=8{return _agcbg .setEightFullBytes (_fbaee ,_eafc );
};return _agcbg .setEightPartlyBytes (_fbaee ,_dde ,_eafc );};func _feba (_bba _dc .Image )(Image ,error ){if _gadf ,_fegb :=_bba .(*Gray16 );_fegb {return _gadf .Copy (),nil ;};_cdcf :=_bba .Bounds ();_cfad ,_egagb :=NewImage (_cdcf .Max .X ,_cdcf .Max .Y ,16,1,nil ,nil ,nil );
if _egagb !=nil {return nil ,_egagb ;};_bdab (_bba ,_cfad ,_cdcf );return _cfad ,nil ;};func _ccg (_bab ,_gdfe *Monochrome ,_daf []byte ,_aff int )(_cdd error ){var (_geg ,_cadb ,_afg ,_fce ,_aad ,_ded ,_cbf ,_edf int ;_fbg ,_bee ,_bce ,_beca uint32 ;_dbb ,_gda byte ;
_adg uint16 ;);_aadf :=make ([]byte ,4);_efg :=make ([]byte ,4);for _afg =0;_afg < _bab .Height -1;_afg ,_fce =_afg +2,_fce +1{_geg =_afg *_bab .BytesPerLine ;_cadb =_fce *_gdfe .BytesPerLine ;for _aad ,_ded =0,0;_aad < _aff ;_aad ,_ded =_aad +4,_ded +1{for _cbf =0;
_cbf < 4;_cbf ++{_edf =_geg +_aad +_cbf ;if _edf <=len (_bab .Data )-1&&_edf < _geg +_bab .BytesPerLine {_aadf [_cbf ]=_bab .Data [_edf ];}else {_aadf [_cbf ]=0x00;};_edf =_geg +_bab .BytesPerLine +_aad +_cbf ;if _edf <=len (_bab .Data )-1&&_edf < _geg +(2*_bab .BytesPerLine ){_efg [_cbf ]=_bab .Data [_edf ];
}else {_efg [_cbf ]=0x00;};};_fbg =_f .BigEndian .Uint32 (_aadf );_bee =_f .BigEndian .Uint32 (_efg );_bce =_fbg &_bee ;_bce |=_bce <<1;_beca =_fbg |_bee ;_beca &=_beca <<1;_bee =_bce &_beca ;_bee &=0xaaaaaaaa;_fbg =_bee |(_bee <<7);_dbb =byte (_fbg >>24);
_gda =byte ((_fbg >>8)&0xff);_edf =_cadb +_ded ;if _edf +1==len (_gdfe .Data )-1||_edf +1>=_cadb +_gdfe .BytesPerLine {if _cdd =_gdfe .setByte (_edf ,_daf [_dbb ]);_cdd !=nil {return _ag .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_edf );};
}else {_adg =(uint16 (_daf [_dbb ])<<8)|uint16 (_daf [_gda ]);if _cdd =_gdfe .setTwoBytes (_edf ,_adg );_cdd !=nil {return _ag .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_edf );
};_ded ++;};};};return nil ;};func (_cadd *RGBA32 )ColorModel ()_e .Model {return _e .NRGBAModel };func _cedfa (_dbfg _dc .Image ,_ddff uint8 )*_dc .Gray {_aedc :=_dbfg .Bounds ();_ggbg :=_dc .NewGray (_aedc );var (_abaac _e .Color ;_cgbcf _e .Gray ;);
for _ccbe :=0;_ccbe < _aedc .Max .X ;_ccbe ++{for _ggfcb :=0;_ggfcb < _aedc .Max .Y ;_ggfcb ++{_abaac =_dbfg .At (_ccbe ,_ggfcb );_ggbg .Set (_ccbe ,_ggfcb ,_abaac );_cgbcf =_ggbg .GrayAt (_ccbe ,_ggfcb );_ggbg .SetGray (_ccbe ,_ggfcb ,_e .Gray {Y :_acbb (_cgbcf .Y ,_ddff )});
};};return _ggbg ;};var _ _dc .Image =&Gray2 {};type monochromeThresholdConverter struct{Threshold uint8 ;};func _aeb (_aeg int )[]uint {var _dfc []uint ;_ce :=_aeg ;_eeb :=_ce /8;if _eeb !=0{for _efa :=0;_efa < _eeb ;_efa ++{_dfc =append (_dfc ,8);};_eee :=_ce %8;
_ce =0;if _eee !=0{_ce =_eee ;};};_ccf :=_ce /4;if _ccf !=0{for _gb :=0;_gb < _ccf ;_gb ++{_dfc =append (_dfc ,4);};_fga :=_ce %4;_ce =0;if _fga !=0{_ce =_fga ;};};_aggc :=_ce /2;if _aggc !=0{for _bg :=0;_bg < _aggc ;_bg ++{_dfc =append (_dfc ,2);};};return _dfc ;
};const (_dgfd shift =iota ;_cbcbe ;);type SMasker interface{HasAlpha ()bool ;GetAlpha ()[]byte ;MakeAlpha ();};func (_acge *RGBA32 )Base ()*ImageBase {return &_acge .ImageBase };func (_geag *Gray16 )Validate ()error {if len (_geag .Data )!=_geag .Height *_geag .BytesPerLine {return ErrInvalidImage ;
};return nil ;};func (_aagg *Monochrome )Copy ()Image {return &Monochrome {ImageBase :_aagg .ImageBase .copy (),ModelThreshold :_aagg .ModelThreshold };};func (_agad *Monochrome )Base ()*ImageBase {return &_agad .ImageBase };func NewImageBase (width int ,height int ,bitsPerComponent int ,colorComponents int ,data []byte ,alpha []byte ,decode []float64 )ImageBase {_efba :=ImageBase {Width :width ,Height :height ,BitsPerComponent :bitsPerComponent ,ColorComponents :colorComponents ,Data :data ,Alpha :alpha ,Decode :decode ,BytesPerLine :BytesPerLine (width ,bitsPerComponent ,colorComponents )};
if data ==nil {_efba .Data =make ([]byte ,height *_efba .BytesPerLine );};return _efba ;};func (_cfa *Gray4 )ColorModel ()_e .Model {return Gray4Model };func _dccf (_ggbb int ,_gdebc int )error {return _ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",_ggbb ,_gdebc );
};func (_efge *Gray8 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtGray8BPC (x ,y ,_efge .BytesPerLine ,_efge .Data ,_efge .Decode );};type monochromeModel uint8 ;func _fgc (_bffg _e .NRGBA )_e .Gray {var _deb _e .NRGBA ;if _bffg ==_deb {return _e .Gray {Y :0xff};
};_fceg ,_aef ,_adbf ,_ :=_bffg .RGBA ();_fgbd :=(19595*_fceg +38470*_aef +7471*_adbf +1<<15)>>24;return _e .Gray {Y :uint8 (_fgbd )};};func _egab (_bdec Gray ,_abfg nrgba64 ,_gcda _dc .Rectangle ){for _agag :=0;_agag < _gcda .Max .X ;_agag ++{for _dbd :=0;
_dbd < _gcda .Max .Y ;_dbd ++{_bcbg :=_adbb (_abfg .NRGBA64At (_agag ,_dbd ));_bdec .SetGray (_agag ,_dbd ,_bcbg );};};};var _ Image =&Monochrome {};func _bed (_afge _dc .Image )(Image ,error ){if _cgbc ,_cbfe :=_afge .(*CMYK32 );_cbfe {return _cgbc .Copy (),nil ;
};_fac :=_afge .Bounds ();_dda ,_efaa :=NewImage (_fac .Max .X ,_fac .Max .Y ,8,4,nil ,nil ,nil );if _efaa !=nil {return nil ,_efaa ;};switch _cgde :=_afge .(type ){case CMYK :_gfbg (_cgde ,_dda .(CMYK ),_fac );case Gray :_cbd (_cgde ,_dda .(CMYK ),_fac );
case NRGBA :_fcf (_cgde ,_dda .(CMYK ),_fac );case RGBA :_acaa (_cgde ,_dda .(CMYK ),_fac );default:_ead (_afge ,_dda ,_fac );};return _dda ,nil ;};func (_adb *CMYK32 )At (x ,y int )_e .Color {_fff ,_ :=_adb .ColorAt (x ,y );return _fff };func _ccae (_fbgc *_dc .NYCbCrA ,_ffbe RGBA ,_ebgaf _dc .Rectangle ){for _cbed :=0;
_cbed < _ebgaf .Max .X ;_cbed ++{for _feccbc :=0;_feccbc < _ebgaf .Max .Y ;_feccbc ++{_aabef :=_fbgc .NYCbCrAAt (_cbed ,_feccbc );_ffbe .SetRGBA (_cbed ,_feccbc ,_gccg (_aabef ));};};};func (_agcb *monochromeThresholdConverter )Convert (img _dc .Image )(Image ,error ){if _dce ,_aeae :=img .(*Monochrome );
_aeae {return _dce .Copy (),nil ;};_eebb :=img .Bounds ();_fagf ,_agbf :=NewImage (_eebb .Max .X ,_eebb .Max .Y ,1,1,nil ,nil ,nil );if _agbf !=nil {return nil ,_agbf ;};_fagf .(*Monochrome ).ModelThreshold =_agcb .Threshold ;for _cdfc :=0;_cdfc < _eebb .Max .X ;
_cdfc ++{for _ceg :=0;_ceg < _eebb .Max .Y ;_ceg ++{_fbdf :=img .At (_cdfc ,_ceg );_fagf .Set (_cdfc ,_ceg ,_fbdf );};};return _fagf ,nil ;};func (_afgb *Gray16 )Histogram ()(_fceca [256]int ){for _begb :=0;_begb < _afgb .Width ;_begb ++{for _egfb :=0;
_egfb < _afgb .Height ;_egfb ++{_fceca [_afgb .GrayAt (_begb ,_egfb ).Y ]++;};};return _fceca ;};func _fgdd (_bceb _e .NRGBA64 )_e .NRGBA {return _e .NRGBA {R :uint8 (_bceb .R >>8),G :uint8 (_bceb .G >>8),B :uint8 (_bceb .B >>8),A :uint8 (_bceb .A >>8)};
};type Monochrome struct{ImageBase ;ModelThreshold uint8 ;};func GetConverter (bitsPerComponent ,colorComponents int )(ColorConverter ,error ){switch colorComponents {case 1:switch bitsPerComponent {case 1:return MonochromeConverter ,nil ;case 2:return Gray2Converter ,nil ;
case 4:return Gray4Converter ,nil ;case 8:return GrayConverter ,nil ;case 16:return Gray16Converter ,nil ;};case 3:switch bitsPerComponent {case 4:return NRGBA16Converter ,nil ;case 8:return NRGBAConverter ,nil ;case 16:return NRGBA64Converter ,nil ;};
case 4:return CMYKConverter ,nil ;};return nil ,_ag .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0063\u006f\u006c\u006f\u0072\u0043o\u006e\u0076\u0065\u0072\u0074\u0065\u0072\u0020\u0070\u0061\u0072\u0061\u006d\u0065t\u0065\u0072\u0073\u002e\u0020\u0042\u0069\u0074\u0073\u0050\u0065\u0072\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u003a\u0020\u0025\u0064\u002c\u0020\u0043\u006f\u006co\u0072\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006et\u0073\u003a \u0025\u0064",bitsPerComponent ,colorComponents );
};func _fbbcd (_gce _dc .Image ,_edfg int )(_dc .Rectangle ,bool ,[]byte ){_dcfee :=_gce .Bounds ();var (_cecb bool ;_gdcg []byte ;);switch _dccg :=_gce .(type ){case SMasker :_cecb =_dccg .HasAlpha ();case NRGBA ,RGBA ,*_dc .RGBA64 ,nrgba64 ,*_dc .NYCbCrA :_gdcg =make ([]byte ,_dcfee .Max .X *_dcfee .Max .Y *_edfg );
case *_dc .Paletted :if !_dccg .Opaque (){_gdcg =make ([]byte ,_dcfee .Max .X *_dcfee .Max .Y *_edfg );};};return _dcfee ,_cecb ,_gdcg ;};func (_ffc *CMYK32 )Copy ()Image {return &CMYK32 {ImageBase :_ffc .copy ()}};func _gbfeg (_affa *Monochrome ,_cbbd ,_bcg ,_agaf ,_gega int ,_abda RasterOperator ,_beef *Monochrome ,_bgc ,_affb int )error {var (_caeaa byte ;
_ecgb int ;_cdba int ;_bdadg ,_ffce int ;_dcac ,_fdg int ;);_ccdbd :=_agaf >>3;_ecbc :=_agaf &7;if _ecbc > 0{_caeaa =_bdfdg [_ecbc ];};_ecgb =_beef .BytesPerLine *_affb +(_bgc >>3);_cdba =_affa .BytesPerLine *_bcg +(_cbbd >>3);switch _abda {case PixSrc :for _dcac =0;
_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=_beef .Data [_bdadg ];_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ],_caeaa );
};};case PixNotSrc :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=^(_beef .Data [_bdadg ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^_beef .Data [_bdadg ],_caeaa );
};};case PixSrcOrDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]|=_beef .Data [_bdadg ];_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ]|_affa .Data [_ffce ],_caeaa );
};};case PixSrcAndDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]&=_beef .Data [_bdadg ];_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ]&_affa .Data [_ffce ],_caeaa );
};};case PixSrcXorDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]^=_beef .Data [_bdadg ];_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ]^_affa .Data [_ffce ],_caeaa );
};};case PixNotSrcOrDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]|=^(_beef .Data [_bdadg ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^(_beef .Data [_bdadg ])|_affa .Data [_ffce ],_caeaa );
};};case PixNotSrcAndDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]&=^(_beef .Data [_bdadg ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^(_beef .Data [_bdadg ])&_affa .Data [_ffce ],_caeaa );
};};case PixSrcOrNotDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=_beef .Data [_bdadg ]|^(_affa .Data [_ffce ]);_ffce ++;
_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ]|^(_affa .Data [_ffce ]),_caeaa );};};case PixSrcAndNotDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;
for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=_beef .Data [_bdadg ]&^(_affa .Data [_ffce ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],_beef .Data [_bdadg ]&^(_affa .Data [_ffce ]),_caeaa );};};case PixNotPixSrcOrDst :for _dcac =0;
_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=^(_beef .Data [_bdadg ]|_affa .Data [_ffce ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^(_beef .Data [_bdadg ]|_affa .Data [_ffce ]),_caeaa );
};};case PixNotPixSrcAndDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=^(_beef .Data [_bdadg ]&_affa .Data [_ffce ]);
_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^(_beef .Data [_bdadg ]&_affa .Data [_ffce ]),_caeaa );};};case PixNotPixSrcXorDst :for _dcac =0;_dcac < _gega ;_dcac ++{_bdadg =_ecgb +_dcac *_beef .BytesPerLine ;_ffce =_cdba +_dcac *_affa .BytesPerLine ;
for _fdg =0;_fdg < _ccdbd ;_fdg ++{_affa .Data [_ffce ]=^(_beef .Data [_bdadg ]^_affa .Data [_ffce ]);_ffce ++;_bdadg ++;};if _ecbc > 0{_affa .Data [_ffce ]=_bbfe (_affa .Data [_ffce ],^(_beef .Data [_bdadg ]^_affa .Data [_ffce ]),_caeaa );};};default:_bc .Log .Debug ("\u0050\u0072ov\u0069\u0064\u0065d\u0020\u0069\u006e\u0076ali\u0064 r\u0061\u0073\u0074\u0065\u0072\u0020\u006fpe\u0072\u0061\u0074\u006f\u0072\u003a\u0020%\u0076",_abda );
return _d .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");};return nil ;};func AutoThresholdTriangle (histogram [256]int )uint8 {var _cge ,_ebae ,_eegf ,_afbg int ;
for _efdg :=0;_efdg < len (histogram );_efdg ++{if histogram [_efdg ]> 0{_cge =_efdg ;break ;};};if _cge > 0{_cge --;};for _dabd :=255;_dabd > 0;_dabd --{if histogram [_dabd ]> 0{_afbg =_dabd ;break ;};};if _afbg < 255{_afbg ++;};for _accb :=0;_accb < 256;
_accb ++{if histogram [_accb ]> _ebae {_eegf =_accb ;_ebae =histogram [_accb ];};};var _gcdb bool ;if (_eegf -_cge )< (_afbg -_eegf ){_gcdb =true ;var _febd int ;_aefg :=255;for _febd < _aefg {_dfgg :=histogram [_febd ];histogram [_febd ]=histogram [_aefg ];
histogram [_aefg ]=_dfgg ;_febd ++;_aefg --;};_cge =255-_afbg ;_eegf =255-_eegf ;};if _cge ==_eegf {return uint8 (_cge );};_bdbga :=float64 (histogram [_eegf ]);_beba :=float64 (_cge -_eegf );_dbgc :=_a .Sqrt (_bdbga *_bdbga +_beba *_beba );_bdbga /=_dbgc ;
_beba /=_dbgc ;_dbgc =_bdbga *float64 (_cge )+_beba *float64 (histogram [_cge ]);_fcfb :=_cge ;var _eeebd float64 ;for _fgdbe :=_cge +1;_fgdbe <=_eegf ;_fgdbe ++{_fggcg :=_bdbga *float64 (_fgdbe )+_beba *float64 (histogram [_fgdbe ])-_dbgc ;if _fggcg > _eeebd {_fcfb =_fgdbe ;
_eeebd =_fggcg ;};};_fcfb --;if _gcdb {var _ddae int ;_bebe :=255;for _ddae < _bebe {_efcd :=histogram [_ddae ];histogram [_ddae ]=histogram [_bebe ];histogram [_bebe ]=_efcd ;_ddae ++;_bebe --;};return uint8 (255-_fcfb );};return uint8 (_fcfb );};func (_gfbd *NRGBA32 )setRGBA (_gfdd int ,_ggfc _e .NRGBA ){_dafd :=3*_gfdd ;
_gfbd .Data [_dafd ]=_ggfc .R ;_gfbd .Data [_dafd +1]=_ggfc .G ;_gfbd .Data [_dafd +2]=_ggfc .B ;if _gfdd < len (_gfbd .Alpha ){_gfbd .Alpha [_gfdd ]=_ggfc .A ;};};func _cd (_fag *Monochrome ,_eg int ,_fg []uint )(*Monochrome ,error ){_df :=_eg *_fag .Width ;
_gf :=_eg *_fag .Height ;_fae :=_cbb (_df ,_gf );for _bd ,_cdf :=range _fg {var _bf error ;switch _cdf {case 2:_bf =_fdd (_fae ,_fag );case 4:_bf =_de (_fae ,_fag );case 8:_bf =_ff (_fae ,_fag );};if _bf !=nil {return nil ,_bf ;};if _bd !=len (_fg )-1{_fag =_fae .copy ();
};};return _fae ,nil ;};func _cece (_fdae _dc .Image ,_cfdb Image ,_ddcc _dc .Rectangle ){if _dcfb ,_afbeb :=_fdae .(SMasker );_afbeb &&_dcfb .HasAlpha (){_cfdb .(SMasker ).MakeAlpha ();};_ead (_fdae ,_cfdb ,_ddcc );};func _fdaed (_aae _dc .Image )(Image ,error ){if _cgaee ,_dgcdf :=_aae .(*RGBA32 );
_dgcdf {return _cgaee .Copy (),nil ;};_fcebg ,_bbff ,_bffag :=_fbbcd (_aae ,1);_dbfc :=&RGBA32 {ImageBase :NewImageBase (_fcebg .Max .X ,_fcebg .Max .Y ,8,3,nil ,_bffag ,nil )};_gcgg (_aae ,_dbfc ,_fcebg );if len (_bffag )!=0&&!_bbff {if _aggcb :=_ffaa (_bffag ,_dbfc );
_aggcb !=nil {return nil ,_aggcb ;};};return _dbfc ,nil ;};func (_debg *NRGBA32 )Base ()*ImageBase {return &_debg .ImageBase };func _effd (_becf _e .NRGBA )_e .CMYK {_acb ,_dbab ,_dcfe ,_ :=_becf .RGBA ();_gdfg ,_fbfc ,_fgba ,_cff :=_e .RGBToCMYK (uint8 (_acb >>8),uint8 (_dbab >>8),uint8 (_dcfe >>8));
return _e .CMYK {C :_gdfg ,M :_fbfc ,Y :_fgba ,K :_cff };};func _aed ()(_ad [256]uint64 ){for _fcbc :=0;_fcbc < 256;_fcbc ++{if _fcbc &0x01!=0{_ad [_fcbc ]|=0xff;};if _fcbc &0x02!=0{_ad [_fcbc ]|=0xff00;};if _fcbc &0x04!=0{_ad [_fcbc ]|=0xff0000;};if _fcbc &0x08!=0{_ad [_fcbc ]|=0xff000000;
};if _fcbc &0x10!=0{_ad [_fcbc ]|=0xff00000000;};if _fcbc &0x20!=0{_ad [_fcbc ]|=0xff0000000000;};if _fcbc &0x40!=0{_ad [_fcbc ]|=0xff000000000000;};if _fcbc &0x80!=0{_ad [_fcbc ]|=0xff00000000000000;};};return _ad ;};func (_cedd *Gray8 )ColorModel ()_e .Model {return _e .GrayModel };
func (_gcdaa *ImageBase )HasAlpha ()bool {if _gcdaa .Alpha ==nil {return false ;};for _bggb :=range _gcdaa .Alpha {if _gcdaa .Alpha [_bggb ]!=0xff{return true ;};};return false ;};func (_ebg *Monochrome )At (x ,y int )_e .Color {_fagd ,_ :=_ebg .ColorAt (x ,y );
return _fagd };func ImgToBinary (i _dc .Image ,threshold uint8 )*_dc .Gray {switch _eaff :=i .(type ){case *_dc .Gray :if _bdge (_eaff ){return _eaff ;};return _cefea (_eaff ,threshold );case *_dc .Gray16 :return _eaec (_eaff ,threshold );default:return _cedfa (_eaff ,threshold );
};};func (_ddbc *Gray4 )setGray (_aeca int ,_feg int ,_fcfcb _e .Gray ){_eacc :=_feg *_ddbc .BytesPerLine ;_bbfd :=_eacc +(_aeca >>1);if _bbfd >=len (_ddbc .Data ){return ;};_facg :=_fcfcb .Y >>4;_ddbc .Data [_bbfd ]=(_ddbc .Data [_bbfd ]&(^(0xf0>>uint (4*(_aeca &1)))))|(_facg <<uint (4-4*(_aeca &1)));
};func (_cdbe *Monochrome )GrayAt (x ,y int )_e .Gray {_becg ,_ :=ColorAtGray1BPC (x ,y ,_cdbe .BytesPerLine ,_cdbe .Data ,_cdbe .Decode );return _becg ;};func _ff (_fbe ,_ca *Monochrome )(_bdf error ){_fde :=_ca .BytesPerLine ;_fdf :=_fbe .BytesPerLine ;
var _eff ,_aga ,_db ,_ge ,_bde int ;for _db =0;_db < _ca .Height ;_db ++{_eff =_db *_fde ;_aga =8*_db *_fdf ;for _ge =0;_ge < _fde ;_ge ++{if _bdf =_fbe .setEightBytes (_aga +_ge *8,_aea [_ca .Data [_eff +_ge ]]);_bdf !=nil {return _bdf ;};};for _bde =1;
_bde < 8;_bde ++{for _ge =0;_ge < _fdf ;_ge ++{if _bdf =_fbe .setByte (_aga +_bde *_fdf +_ge ,_fbe .Data [_aga +_ge ]);_bdf !=nil {return _bdf ;};};};};return nil ;};type Gray8 struct{ImageBase };func (_gagc *Gray2 )At (x ,y int )_e .Color {_gecc ,_ :=_gagc .ColorAt (x ,y );
return _gecc };type RGBA32 struct{ImageBase };var _ Gray =&Monochrome {};var _ _dc .Image =&Gray4 {};var _ Image =&Gray16 {};func (_fdga *RGBA32 )setRGBA (_eedf int ,_cdaa _e .RGBA ){_gfbb :=3*_eedf ;_fdga .Data [_gfbb ]=_cdaa .R ;_fdga .Data [_gfbb +1]=_cdaa .G ;
_fdga .Data [_gfbb +2]=_cdaa .B ;if _eedf < len (_fdga .Alpha ){_fdga .Alpha [_eedf ]=_cdaa .A ;};};func (_ggfa *NRGBA32 )Validate ()error {if len (_ggfa .Data )!=3*_ggfa .Width *_ggfa .Height {return _d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _fge (_bbf ,_acc *Monochrome ,_bec []byte ,_fgae int )(_cad error ){var (_fcbf ,_cfg ,_abaf ,_abaff ,_ace ,_dcf ,_gef ,_dcb int ;_edd ,_cfeg uint32 ;_gca ,_adf byte ;_dae uint16 ;);_aec :=make ([]byte ,4);_ged :=make ([]byte ,4);for _abaf =0;
_abaf < _bbf .Height -1;_abaf ,_abaff =_abaf +2,_abaff +1{_fcbf =_abaf *_bbf .BytesPerLine ;_cfg =_abaff *_acc .BytesPerLine ;for _ace ,_dcf =0,0;_ace < _fgae ;_ace ,_dcf =_ace +4,_dcf +1{for _gef =0;_gef < 4;_gef ++{_dcb =_fcbf +_ace +_gef ;if _dcb <=len (_bbf .Data )-1&&_dcb < _fcbf +_bbf .BytesPerLine {_aec [_gef ]=_bbf .Data [_dcb ];
}else {_aec [_gef ]=0x00;};_dcb =_fcbf +_bbf .BytesPerLine +_ace +_gef ;if _dcb <=len (_bbf .Data )-1&&_dcb < _fcbf +(2*_bbf .BytesPerLine ){_ged [_gef ]=_bbf .Data [_dcb ];}else {_ged [_gef ]=0x00;};};_edd =_f .BigEndian .Uint32 (_aec );_cfeg =_f .BigEndian .Uint32 (_ged );
_cfeg |=_edd ;_cfeg |=_cfeg <<1;_cfeg &=0xaaaaaaaa;_edd =_cfeg |(_cfeg <<7);_gca =byte (_edd >>24);_adf =byte ((_edd >>8)&0xff);_dcb =_cfg +_dcf ;if _dcb +1==len (_acc .Data )-1||_dcb +1>=_cfg +_acc .BytesPerLine {_acc .Data [_dcb ]=_bec [_gca ];}else {_dae =(uint16 (_bec [_gca ])<<8)|uint16 (_bec [_adf ]);
if _cad =_acc .setTwoBytes (_dcb ,_dae );_cad !=nil {return _ag .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_dcb );
};_dcf ++;};};};return nil ;};func (_bffa *Gray4 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_bffa .Width ,Y :_bffa .Height }};};func (_aefa *NRGBA16 )setNRGBA (_fbfd ,_efff ,_gcba int ,_bfbg _e .NRGBA ){if _fbfd *3%2==0{_aefa .Data [_gcba ]=(_bfbg .R >>4)<<4|(_bfbg .G >>4);
_aefa .Data [_gcba +1]=(_bfbg .B >>4)<<4|(_aefa .Data [_gcba +1]&0xf);}else {_aefa .Data [_gcba ]=(_aefa .Data [_gcba ]&0xf0)|(_bfbg .R >>4);_aefa .Data [_gcba +1]=(_bfbg .G >>4)<<4|(_bfbg .B >>4);};if _aefa .Alpha !=nil {_afef :=_efff *BytesPerLine (_aefa .Width ,4,1);
if _afef < len (_aefa .Alpha ){if _fbfd %2==0{_aefa .Alpha [_afef ]=(_bfbg .A >>uint (4))<<uint (4)|(_aefa .Alpha [_gcba ]&0xf);}else {_aefa .Alpha [_afef ]=(_aefa .Alpha [_afef ]&0xf0)|(_bfbg .A >>uint (4));};};};};func init (){_cdfa ()};func (_faf *Gray8 )Copy ()Image {return &Gray8 {ImageBase :_faf .copy ()}};
func _dbe (_fdc *Monochrome ,_fcbe ...int )(_afc *Monochrome ,_ade error ){if _fdc ==nil {return nil ,_d .New ("\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if len (_fcbe )==0{return nil ,_d .New ("\u0074h\u0065\u0072e\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061\u0074 \u006c\u0065\u0061\u0073\u0074\u0020o\u006e\u0065\u0020\u006c\u0065\u0076\u0065\u006c\u0020\u006f\u0066 \u0072\u0065\u0064\u0075\u0063\u0074\u0069\u006f\u006e");
};_bgf :=_dcbf ();_afc =_fdc ;for _ ,_ecf :=range _fcbe {if _ecf <=0{break ;};_afc ,_ade =_ecb (_afc ,_ecf ,_bgf );if _ade !=nil {return nil ,_ade ;};};return _afc ,nil ;};func _adbb (_affc _e .NRGBA64 )_e .Gray {var _gag _e .NRGBA64 ;if _affc ==_gag {return _e .Gray {Y :0xff};
};_bgd ,_fdff ,_fea ,_ :=_affc .RGBA ();_cfgd :=(19595*_bgd +38470*_fdff +7471*_fea +1<<15)>>24;return _e .Gray {Y :uint8 (_cfgd )};};type Image interface{_ec .Image ;Base ()*ImageBase ;Copy ()Image ;Pix ()[]byte ;ColorAt (_fadb ,_gabd int )(_e .Color ,error );
Validate ()error ;};func (_gbac *RGBA32 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtRGBA32 (x ,y ,_gbac .Width ,_gbac .Data ,_gbac .Alpha ,_gbac .Decode );};func _ddg (_fbea _e .Gray )_e .NRGBA {return _e .NRGBA {R :_fbea .Y ,G :_fbea .Y ,B :_fbea .Y ,A :0xff}};
type CMYK32 struct{ImageBase };func (_ecdf *ImageBase )newAlpha (){_faag :=BytesPerLine (_ecdf .Width ,_ecdf .BitsPerComponent ,1);_ecdf .Alpha =make ([]byte ,_ecdf .Height *_faag );};func (_aebg *Monochrome )setGray (_fgaf int ,_dgc _e .Gray ,_abg int ){if _dgc .Y ==0{_aebg .clearBit (_abg ,_fgaf );
}else {_aebg .setGrayBit (_abg ,_fgaf );};};func (_gdfb *NRGBA16 )ColorModel ()_e .Model {return NRGBA16Model };func _babc (_bad _e .Gray )_e .CMYK {return _e .CMYK {K :0xff-_bad .Y }};var _gege [256]uint8 ;func (_beffb *Monochrome )setGrayBit (_cceb ,_bdee int ){_beffb .Data [_cceb ]|=0x80>>uint (_bdee &7)};
func (_bdac *Gray2 )ColorModel ()_e .Model {return Gray2Model };func (_ecaab *NRGBA16 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_ecaab .Width ,Y :_ecaab .Height }};};func (_bfgga *NRGBA64 )SetNRGBA64 (x ,y int ,c _e .NRGBA64 ){_ebaa :=(y *_bfgga .Width +x )*2;
_aeee :=_ebaa *3;if _aeee +5>=len (_bfgga .Data ){return ;};_bfgga .setNRGBA64 (_aeee ,c ,_ebaa );};func (_bgea *NRGBA16 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtNRGBA16 (x ,y ,_bgea .Width ,_bgea .BytesPerLine ,_bgea .Data ,_bgea .Alpha ,_bgea .Decode );
};func (_ceca *CMYK32 )Base ()*ImageBase {return &_ceca .ImageBase };func (_aabf *ImageBase )GetAlpha ()[]byte {return _aabf .Alpha };var (_bdfdg =[]byte {0x00,0x80,0xC0,0xE0,0xF0,0xF8,0xFC,0xFE,0xFF};_eegad =[]byte {0x00,0x01,0x03,0x07,0x0F,0x1F,0x3F,0x7F,0xFF};
);func _cbbf (_ffffg CMYK ,_fcgfb RGBA ,_bedf _dc .Rectangle ){for _gcbdd :=0;_gcbdd < _bedf .Max .X ;_gcbdd ++{for _dcfd :=0;_dcfd < _bedf .Max .Y ;_dcfd ++{_dbae :=_ffffg .CMYKAt (_gcbdd ,_dcfd );_fcgfb .SetRGBA (_gcbdd ,_dcfd ,_eca (_dbae ));};};};func _aeaed (_cbeg _dc .Image )(Image ,error ){if _aagf ,_fecc :=_cbeg .(*Gray2 );
_fecc {return _aagf .Copy (),nil ;};_acfa :=_cbeg .Bounds ();_fdcd ,_edae :=NewImage (_acfa .Max .X ,_acfa .Max .Y ,2,1,nil ,nil ,nil );if _edae !=nil {return nil ,_edae ;};_bdab (_cbeg ,_fdcd ,_acfa );return _fdcd ,nil ;};func (_ccbd *Gray16 )GrayAt (x ,y int )_e .Gray {_bbg ,_ :=_ccbd .ColorAt (x ,y );
return _e .Gray {Y :uint8 (_bbg .(_e .Gray16 ).Y >>8)};};func (_eadd *Gray2 )Set (x ,y int ,c _e .Color ){if x >=_eadd .Width ||y >=_eadd .Height {return ;};_adfa :=Gray2Model .Convert (c ).(_e .Gray );_eaba :=y *_eadd .BytesPerLine ;_dgce :=_eaba +(x >>2);
_beg :=_adfa .Y >>6;_eadd .Data [_dgce ]=(_eadd .Data [_dgce ]&(^(0xc0>>uint (2*((x )&3)))))|(_beg <<uint (6-2*(x &3)));};func (_cfecc *NRGBA64 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_cfecc .Width ,Y :_cfecc .Height }};};func FromGoImage (i _dc .Image )(Image ,error ){switch _cagg :=i .(type ){case Image :return _cagg .Copy (),nil ;
case Gray :return GrayConverter .Convert (i );case *_dc .Gray16 :return Gray16Converter .Convert (i );case CMYK :return CMYKConverter .Convert (i );case *_dc .NRGBA64 :return NRGBA64Converter .Convert (i );default:return NRGBAConverter .Convert (i );};
};func _gdee (_fda ,_ccaa Gray ,_ggef _dc .Rectangle ){for _dgdg :=0;_dgdg < _ggef .Max .X ;_dgdg ++{for _adda :=0;_adda < _ggef .Max .Y ;_adda ++{_ccaa .SetGray (_dgdg ,_adda ,_fda .GrayAt (_dgdg ,_adda ));};};};func _fedae (_bdgf ,_dbdcd NRGBA ,_febbd _dc .Rectangle ){for _ggba :=0;
_ggba < _febbd .Max .X ;_ggba ++{for _dccfe :=0;_dccfe < _febbd .Max .Y ;_dccfe ++{_dbdcd .SetNRGBA (_ggba ,_dccfe ,_bdgf .NRGBAAt (_ggba ,_dccfe ));};};};func (_dgba *NRGBA64 )Validate ()error {if len (_dgba .Data )!=3*2*_dgba .Width *_dgba .Height {return _d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func (_aadg *CMYK32 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_aadg .Width ,Y :_aadg .Height }};};func (_gggb *Monochrome )ScaleLow (width ,height int )(*Monochrome ,error ){if width < 0||height < 0{return nil ,_d .New ("\u0070\u0072\u006f\u0076\u0069\u0064e\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0077\u0069\u0064t\u0068\u0020\u0061\u006e\u0064\u0020\u0068e\u0069\u0067\u0068\u0074");
};_beb :=_cbb (width ,height );_ffab :=make ([]int ,height );_cgdg :=make ([]int ,width );_eac :=float64 (_gggb .Width )/float64 (width );_ffb :=float64 (_gggb .Height )/float64 (height );for _cab :=0;_cab < height ;_cab ++{_ffab [_cab ]=int (_a .Min (_ffb *float64 (_cab )+0.5,float64 (_gggb .Height -1)));
};for _ccdb :=0;_ccdb < width ;_ccdb ++{_cgdg [_ccdb ]=int (_a .Min (_eac *float64 (_ccdb )+0.5,float64 (_gggb .Width -1)));};_eae :=-1;_cdc :=byte (0);for _efgb :=0;_efgb < height ;_efgb ++{_abac :=_ffab [_efgb ]*_gggb .BytesPerLine ;_gfaa :=_efgb *_beb .BytesPerLine ;
for _accd :=0;_accd < width ;_accd ++{_gdac :=_cgdg [_accd ];if _gdac !=_eae {_cdc =_gggb .getBit (_abac ,_gdac );if _cdc !=0{_beb .setBit (_gfaa ,_accd );};_eae =_gdac ;}else {if _cdc !=0{_beb .setBit (_gfaa ,_accd );};};};};return _beb ,nil ;};func _fdd (_fe ,_gfe *Monochrome )(_bfb error ){_ac :=_gfe .BytesPerLine ;
_gc :=_fe .BytesPerLine ;var (_ba byte ;_gd uint16 ;_af ,_dgf ,_bfe ,_gdb ,_agg int ;);for _bfe =0;_bfe < _gfe .Height ;_bfe ++{_af =_bfe *_ac ;_dgf =2*_bfe *_gc ;for _gdb =0;_gdb < _ac ;_gdb ++{_ba =_gfe .Data [_af +_gdb ];_gd =_cc [_ba ];_agg =_dgf +_gdb *2;
if _fe .BytesPerLine !=_gfe .BytesPerLine *2&&(_gdb +1)*2> _fe .BytesPerLine {_bfb =_fe .setByte (_agg ,byte (_gd >>8));}else {_bfb =_fe .setTwoBytes (_agg ,_gd );};if _bfb !=nil {return _bfb ;};};for _gdb =0;_gdb < _gc ;_gdb ++{_agg =_dgf +_gc +_gdb ;
_ba =_fe .Data [_dgf +_gdb ];if _bfb =_fe .setByte (_agg ,_ba );_bfb !=nil {return _bfb ;};};};return nil ;};func _feccb (_bcde nrgba64 ,_abgg NRGBA ,_adac _dc .Rectangle ){for _cgbb :=0;_cgbb < _adac .Max .X ;_cgbb ++{for _gdaec :=0;_gdaec < _adac .Max .Y ;
_gdaec ++{_ffga :=_bcde .NRGBA64At (_cgbb ,_gdaec );_abgg .SetNRGBA (_cgbb ,_gdaec ,_fgdd (_ffga ));};};};func _edg (_cecf _e .NRGBA )_e .RGBA {_abbb ,_ceb ,_dfgd ,_gdfae :=_cecf .RGBA ();return _e .RGBA {R :uint8 (_abbb >>8),G :uint8 (_ceb >>8),B :uint8 (_dfgd >>8),A :uint8 (_gdfae >>8)};
};func (_efd *Gray8 )GrayAt (x ,y int )_e .Gray {_cada ,_ :=ColorAtGray8BPC (x ,y ,_efd .BytesPerLine ,_efd .Data ,_efd .Decode );return _cada ;};const (PixSrc RasterOperator =0xc;PixDst RasterOperator =0xa;PixNotSrc RasterOperator =0x3;PixNotDst RasterOperator =0x5;
PixClr RasterOperator =0x0;PixSet RasterOperator =0xf;PixSrcOrDst RasterOperator =0xe;PixSrcAndDst RasterOperator =0x8;PixSrcXorDst RasterOperator =0x6;PixNotSrcOrDst RasterOperator =0xb;PixNotSrcAndDst RasterOperator =0x2;PixSrcOrNotDst RasterOperator =0xd;
PixSrcAndNotDst RasterOperator =0x4;PixNotPixSrcOrDst RasterOperator =0x1;PixNotPixSrcAndDst RasterOperator =0x7;PixNotPixSrcXorDst RasterOperator =0x9;PixPaint =PixSrcOrDst ;PixSubtract =PixNotSrcAndDst ;PixMask =PixSrcAndDst ;);func _acaac (_gfbge _e .Gray )_e .RGBA {return _e .RGBA {R :_gfbge .Y ,G :_gfbge .Y ,B :_gfbge .Y ,A :0xff}};
func ConverterFunc (converterFunc func (_gee _dc .Image )(Image ,error ))ColorConverter {return colorConverter {_dccb :converterFunc };};func (_beeb *Monochrome )Set (x ,y int ,c _e .Color ){_egd :=y *_beeb .BytesPerLine +x >>3;if _egd > len (_beeb .Data )-1{return ;
};_facc :=_beeb .ColorModel ().Convert (c ).(_e .Gray );_beeb .setGray (x ,_facc ,_egd );};var _ _dc .Image =&Gray16 {};func BytesPerLine (width ,bitsPerComponent ,colorComponents int )int {return ((width *bitsPerComponent )*colorComponents +7)>>3;};func (_fgaff *ImageBase )setEightFullBytes (_bdfc int ,_baeef uint64 )error {if _bdfc +7> len (_fgaff .Data )-1{return _d .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_fgaff .Data [_bdfc ]=byte ((_baeef &0xff00000000000000)>>56);_fgaff .Data [_bdfc +1]=byte ((_baeef &0xff000000000000)>>48);_fgaff .Data [_bdfc +2]=byte ((_baeef &0xff0000000000)>>40);_fgaff .Data [_bdfc +3]=byte ((_baeef &0xff00000000)>>32);_fgaff .Data [_bdfc +4]=byte ((_baeef &0xff000000)>>24);
_fgaff .Data [_bdfc +5]=byte ((_baeef &0xff0000)>>16);_fgaff .Data [_bdfc +6]=byte ((_baeef &0xff00)>>8);_fgaff .Data [_bdfc +7]=byte (_baeef &0xff);return nil ;};func NextPowerOf2 (n uint )uint {if IsPowerOf2 (n ){return n ;};return 1<<(_ccaac (n )+1);
};func InDelta (expected ,current ,delta float64 )bool {_fafg :=expected -current ;if _fafg <=-delta ||_fafg >=delta {return false ;};return true ;};func _gcce (_eebf *Monochrome ,_fafa ,_aebc ,_gecb ,_agafb int ,_afgc RasterOperator ,_abgf *Monochrome ,_dbbd ,_cdfb int )error {var (_faga bool ;
_cbdb bool ;_fdag int ;_dbdc int ;_bfdc int ;_ccbg bool ;_bdcb byte ;_eddc int ;_ecfab int ;_dfeg int ;_dgcg ,_acbe int ;);_gac :=8-(_fafa &7);_efbgb :=_eegad [_gac ];_gagdef :=_eebf .BytesPerLine *_aebc +(_fafa >>3);_ffdd :=_abgf .BytesPerLine *_cdfb +(_dbbd >>3);
if _gecb < _gac {_faga =true ;_efbgb &=_bdfdg [8-_gac +_gecb ];};if !_faga {_fdag =(_gecb -_gac )>>3;if _fdag > 0{_cbdb =true ;_dbdc =_gagdef +1;_bfdc =_ffdd +1;};};_eddc =(_fafa +_gecb )&7;if !(_faga ||_eddc ==0){_ccbg =true ;_bdcb =_bdfdg [_eddc ];_ecfab =_gagdef +1+_fdag ;
_dfeg =_ffdd +1+_fdag ;};switch _afgc {case PixSrc :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ],_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ],_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotSrc :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^_abgf .Data [_ffdd ],_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;
};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=^_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^_abgf .Data [_dfeg ],_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixSrcOrDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ]|_eebf .Data [_gagdef ],_efbgb );_gagdef +=_eebf .BytesPerLine ;
_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]|=_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ]|_eebf .Data [_ecfab ],_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixSrcAndDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ]&_eebf .Data [_gagdef ],_efbgb );
_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]&=_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;
};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ]&_eebf .Data [_ecfab ],_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixSrcXorDst :for _dgcg =0;_dgcg < _agafb ;
_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ]^_eebf .Data [_gagdef ],_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;
_acbe ++{_eebf .Data [_dbdc +_acbe ]^=_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ]^_eebf .Data [_ecfab ],_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotSrcOrDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^(_abgf .Data [_ffdd ])|_eebf .Data [_gagdef ],_efbgb );_gagdef +=_eebf .BytesPerLine ;
_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]|=^(_abgf .Data [_bfdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^(_abgf .Data [_dfeg ])|_eebf .Data [_ecfab ],_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotSrcAndDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^(_abgf .Data [_ffdd ])&_eebf .Data [_gagdef ],_efbgb );
_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]&=^_abgf .Data [_bfdc +_acbe ];};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;
};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^(_abgf .Data [_dfeg ])&_eebf .Data [_ecfab ],_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixSrcOrNotDst :for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ]|^(_eebf .Data [_gagdef ]),_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;
_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=_abgf .Data [_bfdc +_acbe ]|^(_eebf .Data [_dbdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ]|^(_eebf .Data [_ecfab ]),_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixSrcAndNotDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],_abgf .Data [_ffdd ]&^(_eebf .Data [_gagdef ]),_efbgb );_gagdef +=_eebf .BytesPerLine ;
_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=_abgf .Data [_bfdc +_acbe ]&^(_eebf .Data [_dbdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;
};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],_abgf .Data [_dfeg ]&^(_eebf .Data [_ecfab ]),_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotPixSrcOrDst :for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^(_abgf .Data [_ffdd ]|_eebf .Data [_gagdef ]),_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;
_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=^(_abgf .Data [_bfdc +_acbe ]|_eebf .Data [_dbdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^(_abgf .Data [_dfeg ]|_eebf .Data [_ecfab ]),_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotPixSrcAndDst :for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^(_abgf .Data [_ffdd ]&_eebf .Data [_gagdef ]),_efbgb );_gagdef +=_eebf .BytesPerLine ;
_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=^(_abgf .Data [_bfdc +_acbe ]&_eebf .Data [_dbdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;
};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^(_abgf .Data [_dfeg ]&_eebf .Data [_ecfab ]),_bdcb );_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};case PixNotPixSrcXorDst :for _dgcg =0;
_dgcg < _agafb ;_dgcg ++{_eebf .Data [_gagdef ]=_bbfe (_eebf .Data [_gagdef ],^(_abgf .Data [_ffdd ]^_eebf .Data [_gagdef ]),_efbgb );_gagdef +=_eebf .BytesPerLine ;_ffdd +=_abgf .BytesPerLine ;};if _cbdb {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{for _acbe =0;
_acbe < _fdag ;_acbe ++{_eebf .Data [_dbdc +_acbe ]=^(_abgf .Data [_bfdc +_acbe ]^_eebf .Data [_dbdc +_acbe ]);};_dbdc +=_eebf .BytesPerLine ;_bfdc +=_abgf .BytesPerLine ;};};if _ccbg {for _dgcg =0;_dgcg < _agafb ;_dgcg ++{_eebf .Data [_ecfab ]=_bbfe (_eebf .Data [_ecfab ],^(_abgf .Data [_dfeg ]^_eebf .Data [_ecfab ]),_bdcb );
_ecfab +=_eebf .BytesPerLine ;_dfeg +=_abgf .BytesPerLine ;};};default:_bc .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0064",_afgc );return _d .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");
};return nil ;};func (_bdfb *Monochrome )ResolveDecode ()error {if len (_bdfb .Decode )!=2{return nil ;};if _bdfb .Decode [0]==1&&_bdfb .Decode [1]==0{if _aefb :=_bdfb .InverseData ();_aefb !=nil {return _aefb ;};_bdfb .Decode =nil ;};return nil ;};func (_ebcdb *RGBA32 )SetRGBA (x ,y int ,c _e .RGBA ){_gfbc :=y *_ebcdb .Width +x ;
_dgcd :=3*_gfbc ;if _dgcd +2>=len (_ebcdb .Data ){return ;};_ebcdb .setRGBA (_gfbc ,c );};func MonochromeThresholdConverter (threshold uint8 )ColorConverter {return &monochromeThresholdConverter {Threshold :threshold };};type RGBA interface{RGBAAt (_ffafd ,_abe int )_e .RGBA ;
SetRGBA (_dcgf ,_caeb int ,_fbaba _e .RGBA );};func _bgg (_aaaf _e .Gray )_e .Gray {_decb :=_aaaf .Y >>6;_decb |=_decb <<2;_aaaf .Y =_decb |_decb <<4;return _aaaf ;};func _de (_fb ,_fba *Monochrome )(_ae error ){_dfd :=_fba .BytesPerLine ;_fbae :=_fb .BytesPerLine ;
_dfg :=_fba .BytesPerLine *4-_fb .BytesPerLine ;var (_be ,_cg byte ;_bea uint32 ;_eb ,_fdb ,_ed ,_cf ,_ab ,_egg ,_cda int ;);for _ed =0;_ed < _fba .Height ;_ed ++{_eb =_ed *_dfd ;_fdb =4*_ed *_fbae ;for _cf =0;_cf < _dfd ;_cf ++{_be =_fba .Data [_eb +_cf ];
_bea =_bcd [_be ];_egg =_fdb +_cf *4;if _dfg !=0&&(_cf +1)*4> _fb .BytesPerLine {for _ab =_dfg ;_ab > 0;_ab --{_cg =byte ((_bea >>uint (_ab *8))&0xff);_cda =_egg +(_dfg -_ab );if _ae =_fb .setByte (_cda ,_cg );_ae !=nil {return _ae ;};};}else if _ae =_fb .setFourBytes (_egg ,_bea );
_ae !=nil {return _ae ;};if _ae =_fb .setFourBytes (_fdb +_cf *4,_bcd [_fba .Data [_eb +_cf ]]);_ae !=nil {return _ae ;};};for _ab =1;_ab < 4;_ab ++{for _cf =0;_cf < _fbae ;_cf ++{if _ae =_fb .setByte (_fdb +_ab *_fbae +_cf ,_fb .Data [_fdb +_cf ]);_ae !=nil {return _ae ;
};};};};return nil ;};var _ _dc .Image =&Gray8 {};func (_fbb *Monochrome )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_fbb .Width ,Y :_fbb .Height }};};func ColorAtGray1BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Gray ,error ){_becc :=y *bytesPerLine +x >>3;
if _becc >=len (data ){return _e .Gray {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_geecd :=data [_becc ]>>uint (7-(x &7))&1;if len (decode )==2{_geecd =uint8 (LinearInterpolate (float64 (_geecd ),0.0,1.0,decode [0],decode [1]))&1;};return _e .Gray {Y :_geecd *255},nil ;};func (_fabg *Monochrome )ColorModel ()_e .Model {return MonochromeModel (_fabg .ModelThreshold )};
var _ Gray =&Gray16 {};func _cefea (_eabba *_dc .Gray ,_cedfeg uint8 )*_dc .Gray {_eefd :=_eabba .Bounds ();_fbfbb :=_dc .NewGray (_eefd );for _gdgb :=0;_gdgb < _eefd .Dx ();_gdgb ++{for _ffgad :=0;_ffgad < _eefd .Dy ();_ffgad ++{_eecf :=_eabba .GrayAt (_gdgb ,_ffgad );
_fbfbb .SetGray (_gdgb ,_ffgad ,_e .Gray {Y :_acbb (_eecf .Y ,_cedfeg )});};};return _fbfbb ;};func ColorAtNRGBA (x ,y ,width ,bytesPerLine ,bitsPerColor int ,data ,alpha []byte ,decode []float64 )(_e .Color ,error ){switch bitsPerColor {case 4:return ColorAtNRGBA16 (x ,y ,width ,bytesPerLine ,data ,alpha ,decode );
case 8:return ColorAtNRGBA32 (x ,y ,width ,data ,alpha ,decode );case 16:return ColorAtNRGBA64 (x ,y ,width ,data ,alpha ,decode );default:return nil ,_ag .Errorf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u0072\u0067\u0062\u0020b\u0069\u0074\u0073\u0020\u0070\u0065\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0061\u006d\u006f\u0075\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",bitsPerColor );
};};func _ecd (_fcfc _e .Color )_e .Color {_cfb :=_e .GrayModel .Convert (_fcfc ).(_e .Gray );return _bgg (_cfb )};func (_fddc *RGBA32 )RGBAAt (x ,y int )_e .RGBA {_ecdfd ,_ :=ColorAtRGBA32 (x ,y ,_fddc .Width ,_fddc .Data ,_fddc .Alpha ,_fddc .Decode );
return _ecdfd ;};var _ NRGBA =&NRGBA16 {};var _ Gray =&Gray2 {};func (_ffca *CMYK32 )Validate ()error {if len (_ffca .Data )!=4*_ffca .Width *_ffca .Height {return _d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _bbfe (_bfcc ,_gfce ,_gadd byte )byte {return (_bfcc &^(_gadd ))|(_gfce &_gadd )};func _ccaac (_dgdgb uint )uint {var _fegfg uint ;for _dgdgb !=0{_dgdgb >>=1;_fegfg ++;};return _fegfg -1;};func _cedfe (_fcaf *Monochrome ,_bfeb ,_acga ,_cege ,_ecbg int ,_facd RasterOperator ,_agge *Monochrome ,_dbda ,_cgcg int )error {var (_fgad bool ;
_gbgf bool ;_afeg byte ;_aac int ;_deddg int ;_cegf int ;_bfcb int ;_afd bool ;_bgga int ;_ebcd int ;_dfdea int ;_fccg bool ;_adgf byte ;_bdae int ;_bfca int ;_gdaee int ;_gacb byte ;_gebe int ;_cebf int ;_ccbdg uint ;_cbce uint ;_gddf byte ;_bdaef shift ;
_fcgf bool ;_cbdge bool ;_ddea ,_gafc int ;);if _dbda &7!=0{_cebf =8-(_dbda &7);};if _bfeb &7!=0{_deddg =8-(_bfeb &7);};if _cebf ==0&&_deddg ==0{_gddf =_eegad [0];}else {if _deddg > _cebf {_ccbdg =uint (_deddg -_cebf );}else {_ccbdg =uint (8-(_cebf -_deddg ));
};_cbce =8-_ccbdg ;_gddf =_eegad [_ccbdg ];};if (_bfeb &7)!=0{_fgad =true ;_aac =8-(_bfeb &7);_afeg =_eegad [_aac ];_cegf =_fcaf .BytesPerLine *_acga +(_bfeb >>3);_bfcb =_agge .BytesPerLine *_cgcg +(_dbda >>3);_gebe =8-(_dbda &7);if _aac > _gebe {_bdaef =_dgfd ;
if _cege >=_cebf {_fcgf =true ;};}else {_bdaef =_cbcbe ;};};if _cege < _aac {_gbgf =true ;_afeg &=_bdfdg [8-_aac +_cege ];};if !_gbgf {_bgga =(_cege -_aac )>>3;if _bgga !=0{_afd =true ;_ebcd =_fcaf .BytesPerLine *_acga +((_bfeb +_deddg )>>3);_dfdea =_agge .BytesPerLine *_cgcg +((_dbda +_deddg )>>3);
};};_bdae =(_bfeb +_cege )&7;if !(_gbgf ||_bdae ==0){_fccg =true ;_adgf =_bdfdg [_bdae ];_bfca =_fcaf .BytesPerLine *_acga +((_bfeb +_deddg )>>3)+_bgga ;_gdaee =_agge .BytesPerLine *_cgcg +((_dbda +_deddg )>>3)+_bgga ;if _bdae > int (_cbce ){_cbdge =true ;
};};switch _facd {case PixSrc :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb ,_afeg );
_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]=_gacb ;
};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb ,_adgf );
_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixNotSrc :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^_gacb ,_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );
_fcaf .Data [_ebcd +_gafc ]=^_gacb ;};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );
};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^_gacb ,_adgf );_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixSrcOrDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb |_fcaf .Data [_cegf ],_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;
_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]|=_gacb ;};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;
_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb |_fcaf .Data [_bfca ],_adgf );_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;
};};case PixSrcAndDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb &_fcaf .Data [_cegf ],_afeg );
_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]&=_gacb ;
};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb &_fcaf .Data [_bfca ],_adgf );
_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixSrcXorDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb ^_fcaf .Data [_cegf ],_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;
_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]^=_gacb ;};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;
_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb ^_fcaf .Data [_bfca ],_adgf );_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;
};};case PixNotSrcOrDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^_gacb |_fcaf .Data [_cegf ],_afeg );
_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]|=^_gacb ;
};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^_gacb |_fcaf .Data [_bfca ],_adgf );
_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixNotSrcAndDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^_gacb &_fcaf .Data [_cegf ],_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;
_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]&=^_gacb ;};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;
_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^_gacb &_fcaf .Data [_bfca ],_adgf );_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;
};};case PixSrcOrNotDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb |^_fcaf .Data [_cegf ],_afeg );
_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]=_gacb |^_fcaf .Data [_ebcd +_gafc ];
};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb |^_fcaf .Data [_bfca ],_adgf );
_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixSrcAndNotDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],_gacb &^_fcaf .Data [_cegf ],_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;
_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]=_gacb &^_fcaf .Data [_ebcd +_gafc ];};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};
if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],_gacb &^_fcaf .Data [_bfca ],_adgf );_bfca +=_fcaf .BytesPerLine ;
_gdaee +=_agge .BytesPerLine ;};};case PixNotPixSrcOrDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;
};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^(_gacb |_fcaf .Data [_cegf ]),_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );
_fcaf .Data [_ebcd +_gafc ]=^(_gacb |_fcaf .Data [_ebcd +_gafc ]);};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );
};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^(_gacb |_fcaf .Data [_bfca ]),_adgf );_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixNotPixSrcAndDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;
if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^(_gacb &_fcaf .Data [_cegf ]),_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;
};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]=^(_gacb &_fcaf .Data [_ebcd +_gafc ]);
};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^(_gacb &_fcaf .Data [_bfca ]),_adgf );
_bfca +=_fcaf .BytesPerLine ;_gdaee +=_agge .BytesPerLine ;};};case PixNotPixSrcXorDst :if _fgad {for _ddea =0;_ddea < _ecbg ;_ddea ++{if _bdaef ==_dgfd {_gacb =_agge .Data [_bfcb ]<<_ccbdg ;if _fcgf {_gacb =_bbfe (_gacb ,_agge .Data [_bfcb +1]>>_cbce ,_gddf );
};}else {_gacb =_agge .Data [_bfcb ]>>_cbce ;};_fcaf .Data [_cegf ]=_bbfe (_fcaf .Data [_cegf ],^(_gacb ^_fcaf .Data [_cegf ]),_afeg );_cegf +=_fcaf .BytesPerLine ;_bfcb +=_agge .BytesPerLine ;};};if _afd {for _ddea =0;_ddea < _ecbg ;_ddea ++{for _gafc =0;
_gafc < _bgga ;_gafc ++{_gacb =_bbfe (_agge .Data [_dfdea +_gafc ]<<_ccbdg ,_agge .Data [_dfdea +_gafc +1]>>_cbce ,_gddf );_fcaf .Data [_ebcd +_gafc ]=^(_gacb ^_fcaf .Data [_ebcd +_gafc ]);};_ebcd +=_fcaf .BytesPerLine ;_dfdea +=_agge .BytesPerLine ;};
};if _fccg {for _ddea =0;_ddea < _ecbg ;_ddea ++{_gacb =_agge .Data [_gdaee ]<<_ccbdg ;if _cbdge {_gacb =_bbfe (_gacb ,_agge .Data [_gdaee +1]>>_cbce ,_gddf );};_fcaf .Data [_bfca ]=_bbfe (_fcaf .Data [_bfca ],^(_gacb ^_fcaf .Data [_bfca ]),_adgf );_bfca +=_fcaf .BytesPerLine ;
_gdaee +=_agge .BytesPerLine ;};};default:_bc .Log .Debug ("\u004f\u0070e\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069tt\u0065\u0064",_facd );return _d .New ("\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065r\u0061\u0074\u0069\u006f\u006e\u0020\u006eo\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064");
};return nil ;};func (_fedf *ImageBase )setFourBytes (_cefb int ,_gdba uint32 )error {if _cefb +3> len (_fedf .Data )-1{return _ag .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_cefb );
};_fedf .Data [_cefb ]=byte ((_gdba &0xff000000)>>24);_fedf .Data [_cefb +1]=byte ((_gdba &0xff0000)>>16);_fedf .Data [_cefb +2]=byte ((_gdba &0xff00)>>8);_fedf .Data [_cefb +3]=byte (_gdba &0xff);return nil ;};func (_acaf *CMYK32 )SetCMYK (x ,y int ,c _e .CMYK ){_agf :=4*(y *_acaf .Width +x );
if _agf +3>=len (_acaf .Data ){return ;};_acaf .Data [_agf ]=c .C ;_acaf .Data [_agf +1]=c .M ;_acaf .Data [_agf +2]=c .Y ;_acaf .Data [_agf +3]=c .K ;};var _ Image =&NRGBA64 {};func (_adge *NRGBA64 )Set (x ,y int ,c _e .Color ){_cebdg :=(y *_adge .Width +x )*2;
_edee :=_cebdg *3;if _edee +5>=len (_adge .Data ){return ;};_aaaa :=_e .NRGBA64Model .Convert (c ).(_e .NRGBA64 );_adge .setNRGBA64 (_edee ,_aaaa ,_cebdg );};func (_ebgg *NRGBA32 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtNRGBA32 (x ,y ,_ebgg .Width ,_ebgg .Data ,_ebgg .Alpha ,_ebgg .Decode );
};func (_dbed *Monochrome )InverseData ()error {return _dbed .RasterOperation (0,0,_dbed .Width ,_dbed .Height ,PixNotDst ,nil ,0,0);};type NRGBA32 struct{ImageBase };func IsPowerOf2 (n uint )bool {return n > 0&&(n &(n -1))==0};func _eaec (_ecaae *_dc .Gray16 ,_fafe uint8 )*_dc .Gray {_aaea :=_ecaae .Bounds ();
_fcfcd :=_dc .NewGray (_aaea );for _dgg :=0;_dgg < _aaea .Dx ();_dgg ++{for _gfdae :=0;_gfdae < _aaea .Dy ();_gfdae ++{_fddg :=_ecaae .Gray16At (_dgg ,_gfdae );_fcfcd .SetGray (_dgg ,_gfdae ,_e .Gray {Y :_acbb (uint8 (_fddg .Y /256),_fafe )});};};return _fcfcd ;
};var _ Gray =&Gray8 {};func (_ddbg *Gray16 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_ddbg .Width ,Y :_ddbg .Height }};};type Gray4 struct{ImageBase };func (_bfde *NRGBA32 )Copy ()Image {return &NRGBA32 {ImageBase :_bfde .copy ()}};
type Gray16 struct{ImageBase };func _ecfca (_egf _e .RGBA )_e .Gray {_dgbc :=(19595*uint32 (_egf .R )+38470*uint32 (_egf .G )+7471*uint32 (_egf .B )+1<<7)>>16;return _e .Gray {Y :uint8 (_dgbc )};};var (Gray2Model =_e .ModelFunc (_ecd );Gray4Model =_e .ModelFunc (_ebf );
NRGBA16Model =_e .ModelFunc (_gbba ););func ColorAtGray16BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Gray16 ,error ){_fgfgf :=(y *bytesPerLine /2+x )*2;if _fgfgf +1>=len (data ){return _e .Gray16 {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_cebd :=uint16 (data [_fgfgf ])<<8|uint16 (data [_fgfgf +1]);if len (decode )==2{_cebd =uint16 (uint64 (LinearInterpolate (float64 (_cebd ),0,65535,decode [0],decode [1])));};return _e .Gray16 {Y :_cebd },nil ;};func (_egaa *Monochrome )Validate ()error {if len (_egaa .Data )!=_egaa .Height *_egaa .BytesPerLine {return ErrInvalidImage ;
};return nil ;};func (_dbba *NRGBA64 )Copy ()Image {return &NRGBA64 {ImageBase :_dbba .copy ()}};func (_gbg *Gray8 )Base ()*ImageBase {return &_gbg .ImageBase };func (_ffaf *Gray2 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_ffaf .Width ,Y :_ffaf .Height }};
};func (_gad *Monochrome )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtGray1BPC (x ,y ,_gad .BytesPerLine ,_gad .Data ,_gad .Decode );};func _gbba (_gagca _e .Color )_e .Color {_agfeg :=_e .NRGBAModel .Convert (_gagca ).(_e .NRGBA );return _bdgb (_agfeg );
};func _gbff (_fdfa RGBA ,_gbgg Gray ,_dcaa _dc .Rectangle ){for _fccba :=0;_fccba < _dcaa .Max .X ;_fccba ++{for _eeab :=0;_eeab < _dcaa .Max .Y ;_eeab ++{_cgf :=_ecfca (_fdfa .RGBAAt (_fccba ,_eeab ));_gbgg .SetGray (_fccba ,_eeab ,_cgf );};};};func (_bffe *ImageBase )copy ()ImageBase {_efbg :=*_bffe ;
_efbg .Data =make ([]byte ,len (_bffe .Data ));copy (_efbg .Data ,_bffe .Data );return _efbg ;};func (_fadc *Gray2 )GrayAt (x ,y int )_e .Gray {_dbabg ,_ :=ColorAtGray2BPC (x ,y ,_fadc .BytesPerLine ,_fadc .Data ,_fadc .Decode );return _dbabg ;};type ImageBase struct{Width ,Height int ;
BitsPerComponent ,ColorComponents int ;Data ,Alpha []byte ;Decode []float64 ;BytesPerLine int ;};func (_dcaaa *ImageBase )MakeAlpha (){_dcaaa .newAlpha ()};func _ada (_dcba ,_ccc *Monochrome ,_aaf []byte ,_faaa int )(_ccec error ){var (_afed ,_cdb ,_fbd ,_aebd ,_ddf ,_dedd ,_ga ,_ggg int ;
_cae ,_cec uint32 ;_fgd ,_aegc byte ;_ecfc uint16 ;);_agb :=make ([]byte ,4);_gfd :=make ([]byte ,4);for _fbd =0;_fbd < _dcba .Height -1;_fbd ,_aebd =_fbd +2,_aebd +1{_afed =_fbd *_dcba .BytesPerLine ;_cdb =_aebd *_ccc .BytesPerLine ;for _ddf ,_dedd =0,0;
_ddf < _faaa ;_ddf ,_dedd =_ddf +4,_dedd +1{for _ga =0;_ga < 4;_ga ++{_ggg =_afed +_ddf +_ga ;if _ggg <=len (_dcba .Data )-1&&_ggg < _afed +_dcba .BytesPerLine {_agb [_ga ]=_dcba .Data [_ggg ];}else {_agb [_ga ]=0x00;};_ggg =_afed +_dcba .BytesPerLine +_ddf +_ga ;
if _ggg <=len (_dcba .Data )-1&&_ggg < _afed +(2*_dcba .BytesPerLine ){_gfd [_ga ]=_dcba .Data [_ggg ];}else {_gfd [_ga ]=0x00;};};_cae =_f .BigEndian .Uint32 (_agb );_cec =_f .BigEndian .Uint32 (_gfd );_cec &=_cae ;_cec &=_cec <<1;_cec &=0xaaaaaaaa;_cae =_cec |(_cec <<7);
_fgd =byte (_cae >>24);_aegc =byte ((_cae >>8)&0xff);_ggg =_cdb +_dedd ;if _ggg +1==len (_ccc .Data )-1||_ggg +1>=_cdb +_ccc .BytesPerLine {_ccc .Data [_ggg ]=_aaf [_fgd ];if _ccec =_ccc .setByte (_ggg ,_aaf [_fgd ]);_ccec !=nil {return _ag .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_ggg );
};}else {_ecfc =(uint16 (_aaf [_fgd ])<<8)|uint16 (_aaf [_aegc ]);if _ccec =_ccc .setTwoBytes (_ggg ,_ecfc );_ccec !=nil {return _ag .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_ggg );
};_dedd ++;};};};return nil ;};type NRGBA interface{NRGBAAt (_baae ,_aafg int )_e .NRGBA ;SetNRGBA (_gga ,_bebb int ,_dbgg _e .NRGBA );};func (_bcac *Gray2 )Histogram ()(_cffb [256]int ){for _bcec :=0;_bcec < _bcac .Width ;_bcec ++{for _bged :=0;_bged < _bcac .Height ;
_bged ++{_cffb [_bcac .GrayAt (_bcec ,_bged ).Y ]++;};};return _cffb ;};func _ega (_beae _e .NRGBA )_e .Gray {_dbf ,_fdfg ,_cga ,_ :=_beae .RGBA ();_cgdc :=(19595*_dbf +38470*_fdfg +7471*_cga +1<<15)>>24;return _e .Gray {Y :uint8 (_cgdc )};};func (_ede *ImageBase )setTwoBytes (_febb int ,_edbg uint16 )error {if _febb +1> len (_ede .Data )-1{return _d .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_ede .Data [_febb ]=byte ((_edbg &0xff00)>>8);_ede .Data [_febb +1]=byte (_edbg &0xff);return nil ;};func (_gcab *Monochrome )getBit (_aabc ,_dccc int )uint8 {return _gcab .Data [_aabc +(_dccc >>3)]>>uint (7-(_dccc &7))&1;};func (_efdc *Gray16 )At (x ,y int )_e .Color {_bafg ,_ :=_efdc .ColorAt (x ,y );
return _bafg };func _cbb (_dfa ,_abc int )*Monochrome {return &Monochrome {ImageBase :NewImageBase (_dfa ,_abc ,1,1,nil ,nil ,nil ),ModelThreshold :0x0f};};func _fcc (_bdb ,_cbc *Monochrome ,_bdfd []byte ,_gfb int )(_gde error ){var (_gbc ,_abbg ,_ccfe ,_afe ,_gbe ,_gdf ,_cef ,_bdbf int ;
_eda ,_ddb ,_bgb ,_cce uint32 ;_dag ,_eea byte ;_fbff uint16 ;);_aebb :=make ([]byte ,4);_fgf :=make ([]byte ,4);for _ccfe =0;_ccfe < _bdb .Height -1;_ccfe ,_afe =_ccfe +2,_afe +1{_gbc =_ccfe *_bdb .BytesPerLine ;_abbg =_afe *_cbc .BytesPerLine ;for _gbe ,_gdf =0,0;
_gbe < _gfb ;_gbe ,_gdf =_gbe +4,_gdf +1{for _cef =0;_cef < 4;_cef ++{_bdbf =_gbc +_gbe +_cef ;if _bdbf <=len (_bdb .Data )-1&&_bdbf < _gbc +_bdb .BytesPerLine {_aebb [_cef ]=_bdb .Data [_bdbf ];}else {_aebb [_cef ]=0x00;};_bdbf =_gbc +_bdb .BytesPerLine +_gbe +_cef ;
if _bdbf <=len (_bdb .Data )-1&&_bdbf < _gbc +(2*_bdb .BytesPerLine ){_fgf [_cef ]=_bdb .Data [_bdbf ];}else {_fgf [_cef ]=0x00;};};_eda =_f .BigEndian .Uint32 (_aebb );_ddb =_f .BigEndian .Uint32 (_fgf );_bgb =_eda &_ddb ;_bgb |=_bgb <<1;_cce =_eda |_ddb ;
_cce &=_cce <<1;_ddb =_bgb |_cce ;_ddb &=0xaaaaaaaa;_eda =_ddb |(_ddb <<7);_dag =byte (_eda >>24);_eea =byte ((_eda >>8)&0xff);_bdbf =_abbg +_gdf ;if _bdbf +1==len (_cbc .Data )-1||_bdbf +1>=_abbg +_cbc .BytesPerLine {if _gde =_cbc .setByte (_bdbf ,_bdfd [_dag ]);
_gde !=nil {return _ag .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_bdbf );};}else {_fbff =(uint16 (_bdfd [_dag ])<<8)|uint16 (_bdfd [_eea ]);if _gde =_cbc .setTwoBytes (_bdbf ,_fbff );_gde !=nil {return _ag .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_bdbf );
};_gdf ++;};};};return nil ;};func (_fegf *ImageBase )Pix ()[]byte {return _fegf .Data };func ColorAtNRGBA32 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_e .NRGBA ,error ){_gedgd :=y *width +x ;_ddbf :=3*_gedgd ;if _ddbf +2>=len (data ){return _e .NRGBA {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_bbfc :=uint8 (0xff);if alpha !=nil &&len (alpha )> _gedgd {_bbfc =alpha [_gedgd ];};_faae ,_begfc ,_bacc :=data [_ddbf ],data [_ddbf +1],data [_ddbf +2];if len (decode )==6{_caf :=LinearInterpolate (float64 (_faae ),0,255.0,decode [0],decode [1]);_gdbe :=LinearInterpolate (float64 (_begfc ),0,255.0,decode [2],decode [3]);
_daba :=LinearInterpolate (float64 (_bacc ),0,255.0,decode [4],decode [5]);if _caf <=1.0&&_gdbe <=1.0&&_daba <=1.0{_caf *=255.0;_gdbe *=255.0;_daba *=255.0;};_faae =uint8 (_caf )&0xff;_begfc =uint8 (_gdbe )&0xff;_bacc =uint8 (_daba )&0xff;};return _e .NRGBA {R :_faae ,G :_begfc ,B :_bacc ,A :_bbfc },nil ;
};func AddDataPadding (width ,height ,bitsPerComponent ,colorComponents int ,data []byte )([]byte ,error ){_ddc :=BytesPerLine (width ,bitsPerComponent ,colorComponents );if _ddc ==width *colorComponents *bitsPerComponent /8{return data ,nil ;};_eaea :=width *colorComponents *bitsPerComponent ;
_deda :=_ddc *8;_cgdgb :=8-(_deda -_eaea );_dgda :=_dg .NewReader (data );_fbbc :=_ddc -1;_deae :=make ([]byte ,_fbbc );_ebge :=make ([]byte ,height *_ddc );_cgff :=_dg .NewWriterMSB (_ebge );var _ddda uint64 ;var _bcfa error ;for _fcebf :=0;_fcebf < height ;
_fcebf ++{_ ,_bcfa =_dgda .Read (_deae );if _bcfa !=nil {return nil ,_bcfa ;};_ ,_bcfa =_cgff .Write (_deae );if _bcfa !=nil {return nil ,_bcfa ;};_ddda ,_bcfa =_dgda .ReadBits (byte (_cgdgb ));if _bcfa !=nil {return nil ,_bcfa ;};_ ,_bcfa =_cgff .WriteBits (_ddda ,_cgdgb );
if _bcfa !=nil {return nil ,_bcfa ;};_cgff .FinishByte ();};return _ebge ,nil ;};func MonochromeModel (threshold uint8 )_e .Model {return monochromeModel (threshold )};func ColorAtNRGBA64 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_e .NRGBA64 ,error ){_efe :=(y *width +x )*2;
_dfcf :=_efe *3;if _dfcf +5>=len (data ){return _e .NRGBA64 {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};const _fbca =0xffff;_fgbc :=uint16 (_fbca );if alpha !=nil &&len (alpha )> _efe +1{_fgbc =uint16 (alpha [_efe ])<<8|uint16 (alpha [_efe +1]);};_dgcgc :=uint16 (data [_dfcf ])<<8|uint16 (data [_dfcf +1]);_ecea :=uint16 (data [_dfcf +2])<<8|uint16 (data [_dfcf +3]);
_ddfd :=uint16 (data [_dfcf +4])<<8|uint16 (data [_dfcf +5]);if len (decode )==6{_dgcgc =uint16 (uint64 (LinearInterpolate (float64 (_dgcgc ),0,65535,decode [0],decode [1]))&_fbca );_ecea =uint16 (uint64 (LinearInterpolate (float64 (_ecea ),0,65535,decode [2],decode [3]))&_fbca );
_ddfd =uint16 (uint64 (LinearInterpolate (float64 (_ddfd ),0,65535,decode [4],decode [5]))&_fbca );};return _e .NRGBA64 {R :_dgcgc ,G :_ecea ,B :_ddfd ,A :_fgbc },nil ;};func (_gggdg *Gray16 )ColorModel ()_e .Model {return _e .Gray16Model };func (_cabb *Gray4 )Histogram ()(_dca [256]int ){for _edb :=0;
_edb < _cabb .Width ;_edb ++{for _deg :=0;_deg < _cabb .Height ;_deg ++{_dca [_cabb .GrayAt (_edb ,_deg ).Y ]++;};};return _dca ;};func _eca (_cdbg _e .CMYK )_e .RGBA {_dab ,_gea ,_agc :=_e .CMYKToRGB (_cdbg .C ,_cdbg .M ,_cdbg .Y ,_cdbg .K );return _e .RGBA {R :_dab ,G :_gea ,B :_agc ,A :0xff};
};func (_egaf *NRGBA64 )At (x ,y int )_e .Color {_efdcf ,_ :=_egaf .ColorAt (x ,y );return _efdcf };func (_eacde *RGBA32 )Set (x ,y int ,c _e .Color ){_ecbeg :=y *_eacde .Width +x ;_eaag :=3*_ecbeg ;if _eaag +2>=len (_eacde .Data ){return ;};_agcg :=_e .RGBAModel .Convert (c ).(_e .RGBA );
_eacde .setRGBA (_ecbeg ,_agcg );};var _ Gray =&Gray4 {};func (_gfdf *Monochrome )setIndexedBit (_cac int ){_gfdf .Data [(_cac >>3)]|=0x80>>uint (_cac &7)};func _fcf (_efb NRGBA ,_abf CMYK ,_ddbb _dc .Rectangle ){for _cfc :=0;_cfc < _ddbb .Max .X ;_cfc ++{for _ebe :=0;
_ebe < _ddbb .Max .Y ;_ebe ++{_dgb :=_efb .NRGBAAt (_cfc ,_ebe );_abf .SetCMYK (_cfc ,_ebe ,_effd (_dgb ));};};};type CMYK interface{CMYKAt (_gcc ,_gggd int )_e .CMYK ;SetCMYK (_cgb ,_dbc int ,_fbef _e .CMYK );};var _ NRGBA =&NRGBA32 {};func (_gccga *Gray16 )SetGray (x ,y int ,g _e .Gray ){_debf :=(y *_gccga .BytesPerLine /2+x )*2;
if _debf +1>=len (_gccga .Data ){return ;};_gccga .Data [_debf ]=g .Y ;_gccga .Data [_debf +1]=g .Y ;};func (_bggd *NRGBA16 )Validate ()error {if len (_bggd .Data )!=3*_bggd .Width *_bggd .Height /2{return _d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _fdcc (_gfac _e .NRGBA64 )_e .RGBA {_fbeb ,_fab ,_cee ,_cfeb :=_gfac .RGBA ();return _e .RGBA {R :uint8 (_fbeb >>8),G :uint8 (_fab >>8),B :uint8 (_cee >>8),A :uint8 (_cfeb >>8)};};func _dfaf (_acfb _dc .Image )(Image ,error ){if _egag ,_dad :=_acfb .(*Gray4 );
_dad {return _egag .Copy (),nil ;};_bddd :=_acfb .Bounds ();_daaa ,_fbab :=NewImage (_bddd .Max .X ,_bddd .Max .Y ,4,1,nil ,nil ,nil );if _fbab !=nil {return nil ,_fbab ;};_bdab (_acfb ,_daaa ,_bddd );return _daaa ,nil ;};func _ffcg (_egc NRGBA ,_gdaf RGBA ,_deeb _dc .Rectangle ){for _bace :=0;
_bace < _deeb .Max .X ;_bace ++{for _ggcfe :=0;_ggcfe < _deeb .Max .Y ;_ggcfe ++{_ebcb :=_egc .NRGBAAt (_bace ,_ggcfe );_gdaf .SetRGBA (_bace ,_ggcfe ,_edg (_ebcb ));};};};func _fee ()(_bae [256]uint16 ){for _fcb :=0;_fcb < 256;_fcb ++{if _fcb &0x01!=0{_bae [_fcb ]|=0x3;
};if _fcb &0x02!=0{_bae [_fcb ]|=0xc;};if _fcb &0x04!=0{_bae [_fcb ]|=0x30;};if _fcb &0x08!=0{_bae [_fcb ]|=0xc0;};if _fcb &0x10!=0{_bae [_fcb ]|=0x300;};if _fcb &0x20!=0{_bae [_fcb ]|=0xc00;};if _fcb &0x40!=0{_bae [_fcb ]|=0x3000;};if _fcb &0x80!=0{_bae [_fcb ]|=0xc000;
};};return _bae ;};func (_aada *Monochrome )Scale (scale float64 )(*Monochrome ,error ){var _agd bool ;_gccgf :=scale ;if scale < 1{_gccgf =1/scale ;_agd =true ;};_bddg :=NextPowerOf2 (uint (_gccgf ));if InDelta (float64 (_bddg ),_gccgf ,0.001){if _agd {return _aada .ReduceBinary (_gccgf );
};return _aada .ExpandBinary (int (_bddg ));};_fedg :=int (_a .RoundToEven (float64 (_aada .Width )*scale ));_gcbb :=int (_a .RoundToEven (float64 (_aada .Height )*scale ));return _aada .ScaleLow (_fedg ,_gcbb );};func (_bca *Monochrome )getBitAt (_dced ,_fbgf int )bool {_eeda :=_fbgf *_bca .BytesPerLine +(_dced >>3);
_bfcg :=_dced &0x07;_ced :=uint (7-_bfcg );if _eeda > len (_bca .Data )-1{return false ;};if (_bca .Data [_eeda ]>>_ced )&0x01>=1{return true ;};return false ;};func ScaleAlphaToMonochrome (data []byte ,width ,height int )([]byte ,error ){_c :=BytesPerLine (width ,8,1);
if len (data )< _c *height {return nil ,nil ;};_ef :=&Gray8 {NewImageBase (width ,height ,8,1,data ,nil ,nil )};_fa ,_fd :=MonochromeConverter .Convert (_ef );if _fd !=nil {return nil ,_fd ;};return _fa .Base ().Data ,nil ;};func (_gcb *CMYK32 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtCMYK (x ,y ,_gcb .Width ,_gcb .Data ,_gcb .Decode );
};var _ Image =&NRGBA16 {};func (_dcec *Monochrome )RasterOperation (dx ,dy ,dw ,dh int ,op RasterOperator ,src *Monochrome ,sx ,sy int )error {return _dadg (_dcec ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );};func _aedd (_dafe *Monochrome ,_cfec ,_becfb int ,_cccae ,_gafcg int ,_dcbd RasterOperator ){var (_fabd bool ;
_dbge bool ;_fabb int ;_gcbbe int ;_ecgg int ;_cdbaf int ;_gcdf bool ;_ebga byte ;);_geed :=8-(_cfec &7);_edgf :=_eegad [_geed ];_fdffc :=_dafe .BytesPerLine *_becfb +(_cfec >>3);if _cccae < _geed {_fabd =true ;_edgf &=_bdfdg [8-_geed +_cccae ];};if !_fabd {_fabb =(_cccae -_geed )>>3;
if _fabb !=0{_dbge =true ;_gcbbe =_fdffc +1;};};_ecgg =(_cfec +_cccae )&7;if !(_fabd ||_ecgg ==0){_gcdf =true ;_ebga =_bdfdg [_ecgg ];_cdbaf =_fdffc +1+_fabb ;};var _ddfe ,_ebca int ;switch _dcbd {case PixClr :for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_fdffc ]=_bbfe (_dafe .Data [_fdffc ],0x0,_edgf );
_fdffc +=_dafe .BytesPerLine ;};if _dbge {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{for _ebca =0;_ebca < _fabb ;_ebca ++{_dafe .Data [_gcbbe +_ebca ]=0x0;};_gcbbe +=_dafe .BytesPerLine ;};};if _gcdf {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_cdbaf ]=_bbfe (_dafe .Data [_cdbaf ],0x0,_ebga );
_cdbaf +=_dafe .BytesPerLine ;};};case PixSet :for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_fdffc ]=_bbfe (_dafe .Data [_fdffc ],0xff,_edgf );_fdffc +=_dafe .BytesPerLine ;};if _dbge {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{for _ebca =0;_ebca < _fabb ;
_ebca ++{_dafe .Data [_gcbbe +_ebca ]=0xff;};_gcbbe +=_dafe .BytesPerLine ;};};if _gcdf {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_cdbaf ]=_bbfe (_dafe .Data [_cdbaf ],0xff,_ebga );_cdbaf +=_dafe .BytesPerLine ;};};case PixNotDst :for _ddfe =0;
_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_fdffc ]=_bbfe (_dafe .Data [_fdffc ],^_dafe .Data [_fdffc ],_edgf );_fdffc +=_dafe .BytesPerLine ;};if _dbge {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{for _ebca =0;_ebca < _fabb ;_ebca ++{_dafe .Data [_gcbbe +_ebca ]=^(_dafe .Data [_gcbbe +_ebca ]);
};_gcbbe +=_dafe .BytesPerLine ;};};if _gcdf {for _ddfe =0;_ddfe < _gafcg ;_ddfe ++{_dafe .Data [_cdbaf ]=_bbfe (_dafe .Data [_cdbaf ],^_dafe .Data [_cdbaf ],_ebga );_cdbaf +=_dafe .BytesPerLine ;};};};};func ColorAtGray8BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Gray ,error ){_bfgg :=y *bytesPerLine +x ;
if _bfgg >=len (data ){return _e .Gray {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_fgga :=data [_bfgg ];if len (decode )==2{_fgga =uint8 (uint32 (LinearInterpolate (float64 (_fgga ),0,255,decode [0],decode [1]))&0xff);};return _e .Gray {Y :_fgga },nil ;};type Histogramer interface{Histogram ()[256]int ;};type Gray2 struct{ImageBase };
func (_daa *Monochrome )Histogram ()(_dcd [256]int ){for _ ,_fgcd :=range _daa .Data {_dcd [0xff]+=int (_gege [_daa .Data [_fgcd ]]);};return _dcd ;};func (_fabe *ImageBase )setEightPartlyBytes (_ffg ,_bbef int ,_fceb uint64 )(_dceb error ){var (_eeg byte ;
_bgbc int ;);for _ccab :=1;_ccab <=_bbef ;_ccab ++{_bgbc =64-_ccab *8;_eeg =byte (_fceb >>uint (_bgbc )&0xff);if _dceb =_fabe .setByte (_ffg +_ccab -1,_eeg );_dceb !=nil {return _dceb ;};};_agaga :=_fabe .BytesPerLine *8-_fabe .Width ;if _agaga ==0{return nil ;
};_bgbc -=8;_eeg =byte (_fceb >>uint (_bgbc )&0xff)<<uint (_agaga );if _dceb =_fabe .setByte (_ffg +_bbef ,_eeg );_dceb !=nil {return _dceb ;};return nil ;};func (_ebd *NRGBA16 )Set (x ,y int ,c _e .Color ){_gdeb :=y *_ebd .BytesPerLine +x *3/2;if _gdeb +1>=len (_ebd .Data ){return ;
};_dfecaf :=NRGBA16Model .Convert (c ).(_e .NRGBA );_ebd .setNRGBA (x ,y ,_gdeb ,_dfecaf );};func (_cdfad *NRGBA32 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_cdfad .Width ,Y :_cdfad .Height }};};func _bac (_adbfd *Monochrome ,_gdfgd ,_gabb int ,_cbafa ,_fgdb int ,_bagc RasterOperator ,_gagde *Monochrome ,_dfeca ,_acae int )error {var _cdcfe ,_bdace ,_bfgf ,_fbag int ;
if _gdfgd < 0{_dfeca -=_gdfgd ;_cbafa +=_gdfgd ;_gdfgd =0;};if _dfeca < 0{_gdfgd -=_dfeca ;_cbafa +=_dfeca ;_dfeca =0;};_cdcfe =_gdfgd +_cbafa -_adbfd .Width ;if _cdcfe > 0{_cbafa -=_cdcfe ;};_bdace =_dfeca +_cbafa -_gagde .Width ;if _bdace > 0{_cbafa -=_bdace ;
};if _gabb < 0{_acae -=_gabb ;_fgdb +=_gabb ;_gabb =0;};if _acae < 0{_gabb -=_acae ;_fgdb +=_acae ;_acae =0;};_bfgf =_gabb +_fgdb -_adbfd .Height ;if _bfgf > 0{_fgdb -=_bfgf ;};_fbag =_acae +_fgdb -_gagde .Height ;if _fbag > 0{_fgdb -=_fbag ;};if _cbafa <=0||_fgdb <=0{return nil ;
};var _geeca error ;switch {case _gdfgd &7==0&&_dfeca &7==0:_geeca =_gbfeg (_adbfd ,_gdfgd ,_gabb ,_cbafa ,_fgdb ,_bagc ,_gagde ,_dfeca ,_acae );case _gdfgd &7==_dfeca &7:_geeca =_gcce (_adbfd ,_gdfgd ,_gabb ,_cbafa ,_fgdb ,_bagc ,_gagde ,_dfeca ,_acae );
default:_geeca =_cedfe (_adbfd ,_gdfgd ,_gabb ,_cbafa ,_fgdb ,_bagc ,_gagde ,_dfeca ,_acae );};if _geeca !=nil {return _geeca ;};return nil ;};func _bcgc (_bfgfg _dc .Image )(Image ,error ){if _gebb ,_cfae :=_bfgfg .(*NRGBA16 );_cfae {return _gebb .Copy (),nil ;
};_bgdf :=_bfgfg .Bounds ();_dff ,_aege :=NewImage (_bgdf .Max .X ,_bgdf .Max .Y ,4,3,nil ,nil ,nil );if _aege !=nil {return nil ,_aege ;};_acgg (_bfgfg ,_dff ,_bgdf );return _dff ,nil ;};func (_gefc *CMYK32 )CMYKAt (x ,y int )_e .CMYK {_fec ,_ :=ColorAtCMYK (x ,y ,_gefc .Width ,_gefc .Data ,_gefc .Decode );
return _fec ;};func _ggcf (_geagd *Monochrome ,_gba ,_gfcb ,_fcgfa ,_gfba int ,_gaee RasterOperator ){if _gba < 0{_fcgfa +=_gba ;_gba =0;};_dcg :=_gba +_fcgfa -_geagd .Width ;if _dcg > 0{_fcgfa -=_dcg ;};if _gfcb < 0{_gfba +=_gfcb ;_gfcb =0;};_bced :=_gfcb +_gfba -_geagd .Height ;
if _bced > 0{_gfba -=_bced ;};if _fcgfa <=0||_gfba <=0{return ;};if (_gba &7)==0{_eeac (_geagd ,_gba ,_gfcb ,_fcgfa ,_gfba ,_gaee );}else {_aedd (_geagd ,_gba ,_gfcb ,_fcgfa ,_gfba ,_gaee );};};func ColorAtGray2BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Gray ,error ){_fccf :=y *bytesPerLine +x >>2;
if _fccf >=len (data ){return _e .Gray {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_abfgg :=data [_fccf ]>>uint (6-(x &3)*2)&3;if len (decode )==2{_abfgg =uint8 (uint32 (LinearInterpolate (float64 (_abfgg ),0,3.0,decode [0],decode [1]))&3);};return _e .Gray {Y :_abfgg *85},nil ;};var _ _dc .Image =&NRGBA16 {};func (_gbggd *NRGBA32 )NRGBAAt (x ,y int )_e .NRGBA {_aeff ,_ :=ColorAtNRGBA32 (x ,y ,_gbggd .Width ,_gbggd .Data ,_gbggd .Alpha ,_gbggd .Decode );
return _aeff ;};func (_dgff *NRGBA32 )Set (x ,y int ,c _e .Color ){_afdg :=y *_dgff .Width +x ;_dcffa :=3*_afdg ;if _dcffa +2>=len (_dgff .Data ){return ;};_fbfb :=_e .NRGBAModel .Convert (c ).(_e .NRGBA );_dgff .setRGBA (_afdg ,_fbfb );};func RasterOperation (dest *Monochrome ,dx ,dy ,dw ,dh int ,op RasterOperator ,src *Monochrome ,sx ,sy int )error {return _dadg (dest ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};func _adef (_becad _dc .Image )(Image ,error ){if _eefc ,_gdbaf :=_becad .(*NRGBA32 );_gdbaf {return _eefc .Copy (),nil ;};_eagf ,_faeg ,_fffd :=_fbbcd (_becad ,1);_fdcg ,_bdege :=NewImage (_eagf .Max .X ,_eagf .Max .Y ,8,3,nil ,_fffd ,nil );if _bdege !=nil {return nil ,_bdege ;
};_acgg (_becad ,_fdcg ,_eagf );if len (_fffd )!=0&&!_faeg {if _eefa :=_ffaa (_fffd ,_fdcg );_eefa !=nil {return nil ,_eefa ;};};return _fdcg ,nil ;};func (_fbcb *Gray8 )Histogram ()(_addf [256]int ){for _cgae :=0;_cgae < len (_fbcb .Data );_cgae ++{_addf [_fbcb .Data [_cgae ]]++;
};return _addf ;};var _ _dc .Image =&NRGBA64 {};func (_acgd *ImageBase )getByte (_ecfa int )(byte ,error ){if _ecfa > len (_acgd .Data )-1||_ecfa < 0{return 0,_ag .Errorf ("\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",_ecfa );
};return _acgd .Data [_ecfa ],nil ;};func ColorAtGrayscale (x ,y ,bitsPerColor ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Color ,error ){switch bitsPerColor {case 1:return ColorAtGray1BPC (x ,y ,bytesPerLine ,data ,decode );case 2:return ColorAtGray2BPC (x ,y ,bytesPerLine ,data ,decode );
case 4:return ColorAtGray4BPC (x ,y ,bytesPerLine ,data ,decode );case 8:return ColorAtGray8BPC (x ,y ,bytesPerLine ,data ,decode );case 16:return ColorAtGray16BPC (x ,y ,bytesPerLine ,data ,decode );default:return nil ,_ag .Errorf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0067\u0072\u0061\u0079\u0020\u0073c\u0061\u006c\u0065\u0020\u0062\u0069\u0074s\u0020\u0070\u0065\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020a\u006d\u006f\u0075\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",bitsPerColor );
};};func (_dedcf *NRGBA64 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtNRGBA64 (x ,y ,_dedcf .Width ,_dedcf .Data ,_dedcf .Alpha ,_dedcf .Decode );};func (_acfc *Gray2 )Base ()*ImageBase {return &_acfc .ImageBase };func (_age *RGBA32 )At (x ,y int )_e .Color {_gfgb ,_ :=_age .ColorAt (x ,y );
return _gfgb };var _ Image =&CMYK32 {};func _agda (_bbdd _dc .Image )(Image ,error ){if _gdfed ,_eec :=_bbdd .(*Gray8 );_eec {return _gdfed .Copy (),nil ;};_agfc :=_bbdd .Bounds ();_deaab ,_efgd :=NewImage (_agfc .Max .X ,_agfc .Max .Y ,8,1,nil ,nil ,nil );
if _efgd !=nil {return nil ,_efgd ;};_bdab (_bbdd ,_deaab ,_agfc );return _deaab ,nil ;};func _dgbd (_eede NRGBA ,_dagad Gray ,_cceed _dc .Rectangle ){for _agfe :=0;_agfe < _cceed .Max .X ;_agfe ++{for _aadad :=0;_aadad < _cceed .Max .Y ;_aadad ++{_adee :=_ega (_eede .NRGBAAt (_agfe ,_aadad ));
_dagad .SetGray (_agfe ,_aadad ,_adee );};};};type Gray interface{GrayAt (_fcca ,_bfc int )_e .Gray ;SetGray (_aebe ,_cbcg int ,_bgad _e .Gray );};func (_abae *Gray4 )Base ()*ImageBase {return &_abae .ImageBase };func _fbc (_cca _dc .Image )(Image ,error ){if _gcf ,_ccac :=_cca .(*Monochrome );
_ccac {return _gcf ,nil ;};_fcee :=_cca .Bounds ();var _ebac Gray ;switch _beff :=_cca .(type ){case Gray :_ebac =_beff ;case NRGBA :_ebac =&Gray8 {ImageBase :NewImageBase (_fcee .Max .X ,_fcee .Max .Y ,8,1,nil ,nil ,nil )};_gbd (_ebac ,_beff ,_fcee );
case nrgba64 :_ebac =&Gray8 {ImageBase :NewImageBase (_fcee .Max .X ,_fcee .Max .Y ,8,1,nil ,nil ,nil )};_egab (_ebac ,_beff ,_fcee );default:_gbb ,_ebeb :=GrayConverter .Convert (_cca );if _ebeb !=nil {return nil ,_ebeb ;};_ebac =_gbb .(Gray );};_acee ,_efag :=NewImage (_fcee .Max .X ,_fcee .Max .Y ,1,1,nil ,nil ,nil );
if _efag !=nil {return nil ,_efag ;};_dfcga :=_acee .(*Monochrome );_bbd :=AutoThresholdTriangle (GrayHistogram (_ebac ));for _cbbe :=0;_cbbe < _fcee .Max .X ;_cbbe ++{for _ggd :=0;_ggd < _fcee .Max .Y ;_ggd ++{_dfde :=_abbd (_ebac .GrayAt (_cbbe ,_ggd ),monochromeModel (_bbd ));
_dfcga .SetGray (_cbbe ,_ggd ,_dfde );};};return _acee ,nil ;};func _gagd (_ddd _e .NYCbCrA )_e .NRGBA {_baf :=int32 (_ddd .Y )*0x10101;_gdc :=int32 (_ddd .Cb )-128;_bdef :=int32 (_ddd .Cr )-128;_gcbd :=_baf +91881*_bdef ;if uint32 (_gcbd )&0xff000000==0{_gcbd >>=8;
}else {_gcbd =^(_gcbd >>31)&0xffff;};_bdc :=_baf -22554*_gdc -46802*_bdef ;if uint32 (_bdc )&0xff000000==0{_bdc >>=8;}else {_bdc =^(_bdc >>31)&0xffff;};_adc :=_baf +116130*_gdc ;if uint32 (_adc )&0xff000000==0{_adc >>=8;}else {_adc =^(_adc >>31)&0xffff;
};return _e .NRGBA {R :uint8 (_gcbd >>8),G :uint8 (_bdc >>8),B :uint8 (_adc >>8),A :_ddd .A };};func _cb ()(_fgg [256]uint32 ){for _agac :=0;_agac < 256;_agac ++{if _agac &0x01!=0{_fgg [_agac ]|=0xf;};if _agac &0x02!=0{_fgg [_agac ]|=0xf0;};if _agac &0x04!=0{_fgg [_agac ]|=0xf00;
};if _agac &0x08!=0{_fgg [_agac ]|=0xf000;};if _agac &0x10!=0{_fgg [_agac ]|=0xf0000;};if _agac &0x20!=0{_fgg [_agac ]|=0xf00000;};if _agac &0x40!=0{_fgg [_agac ]|=0xf000000;};if _agac &0x80!=0{_fgg [_agac ]|=0xf0000000;};};return _fgg ;};func (_ffd *CMYK32 )ColorModel ()_e .Model {return _e .CMYKModel };
func _gcgg (_eeeb _dc .Image ,_ccgd Image ,_ccge _dc .Rectangle ){if _bcdf ,_cbba :=_eeeb .(SMasker );_cbba &&_bcdf .HasAlpha (){_ccgd .(SMasker ).MakeAlpha ();};switch _ebfb :=_eeeb .(type ){case Gray :_bgdfa (_ebfb ,_ccgd .(RGBA ),_ccge );case NRGBA :_ffcg (_ebfb ,_ccgd .(RGBA ),_ccge );
case *_dc .NYCbCrA :_ccae (_ebfb ,_ccgd .(RGBA ),_ccge );case CMYK :_cbbf (_ebfb ,_ccgd .(RGBA ),_ccge );case RGBA :_deff (_ebfb ,_ccgd .(RGBA ),_ccge );case nrgba64 :_afgeg (_ebfb ,_ccgd .(RGBA ),_ccge );default:_ead (_eeeb ,_ccgd ,_ccge );};};func ColorAtNRGBA16 (x ,y ,width ,bytesPerLine int ,data ,alpha []byte ,decode []float64 )(_e .NRGBA ,error ){_fdffe :=y *bytesPerLine +x *3/2;
if _fdffe +1>=len (data ){return _e .NRGBA {},_dccf (x ,y );};const (_gdbbe =0xf;_ebb =uint8 (0xff););_abaa :=_ebb ;if alpha !=nil {_ddce :=y *BytesPerLine (width ,4,1);if _ddce < len (alpha ){if x %2==0{_abaa =(alpha [_ddce ]>>uint (4))&_gdbbe ;}else {_abaa =alpha [_ddce ]&_gdbbe ;
};_abaa |=_abaa <<4;};};var _bbc ,_ege ,_bbag uint8 ;if x *3%2==0{_bbc =(data [_fdffe ]>>uint (4))&_gdbbe ;_ege =data [_fdffe ]&_gdbbe ;_bbag =(data [_fdffe +1]>>uint (4))&_gdbbe ;}else {_bbc =data [_fdffe ]&_gdbbe ;_ege =(data [_fdffe +1]>>uint (4))&_gdbbe ;
_bbag =data [_fdffe +1]&_gdbbe ;};if len (decode )==6{_bbc =uint8 (uint32 (LinearInterpolate (float64 (_bbc ),0,15,decode [0],decode [1]))&0xf);_ege =uint8 (uint32 (LinearInterpolate (float64 (_ege ),0,15,decode [2],decode [3]))&0xf);_bbag =uint8 (uint32 (LinearInterpolate (float64 (_bbag ),0,15,decode [4],decode [5]))&0xf);
};return _e .NRGBA {R :(_bbc <<4)|(_bbc &0xf),G :(_ege <<4)|(_ege &0xf),B :(_bbag <<4)|(_bbag &0xf),A :_abaa },nil ;};func _deff (_afa ,_daaae RGBA ,_bcea _dc .Rectangle ){for _acdb :=0;_acdb < _bcea .Max .X ;_acdb ++{for _ffgac :=0;_ffgac < _bcea .Max .Y ;
_ffgac ++{_daaae .SetRGBA (_acdb ,_ffgac ,_afa .RGBAAt (_acdb ,_ffgac ));};};};func (_fefe *Gray8 )At (x ,y int )_e .Color {_bddf ,_ :=_fefe .ColorAt (x ,y );return _bddf };func _dcbf ()(_ece []byte ){_ece =make ([]byte ,256);for _cag :=0;_cag < 256;_cag ++{_eag :=byte (_cag );
_ece [_eag ]=(_eag &0x01)|((_eag &0x04)>>1)|((_eag &0x10)>>2)|((_eag &0x40)>>3)|((_eag &0x02)<<3)|((_eag &0x08)<<2)|((_eag &0x20)<<1)|(_eag &0x80);};return _ece ;};func (_cfgb *Gray16 )Base ()*ImageBase {return &_cfgb .ImageBase };func (_cbcc *Gray4 )SetGray (x ,y int ,g _e .Gray ){if x >=_cbcc .Width ||y >=_cbcc .Height {return ;
};g =_cffe (g );_cbcc .setGray (x ,y ,g );};func (_fcgc *NRGBA16 )Base ()*ImageBase {return &_fcgc .ImageBase };func _cdfa (){for _aabd :=0;_aabd < 256;_aabd ++{_gege [_aabd ]=uint8 (_aabd &0x1)+(uint8 (_aabd >>1)&0x1)+(uint8 (_aabd >>2)&0x1)+(uint8 (_aabd >>3)&0x1)+(uint8 (_aabd >>4)&0x1)+(uint8 (_aabd >>5)&0x1)+(uint8 (_aabd >>6)&0x1)+(uint8 (_aabd >>7)&0x1);
};};func (_adbd *Gray4 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtGray4BPC (x ,y ,_adbd .BytesPerLine ,_adbd .Data ,_adbd .Decode );};func (_dabg *NRGBA32 )SetNRGBA (x ,y int ,c _e .NRGBA ){_cfga :=y *_dabg .Width +x ;_baaec :=3*_cfga ;if _baaec +2>=len (_dabg .Data ){return ;
};_dabg .setRGBA (_cfga ,c );};func _acaa (_gdeg RGBA ,_aggf CMYK ,_dccd _dc .Rectangle ){for _gfda :=0;_gfda < _dccd .Max .X ;_gfda ++{for _dfe :=0;_dfe < _dccd .Max .Y ;_dfe ++{_acca :=_gdeg .RGBAAt (_gfda ,_dfe );_aggf .SetCMYK (_gfda ,_dfe ,_cbe (_acca ));
};};};func (_fecf *Gray4 )Validate ()error {if len (_fecf .Data )!=_fecf .Height *_fecf .BytesPerLine {return ErrInvalidImage ;};return nil ;};func (_gedd *RGBA32 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_gedd .Width ,Y :_gedd .Height }};
};func (_adgc *Monochrome )SetGray (x ,y int ,g _e .Gray ){_aaga :=y *_adgc .BytesPerLine +x >>3;if _aaga > len (_adgc .Data )-1{return ;};g =_abbd (g ,monochromeModel (_adgc .ModelThreshold ));_adgc .setGray (x ,g ,_aaga );};func (_dcff *Monochrome )ReduceBinary (factor float64 )(*Monochrome ,error ){_bdad :=_ccaac (uint (factor ));
if !IsPowerOf2 (uint (factor )){_bdad ++;};_dceg :=make ([]int ,_bdad );for _caea :=range _dceg {_dceg [_caea ]=4;};_daga ,_bage :=_dbe (_dcff ,_dceg ...);if _bage !=nil {return nil ,_bage ;};return _daga ,nil ;};func (_fccb *Gray2 )Validate ()error {if len (_fccb .Data )!=_fccb .Height *_fccb .BytesPerLine {return ErrInvalidImage ;
};return nil ;};var ErrInvalidImage =_d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
var _ Image =&RGBA32 {};type NRGBA64 struct{ImageBase };func _gdg (_dedc CMYK ,_fggdb Gray ,_afbec _dc .Rectangle ){for _eecc :=0;_eecc < _afbec .Max .X ;_eecc ++{for _ccbf :=0;_ccbf < _afbec .Max .Y ;_ccbf ++{_cedf :=_daec (_dedc .CMYKAt (_eecc ,_ccbf ));
_fggdb .SetGray (_eecc ,_ccbf ,_cedf );};};};func (_fgadd *NRGBA64 )setNRGBA64 (_ccga int ,_bebg _e .NRGBA64 ,_bdbg int ){_fgadd .Data [_ccga ]=uint8 (_bebg .R >>8);_fgadd .Data [_ccga +1]=uint8 (_bebg .R &0xff);_fgadd .Data [_ccga +2]=uint8 (_bebg .G >>8);
_fgadd .Data [_ccga +3]=uint8 (_bebg .G &0xff);_fgadd .Data [_ccga +4]=uint8 (_bebg .B >>8);_fgadd .Data [_ccga +5]=uint8 (_bebg .B &0xff);if _bdbg +1< len (_fgadd .Alpha ){_fgadd .Alpha [_bdbg ]=uint8 (_bebg .A >>8);_fgadd .Alpha [_bdbg +1]=uint8 (_bebg .A &0xff);
};};type ColorConverter interface{Convert (_gdae _dc .Image )(Image ,error );};var _ RGBA =&RGBA32 {};var _ Image =&NRGBA32 {};func _eeac (_bdaf *Monochrome ,_bbad ,_aeba int ,_bfa ,_gbgfa int ,_geecaa RasterOperator ){var (_dga int ;_cbdf byte ;_bcc ,_bgac int ;
_dbbcf int ;);_ebfg :=_bfa >>3;_baa :=_bfa &7;if _baa > 0{_cbdf =_bdfdg [_baa ];};_dga =_bdaf .BytesPerLine *_aeba +(_bbad >>3);switch _geecaa {case PixClr :for _bcc =0;_bcc < _gbgfa ;_bcc ++{_dbbcf =_dga +_bcc *_bdaf .BytesPerLine ;for _bgac =0;_bgac < _ebfg ;
_bgac ++{_bdaf .Data [_dbbcf ]=0x0;_dbbcf ++;};if _baa > 0{_bdaf .Data [_dbbcf ]=_bbfe (_bdaf .Data [_dbbcf ],0x0,_cbdf );};};case PixSet :for _bcc =0;_bcc < _gbgfa ;_bcc ++{_dbbcf =_dga +_bcc *_bdaf .BytesPerLine ;for _bgac =0;_bgac < _ebfg ;_bgac ++{_bdaf .Data [_dbbcf ]=0xff;
_dbbcf ++;};if _baa > 0{_bdaf .Data [_dbbcf ]=_bbfe (_bdaf .Data [_dbbcf ],0xff,_cbdf );};};case PixNotDst :for _bcc =0;_bcc < _gbgfa ;_bcc ++{_dbbcf =_dga +_bcc *_bdaf .BytesPerLine ;for _bgac =0;_bgac < _ebfg ;_bgac ++{_bdaf .Data [_dbbcf ]=^_bdaf .Data [_dbbcf ];
_dbbcf ++;};if _baa > 0{_bdaf .Data [_dbbcf ]=_bbfe (_bdaf .Data [_dbbcf ],^_bdaf .Data [_dbbcf ],_cbdf );};};};};func _gbd (_beac Gray ,_fgfg NRGBA ,_fded _dc .Rectangle ){for _bcb :=0;_bcb < _fded .Max .X ;_bcb ++{for _gfaca :=0;_gfaca < _fded .Max .Y ;
_gfaca ++{_bge :=_fgc (_fgfg .NRGBAAt (_bcb ,_gfaca ));_beac .SetGray (_bcb ,_gfaca ,_bge );};};};var _ Image =&Gray2 {};func _bgdfa (_dfcc Gray ,_aage RGBA ,_gfcg _dc .Rectangle ){for _ffddb :=0;_ffddb < _gfcg .Max .X ;_ffddb ++{for _bdfa :=0;_bdfa < _gfcg .Max .Y ;
_bdfa ++{_beefd :=_dfcc .GrayAt (_ffddb ,_bdfa );_aage .SetRGBA (_ffddb ,_bdfa ,_acaac (_beefd ));};};};func (_abd *CMYK32 )Set (x ,y int ,c _e .Color ){_ffa :=4*(y *_abd .Width +x );if _ffa +3>=len (_abd .Data ){return ;};_bceg :=_e .CMYKModel .Convert (c ).(_e .CMYK );
_abd .Data [_ffa ]=_bceg .C ;_abd .Data [_ffa +1]=_bceg .M ;_abd .Data [_ffa +2]=_bceg .Y ;_abd .Data [_ffa +3]=_bceg .K ;};func ColorAt (x ,y ,width ,bitsPerColor ,colorComponents ,bytesPerLine int ,data ,alpha []byte ,decode []float64 )(_e .Color ,error ){switch colorComponents {case 1:return ColorAtGrayscale (x ,y ,bitsPerColor ,bytesPerLine ,data ,decode );
case 3:return ColorAtNRGBA (x ,y ,width ,bytesPerLine ,bitsPerColor ,data ,alpha ,decode );case 4:return ColorAtCMYK (x ,y ,width ,data ,decode );default:return nil ,_ag .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0063o\u006c\u006f\u0072\u0020\u0063\u006f\u006dp\u006f\u006e\u0065\u006e\u0074\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0064",colorComponents );
};};func (_dfb *NRGBA16 )Copy ()Image {return &NRGBA16 {ImageBase :_dfb .copy ()}};func _ecb (_eef *Monochrome ,_aca int ,_eba []byte )(_abb *Monochrome ,_aba error ){const _fbf ="\u0072\u0065d\u0075\u0063\u0065R\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079";
if _eef ==nil {return nil ,_d .New ("\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if _aca < 1||_aca > 4{return nil ,_d .New ("\u006c\u0065\u0076\u0065\u006c\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020\u0073e\u0074\u0020\u007b\u0031\u002c\u0032\u002c\u0033\u002c\u0034\u007d");
};if _eef .Height <=1{return nil ,_d .New ("\u0073\u006f\u0075rc\u0065\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020m\u0075s\u0074 \u0062e\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0027\u0032\u0027");};_abb =_cbb (_eef .Width /2,_eef .Height /2);
if _eba ==nil {_eba =_dcbf ();};_bga :=_eega (_eef .BytesPerLine ,2*_abb .BytesPerLine );switch _aca {case 1:_aba =_fge (_eef ,_abb ,_eba ,_bga );case 2:_aba =_fcc (_eef ,_abb ,_eba ,_bga );case 3:_aba =_ccg (_eef ,_abb ,_eba ,_bga );case 4:_aba =_ada (_eef ,_abb ,_eba ,_bga );
};if _aba !=nil {return nil ,_aba ;};return _abb ,nil ;};func (_bgdd *Gray8 )Bounds ()_dc .Rectangle {return _dc .Rectangle {Max :_dc .Point {X :_bgdd .Width ,Y :_bgdd .Height }};};func _bdge (_eecg *_dc .Gray )bool {for _eafb :=0;_eafb < len (_eecg .Pix );
_eafb ++{if !_aadfg (_eecg .Pix [_eafb ]){return false ;};};return true ;};func ImgToGray (i _dc .Image )*_dc .Gray {if _edff ,_aeaf :=i .(*_dc .Gray );_aeaf {return _edff ;};_fcgfe :=i .Bounds ();_gdcaf :=_dc .NewGray (_fcgfe );for _bgaa :=0;_bgaa < _fcgfe .Max .X ;
_bgaa ++{for _ggfb :=0;_ggfb < _fcgfe .Max .Y ;_ggfb ++{_cfge :=i .At (_bgaa ,_ggfb );_gdcaf .Set (_bgaa ,_ggfb ,_cfge );};};return _gdcaf ;};func (_fbaa *NRGBA32 )ColorModel ()_e .Model {return _e .NRGBAModel };func _gfbg (_gfg ,_ecg CMYK ,_bbe _dc .Rectangle ){for _gcbg :=0;
_gcbg < _bbe .Max .X ;_gcbg ++{for _aggd :=0;_aggd < _bbe .Max .Y ;_aggd ++{_ecg .SetCMYK (_gcbg ,_aggd ,_gfg .CMYKAt (_gcbg ,_aggd ));};};};func (_aeag *Gray4 )At (x ,y int )_e .Color {_cefe ,_ :=_aeag .ColorAt (x ,y );return _cefe };func _gbf (_fed _e .CMYK )_e .NRGBA {_geec ,_geea ,_ecbe :=_e .CMYKToRGB (_fed .C ,_fed .M ,_fed .Y ,_fed .K );
return _e .NRGBA {R :_geec ,G :_geea ,B :_ecbe ,A :0xff};};func (_dfdg *Gray4 )Copy ()Image {return &Gray4 {ImageBase :_dfdg .copy ()}};func (_bcf *Gray16 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtGray16BPC (x ,y ,_bcf .BytesPerLine ,_bcf .Data ,_bcf .Decode );
};func _gebg (_agacb Gray ,_dbdaf NRGBA ,_gdda _dc .Rectangle ){for _cgce :=0;_cgce < _gdda .Max .X ;_cgce ++{for _bfce :=0;_bfce < _gdda .Max .Y ;_bfce ++{_ggbe :=_agacb .GrayAt (_cgce ,_bfce );_dbdaf .SetNRGBA (_cgce ,_bfce ,_ddg (_ggbe ));};};};func (_bfcgc *NRGBA16 )SetNRGBA (x ,y int ,c _e .NRGBA ){_eacd :=y *_bfcgc .BytesPerLine +x *3/2;
if _eacd +1>=len (_bfcgc .Data ){return ;};c =_bdgb (c );_bfcgc .setNRGBA (x ,y ,_eacd ,c );};func IsGrayImgBlackAndWhite (i *_dc .Gray )bool {return _bdge (i )};func ColorAtRGBA32 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_e .RGBA ,error ){_gfgag :=y *width +x ;
_eddb :=3*_gfgag ;if _eddb +2>=len (data ){return _e .RGBA {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_deec :=uint8 (0xff);if alpha !=nil &&len (alpha )> _gfgag {_deec =alpha [_gfgag ];};_gdfee ,_gfgad ,_ddgg :=data [_eddb ],data [_eddb +1],data [_eddb +2];if len (decode )==6{_gdfee =uint8 (uint32 (LinearInterpolate (float64 (_gdfee ),0,255,decode [0],decode [1]))&0xff);
_gfgad =uint8 (uint32 (LinearInterpolate (float64 (_gfgad ),0,255,decode [2],decode [3]))&0xff);_ddgg =uint8 (uint32 (LinearInterpolate (float64 (_ddgg ),0,255,decode [4],decode [5]))&0xff);};return _e .RGBA {R :_gdfee ,G :_gfgad ,B :_ddgg ,A :_deec },nil ;
};type NRGBA16 struct{ImageBase };func (_afbe *Gray4 )Set (x ,y int ,c _e .Color ){if x >=_afbe .Width ||y >=_afbe .Height {return ;};_dgd :=Gray4Model .Convert (c ).(_e .Gray );_afbe .setGray (x ,y ,_dgd );};func (_dcaf *NRGBA32 )At (x ,y int )_e .Color {_fegg ,_ :=_dcaf .ColorAt (x ,y );
return _fegg };func (_aegcd *Monochrome )AddPadding ()(_cbaf error ){if _gfga :=((_aegcd .Width *_aegcd .Height )+7)>>3;len (_aegcd .Data )< _gfga {return _ag .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064a\u0074\u0061\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002e\u0020\u0054\u0068\u0065\u0020\u0064\u0061t\u0061\u0020s\u0068\u006fu\u006c\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0074 l\u0065\u0061\u0073\u0074\u003a\u0020\u0027\u0025\u0064'\u0020\u0062\u0079\u0074\u0065\u0073",len (_aegcd .Data ),_gfga );
};_ecaa :=_aegcd .Width %8;if _ecaa ==0{return nil ;};_cea :=_aegcd .Width /8;_ggc :=_dg .NewReader (_aegcd .Data );_cfebd :=make ([]byte ,_aegcd .Height *_aegcd .BytesPerLine );_gcbde :=_dg .NewWriterMSB (_cfebd );_ggb :=make ([]byte ,_cea );var (_def int ;
_ddgc uint64 ;);for _def =0;_def < _aegcd .Height ;_def ++{if _ ,_cbaf =_ggc .Read (_ggb );_cbaf !=nil {return _cbaf ;};if _ ,_cbaf =_gcbde .Write (_ggb );_cbaf !=nil {return _cbaf ;};if _ddgc ,_cbaf =_ggc .ReadBits (byte (_ecaa ));_cbaf !=nil {return _cbaf ;
};if _cbaf =_gcbde .WriteByte (byte (_ddgc )<<uint (8-_ecaa ));_cbaf !=nil {return _cbaf ;};};_aegcd .Data =_gcbde .Data ();return nil ;};func (_dbeg *Gray2 )ColorAt (x ,y int )(_e .Color ,error ){return ColorAtGray2BPC (x ,y ,_dbeg .BytesPerLine ,_dbeg .Data ,_dbeg .Decode );
};func (_gfcd *NRGBA16 )NRGBAAt (x ,y int )_e .NRGBA {_aebcg ,_ :=ColorAtNRGBA16 (x ,y ,_gfcd .Width ,_gfcd .BytesPerLine ,_gfcd .Data ,_gfcd .Alpha ,_gfcd .Decode );return _aebcg ;};func (_gab *Monochrome )IsUnpadded ()bool {return (_gab .Width *_gab .Height )==len (_gab .Data )};
func LinearInterpolate (x ,xmin ,xmax ,ymin ,ymax float64 )float64 {if _a .Abs (xmax -xmin )< 0.000001{return ymin ;};_bdeg :=ymin +(x -xmin )*(ymax -ymin )/(xmax -xmin );return _bdeg ;};type colorConverter struct{_dccb func (_fad _dc .Image )(Image ,error );
};func GrayHistogram (g Gray )(_fgcf [256]int ){switch _bdgg :=g .(type ){case Histogramer :return _bdgg .Histogram ();case _dc .Image :_dgac :=_bdgg .Bounds ();for _bbgg :=0;_bbgg < _dgac .Max .X ;_bbgg ++{for _ggfd :=0;_ggfd < _dgac .Max .Y ;_ggfd ++{_fgcf [g .GrayAt (_bbgg ,_ggfd ).Y ]++;
};};return _fgcf ;default:return [256]int {};};};func _acbb (_beagf ,_dfce uint8 )uint8 {if _beagf < _dfce {return 255;};return 0;};func (_dee *NRGBA16 )At (x ,y int )_e .Color {_dcae ,_ :=_dee .ColorAt (x ,y );return _dcae };type nrgba64 interface{NRGBA64At (_cbff ,_bdcbb int )_e .NRGBA64 ;
SetNRGBA64 (_gede ,_afcd int ,_baea _e .NRGBA64 );};func _daaf (_faee *_dc .NYCbCrA ,_bbde NRGBA ,_cdg _dc .Rectangle ){for _bbefd :=0;_bbefd < _cdg .Max .X ;_bbefd ++{for _dbee :=0;_dbee < _cdg .Max .Y ;_dbee ++{_edgd :=_faee .NYCbCrAAt (_bbefd ,_dbee );
_bbde .SetNRGBA (_bbefd ,_dbee ,_gagd (_edgd ));};};};func _bb (_g *Monochrome ,_ee int )(*Monochrome ,error ){if _g ==nil {return nil ,_d .New ("\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");};if _ee ==1{return _g .copy (),nil ;
};if !IsPowerOf2 (uint (_ee )){return nil ,_ag .Errorf ("\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006ci\u0064 \u0065x\u0070a\u006e\u0064\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020\u0025\u0064",_ee );};_ea :=_aeb (_ee );
return _cd (_g ,_ee ,_ea );};func (_bdaa *Monochrome )copy ()*Monochrome {_fbda :=_cbb (_bdaa .Width ,_bdaa .Height );_fbda .ModelThreshold =_bdaa .ModelThreshold ;_fbda .Data =make ([]byte ,len (_bdaa .Data ));copy (_fbda .Data ,_bdaa .Data );if len (_bdaa .Decode )!=0{_fbda .Decode =make ([]float64 ,len (_bdaa .Decode ));
copy (_fbda .Decode ,_bdaa .Decode );};if len (_bdaa .Alpha )!=0{_fbda .Alpha =make ([]byte ,len (_bdaa .Alpha ));copy (_fbda .Alpha ,_bdaa .Alpha );};return _fbda ;};var _ Image =&Gray8 {};func (_eabb *Monochrome )ExpandBinary (factor int )(*Monochrome ,error ){if !IsPowerOf2 (uint (factor )){return nil ,_ag .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0065\u0078\u0070\u0061\u006e\u0064\u0020b\u0069n\u0061\u0072\u0079\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020\u0025\u0064",factor );
};return _bb (_eabb ,factor );};func _bedc (_cadea CMYK ,_bbaf NRGBA ,_dfef _dc .Rectangle ){for _fdgf :=0;_fdgf < _dfef .Max .X ;_fdgf ++{for _gdaa :=0;_gdaa < _dfef .Max .Y ;_gdaa ++{_fdab :=_cadea .CMYKAt (_fdgf ,_gdaa );_bbaf .SetNRGBA (_fdgf ,_gdaa ,_gbf (_fdab ));
};};};func _efc (_ddbbg _e .RGBA )_e .NRGBA {switch _ddbbg .A {case 0xff:return _e .NRGBA {R :_ddbbg .R ,G :_ddbbg .G ,B :_ddbbg .B ,A :0xff};case 0x00:return _e .NRGBA {};default:_cbdg ,_ecad ,_cade ,_aag :=_ddbbg .RGBA ();_cbdg =(_cbdg *0xffff)/_aag ;
_ecad =(_ecad *0xffff)/_aag ;_cade =(_cade *0xffff)/_aag ;return _e .NRGBA {R :uint8 (_cbdg >>8),G :uint8 (_ecad >>8),B :uint8 (_cade >>8),A :uint8 (_aag >>8)};};};type shift int ;func _ffaa (_gcea []byte ,_fafaa Image )error {_agfd :=true ;for _bgeb :=0;
_bgeb < len (_gcea );_bgeb ++{if _gcea [_bgeb ]!=0xff{_agfd =false ;break ;};};if _agfd {switch _caedd :=_fafaa .(type ){case *NRGBA32 :_caedd .Alpha =nil ;case *NRGBA64 :_caedd .Alpha =nil ;default:return _ag .Errorf ("i\u006ete\u0072n\u0061l\u0020\u0065\u0072\u0072\u006fr\u0020\u002d\u0020i\u006d\u0061\u0067\u0065\u0020s\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u006f\u0066\u0020\u0074\u0079\u0070e\u0020\u002a\u004eRGB\u0041\u0033\u0032\u0020\u006f\u0072 \u002a\u004e\u0052\u0047\u0042\u0041\u0036\u0034\u0020\u0062\u0075\u0074 \u0069s\u003a\u0020\u0025\u0054",_fafaa );
};};return nil ;};var _ Image =&Gray4 {};func _bdab (_cgc _dc .Image ,_cgcd Image ,_fbdg _dc .Rectangle ){switch _aaad :=_cgc .(type ){case Gray :_gdee (_aaad ,_cgcd .(Gray ),_fbdg );case NRGBA :_dgbd (_aaad ,_cgcd .(Gray ),_fbdg );case CMYK :_gdg (_aaad ,_cgcd .(Gray ),_fbdg );
case RGBA :_gbff (_aaad ,_cgcd .(Gray ),_fbdg );default:_ead (_cgc ,_cgcd ,_fbdg );};};func _cffe (_dbg _e .Gray )_e .Gray {_dbg .Y >>=4;_dbg .Y |=_dbg .Y <<4;return _dbg };func ColorAtCMYK (x ,y ,width int ,data []byte ,decode []float64 )(_e .CMYK ,error ){_acd :=4*(y *width +x );
if _acd +3>=len (data ){return _e .CMYK {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};C :=data [_acd ]&0xff;M :=data [_acd +1]&0xff;Y :=data [_acd +2]&0xff;K :=data [_acd +3]&0xff;if len (decode )==8{C =uint8 (uint32 (LinearInterpolate (float64 (C ),0,255,decode [0],decode [1]))&0xff);M =uint8 (uint32 (LinearInterpolate (float64 (M ),0,255,decode [2],decode [3]))&0xff);
Y =uint8 (uint32 (LinearInterpolate (float64 (Y ),0,255,decode [4],decode [5]))&0xff);K =uint8 (uint32 (LinearInterpolate (float64 (K ),0,255,decode [6],decode [7]))&0xff);};return _e .CMYK {C :C ,M :M ,Y :Y ,K :K },nil ;};func (_cbdc *NRGBA64 )Base ()*ImageBase {return &_cbdc .ImageBase };
func _ead (_dedg _dc .Image ,_aab Image ,_bgbb _dc .Rectangle ){for _cbcb :=0;_cbcb < _bgbb .Max .X ;_cbcb ++{for _cdff :=0;_cdff < _bgbb .Max .Y ;_cdff ++{_dea :=_dedg .At (_cbcb ,_cdff );_aab .Set (_cbcb ,_cdff ,_dea );};};};func (_fef colorConverter )Convert (src _dc .Image )(Image ,error ){return _fef ._dccb (src )};
func _dadg (_adbc *Monochrome ,_bcfab ,_gfbf ,_dbfd ,_fgfb int ,_bege RasterOperator ,_dbbc *Monochrome ,_gbcf ,_fcef int )error {if _adbc ==nil {return _d .New ("\u006e\u0069\u006c\u0020\u0027\u0064\u0065\u0073\u0074\u0027\u0020\u0042i\u0074\u006d\u0061\u0070");
};if _bege ==PixDst {return nil ;};switch _bege {case PixClr ,PixSet ,PixNotDst :_ggcf (_adbc ,_bcfab ,_gfbf ,_dbfd ,_fgfb ,_bege );return nil ;};if _dbbc ==nil {_bc .Log .Debug ("\u0052a\u0073\u0074e\u0072\u004f\u0070\u0065r\u0061\u0074\u0069o\u006e\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020bi\u0074\u006d\u0061p\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0064e\u0066\u0069n\u0065\u0064");
return _d .New ("\u006e\u0069l\u0020\u0027\u0073r\u0063\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _ccdc :=_bac (_adbc ,_bcfab ,_gfbf ,_dbfd ,_fgfb ,_bege ,_dbbc ,_gbcf ,_fcef );_ccdc !=nil {return _ccdc ;};return nil ;};func NewImage (width ,height ,bitsPerComponent ,colorComponents int ,data ,alpha []byte ,decode []float64 )(Image ,error ){_dfec :=NewImageBase (width ,height ,bitsPerComponent ,colorComponents ,data ,alpha ,decode );
var _gbbf Image ;switch colorComponents {case 1:switch bitsPerComponent {case 1:_gbbf =&Monochrome {ImageBase :_dfec ,ModelThreshold :0x0f};case 2:_gbbf =&Gray2 {ImageBase :_dfec };case 4:_gbbf =&Gray4 {ImageBase :_dfec };case 8:_gbbf =&Gray8 {ImageBase :_dfec };
case 16:_gbbf =&Gray16 {ImageBase :_dfec };};case 3:switch bitsPerComponent {case 4:_gbbf =&NRGBA16 {ImageBase :_dfec };case 8:_gbbf =&NRGBA32 {ImageBase :_dfec };case 16:_gbbf =&NRGBA64 {ImageBase :_dfec };};case 4:_gbbf =&CMYK32 {ImageBase :_dfec };};
if _gbbf ==nil {return nil ,ErrInvalidImage ;};return _gbbf ,nil ;};func (_agcd *RGBA32 )Copy ()Image {return &RGBA32 {ImageBase :_agcd .copy ()}};func _cbe (_gfc _e .RGBA )_e .CMYK {_cba ,_cffc ,_bbb ,_eeae :=_e .RGBToCMYK (_gfc .R ,_gfc .G ,_gfc .B );
return _e .CMYK {C :_cba ,M :_cffc ,Y :_bbb ,K :_eeae };};func (_gaf *Gray8 )Set (x ,y int ,c _e .Color ){_dge :=y *_gaf .BytesPerLine +x ;if _dge > len (_gaf .Data )-1{return ;};_eebe :=_e .GrayModel .Convert (c );_gaf .Data [_dge ]=_eebe .(_e .Gray ).Y ;
};var _ _dc .Image =&RGBA32 {};func ColorAtGray4BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_e .Gray ,error ){_fecg :=y *bytesPerLine +x >>1;if _fecg >=len (data ){return _e .Gray {},_ag .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_cde :=data [_fecg ]>>uint (4-(x &1)*4)&0xf;if len (decode )==2{_cde =uint8 (uint32 (LinearInterpolate (float64 (_cde ),0,15,decode [0],decode [1]))&0xf);};return _e .Gray {Y :_cde *17&0xff},nil ;};func (_fege *Gray16 )Copy ()Image {return &Gray16 {ImageBase :_fege .copy ()}};
func (_agagb *RGBA32 )Validate ()error {if len (_agagb .Data )!=3*_agagb .Width *_agagb .Height {return _d .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _afgeg (_gaba nrgba64 ,_dafg RGBA ,_cfac _dc .Rectangle ){for _bgebd :=0;_bgebd < _cfac .Max .X ;_bgebd ++{for _beag :=0;_beag < _cfac .Max .Y ;_beag ++{_gbab :=_gaba .NRGBA64At (_bgebd ,_beag );_dafg .SetRGBA (_bgebd ,_beag ,_fdcc (_gbab ));
};};};func (_dddg *ImageBase )setByte (_aceeb int ,_defa byte )error {if _aceeb > len (_dddg .Data )-1{return _d .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_dddg .Data [_aceeb ]=_defa ;return nil ;
};func _eega (_eadc int ,_efce int )int {if _eadc < _efce {return _eadc ;};return _efce ;};func (_ffcf *Gray16 )Set (x ,y int ,c _e .Color ){_feb :=(y *_ffcf .BytesPerLine /2+x )*2;if _feb +1>=len (_ffcf .Data ){return ;};_acde :=_e .Gray16Model .Convert (c ).(_e .Gray16 );
_ffcf .Data [_feb ],_ffcf .Data [_feb +1]=uint8 (_acde .Y >>8),uint8 (_acde .Y &0xff);};var _ _dc .Image =&NRGBA32 {};func _daec (_gcd _e .CMYK )_e .Gray {_fcag ,_dfcg ,_bgdg :=_e .CMYKToRGB (_gcd .C ,_gcd .M ,_gcd .Y ,_gcd .K );_acf :=(19595*uint32 (_fcag )+38470*uint32 (_dfcg )+7471*uint32 (_bgdg )+1<<7)>>16;
return _e .Gray {Y :uint8 (_acf )};};func (_gbee monochromeModel )Convert (c _e .Color )_e .Color {_eebd :=_e .GrayModel .Convert (c ).(_e .Gray );return _abbd (_eebd ,_gbee );};var (MonochromeConverter =ConverterFunc (_fbc );Gray2Converter =ConverterFunc (_aeaed );
Gray4Converter =ConverterFunc (_dfaf );GrayConverter =ConverterFunc (_agda );Gray16Converter =ConverterFunc (_feba );NRGBA16Converter =ConverterFunc (_bcgc );NRGBAConverter =ConverterFunc (_adef );NRGBA64Converter =ConverterFunc (_eacf );RGBAConverter =ConverterFunc (_fdaed );
CMYKConverter =ConverterFunc (_bed ););func (_fbge *NRGBA64 )ColorModel ()_e .Model {return _e .NRGBA64Model };func _cfega (_bfdg RGBA ,_bgeg NRGBA ,_bgebb _dc .Rectangle ){for _bgdc :=0;_bgdc < _bgebb .Max .X ;_bgdc ++{for _bcga :=0;_bcga < _bgebb .Max .Y ;
_bcga ++{_fggc :=_bfdg .RGBAAt (_bgdc ,_bcga );_bgeg .SetNRGBA (_bgdc ,_bcga ,_efc (_fggc ));};};};func _gccg (_egfa _e .NYCbCrA )_e .RGBA {_dbcc ,_bef ,_decf ,_ccd :=_gagd (_egfa ).RGBA ();return _e .RGBA {R :uint8 (_dbcc >>8),G :uint8 (_bef >>8),B :uint8 (_decf >>8),A :uint8 (_ccd >>8)};
};func (_gdbgf *NRGBA64 )NRGBA64At (x ,y int )_e .NRGBA64 {_bdaee ,_ :=ColorAtNRGBA64 (x ,y ,_gdbgf .Width ,_gdbgf .Data ,_gdbgf .Alpha ,_gdbgf .Decode );return _bdaee ;};func (_dagac *Monochrome )setBit (_gbed ,_gedg int ){_dagac .Data [_gbed +(_gedg >>3)]|=0x80>>uint (_gedg &7);
};func _aadfg (_beaec uint8 )bool {if _beaec ==0||_beaec ==255{return true ;};return false ;};func (_daff *Gray2 )SetGray (x ,y int ,gray _e .Gray ){_deaa :=_bgg (gray );_abfe :=y *_daff .BytesPerLine ;_aee :=_abfe +(x >>2);if _aee >=len (_daff .Data ){return ;
};_gbfe :=_deaa .Y >>6;_daff .Data [_aee ]=(_daff .Data [_aee ]&(^(0xc0>>uint (2*((x )&3)))))|(_gbfe <<uint (6-2*(x &3)));};func _eacf (_babd _dc .Image )(Image ,error ){if _edfgf ,_feda :=_babd .(*NRGBA64 );_feda {return _edfgf .Copy (),nil ;};_gada ,_bgbe ,_adgea :=_fbbcd (_babd ,2);
_gcg ,_gbcg :=NewImage (_gada .Max .X ,_gada .Max .Y ,16,3,nil ,_adgea ,nil );if _gbcg !=nil {return nil ,_gbcg ;};_cece (_babd ,_gcg ,_gada );if len (_adgea )!=0&&!_bgbe {if _cebb :=_ffaa (_adgea ,_gcg );_cebb !=nil {return nil ,_cebb ;};};return _gcg ,nil ;
};var (_cc =_fee ();_bcd =_cb ();_aea =_aed (););func _bdgb (_ccfb _e .NRGBA )_e .NRGBA {_ccfb .R =_ccfb .R >>4|(_ccfb .R >>4)<<4;_ccfb .G =_ccfb .G >>4|(_ccfb .G >>4)<<4;_ccfb .B =_ccfb .B >>4|(_ccfb .B >>4)<<4;return _ccfb ;};func (_fede *Gray4 )GrayAt (x ,y int )_e .Gray {_bfd ,_ :=ColorAtGray4BPC (x ,y ,_fede .BytesPerLine ,_fede .Data ,_fede .Decode );
return _bfd ;};func _acgg (_fbbb _dc .Image ,_dfdeab Image ,_afeb _dc .Rectangle ){if _geae ,_eefg :=_fbbb .(SMasker );_eefg &&_geae .HasAlpha (){_dfdeab .(SMasker ).MakeAlpha ();};switch _egeb :=_fbbb .(type ){case Gray :_gebg (_egeb ,_dfdeab .(NRGBA ),_afeb );
case NRGBA :_fedae (_egeb ,_dfdeab .(NRGBA ),_afeb );case *_dc .NYCbCrA :_daaf (_egeb ,_dfdeab .(NRGBA ),_afeb );case CMYK :_bedc (_egeb ,_dfdeab .(NRGBA ),_afeb );case RGBA :_cfega (_egeb ,_dfdeab .(NRGBA ),_afeb );case nrgba64 :_feccb (_egeb ,_dfdeab .(NRGBA ),_afeb );
default:_ead (_fbbb ,_dfdeab ,_afeb );};};func (_geb *Gray2 )Copy ()Image {return &Gray2 {ImageBase :_geb .copy ()}};