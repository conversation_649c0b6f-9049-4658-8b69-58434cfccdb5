//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package sampling ;import (_af "github.com/unidoc/unipdf/v4/internal/bitwise";_ab "github.com/unidoc/unipdf/v4/internal/imageutil";_a "io";);func (_ed *Reader )ReadSample ()(uint32 ,error ){if _ed ._fb ==_ed ._d .Height {return 0,_a .EOF ;};_cb ,_bff :=_ed ._b .ReadBits (byte (_ed ._d .BitsPerComponent ));
if _bff !=nil {return 0,_bff ;};_ed ._c --;if _ed ._c ==0{_ed ._c =_ed ._d .ColorComponents ;_ed ._f ++;};if _ed ._f ==_ed ._d .Width {if _ed ._bf {_ed ._b .ConsumeRemainingBits ();};_ed ._f =0;_ed ._fb ++;};return uint32 (_cb ),nil ;};func (_fec *Writer )WriteSample (sample uint32 )error {if _ ,_afd :=_fec ._cea .WriteBits (uint64 (sample ),_fec ._dcb .BitsPerComponent );
_afd !=nil {return _afd ;};_fec ._gb --;if _fec ._gb ==0{_fec ._gb =_fec ._dcb .ColorComponents ;_fec ._eec ++;};if _fec ._eec ==_fec ._dcb .Width {if _fec ._cgd {_fec ._cea .FinishByte ();};_fec ._eec =0;};return nil ;};func ResampleUint32 (data []uint32 ,bitsPerInputSample int ,bitsPerOutputSample int )[]uint32 {var _da []uint32 ;
_ee :=bitsPerOutputSample ;var _bb uint32 ;var _ae uint32 ;_cbe :=0;_fdd :=0;_abb :=0;for _abb < len (data ){if _cbe > 0{_bbd :=_cbe ;if _ee < _bbd {_bbd =_ee ;};_bb =(_bb <<uint (_bbd ))|(_ae >>uint (bitsPerInputSample -_bbd ));_cbe -=_bbd ;if _cbe > 0{_ae =_ae <<uint (_bbd );
}else {_ae =0;};_ee -=_bbd ;if _ee ==0{_da =append (_da ,_bb );_ee =bitsPerOutputSample ;_bb =0;_fdd ++;};}else {_cf :=data [_abb ];_abb ++;_cfd :=bitsPerInputSample ;if _ee < _cfd {_cfd =_ee ;};_cbe =bitsPerInputSample -_cfd ;_bb =(_bb <<uint (_cfd ))|(_cf >>uint (_cbe ));
if _cfd < bitsPerInputSample {_ae =_cf <<uint (_cfd );};_ee -=_cfd ;if _ee ==0{_da =append (_da ,_bb );_ee =bitsPerOutputSample ;_bb =0;_fdd ++;};};};for _cbe >=bitsPerOutputSample {_efb :=_cbe ;if _ee < _efb {_efb =_ee ;};_bb =(_bb <<uint (_efb ))|(_ae >>uint (bitsPerInputSample -_efb ));
_cbe -=_efb ;if _cbe > 0{_ae =_ae <<uint (_efb );}else {_ae =0;};_ee -=_efb ;if _ee ==0{_da =append (_da ,_bb );_ee =bitsPerOutputSample ;_bb =0;_fdd ++;};};if _ee > 0&&_ee < bitsPerOutputSample {_bb <<=uint (_ee );_da =append (_da ,_bb );};return _da ;
};type Reader struct{_d _ab .ImageBase ;_b *_af .Reader ;_f ,_fb ,_c int ;_bf bool ;};type Writer struct{_dcb _ab .ImageBase ;_cea *_af .Writer ;_eec ,_gb int ;_cgd bool ;};type SampleReader interface{ReadSample ()(uint32 ,error );ReadSamples (_e []uint32 )error ;
};func ResampleBytes (data []byte ,bitsPerSample int )[]uint32 {var _fe []uint32 ;_cc :=bitsPerSample ;var _ac uint32 ;var _acd byte ;_ce :=0;_dc :=0;_ag :=0;for _ag < len (data ){if _ce > 0{_abd :=_ce ;if _cc < _abd {_abd =_cc ;};_ac =(_ac <<uint (_abd ))|uint32 (_acd >>uint (8-_abd ));
_ce -=_abd ;if _ce > 0{_acd =_acd <<uint (_abd );}else {_acd =0;};_cc -=_abd ;if _cc ==0{_fe =append (_fe ,_ac );_cc =bitsPerSample ;_ac =0;_dc ++;};}else {_gf :=data [_ag ];_ag ++;_fd :=8;if _cc < _fd {_fd =_cc ;};_ce =8-_fd ;_ac =(_ac <<uint (_fd ))|uint32 (_gf >>uint (_ce ));
if _fd < 8{_acd =_gf <<uint (_fd );};_cc -=_fd ;if _cc ==0{_fe =append (_fe ,_ac );_cc =bitsPerSample ;_ac =0;_dc ++;};};};for _ce >=bitsPerSample {_de :=_ce ;if _cc < _de {_de =_cc ;};_ac =(_ac <<uint (_de ))|uint32 (_acd >>uint (8-_de ));_ce -=_de ;if _ce > 0{_acd =_acd <<uint (_de );
}else {_acd =0;};_cc -=_de ;if _cc ==0{_fe =append (_fe ,_ac );_cc =bitsPerSample ;_ac =0;_dc ++;};};return _fe ;};type SampleWriter interface{WriteSample (_ec uint32 )error ;WriteSamples (_cg []uint32 )error ;};func NewWriter (img _ab .ImageBase )*Writer {return &Writer {_cea :_af .NewWriterMSB (img .Data ),_dcb :img ,_gb :img .ColorComponents ,_cgd :img .BytesPerLine *8!=img .ColorComponents *img .BitsPerComponent *img .Width };
};func NewReader (img _ab .ImageBase )*Reader {return &Reader {_b :_af .NewReader (img .Data ),_d :img ,_c :img .ColorComponents ,_bf :img .BytesPerLine *8!=img .ColorComponents *img .BitsPerComponent *img .Width };};func (_df *Reader )ReadSamples (samples []uint32 )(_cbg error ){for _ef :=0;
_ef < len (samples );_ef ++{samples [_ef ],_cbg =_df .ReadSample ();if _cbg !=nil {return _cbg ;};};return nil ;};func (_aca *Writer )WriteSamples (samples []uint32 )error {for _bbdb :=0;_bbdb < len (samples );_bbdb ++{if _ad :=_aca .WriteSample (samples [_bbdb ]);
_ad !=nil {return _ad ;};};return nil ;};