//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package graphic2d ;import (_b "image/color";_a "math";);func EllipseToCubicBeziers (startX ,startY ,rx ,ry ,rot float64 ,large ,sweep bool ,endX ,endY float64 )[][4]Point {rx =_a .Abs (rx );ry =_a .Abs (ry );if rx < ry {rx ,ry =ry ,rx ;rot +=90.0;};_ce :=_dag (rot *_a .Pi /180.0);
if _a .Pi <=_ce {_ce -=_a .Pi ;};_f ,_e ,_g ,_bb :=_edc (startX ,startY ,rx ,ry ,_ce ,large ,sweep ,endX ,endY );_cef :=_a .Pi /2.0;_ee :=int (_a .Ceil (_a .Abs (_bb -_g )/_cef ));_cef =_a .Abs (_bb -_g )/float64 (_ee );_gb :=_a .Sin (_cef )*(_a .Sqrt (4.0+3.0*_a .Pow (_a .Tan (_cef /2.0),2.0))-1.0)/3.0;
if !sweep {_cef =-_cef ;};_ga :=Point {X :startX ,Y :startY };_af ,_fg :=_eef (rx ,ry ,_ce ,sweep ,_g );_d :=Point {X :_af ,Y :_fg };_fgc :=[][4]Point {};for _ab :=1;_ab < _ee +1;_ab ++{_eb :=_g +float64 (_ab )*_cef ;_dd ,_ac :=_cb (rx ,ry ,_ce ,_f ,_e ,_eb );
_bc :=Point {X :_dd ,Y :_ac };_eg ,_ddg :=_eef (rx ,ry ,_ce ,sweep ,_eb );_gf :=Point {X :_eg ,Y :_ddg };_cf :=_ga .Add (_d .Mul (_gb ));_gd :=_bc .Sub (_gf .Mul (_gb ));_fgc =append (_fgc ,[4]Point {_ga ,_cf ,_gd ,_bc });_d =_gf ;_ga =_bc ;};return _fgc ;
};func _eef (_fdg ,_abf ,_fea float64 ,_afg bool ,_cbf float64 )(float64 ,float64 ){_ecc ,_gde :=_a .Sincos (_cbf );_gad ,_eec :=_a .Sincos (_fea );_eba :=-_fdg *_ecc *_eec -_abf *_gde *_gad ;_gdg :=-_fdg *_ecc *_gad +_abf *_gde *_eec ;if !_afg {return -_eba ,-_gdg ;
};return _eba ,_gdg ;};func (_ddge Point )Add (q Point )Point {return Point {_ddge .X +q .X ,_ddge .Y +q .Y }};var ColorMap =map[string ]_b .RGBA {"\u0061l\u0069\u0063\u0065\u0062\u006c\u0075e":_b .RGBA {0xf0,0xf8,0xff,0xff},"\u0061\u006e\u0074i\u0071\u0075\u0065\u0077\u0068\u0069\u0074\u0065":_b .RGBA {0xfa,0xeb,0xd7,0xff},"\u0061\u0071\u0075\u0061":_b .RGBA {0x00,0xff,0xff,0xff},"\u0061\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065":_b .RGBA {0x7f,0xff,0xd4,0xff},"\u0061\u007a\u0075r\u0065":_b .RGBA {0xf0,0xff,0xff,0xff},"\u0062\u0065\u0069g\u0065":_b .RGBA {0xf5,0xf5,0xdc,0xff},"\u0062\u0069\u0073\u0071\u0075\u0065":_b .RGBA {0xff,0xe4,0xc4,0xff},"\u0062\u006c\u0061c\u006b":_b .RGBA {0x00,0x00,0x00,0xff},"\u0062\u006c\u0061\u006e\u0063\u0068\u0065\u0064\u0061l\u006d\u006f\u006e\u0064":_b .RGBA {0xff,0xeb,0xcd,0xff},"\u0062\u006c\u0075\u0065":_b .RGBA {0x00,0x00,0xff,0xff},"\u0062\u006c\u0075\u0065\u0076\u0069\u006f\u006c\u0065\u0074":_b .RGBA {0x8a,0x2b,0xe2,0xff},"\u0062\u0072\u006fw\u006e":_b .RGBA {0xa5,0x2a,0x2a,0xff},"\u0062u\u0072\u006c\u0079\u0077\u006f\u006fd":_b .RGBA {0xde,0xb8,0x87,0xff},"\u0063a\u0064\u0065\u0074\u0062\u006c\u0075e":_b .RGBA {0x5f,0x9e,0xa0,0xff},"\u0063\u0068\u0061\u0072\u0074\u0072\u0065\u0075\u0073\u0065":_b .RGBA {0x7f,0xff,0x00,0xff},"\u0063h\u006f\u0063\u006f\u006c\u0061\u0074e":_b .RGBA {0xd2,0x69,0x1e,0xff},"\u0063\u006f\u0072a\u006c":_b .RGBA {0xff,0x7f,0x50,0xff},"\u0063\u006f\u0072\u006e\u0066\u006c\u006f\u0077\u0065r\u0062\u006c\u0075\u0065":_b .RGBA {0x64,0x95,0xed,0xff},"\u0063\u006f\u0072\u006e\u0073\u0069\u006c\u006b":_b .RGBA {0xff,0xf8,0xdc,0xff},"\u0063r\u0069\u006d\u0073\u006f\u006e":_b .RGBA {0xdc,0x14,0x3c,0xff},"\u0063\u0079\u0061\u006e":_b .RGBA {0x00,0xff,0xff,0xff},"\u0064\u0061\u0072\u006b\u0062\u006c\u0075\u0065":_b .RGBA {0x00,0x00,0x8b,0xff},"\u0064\u0061\u0072\u006b\u0063\u0079\u0061\u006e":_b .RGBA {0x00,0x8b,0x8b,0xff},"\u0064\u0061\u0072\u006b\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064":_b .RGBA {0xb8,0x86,0x0b,0xff},"\u0064\u0061\u0072\u006b\u0067\u0072\u0061\u0079":_b .RGBA {0xa9,0xa9,0xa9,0xff},"\u0064a\u0072\u006b\u0067\u0072\u0065\u0065n":_b .RGBA {0x00,0x64,0x00,0xff},"\u0064\u0061\u0072\u006b\u0067\u0072\u0065\u0079":_b .RGBA {0xa9,0xa9,0xa9,0xff},"\u0064a\u0072\u006b\u006b\u0068\u0061\u006bi":_b .RGBA {0xbd,0xb7,0x6b,0xff},"d\u0061\u0072\u006b\u006d\u0061\u0067\u0065\u006e\u0074\u0061":_b .RGBA {0x8b,0x00,0x8b,0xff},"\u0064\u0061\u0072\u006b\u006f\u006c\u0069\u0076\u0065g\u0072\u0065\u0065\u006e":_b .RGBA {0x55,0x6b,0x2f,0xff},"\u0064\u0061\u0072\u006b\u006f\u0072\u0061\u006e\u0067\u0065":_b .RGBA {0xff,0x8c,0x00,0xff},"\u0064\u0061\u0072\u006b\u006f\u0072\u0063\u0068\u0069\u0064":_b .RGBA {0x99,0x32,0xcc,0xff},"\u0064a\u0072\u006b\u0072\u0065\u0064":_b .RGBA {0x8b,0x00,0x00,0xff},"\u0064\u0061\u0072\u006b\u0073\u0061\u006c\u006d\u006f\u006e":_b .RGBA {0xe9,0x96,0x7a,0xff},"\u0064\u0061\u0072k\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x8f,0xbc,0x8f,0xff},"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065":_b .RGBA {0x48,0x3d,0x8b,0xff},"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0061\u0079":_b .RGBA {0x2f,0x4f,0x4f,0xff},"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0065\u0079":_b .RGBA {0x2f,0x4f,0x4f,0xff},"\u0064\u0061\u0072\u006b\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":_b .RGBA {0x00,0xce,0xd1,0xff},"\u0064\u0061\u0072\u006b\u0076\u0069\u006f\u006c\u0065\u0074":_b .RGBA {0x94,0x00,0xd3,0xff},"\u0064\u0065\u0065\u0070\u0070\u0069\u006e\u006b":_b .RGBA {0xff,0x14,0x93,0xff},"d\u0065\u0065\u0070\u0073\u006b\u0079\u0062\u006c\u0075\u0065":_b .RGBA {0x00,0xbf,0xff,0xff},"\u0064i\u006d\u0067\u0072\u0061\u0079":_b .RGBA {0x69,0x69,0x69,0xff},"\u0064i\u006d\u0067\u0072\u0065\u0079":_b .RGBA {0x69,0x69,0x69,0xff},"\u0064\u006f\u0064\u0067\u0065\u0072\u0062\u006c\u0075\u0065":_b .RGBA {0x1e,0x90,0xff,0xff},"\u0066i\u0072\u0065\u0062\u0072\u0069\u0063k":_b .RGBA {0xb2,0x22,0x22,0xff},"f\u006c\u006f\u0072\u0061\u006c\u0077\u0068\u0069\u0074\u0065":_b .RGBA {0xff,0xfa,0xf0,0xff},"f\u006f\u0072\u0065\u0073\u0074\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x22,0x8b,0x22,0xff},"\u0066u\u0063\u0068\u0073\u0069\u0061":_b .RGBA {0xff,0x00,0xff,0xff},"\u0067a\u0069\u006e\u0073\u0062\u006f\u0072o":_b .RGBA {0xdc,0xdc,0xdc,0xff},"\u0067\u0068\u006f\u0073\u0074\u0077\u0068\u0069\u0074\u0065":_b .RGBA {0xf8,0xf8,0xff,0xff},"\u0067\u006f\u006c\u0064":_b .RGBA {0xff,0xd7,0x00,0xff},"\u0067o\u006c\u0064\u0065\u006e\u0072\u006fd":_b .RGBA {0xda,0xa5,0x20,0xff},"\u0067\u0072\u0061\u0079":_b .RGBA {0x80,0x80,0x80,0xff},"\u0067\u0072\u0065e\u006e":_b .RGBA {0x00,0x80,0x00,0xff},"g\u0072\u0065\u0065\u006e\u0079\u0065\u006c\u006c\u006f\u0077":_b .RGBA {0xad,0xff,0x2f,0xff},"\u0067\u0072\u0065\u0079":_b .RGBA {0x80,0x80,0x80,0xff},"\u0068\u006f\u006e\u0065\u0079\u0064\u0065\u0077":_b .RGBA {0xf0,0xff,0xf0,0xff},"\u0068o\u0074\u0070\u0069\u006e\u006b":_b .RGBA {0xff,0x69,0xb4,0xff},"\u0069n\u0064\u0069\u0061\u006e\u0072\u0065d":_b .RGBA {0xcd,0x5c,0x5c,0xff},"\u0069\u006e\u0064\u0069\u0067\u006f":_b .RGBA {0x4b,0x00,0x82,0xff},"\u0069\u0076\u006fr\u0079":_b .RGBA {0xff,0xff,0xf0,0xff},"\u006b\u0068\u0061k\u0069":_b .RGBA {0xf0,0xe6,0x8c,0xff},"\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072":_b .RGBA {0xe6,0xe6,0xfa,0xff},"\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072\u0062\u006c\u0075\u0073\u0068":_b .RGBA {0xff,0xf0,0xf5,0xff},"\u006ca\u0077\u006e\u0067\u0072\u0065\u0065n":_b .RGBA {0x7c,0xfc,0x00,0xff},"\u006c\u0065\u006do\u006e\u0063\u0068\u0069\u0066\u0066\u006f\u006e":_b .RGBA {0xff,0xfa,0xcd,0xff},"\u006ci\u0067\u0068\u0074\u0062\u006c\u0075e":_b .RGBA {0xad,0xd8,0xe6,0xff},"\u006c\u0069\u0067\u0068\u0074\u0063\u006f\u0072\u0061\u006c":_b .RGBA {0xf0,0x80,0x80,0xff},"\u006ci\u0067\u0068\u0074\u0063\u0079\u0061n":_b .RGBA {0xe0,0xff,0xff,0xff},"l\u0069g\u0068\u0074\u0067\u006f\u006c\u0064\u0065\u006er\u006f\u0064\u0079\u0065ll\u006f\u0077":_b .RGBA {0xfa,0xfa,0xd2,0xff},"\u006ci\u0067\u0068\u0074\u0067\u0072\u0061y":_b .RGBA {0xd3,0xd3,0xd3,0xff},"\u006c\u0069\u0067\u0068\u0074\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x90,0xee,0x90,0xff},"\u006ci\u0067\u0068\u0074\u0067\u0072\u0065y":_b .RGBA {0xd3,0xd3,0xd3,0xff},"\u006ci\u0067\u0068\u0074\u0070\u0069\u006ek":_b .RGBA {0xff,0xb6,0xc1,0xff},"l\u0069\u0067\u0068\u0074\u0073\u0061\u006c\u006d\u006f\u006e":_b .RGBA {0xff,0xa0,0x7a,0xff},"\u006c\u0069\u0067\u0068\u0074\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x20,0xb2,0xaa,0xff},"\u006c\u0069\u0067h\u0074\u0073\u006b\u0079\u0062\u006c\u0075\u0065":_b .RGBA {0x87,0xce,0xfa,0xff},"\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0061\u0079":_b .RGBA {0x77,0x88,0x99,0xff},"\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0065\u0079":_b .RGBA {0x77,0x88,0x99,0xff},"\u006c\u0069\u0067\u0068\u0074\u0073\u0074\u0065\u0065l\u0062\u006c\u0075\u0065":_b .RGBA {0xb0,0xc4,0xde,0xff},"l\u0069\u0067\u0068\u0074\u0079\u0065\u006c\u006c\u006f\u0077":_b .RGBA {0xff,0xff,0xe0,0xff},"\u006c\u0069\u006d\u0065":_b .RGBA {0x00,0xff,0x00,0xff},"\u006ci\u006d\u0065\u0067\u0072\u0065\u0065n":_b .RGBA {0x32,0xcd,0x32,0xff},"\u006c\u0069\u006ee\u006e":_b .RGBA {0xfa,0xf0,0xe6,0xff},"\u006da\u0067\u0065\u006e\u0074\u0061":_b .RGBA {0xff,0x00,0xff,0xff},"\u006d\u0061\u0072\u006f\u006f\u006e":_b .RGBA {0x80,0x00,0x00,0xff},"\u006d\u0065d\u0069\u0075\u006da\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065":_b .RGBA {0x66,0xcd,0xaa,0xff},"\u006d\u0065\u0064\u0069\u0075\u006d\u0062\u006c\u0075\u0065":_b .RGBA {0x00,0x00,0xcd,0xff},"\u006d\u0065\u0064i\u0075\u006d\u006f\u0072\u0063\u0068\u0069\u0064":_b .RGBA {0xba,0x55,0xd3,0xff},"\u006d\u0065\u0064i\u0075\u006d\u0070\u0075\u0072\u0070\u006c\u0065":_b .RGBA {0x93,0x70,0xdb,0xff},"\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0065\u0061g\u0072\u0065\u0065\u006e":_b .RGBA {0x3c,0xb3,0x71,0xff},"\u006de\u0064i\u0075\u006d\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065":_b .RGBA {0x7b,0x68,0xee,0xff},"\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0070\u0072\u0069\u006e\u0067g\u0072\u0065\u0065\u006e":_b .RGBA {0x00,0xfa,0x9a,0xff},"\u006de\u0064i\u0075\u006d\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":_b .RGBA {0x48,0xd1,0xcc,0xff},"\u006de\u0064i\u0075\u006d\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064":_b .RGBA {0xc7,0x15,0x85,0xff},"\u006d\u0069\u0064n\u0069\u0067\u0068\u0074\u0062\u006c\u0075\u0065":_b .RGBA {0x19,0x19,0x70,0xff},"\u006di\u006e\u0074\u0063\u0072\u0065\u0061m":_b .RGBA {0xf5,0xff,0xfa,0xff},"\u006di\u0073\u0074\u0079\u0072\u006f\u0073e":_b .RGBA {0xff,0xe4,0xe1,0xff},"\u006d\u006f\u0063\u0063\u0061\u0073\u0069\u006e":_b .RGBA {0xff,0xe4,0xb5,0xff},"n\u0061\u0076\u0061\u006a\u006f\u0077\u0068\u0069\u0074\u0065":_b .RGBA {0xff,0xde,0xad,0xff},"\u006e\u0061\u0076\u0079":_b .RGBA {0x00,0x00,0x80,0xff},"\u006fl\u0064\u006c\u0061\u0063\u0065":_b .RGBA {0xfd,0xf5,0xe6,0xff},"\u006f\u006c\u0069v\u0065":_b .RGBA {0x80,0x80,0x00,0xff},"\u006fl\u0069\u0076\u0065\u0064\u0072\u0061b":_b .RGBA {0x6b,0x8e,0x23,0xff},"\u006f\u0072\u0061\u006e\u0067\u0065":_b .RGBA {0xff,0xa5,0x00,0xff},"\u006fr\u0061\u006e\u0067\u0065\u0072\u0065d":_b .RGBA {0xff,0x45,0x00,0xff},"\u006f\u0072\u0063\u0068\u0069\u0064":_b .RGBA {0xda,0x70,0xd6,0xff},"\u0070\u0061\u006c\u0065\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064":_b .RGBA {0xee,0xe8,0xaa,0xff},"\u0070a\u006c\u0065\u0067\u0072\u0065\u0065n":_b .RGBA {0x98,0xfb,0x98,0xff},"\u0070\u0061\u006c\u0065\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":_b .RGBA {0xaf,0xee,0xee,0xff},"\u0070\u0061\u006c\u0065\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064":_b .RGBA {0xdb,0x70,0x93,0xff},"\u0070\u0061\u0070\u0061\u0079\u0061\u0077\u0068\u0069\u0070":_b .RGBA {0xff,0xef,0xd5,0xff},"\u0070e\u0061\u0063\u0068\u0070\u0075\u0066f":_b .RGBA {0xff,0xda,0xb9,0xff},"\u0070\u0065\u0072\u0075":_b .RGBA {0xcd,0x85,0x3f,0xff},"\u0070\u0069\u006e\u006b":_b .RGBA {0xff,0xc0,0xcb,0xff},"\u0070\u006c\u0075\u006d":_b .RGBA {0xdd,0xa0,0xdd,0xff},"\u0070\u006f\u0077\u0064\u0065\u0072\u0062\u006c\u0075\u0065":_b .RGBA {0xb0,0xe0,0xe6,0xff},"\u0070\u0075\u0072\u0070\u006c\u0065":_b .RGBA {0x80,0x00,0x80,0xff},"\u0072\u0065\u0064":_b .RGBA {0xff,0x00,0x00,0xff},"\u0072o\u0073\u0079\u0062\u0072\u006f\u0077n":_b .RGBA {0xbc,0x8f,0x8f,0xff},"\u0072o\u0079\u0061\u006c\u0062\u006c\u0075e":_b .RGBA {0x41,0x69,0xe1,0xff},"s\u0061\u0064\u0064\u006c\u0065\u0062\u0072\u006f\u0077\u006e":_b .RGBA {0x8b,0x45,0x13,0xff},"\u0073\u0061\u006c\u006d\u006f\u006e":_b .RGBA {0xfa,0x80,0x72,0xff},"\u0073\u0061\u006e\u0064\u0079\u0062\u0072\u006f\u0077\u006e":_b .RGBA {0xf4,0xa4,0x60,0xff},"\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x2e,0x8b,0x57,0xff},"\u0073\u0065\u0061\u0073\u0068\u0065\u006c\u006c":_b .RGBA {0xff,0xf5,0xee,0xff},"\u0073\u0069\u0065\u006e\u006e\u0061":_b .RGBA {0xa0,0x52,0x2d,0xff},"\u0073\u0069\u006c\u0076\u0065\u0072":_b .RGBA {0xc0,0xc0,0xc0,0xff},"\u0073k\u0079\u0062\u006c\u0075\u0065":_b .RGBA {0x87,0xce,0xeb,0xff},"\u0073l\u0061\u0074\u0065\u0062\u006c\u0075e":_b .RGBA {0x6a,0x5a,0xcd,0xff},"\u0073l\u0061\u0074\u0065\u0067\u0072\u0061y":_b .RGBA {0x70,0x80,0x90,0xff},"\u0073l\u0061\u0074\u0065\u0067\u0072\u0065y":_b .RGBA {0x70,0x80,0x90,0xff},"\u0073\u006e\u006f\u0077":_b .RGBA {0xff,0xfa,0xfa,0xff},"s\u0070\u0072\u0069\u006e\u0067\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x00,0xff,0x7f,0xff},"\u0073t\u0065\u0065\u006c\u0062\u006c\u0075e":_b .RGBA {0x46,0x82,0xb4,0xff},"\u0074\u0061\u006e":_b .RGBA {0xd2,0xb4,0x8c,0xff},"\u0074\u0065\u0061\u006c":_b .RGBA {0x00,0x80,0x80,0xff},"\u0074h\u0069\u0073\u0074\u006c\u0065":_b .RGBA {0xd8,0xbf,0xd8,0xff},"\u0074\u006f\u006d\u0061\u0074\u006f":_b .RGBA {0xff,0x63,0x47,0xff},"\u0074u\u0072\u0071\u0075\u006f\u0069\u0073e":_b .RGBA {0x40,0xe0,0xd0,0xff},"\u0076\u0069\u006f\u006c\u0065\u0074":_b .RGBA {0xee,0x82,0xee,0xff},"\u0077\u0068\u0065a\u0074":_b .RGBA {0xf5,0xde,0xb3,0xff},"\u0077\u0068\u0069t\u0065":_b .RGBA {0xff,0xff,0xff,0xff},"\u0077\u0068\u0069\u0074\u0065\u0073\u006d\u006f\u006b\u0065":_b .RGBA {0xf5,0xf5,0xf5,0xff},"\u0079\u0065\u006c\u006c\u006f\u0077":_b .RGBA {0xff,0xff,0x00,0xff},"y\u0065\u006c\u006c\u006f\u0077\u0067\u0072\u0065\u0065\u006e":_b .RGBA {0x9a,0xcd,0x32,0xff}};
var Names =[]string {"\u0061l\u0069\u0063\u0065\u0062\u006c\u0075e","\u0061\u006e\u0074i\u0071\u0075\u0065\u0077\u0068\u0069\u0074\u0065","\u0061\u0071\u0075\u0061","\u0061\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065","\u0061\u007a\u0075r\u0065","\u0062\u0065\u0069g\u0065","\u0062\u0069\u0073\u0071\u0075\u0065","\u0062\u006c\u0061c\u006b","\u0062\u006c\u0061\u006e\u0063\u0068\u0065\u0064\u0061l\u006d\u006f\u006e\u0064","\u0062\u006c\u0075\u0065","\u0062\u006c\u0075\u0065\u0076\u0069\u006f\u006c\u0065\u0074","\u0062\u0072\u006fw\u006e","\u0062u\u0072\u006c\u0079\u0077\u006f\u006fd","\u0063a\u0064\u0065\u0074\u0062\u006c\u0075e","\u0063\u0068\u0061\u0072\u0074\u0072\u0065\u0075\u0073\u0065","\u0063h\u006f\u0063\u006f\u006c\u0061\u0074e","\u0063\u006f\u0072a\u006c","\u0063\u006f\u0072\u006e\u0066\u006c\u006f\u0077\u0065r\u0062\u006c\u0075\u0065","\u0063\u006f\u0072\u006e\u0073\u0069\u006c\u006b","\u0063r\u0069\u006d\u0073\u006f\u006e","\u0063\u0079\u0061\u006e","\u0064\u0061\u0072\u006b\u0062\u006c\u0075\u0065","\u0064\u0061\u0072\u006b\u0063\u0079\u0061\u006e","\u0064\u0061\u0072\u006b\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064","\u0064\u0061\u0072\u006b\u0067\u0072\u0061\u0079","\u0064a\u0072\u006b\u0067\u0072\u0065\u0065n","\u0064\u0061\u0072\u006b\u0067\u0072\u0065\u0079","\u0064a\u0072\u006b\u006b\u0068\u0061\u006bi","d\u0061\u0072\u006b\u006d\u0061\u0067\u0065\u006e\u0074\u0061","\u0064\u0061\u0072\u006b\u006f\u006c\u0069\u0076\u0065g\u0072\u0065\u0065\u006e","\u0064\u0061\u0072\u006b\u006f\u0072\u0061\u006e\u0067\u0065","\u0064\u0061\u0072\u006b\u006f\u0072\u0063\u0068\u0069\u0064","\u0064a\u0072\u006b\u0072\u0065\u0064","\u0064\u0061\u0072\u006b\u0073\u0061\u006c\u006d\u006f\u006e","\u0064\u0061\u0072k\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e","\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065","\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0061\u0079","\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0065\u0079","\u0064\u0061\u0072\u006b\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065","\u0064\u0061\u0072\u006b\u0076\u0069\u006f\u006c\u0065\u0074","\u0064\u0065\u0065\u0070\u0070\u0069\u006e\u006b","d\u0065\u0065\u0070\u0073\u006b\u0079\u0062\u006c\u0075\u0065","\u0064i\u006d\u0067\u0072\u0061\u0079","\u0064i\u006d\u0067\u0072\u0065\u0079","\u0064\u006f\u0064\u0067\u0065\u0072\u0062\u006c\u0075\u0065","\u0066i\u0072\u0065\u0062\u0072\u0069\u0063k","f\u006c\u006f\u0072\u0061\u006c\u0077\u0068\u0069\u0074\u0065","f\u006f\u0072\u0065\u0073\u0074\u0067\u0072\u0065\u0065\u006e","\u0066u\u0063\u0068\u0073\u0069\u0061","\u0067a\u0069\u006e\u0073\u0062\u006f\u0072o","\u0067\u0068\u006f\u0073\u0074\u0077\u0068\u0069\u0074\u0065","\u0067\u006f\u006c\u0064","\u0067o\u006c\u0064\u0065\u006e\u0072\u006fd","\u0067\u0072\u0061\u0079","\u0067\u0072\u0065e\u006e","g\u0072\u0065\u0065\u006e\u0079\u0065\u006c\u006c\u006f\u0077","\u0067\u0072\u0065\u0079","\u0068\u006f\u006e\u0065\u0079\u0064\u0065\u0077","\u0068o\u0074\u0070\u0069\u006e\u006b","\u0069n\u0064\u0069\u0061\u006e\u0072\u0065d","\u0069\u006e\u0064\u0069\u0067\u006f","\u0069\u0076\u006fr\u0079","\u006b\u0068\u0061k\u0069","\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072","\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072\u0062\u006c\u0075\u0073\u0068","\u006ca\u0077\u006e\u0067\u0072\u0065\u0065n","\u006c\u0065\u006do\u006e\u0063\u0068\u0069\u0066\u0066\u006f\u006e","\u006ci\u0067\u0068\u0074\u0062\u006c\u0075e","\u006c\u0069\u0067\u0068\u0074\u0063\u006f\u0072\u0061\u006c","\u006ci\u0067\u0068\u0074\u0063\u0079\u0061n","l\u0069g\u0068\u0074\u0067\u006f\u006c\u0064\u0065\u006er\u006f\u0064\u0079\u0065ll\u006f\u0077","\u006ci\u0067\u0068\u0074\u0067\u0072\u0061y","\u006c\u0069\u0067\u0068\u0074\u0067\u0072\u0065\u0065\u006e","\u006ci\u0067\u0068\u0074\u0067\u0072\u0065y","\u006ci\u0067\u0068\u0074\u0070\u0069\u006ek","l\u0069\u0067\u0068\u0074\u0073\u0061\u006c\u006d\u006f\u006e","\u006c\u0069\u0067\u0068\u0074\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e","\u006c\u0069\u0067h\u0074\u0073\u006b\u0079\u0062\u006c\u0075\u0065","\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0061\u0079","\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0065\u0079","\u006c\u0069\u0067\u0068\u0074\u0073\u0074\u0065\u0065l\u0062\u006c\u0075\u0065","l\u0069\u0067\u0068\u0074\u0079\u0065\u006c\u006c\u006f\u0077","\u006c\u0069\u006d\u0065","\u006ci\u006d\u0065\u0067\u0072\u0065\u0065n","\u006c\u0069\u006ee\u006e","\u006da\u0067\u0065\u006e\u0074\u0061","\u006d\u0061\u0072\u006f\u006f\u006e","\u006d\u0065d\u0069\u0075\u006da\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065","\u006d\u0065\u0064\u0069\u0075\u006d\u0062\u006c\u0075\u0065","\u006d\u0065\u0064i\u0075\u006d\u006f\u0072\u0063\u0068\u0069\u0064","\u006d\u0065\u0064i\u0075\u006d\u0070\u0075\u0072\u0070\u006c\u0065","\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0065\u0061g\u0072\u0065\u0065\u006e","\u006de\u0064i\u0075\u006d\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065","\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0070\u0072\u0069\u006e\u0067g\u0072\u0065\u0065\u006e","\u006de\u0064i\u0075\u006d\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065","\u006de\u0064i\u0075\u006d\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064","\u006d\u0069\u0064n\u0069\u0067\u0068\u0074\u0062\u006c\u0075\u0065","\u006di\u006e\u0074\u0063\u0072\u0065\u0061m","\u006di\u0073\u0074\u0079\u0072\u006f\u0073e","\u006d\u006f\u0063\u0063\u0061\u0073\u0069\u006e","n\u0061\u0076\u0061\u006a\u006f\u0077\u0068\u0069\u0074\u0065","\u006e\u0061\u0076\u0079","\u006fl\u0064\u006c\u0061\u0063\u0065","\u006f\u006c\u0069v\u0065","\u006fl\u0069\u0076\u0065\u0064\u0072\u0061b","\u006f\u0072\u0061\u006e\u0067\u0065","\u006fr\u0061\u006e\u0067\u0065\u0072\u0065d","\u006f\u0072\u0063\u0068\u0069\u0064","\u0070\u0061\u006c\u0065\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064","\u0070a\u006c\u0065\u0067\u0072\u0065\u0065n","\u0070\u0061\u006c\u0065\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065","\u0070\u0061\u006c\u0065\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064","\u0070\u0061\u0070\u0061\u0079\u0061\u0077\u0068\u0069\u0070","\u0070e\u0061\u0063\u0068\u0070\u0075\u0066f","\u0070\u0065\u0072\u0075","\u0070\u0069\u006e\u006b","\u0070\u006c\u0075\u006d","\u0070\u006f\u0077\u0064\u0065\u0072\u0062\u006c\u0075\u0065","\u0070\u0075\u0072\u0070\u006c\u0065","\u0072\u0065\u0064","\u0072o\u0073\u0079\u0062\u0072\u006f\u0077n","\u0072o\u0079\u0061\u006c\u0062\u006c\u0075e","s\u0061\u0064\u0064\u006c\u0065\u0062\u0072\u006f\u0077\u006e","\u0073\u0061\u006c\u006d\u006f\u006e","\u0073\u0061\u006e\u0064\u0079\u0062\u0072\u006f\u0077\u006e","\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e","\u0073\u0065\u0061\u0073\u0068\u0065\u006c\u006c","\u0073\u0069\u0065\u006e\u006e\u0061","\u0073\u0069\u006c\u0076\u0065\u0072","\u0073k\u0079\u0062\u006c\u0075\u0065","\u0073l\u0061\u0074\u0065\u0062\u006c\u0075e","\u0073l\u0061\u0074\u0065\u0067\u0072\u0061y","\u0073l\u0061\u0074\u0065\u0067\u0072\u0065y","\u0073\u006e\u006f\u0077","s\u0070\u0072\u0069\u006e\u0067\u0067\u0072\u0065\u0065\u006e","\u0073t\u0065\u0065\u006c\u0062\u006c\u0075e","\u0074\u0061\u006e","\u0074\u0065\u0061\u006c","\u0074h\u0069\u0073\u0074\u006c\u0065","\u0074\u006f\u006d\u0061\u0074\u006f","\u0074u\u0072\u0071\u0075\u006f\u0069\u0073e","\u0076\u0069\u006f\u006c\u0065\u0074","\u0077\u0068\u0065a\u0074","\u0077\u0068\u0069t\u0065","\u0077\u0068\u0069\u0074\u0065\u0073\u006d\u006f\u006b\u0065","\u0079\u0065\u006c\u006c\u006f\u0077","y\u0065\u006c\u006c\u006f\u0077\u0067\u0072\u0065\u0065\u006e"};
func (_dgg Point )Sub (q Point )Point {return Point {_dgg .X -q .X ,_dgg .Y -q .Y }};type Point struct{X ,Y float64 ;};func _edc (_df ,_de ,_fef ,_ca ,_acc float64 ,_ffg ,_ef bool ,_aca ,_ea float64 )(float64 ,float64 ,float64 ,float64 ){if _cdd (_df ,_aca )&&_cdd (_de ,_ea ){return _df ,_de ,0.0,0.0;
};_gfbc ,_cba :=_a .Sincos (_acc );_cgf :=_cba *(_df -_aca )/2.0+_gfbc *(_de -_ea )/2.0;_cd :=-_gfbc *(_df -_aca )/2.0+_cba *(_de -_ea )/2.0;_ge :=_cgf *_cgf /_fef /_fef +_cd *_cd /_ca /_ca ;if _ge > 1.0{_fef *=_a .Sqrt (_ge );_ca *=_a .Sqrt (_ge );};_efa :=(_fef *_fef *_ca *_ca -_fef *_fef *_cd *_cd -_ca *_ca *_cgf *_cgf )/(_fef *_fef *_cd *_cd +_ca *_ca *_cgf *_cgf );
if _efa < 0.0{_efa =0.0;};_ec :=_a .Sqrt (_efa );if _ffg ==_ef {_ec =-_ec ;};_bd :=_ec *_fef *_cd /_ca ;_cbe :=_ec *-_ca *_cgf /_fef ;_cce :=_cba *_bd -_gfbc *_cbe +(_df +_aca )/2.0;_ddb :=_gfbc *_bd +_cba *_cbe +(_de +_ea )/2.0;_ae :=(_cgf -_bd )/_fef ;
_fd :=(_cd -_cbe )/_ca ;_fee :=-(_cgf +_bd )/_fef ;_ccg :=-(_cd +_cbe )/_ca ;_gdd :=_a .Acos (_ae /_a .Sqrt (_ae *_ae +_fd *_fd ));if _fd < 0.0{_gdd =-_gdd ;};_gdd =_dag (_gdd );_ace :=(_ae *_fee +_fd *_ccg )/_a .Sqrt ((_ae *_ae +_fd *_fd )*(_fee *_fee +_ccg *_ccg ));
_ace =_a .Min (1.0,_a .Max (-1.0,_ace ));_cff :=_a .Acos (_ace );if _ae *_ccg -_fd *_fee < 0.0{_cff =-_cff ;};if !_ef &&_cff > 0.0{_cff -=2.0*_a .Pi ;}else if _ef &&_cff < 0.0{_cff +=2.0*_a .Pi ;};return _cce ,_ddb ,_gdd ,_gdd +_cff ;};func _cb (_cc ,_cg ,_ba ,_abc ,_be ,_ff float64 )(float64 ,float64 ){_fe ,_cfc :=_a .Sincos (_ff );
_ed ,_baf :=_a .Sincos (_ba );_dg :=_abc +_cc *_cfc *_baf -_cg *_fe *_ed ;_gfb :=_be +_cc *_cfc *_ed +_cg *_fe *_baf ;return _dg ,_gfb ;};func (_feec Point )Mul (f float64 )Point {return Point {f *_feec .X ,f *_feec .Y }};func QuadraticToCubicBezier (startX ,startY ,x1 ,y1 ,x ,y float64 )(Point ,Point ){_cdf :=Point {X :startX ,Y :startY };
_bce :=Point {X :x1 ,Y :y1 };_gag :=Point {X :x ,Y :y };_feg :=_cdf .Interpolate (_bce ,2.0/3.0);_da :=_gag .Interpolate (_bce ,2.0/3.0);return _feg ,_da ;};func (_bf Point )Interpolate (q Point ,t float64 )Point {return Point {(1-t )*_bf .X +t *q .X ,(1-t )*_bf .Y +t *q .Y };
};const _db =1e-10;var (Aliceblue =_b .RGBA {0xf0,0xf8,0xff,0xff};Antiquewhite =_b .RGBA {0xfa,0xeb,0xd7,0xff};Aqua =_b .RGBA {0x00,0xff,0xff,0xff};Aquamarine =_b .RGBA {0x7f,0xff,0xd4,0xff};Azure =_b .RGBA {0xf0,0xff,0xff,0xff};Beige =_b .RGBA {0xf5,0xf5,0xdc,0xff};
Bisque =_b .RGBA {0xff,0xe4,0xc4,0xff};Black =_b .RGBA {0x00,0x00,0x00,0xff};Blanchedalmond =_b .RGBA {0xff,0xeb,0xcd,0xff};Blue =_b .RGBA {0x00,0x00,0xff,0xff};Blueviolet =_b .RGBA {0x8a,0x2b,0xe2,0xff};Brown =_b .RGBA {0xa5,0x2a,0x2a,0xff};Burlywood =_b .RGBA {0xde,0xb8,0x87,0xff};
Cadetblue =_b .RGBA {0x5f,0x9e,0xa0,0xff};Chartreuse =_b .RGBA {0x7f,0xff,0x00,0xff};Chocolate =_b .RGBA {0xd2,0x69,0x1e,0xff};Coral =_b .RGBA {0xff,0x7f,0x50,0xff};Cornflowerblue =_b .RGBA {0x64,0x95,0xed,0xff};Cornsilk =_b .RGBA {0xff,0xf8,0xdc,0xff};
Crimson =_b .RGBA {0xdc,0x14,0x3c,0xff};Cyan =_b .RGBA {0x00,0xff,0xff,0xff};Darkblue =_b .RGBA {0x00,0x00,0x8b,0xff};Darkcyan =_b .RGBA {0x00,0x8b,0x8b,0xff};Darkgoldenrod =_b .RGBA {0xb8,0x86,0x0b,0xff};Darkgray =_b .RGBA {0xa9,0xa9,0xa9,0xff};Darkgreen =_b .RGBA {0x00,0x64,0x00,0xff};
Darkgrey =_b .RGBA {0xa9,0xa9,0xa9,0xff};Darkkhaki =_b .RGBA {0xbd,0xb7,0x6b,0xff};Darkmagenta =_b .RGBA {0x8b,0x00,0x8b,0xff};Darkolivegreen =_b .RGBA {0x55,0x6b,0x2f,0xff};Darkorange =_b .RGBA {0xff,0x8c,0x00,0xff};Darkorchid =_b .RGBA {0x99,0x32,0xcc,0xff};
Darkred =_b .RGBA {0x8b,0x00,0x00,0xff};Darksalmon =_b .RGBA {0xe9,0x96,0x7a,0xff};Darkseagreen =_b .RGBA {0x8f,0xbc,0x8f,0xff};Darkslateblue =_b .RGBA {0x48,0x3d,0x8b,0xff};Darkslategray =_b .RGBA {0x2f,0x4f,0x4f,0xff};Darkslategrey =_b .RGBA {0x2f,0x4f,0x4f,0xff};
Darkturquoise =_b .RGBA {0x00,0xce,0xd1,0xff};Darkviolet =_b .RGBA {0x94,0x00,0xd3,0xff};Deeppink =_b .RGBA {0xff,0x14,0x93,0xff};Deepskyblue =_b .RGBA {0x00,0xbf,0xff,0xff};Dimgray =_b .RGBA {0x69,0x69,0x69,0xff};Dimgrey =_b .RGBA {0x69,0x69,0x69,0xff};
Dodgerblue =_b .RGBA {0x1e,0x90,0xff,0xff};Firebrick =_b .RGBA {0xb2,0x22,0x22,0xff};Floralwhite =_b .RGBA {0xff,0xfa,0xf0,0xff};Forestgreen =_b .RGBA {0x22,0x8b,0x22,0xff};Fuchsia =_b .RGBA {0xff,0x00,0xff,0xff};Gainsboro =_b .RGBA {0xdc,0xdc,0xdc,0xff};
Ghostwhite =_b .RGBA {0xf8,0xf8,0xff,0xff};Gold =_b .RGBA {0xff,0xd7,0x00,0xff};Goldenrod =_b .RGBA {0xda,0xa5,0x20,0xff};Gray =_b .RGBA {0x80,0x80,0x80,0xff};Green =_b .RGBA {0x00,0x80,0x00,0xff};Greenyellow =_b .RGBA {0xad,0xff,0x2f,0xff};Grey =_b .RGBA {0x80,0x80,0x80,0xff};
Honeydew =_b .RGBA {0xf0,0xff,0xf0,0xff};Hotpink =_b .RGBA {0xff,0x69,0xb4,0xff};Indianred =_b .RGBA {0xcd,0x5c,0x5c,0xff};Indigo =_b .RGBA {0x4b,0x00,0x82,0xff};Ivory =_b .RGBA {0xff,0xff,0xf0,0xff};Khaki =_b .RGBA {0xf0,0xe6,0x8c,0xff};Lavender =_b .RGBA {0xe6,0xe6,0xfa,0xff};
Lavenderblush =_b .RGBA {0xff,0xf0,0xf5,0xff};Lawngreen =_b .RGBA {0x7c,0xfc,0x00,0xff};Lemonchiffon =_b .RGBA {0xff,0xfa,0xcd,0xff};Lightblue =_b .RGBA {0xad,0xd8,0xe6,0xff};Lightcoral =_b .RGBA {0xf0,0x80,0x80,0xff};Lightcyan =_b .RGBA {0xe0,0xff,0xff,0xff};
Lightgoldenrodyellow =_b .RGBA {0xfa,0xfa,0xd2,0xff};Lightgray =_b .RGBA {0xd3,0xd3,0xd3,0xff};Lightgreen =_b .RGBA {0x90,0xee,0x90,0xff};Lightgrey =_b .RGBA {0xd3,0xd3,0xd3,0xff};Lightpink =_b .RGBA {0xff,0xb6,0xc1,0xff};Lightsalmon =_b .RGBA {0xff,0xa0,0x7a,0xff};
Lightseagreen =_b .RGBA {0x20,0xb2,0xaa,0xff};Lightskyblue =_b .RGBA {0x87,0xce,0xfa,0xff};Lightslategray =_b .RGBA {0x77,0x88,0x99,0xff};Lightslategrey =_b .RGBA {0x77,0x88,0x99,0xff};Lightsteelblue =_b .RGBA {0xb0,0xc4,0xde,0xff};Lightyellow =_b .RGBA {0xff,0xff,0xe0,0xff};
Lime =_b .RGBA {0x00,0xff,0x00,0xff};Limegreen =_b .RGBA {0x32,0xcd,0x32,0xff};Linen =_b .RGBA {0xfa,0xf0,0xe6,0xff};Magenta =_b .RGBA {0xff,0x00,0xff,0xff};Maroon =_b .RGBA {0x80,0x00,0x00,0xff};Mediumaquamarine =_b .RGBA {0x66,0xcd,0xaa,0xff};Mediumblue =_b .RGBA {0x00,0x00,0xcd,0xff};
Mediumorchid =_b .RGBA {0xba,0x55,0xd3,0xff};Mediumpurple =_b .RGBA {0x93,0x70,0xdb,0xff};Mediumseagreen =_b .RGBA {0x3c,0xb3,0x71,0xff};Mediumslateblue =_b .RGBA {0x7b,0x68,0xee,0xff};Mediumspringgreen =_b .RGBA {0x00,0xfa,0x9a,0xff};Mediumturquoise =_b .RGBA {0x48,0xd1,0xcc,0xff};
Mediumvioletred =_b .RGBA {0xc7,0x15,0x85,0xff};Midnightblue =_b .RGBA {0x19,0x19,0x70,0xff};Mintcream =_b .RGBA {0xf5,0xff,0xfa,0xff};Mistyrose =_b .RGBA {0xff,0xe4,0xe1,0xff};Moccasin =_b .RGBA {0xff,0xe4,0xb5,0xff};Navajowhite =_b .RGBA {0xff,0xde,0xad,0xff};
Navy =_b .RGBA {0x00,0x00,0x80,0xff};Oldlace =_b .RGBA {0xfd,0xf5,0xe6,0xff};Olive =_b .RGBA {0x80,0x80,0x00,0xff};Olivedrab =_b .RGBA {0x6b,0x8e,0x23,0xff};Orange =_b .RGBA {0xff,0xa5,0x00,0xff};Orangered =_b .RGBA {0xff,0x45,0x00,0xff};Orchid =_b .RGBA {0xda,0x70,0xd6,0xff};
Palegoldenrod =_b .RGBA {0xee,0xe8,0xaa,0xff};Palegreen =_b .RGBA {0x98,0xfb,0x98,0xff};Paleturquoise =_b .RGBA {0xaf,0xee,0xee,0xff};Palevioletred =_b .RGBA {0xdb,0x70,0x93,0xff};Papayawhip =_b .RGBA {0xff,0xef,0xd5,0xff};Peachpuff =_b .RGBA {0xff,0xda,0xb9,0xff};
Peru =_b .RGBA {0xcd,0x85,0x3f,0xff};Pink =_b .RGBA {0xff,0xc0,0xcb,0xff};Plum =_b .RGBA {0xdd,0xa0,0xdd,0xff};Powderblue =_b .RGBA {0xb0,0xe0,0xe6,0xff};Purple =_b .RGBA {0x80,0x00,0x80,0xff};Red =_b .RGBA {0xff,0x00,0x00,0xff};Rosybrown =_b .RGBA {0xbc,0x8f,0x8f,0xff};
Royalblue =_b .RGBA {0x41,0x69,0xe1,0xff};Saddlebrown =_b .RGBA {0x8b,0x45,0x13,0xff};Salmon =_b .RGBA {0xfa,0x80,0x72,0xff};Sandybrown =_b .RGBA {0xf4,0xa4,0x60,0xff};Seagreen =_b .RGBA {0x2e,0x8b,0x57,0xff};Seashell =_b .RGBA {0xff,0xf5,0xee,0xff};Sienna =_b .RGBA {0xa0,0x52,0x2d,0xff};
Silver =_b .RGBA {0xc0,0xc0,0xc0,0xff};Skyblue =_b .RGBA {0x87,0xce,0xeb,0xff};Slateblue =_b .RGBA {0x6a,0x5a,0xcd,0xff};Slategray =_b .RGBA {0x70,0x80,0x90,0xff};Slategrey =_b .RGBA {0x70,0x80,0x90,0xff};Snow =_b .RGBA {0xff,0xfa,0xfa,0xff};Springgreen =_b .RGBA {0x00,0xff,0x7f,0xff};
Steelblue =_b .RGBA {0x46,0x82,0xb4,0xff};Tan =_b .RGBA {0xd2,0xb4,0x8c,0xff};Teal =_b .RGBA {0x00,0x80,0x80,0xff};Thistle =_b .RGBA {0xd8,0xbf,0xd8,0xff};Tomato =_b .RGBA {0xff,0x63,0x47,0xff};Turquoise =_b .RGBA {0x40,0xe0,0xd0,0xff};Violet =_b .RGBA {0xee,0x82,0xee,0xff};
Wheat =_b .RGBA {0xf5,0xde,0xb3,0xff};White =_b .RGBA {0xff,0xff,0xff,0xff};Whitesmoke =_b .RGBA {0xf5,0xf5,0xf5,0xff};Yellow =_b .RGBA {0xff,0xff,0x00,0xff};Yellowgreen =_b .RGBA {0x9a,0xcd,0x32,0xff};);func _cdd (_aa ,_gea float64 )bool {return _a .Abs (_aa -_gea )<=_db };
func _dag (_bdd float64 )float64 {_bdd =_a .Mod (_bdd ,2.0*_a .Pi );if _bdd < 0.0{_bdd +=2.0*_a .Pi ;};return _bdd ;};