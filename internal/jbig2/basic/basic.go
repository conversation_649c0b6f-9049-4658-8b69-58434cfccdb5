//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package basic ;import _b "github.com/unidoc/unipdf/v4/internal/jbig2/errors";func (_ge *Stack )Peek ()(_ab interface{},_ga bool ){return _ge .peek ()};func (_dc IntSlice )Size ()int {return len (_dc )};func (_ce IntsMap )GetSlice (key uint64 )([]int ,bool ){_ca ,_gb :=_ce [key ];
if !_gb {return nil ,false ;};return _ca ,true ;};func NewNumSlice (i int )*NumSlice {_fb :=NumSlice (make ([]float32 ,i ));return &_fb };func (_dg *Stack )Len ()int {return len (_dg .Data )};func (_dge *Stack )top ()int {return len (_dge .Data )-1};type Stack struct{Data []interface{};
Aux *Stack ;};func Abs (v int )int {if v > 0{return v ;};return -v ;};func Ceil (numerator ,denominator int )int {if numerator %denominator ==0{return numerator /denominator ;};return (numerator /denominator )+1;};func (_bb NumSlice )GetIntSlice ()[]int {_bbg :=make ([]int ,len (_bb ));
for _gbg ,_gg :=range _bb {_bbg [_gbg ]=int (_gg );};return _bbg ;};func (_a *IntSlice )Copy ()*IntSlice {_ac :=IntSlice (make ([]int ,len (*_a )));copy (_ac ,*_a );return &_ac ;};func (_ee *Stack )peek ()(interface{},bool ){_bae :=_ee .top ();if _bae ==-1{return nil ,false ;
};return _ee .Data [_bae ],true ;};func (_cag *Stack )Push (v interface{}){_cag .Data =append (_cag .Data ,v )};func Max (x ,y int )int {if x > y {return x ;};return y ;};func Sign (v float32 )float32 {if v >=0.0{return 1.0;};return -1.0;};func (_ba *Stack )Pop ()(_cc interface{},_ef bool ){_cc ,_ef =_ba .peek ();
if !_ef {return nil ,_ef ;};_ba .Data =_ba .Data [:_ba .top ()];return _cc ,true ;};type IntSlice []int ;func (_dag NumSlice )GetInt (i int )(int ,error ){const _ff ="\u0047\u0065\u0074\u0049\u006e\u0074";if i < 0||i > len (_dag )-1{return 0,_b .Errorf (_ff ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};_bff :=_dag [i ];return int (_bff +Sign (_bff )*0.5),nil ;};type NumSlice []float32 ;func (_e *IntSlice )Add (v int )error {if _e ==nil {return _b .Error ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0041\u0064\u0064","\u0073\u006c\u0069\u0063\u0065\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};*_e =append (*_e ,v );return nil ;};func (_da IntSlice )Get (index int )(int ,error ){if index > len (_da )-1{return 0,_b .Errorf ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",index );
};return _da [index ],nil ;};func (_bf *NumSlice )AddInt (v int ){*_bf =append (*_bf ,float32 (v ))};func NewIntSlice (i int )*IntSlice {_dd :=IntSlice (make ([]int ,i ));return &_dd };func (_db IntsMap )Delete (key uint64 ){delete (_db ,key )};func Min (x ,y int )int {if x < y {return x ;
};return y ;};type IntsMap map[uint64 ][]int ;func (_c IntsMap )Add (key uint64 ,value int ){_c [key ]=append (_c [key ],value )};func (_fe *NumSlice )Add (v float32 ){*_fe =append (*_fe ,v )};func (_gc IntsMap )Get (key uint64 )(int ,bool ){_f ,_d :=_gc [key ];
if !_d {return 0,false ;};if len (_f )==0{return 0,false ;};return _f [0],true ;};func (_cb NumSlice )Get (i int )(float32 ,error ){if i < 0||i > len (_cb )-1{return 0,_b .Errorf ("\u004e\u0075\u006dS\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _cb [i ],nil ;};