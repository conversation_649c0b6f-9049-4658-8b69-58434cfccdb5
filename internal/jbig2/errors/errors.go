//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package errors ;import (_c "fmt";_cb "golang.org/x/xerrors";);func (_gg *processError )Error ()string {var _cd string ;if _gg ._g !=""{_cd =_gg ._g ;};_cd +="\u0050r\u006f\u0063\u0065\u0073\u0073\u003a "+_gg ._d ;if _gg ._ge !=""{_cd +="\u0020\u004d\u0065\u0073\u0073\u0061\u0067\u0065\u003a\u0020"+_gg ._ge ;
};if _gg ._f !=nil {_cd +="\u002e\u0020"+_gg ._f .<PERSON>rror ();};return _cd ;};func Errorf (processName ,message string ,arguments ...interface{})error {return _e (_c .Sprintf (message ,arguments ...),processName );};var _ _cb .Wrapper =(*processError )(nil );
func Wrap (err error ,processName ,message string )error {if _da ,_fa :=err .(*processError );_fa {_da ._g ="";};_daf :=_e (message ,processName );_daf ._f =err ;return _daf ;};func Wrapf (err error ,processName ,message string ,arguments ...interface{})error {if _dd ,_eb :=err .(*processError );
_eb {_dd ._g ="";};_gd :=_e (_c .Sprintf (message ,arguments ...),processName );_gd ._f =err ;return _gd ;};func (_ag *processError )Unwrap ()error {return _ag ._f };func Error (processName ,message string )error {return _e (message ,processName )};type processError struct{_g string ;
_d string ;_ge string ;_f error ;};func _e (_af ,_ca string )*processError {return &processError {_g :"\u005b\u0055\u006e\u0069\u0050\u0044\u0046\u005d",_ge :_af ,_d :_ca };};