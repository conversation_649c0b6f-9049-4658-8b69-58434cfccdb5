//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package mmr ;import (_c "errors";_b "fmt";_da "github.com/unidoc/unipdf/v4/common";_gb "github.com/unidoc/unipdf/v4/internal/bitwise";_e "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";_d "io";);func (_fee *Decoder )UncompressMMR ()(_aac *_e .Bitmap ,_gf error ){_aac =_e .New (_fee ._deb ,_fee ._ceb );
_ad :=make ([]int ,_aac .Width +5);_def :=make ([]int ,_aac .Width +5);_def [0]=_aac .Width ;_eb :=1;var _fff int ;for _dbg :=0;_dbg < _aac .Height ;_dbg ++{_fff ,_gf =_fee .uncompress2d (_fee ._fbd ,_def ,_eb ,_ad ,_aac .Width );if _gf !=nil {return nil ,_gf ;
};if _fff ==EOF {break ;};if _fff > 0{_gf =_fee .fillBitmap (_aac ,_dbg ,_ad ,_fff );if _gf !=nil {return nil ,_gf ;};};_def ,_ad =_ad ,_def ;_eb =_fff ;};if _gf =_fee .detectAndSkipEOL ();_gf !=nil {return nil ,_gf ;};_fee ._fbd .align ();return _aac ,nil ;
};func (_dbc *Decoder )detectAndSkipEOL ()error {for {_fbb ,_gcg :=_dbc ._fbd .uncompressGetCode (_dbc ._cf );if _gcg !=nil {return _gcg ;};if _fbb !=nil &&_fbb ._f ==EOL {_dbc ._fbd ._cgde +=_fbb ._bc ;}else {return nil ;};};};func _cg (_fg [3]int )*code {return &code {_bc :_fg [0],_bcf :_fg [1],_f :_fg [2]}};
const (_a mmrCode =iota ;_dgc ;_fgg ;_ee ;_fb ;_ec ;_db ;_cad ;_be ;_ae ;_gc ;);func (_eff *runData )uncompressGetCode (_bgd []*code )(*code ,error ){return _eff .uncompressGetCodeLittleEndian (_bgd );};type code struct{_bc int ;_bcf int ;_f int ;_df []*code ;
_fc bool ;};const (_adbd int =1024<<7;_eag int =3;_cdg uint =24;);func (_gg *Decoder )uncompress2d (_cab *runData ,_age []int ,_aeg int ,_gcba []int ,_caa int )(int ,error ){var (_eeb int ;_bfd int ;_beg int ;_dfbe =true ;_cfa error ;_cgb *code ;);_age [_aeg ]=_caa ;
_age [_aeg +1]=_caa ;_age [_aeg +2]=_caa +1;_age [_aeg +3]=_caa +1;_eea :for _beg < _caa {_cgb ,_cfa =_cab .uncompressGetCode (_gg ._cf );if _cfa !=nil {return EOL ,nil ;};if _cgb ==nil {_cab ._cgde ++;break _eea ;};_cab ._cgde +=_cgb ._bc ;switch mmrCode (_cgb ._f ){case _fgg :_beg =_age [_eeb ];
case _ee :_beg =_age [_eeb ]+1;case _db :_beg =_age [_eeb ]-1;case _dgc :for {var _ccef []*code ;if _dfbe {_ccef =_gg ._aa ;}else {_ccef =_gg ._dac ;};_cgb ,_cfa =_cab .uncompressGetCode (_ccef );if _cfa !=nil {return 0,_cfa ;};if _cgb ==nil {break _eea ;
};_cab ._cgde +=_cgb ._bc ;if _cgb ._f < 64{if _cgb ._f < 0{_gcba [_bfd ]=_beg ;_bfd ++;_cgb =nil ;break _eea ;};_beg +=_cgb ._f ;_gcba [_bfd ]=_beg ;_bfd ++;break ;};_beg +=_cgb ._f ;};_bbe :=_beg ;_fa :for {var _dacb []*code ;if !_dfbe {_dacb =_gg ._aa ;
}else {_dacb =_gg ._dac ;};_cgb ,_cfa =_cab .uncompressGetCode (_dacb );if _cfa !=nil {return 0,_cfa ;};if _cgb ==nil {break _eea ;};_cab ._cgde +=_cgb ._bc ;if _cgb ._f < 64{if _cgb ._f < 0{_gcba [_bfd ]=_beg ;_bfd ++;break _eea ;};_beg +=_cgb ._f ;if _beg < _caa ||_beg !=_bbe {_gcba [_bfd ]=_beg ;
_bfd ++;};break _fa ;};_beg +=_cgb ._f ;};for _beg < _caa &&_age [_eeb ]<=_beg {_eeb +=2;};continue _eea ;case _a :_eeb ++;_beg =_age [_eeb ];_eeb ++;continue _eea ;case _fb :_beg =_age [_eeb ]+2;case _cad :_beg =_age [_eeb ]-2;case _ec :_beg =_age [_eeb ]+3;
case _be :_beg =_age [_eeb ]-3;default:if _cab ._cgde ==12&&_cgb ._f ==EOL {_cab ._cgde =0;if _ ,_cfa =_gg .uncompress1d (_cab ,_age ,_caa );_cfa !=nil {return 0,_cfa ;};_cab ._cgde ++;if _ ,_cfa =_gg .uncompress1d (_cab ,_gcba ,_caa );_cfa !=nil {return 0,_cfa ;
};_eaad ,_cdda :=_gg .uncompress1d (_cab ,_age ,_caa );if _cdda !=nil {return EOF ,_cdda ;};_cab ._cgde ++;return _eaad ,nil ;};_beg =_caa ;continue _eea ;};if _beg <=_caa {_dfbe =!_dfbe ;_gcba [_bfd ]=_beg ;_bfd ++;if _eeb > 0{_eeb --;}else {_eeb ++;};
for _beg < _caa &&_age [_eeb ]<=_beg {_eeb +=2;};};};if _gcba [_bfd ]!=_caa {_gcba [_bfd ]=_caa ;};if _cgb ==nil {return EOL ,nil ;};return _bfd ,nil ;};func (_ccb *runData )align (){_ccb ._cgde =((_ccb ._cgde +7)>>3)<<3};func (_gad *runData )uncompressGetNextCodeLittleEndian ()(int ,error ){_fcda :=_gad ._cgde -_gad ._geg ;
if _fcda < 0||_fcda > 24{_bdb :=(_gad ._cgde >>3)-_gad ._adfa ;if _bdb >=_gad ._egd {_bdb +=_gad ._adfa ;if _cec :=_gad .fillBuffer (_bdb );_cec !=nil {return 0,_cec ;};_bdb -=_gad ._adfa ;};_dgb :=(uint32 (_gad ._feb [_bdb ]&0xFF)<<16)|(uint32 (_gad ._feb [_bdb +1]&0xFF)<<8)|(uint32 (_gad ._feb [_bdb +2]&0xFF));
_ega :=uint32 (_gad ._cgde &7);_dgb <<=_ega ;_gad ._ffde =int (_dgb );}else {_ccba :=_gad ._geg &7;_dff :=7-_ccba ;if _fcda <=_dff {_gad ._ffde <<=uint (_fcda );}else {_gce :=(_gad ._geg >>3)+3-_gad ._adfa ;if _gce >=_gad ._egd {_gce +=_gad ._adfa ;if _ab :=_gad .fillBuffer (_gce );
_ab !=nil {return 0,_ab ;};_gce -=_gad ._adfa ;};_ccba =8-_ccba ;for {_gad ._ffde <<=uint (_ccba );_gad ._ffde |=int (uint (_gad ._feb [_gce ])&0xFF);_fcda -=_ccba ;_gce ++;_ccba =8;if !(_fcda >=8){break ;};};_gad ._ffde <<=uint (_fcda );};};_gad ._geg =_gad ._cgde ;
return _gad ._ffde ,nil ;};func (_dgd *runData )fillBuffer (_gbe int )error {_dgd ._adfa =_gbe ;_ ,_egda :=_dgd ._dca .Seek (int64 (_gbe ),_d .SeekStart );if _egda !=nil {if _egda ==_d .EOF {_da .Log .Debug ("\u0053\u0065\u0061\u006b\u0020\u0045\u004f\u0046");
_dgd ._egd =-1;}else {return _egda ;};};if _egda ==nil {_dgd ._egd ,_egda =_dgd ._dca .Read (_dgd ._feb );if _egda !=nil {if _egda ==_d .EOF {_da .Log .Trace ("\u0052\u0065\u0061\u0064\u0020\u0045\u004f\u0046");_dgd ._egd =-1;}else {return _egda ;};};};
if _dgd ._egd > -1&&_dgd ._egd < 3{for _dgd ._egd < 3{_aed ,_ead :=_dgd ._dca .ReadByte ();if _ead !=nil {if _ead ==_d .EOF {_dgd ._feb [_dgd ._egd ]=0;}else {return _ead ;};}else {_dgd ._feb [_dgd ._egd ]=_aed &0xFF;};_dgd ._egd ++;};};_dgd ._egd -=3;
if _dgd ._egd < 0{_dgd ._feb =make ([]byte ,len (_dgd ._feb ));_dgd ._egd =len (_dgd ._feb )-3;};return nil ;};func New (r *_gb .Reader ,width ,height int ,dataOffset ,dataLength int64 )(*Decoder ,error ){_fd :=&Decoder {_deb :width ,_ceb :height };_dba ,_fgf :=r .NewPartialReader (int (dataOffset ),int (dataLength ),false );
if _fgf !=nil {return nil ,_fgf ;};_ed ,_fgf :=_ffc (_dba );if _fgf !=nil {return nil ,_fgf ;};_ ,_fgf =r .Seek (_dba .RelativePosition (),_d .SeekCurrent );if _fgf !=nil {return nil ,_fgf ;};_fd ._fbd =_ed ;if _gac :=_fd .initTables ();_gac !=nil {return nil ,_gac ;
};return _fd ,nil ;};type mmrCode int ;func (_gd *Decoder )fillBitmap (_gbg *_e .Bitmap ,_adf int ,_gbgb []int ,_ge int )error {var _gaa byte ;_aad :=0;_af :=_gbg .GetByteIndex (_aad ,_adf );for _eg :=0;_eg < _ge ;_eg ++{_dc :=byte (1);_ddb :=_gbgb [_eg ];
if (_eg &1)==0{_dc =0;};for _aad < _ddb {_gaa =(_gaa <<1)|_dc ;_aad ++;if (_aad &7)==0{if _ag :=_gbg .SetByte (_af ,_gaa );_ag !=nil {return _ag ;};_af ++;_gaa =0;};};};if (_aad &7)!=0{_gaa <<=uint (8-(_aad &7));if _acd :=_gbg .SetByte (_af ,_gaa );_acd !=nil {return _acd ;
};};return nil ;};const (EOF =-3;_de =-2;EOL =-1;_bd =8;_ff =(1<<_bd )-1;_ce =5;_eee =(1<<_ce )-1;);func (_eae *runData )uncompressGetCodeLittleEndian (_bbc []*code )(*code ,error ){_egdc ,_bbcg :=_eae .uncompressGetNextCodeLittleEndian ();if _bbcg !=nil {_da .Log .Debug ("\u0055n\u0063\u006fm\u0070\u0072\u0065\u0073s\u0047\u0065\u0074N\u0065\u0078\u0074\u0043\u006f\u0064\u0065\u004c\u0069tt\u006c\u0065\u0045n\u0064\u0069a\u006e\u0020\u0066\u0061\u0069\u006ce\u0064\u003a \u0025\u0076",_bbcg );
return nil ,_bbcg ;};_egdc &=0xffffff;_ccefa :=_egdc >>(_cdg -_bd );_edfa :=_bbc [_ccefa ];if _edfa !=nil &&_edfa ._fc {_ccefa =(_egdc >>(_cdg -_bd -_ce ))&_eee ;_edfa =_edfa ._df [_ccefa ];};return _edfa ,nil ;};var (_dfb =[][3]int {{4,0x1,int (_a )},{3,0x1,int (_dgc )},{1,0x1,int (_fgg )},{3,0x3,int (_ee )},{6,0x3,int (_fb )},{7,0x3,int (_ec )},{3,0x2,int (_db )},{6,0x2,int (_cad )},{7,0x2,int (_be )},{10,0xf,int (_ae )},{12,0xf,int (_gc )},{12,0x1,int (EOL )}};
_beb =[][3]int {{4,0x07,2},{4,0x08,3},{4,0x0B,4},{4,0x0C,5},{4,0x0E,6},{4,0x0F,7},{5,0x12,128},{5,0x13,8},{5,0x14,9},{5,0x1B,64},{5,0x07,10},{5,0x08,11},{6,0x17,192},{6,0x18,1664},{6,0x2A,16},{6,0x2B,17},{6,0x03,13},{6,0x34,14},{6,0x35,15},{6,0x07,1},{6,0x08,12},{7,0x13,26},{7,0x17,21},{7,0x18,28},{7,0x24,27},{7,0x27,18},{7,0x28,24},{7,0x2B,25},{7,0x03,22},{7,0x37,256},{7,0x04,23},{7,0x08,20},{7,0xC,19},{8,0x12,33},{8,0x13,34},{8,0x14,35},{8,0x15,36},{8,0x16,37},{8,0x17,38},{8,0x1A,31},{8,0x1B,32},{8,0x02,29},{8,0x24,53},{8,0x25,54},{8,0x28,39},{8,0x29,40},{8,0x2A,41},{8,0x2B,42},{8,0x2C,43},{8,0x2D,44},{8,0x03,30},{8,0x32,61},{8,0x33,62},{8,0x34,63},{8,0x35,0},{8,0x36,320},{8,0x37,384},{8,0x04,45},{8,0x4A,59},{8,0x4B,60},{8,0x5,46},{8,0x52,49},{8,0x53,50},{8,0x54,51},{8,0x55,52},{8,0x58,55},{8,0x59,56},{8,0x5A,57},{8,0x5B,58},{8,0x64,448},{8,0x65,512},{8,0x67,640},{8,0x68,576},{8,0x0A,47},{8,0x0B,48},{9,0x01,_de },{9,0x98,1472},{9,0x99,1536},{9,0x9A,1600},{9,0x9B,1728},{9,0xCC,704},{9,0xCD,768},{9,0xD2,832},{9,0xD3,896},{9,0xD4,960},{9,0xD5,1024},{9,0xD6,1088},{9,0xD7,1152},{9,0xD8,1216},{9,0xD9,1280},{9,0xDA,1344},{9,0xDB,1408},{10,0x01,_de },{11,0x01,_de },{11,0x08,1792},{11,0x0C,1856},{11,0x0D,1920},{12,0x00,EOF },{12,0x01,EOL },{12,0x12,1984},{12,0x13,2048},{12,0x14,2112},{12,0x15,2176},{12,0x16,2240},{12,0x17,2304},{12,0x1C,2368},{12,0x1D,2432},{12,0x1E,2496},{12,0x1F,2560}};
_eeg =[][3]int {{2,0x02,3},{2,0x03,2},{3,0x02,1},{3,0x03,4},{4,0x02,6},{4,0x03,5},{5,0x03,7},{6,0x04,9},{6,0x05,8},{7,0x04,10},{7,0x05,11},{7,0x07,12},{8,0x04,13},{8,0x07,14},{9,0x01,_de },{9,0x18,15},{10,0x01,_de },{10,0x17,16},{10,0x18,17},{10,0x37,0},{10,0x08,18},{10,0x0F,64},{11,0x01,_de },{11,0x17,24},{11,0x18,25},{11,0x28,23},{11,0x37,22},{11,0x67,19},{11,0x68,20},{11,0x6C,21},{11,0x08,1792},{11,0x0C,1856},{11,0x0D,1920},{12,0x00,EOF },{12,0x01,EOL },{12,0x12,1984},{12,0x13,2048},{12,0x14,2112},{12,0x15,2176},{12,0x16,2240},{12,0x17,2304},{12,0x1C,2368},{12,0x1D,2432},{12,0x1E,2496},{12,0x1F,2560},{12,0x24,52},{12,0x27,55},{12,0x28,56},{12,0x2B,59},{12,0x2C,60},{12,0x33,320},{12,0x34,384},{12,0x35,448},{12,0x37,53},{12,0x38,54},{12,0x52,50},{12,0x53,51},{12,0x54,44},{12,0x55,45},{12,0x56,46},{12,0x57,47},{12,0x58,57},{12,0x59,58},{12,0x5A,61},{12,0x5B,256},{12,0x64,48},{12,0x65,49},{12,0x66,62},{12,0x67,63},{12,0x68,30},{12,0x69,31},{12,0x6A,32},{12,0x6B,33},{12,0x6C,40},{12,0x6D,41},{12,0xC8,128},{12,0xC9,192},{12,0xCA,26},{12,0xCB,27},{12,0xCC,28},{12,0xCD,29},{12,0xD2,34},{12,0xD3,35},{12,0xD4,36},{12,0xD5,37},{12,0xD6,38},{12,0xD7,39},{12,0xDA,42},{12,0xDB,43},{13,0x4A,640},{13,0x4B,704},{13,0x4C,768},{13,0x4D,832},{13,0x52,1280},{13,0x53,1344},{13,0x54,1408},{13,0x55,1472},{13,0x5A,1536},{13,0x5B,1600},{13,0x64,1664},{13,0x65,1728},{13,0x6C,512},{13,0x6D,576},{13,0x72,896},{13,0x73,960},{13,0x74,1024},{13,0x75,1088},{13,0x76,1152},{13,0x77,1216}};
);func (_eef *Decoder )createLittleEndianTable (_cd [][3]int )([]*code ,error ){_gcb :=make ([]*code ,_ff +1);for _ac :=0;_ac < len (_cd );_ac ++{_bb :=_cg (_cd [_ac ]);if _bb ._bc <=_bd {_dd :=_bd -_bb ._bc ;_adb :=_bb ._bcf <<uint (_dd );for _dad :=(1<<uint (_dd ))-1;
_dad >=0;_dad --{_dfe :=_adb |_dad ;_gcb [_dfe ]=_bb ;};}else {_bf :=_bb ._bcf >>uint (_bb ._bc -_bd );if _gcb [_bf ]==nil {var _fbf =_cg ([3]int {});_fbf ._df =make ([]*code ,_eee +1);_gcb [_bf ]=_fbf ;};if _bb ._bc <=_bd +_ce {_cdd :=_bd +_ce -_bb ._bc ;
_eaa :=(_bb ._bcf <<uint (_cdd ))&_eee ;_gcb [_bf ]._fc =true ;for _cef :=(1<<uint (_cdd ))-1;_cef >=0;_cef --{_gcb [_bf ]._df [_eaa |_cef ]=_bb ;};}else {return nil ,_c .New ("\u0043\u006f\u0064\u0065\u0020\u0074a\u0062\u006c\u0065\u0020\u006f\u0076\u0065\u0072\u0066\u006c\u006f\u0077\u0020i\u006e\u0020\u004d\u004d\u0052\u0044\u0065c\u006f\u0064\u0065\u0072");
};};};return _gcb ,nil ;};type runData struct{_dca *_gb .Reader ;_cgde int ;_geg int ;_ffde int ;_feb []byte ;_adfa int ;_egd int ;};func _ea (_ef ,_ga int )int {if _ef > _ga {return _ga ;};return _ef ;};func (_dfef *Decoder )uncompress1d (_fcd *runData ,_gag []int ,_ddbe int )(int ,error ){var (_fec =true ;
_edf int ;_fed *code ;_fgff int ;_bge error ;);_cce :for _edf < _ddbe {_aada :for {if _fec {_fed ,_bge =_fcd .uncompressGetCode (_dfef ._aa );if _bge !=nil {return 0,_bge ;};}else {_fed ,_bge =_fcd .uncompressGetCode (_dfef ._dac );if _bge !=nil {return 0,_bge ;
};};_fcd ._cgde +=_fed ._bc ;if _fed ._f < 0{break _cce ;};_edf +=_fed ._f ;if _fed ._f < 64{_fec =!_fec ;_gag [_fgff ]=_edf ;_fgff ++;break _aada ;};};};if _gag [_fgff ]!=_ddbe {_gag [_fgff ]=_ddbe ;};_cddg :=EOL ;if _fed !=nil &&_fed ._f !=EOL {_cddg =_fgff ;
};return _cddg ,nil ;};func (_ffd *Decoder )initTables ()(_cgd error ){if _ffd ._aa ==nil {_ffd ._aa ,_cgd =_ffd .createLittleEndianTable (_beb );if _cgd !=nil {return ;};_ffd ._dac ,_cgd =_ffd .createLittleEndianTable (_eeg );if _cgd !=nil {return ;};
_ffd ._cf ,_cgd =_ffd .createLittleEndianTable (_dfb );if _cgd !=nil {return ;};};return nil ;};type Decoder struct{_deb ,_ceb int ;_fbd *runData ;_aa []*code ;_dac []*code ;_cf []*code ;};func _ca (_fe ,_bg int )int {if _fe < _bg {return _bg ;};return _fe ;
};func (_dg *code )String ()string {return _b .Sprintf ("\u0025\u0064\u002f\u0025\u0064\u002f\u0025\u0064",_dg ._bc ,_dg ._bcf ,_dg ._f );};func _ffc (_aef *_gb .Reader )(*runData ,error ){_aab :=&runData {_dca :_aef ,_cgde :0,_geg :1};_fge :=_ea (_ca (_eag ,int (_aef .Length ())),_adbd );
_aab ._feb =make ([]byte ,_fge );if _fac :=_aab .fillBuffer (0);_fac !=nil {if _fac ==_d .EOF {_aab ._feb =make ([]byte ,10);_da .Log .Debug ("F\u0069\u006c\u006c\u0042uf\u0066e\u0072\u0020\u0066\u0061\u0069l\u0065\u0064\u003a\u0020\u0025\u0076",_fac );
}else {return nil ,_fac ;};};return _aab ,nil ;};