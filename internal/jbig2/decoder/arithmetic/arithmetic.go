//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package arithmetic ;import (_e "fmt";_cg "github.com/unidoc/unipdf/v4/common";_cge "github.com/unidoc/unipdf/v4/internal/bitwise";_cc "github.com/unidoc/unipdf/v4/internal/jbig2/internal";_a "io";_d "strings";);func (_ca *Decoder )DecodeBit (stats *DecoderStats )(int ,error ){var (_fd int ;
_bb =_dd [stats .cx ()][0];_fg =int32 (stats .cx ()););defer func (){_ca ._da ++}();_ca ._ad -=_bb ;if (_ca ._b >>16)< uint64 (_bb ){_fd =_ca .lpsExchange (stats ,_fg ,_bb );if _fa :=_ca .renormalize ();_fa !=nil {return 0,_fa ;};}else {_ca ._b -=uint64 (_bb )<<16;
if (_ca ._ad &0x8000)==0{_fd =_ca .mpsExchange (stats ,_fg );if _ebb :=_ca .renormalize ();_ebb !=nil {return 0,_ebb ;};}else {_fd =int (stats .getMps ());};};return _fd ,nil ;};func (_dg *Decoder )DecodeInt (stats *DecoderStats )(int32 ,error ){var (_ef ,_af int32 ;
_bbb ,_dag ,_ge int ;_fb error ;);if stats ==nil {stats =NewStats (512,1);};_dg ._df =1;_dag ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};_bbb ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _bbb ==1{_bbb ,_fb =_dg .decodeIntBit (stats );
if _fb !=nil {return 0,_fb ;};if _bbb ==1{_bbb ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _bbb ==1{_bbb ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _bbb ==1{_bbb ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;
};if _bbb ==1{_ge =32;_af =4436;}else {_ge =12;_af =340;};}else {_ge =8;_af =84;};}else {_ge =6;_af =20;};}else {_ge =4;_af =4;};}else {_ge =2;_af =0;};for _fc :=0;_fc < _ge ;_fc ++{_bbb ,_fb =_dg .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};_ef =(_ef <<1)|int32 (_bbb );
};_ef +=_af ;if _dag ==0{return _ef ,nil ;}else if _dag ==1&&_ef > 0{return -_ef ,nil ;};return 0,_cc .ErrOOB ;};func (_ea *DecoderStats )SetIndex (index int32 ){_ea ._dae =index };func NewStats (contextSize int32 ,index int32 )*DecoderStats {return &DecoderStats {_dae :index ,_cgea :contextSize ,_fbf :make ([]byte ,contextSize ),_ddf :make ([]byte ,contextSize )};
};func (_fdg *Decoder )renormalize ()error {for {if _fdg ._cga ==0{if _gef :=_fdg .readByte ();_gef !=nil {return _gef ;};};_fdg ._ad <<=1;_fdg ._b <<=1;_fdg ._cga --;if (_fdg ._ad &0x8000)!=0{break ;};};_fdg ._b &=0xffffffff;return nil ;};func (_ff *Decoder )DecodeIAID (codeLen uint64 ,stats *DecoderStats )(int64 ,error ){_ff ._df =1;
var _ee uint64 ;for _ee =0;_ee < codeLen ;_ee ++{stats .SetIndex (int32 (_ff ._df ));_cf ,_fgf :=_ff .DecodeBit (stats );if _fgf !=nil {return 0,_fgf ;};_ff ._df =(_ff ._df <<1)|int64 (_cf );};_daf :=_ff ._df -(1<<codeLen );return _daf ,nil ;};func (_bf *Decoder )init ()error {_bf ._gf =_bf ._g .AbsolutePosition ();
_dac ,_bfc :=_bf ._g .ReadByte ();if _bfc !=nil {_cg .Log .Debug ("B\u0075\u0066\u0066\u0065\u0072\u0030 \u0072\u0065\u0061\u0064\u0042\u0079\u0074\u0065\u0020f\u0061\u0069\u006ce\u0064.\u0020\u0025\u0076",_bfc );return _bfc ;};_bf ._f =_dac ;_bf ._b =uint64 (_dac )<<16;
if _bfc =_bf .readByte ();_bfc !=nil {return _bfc ;};_bf ._b <<=7;_bf ._cga -=7;_bf ._ad =0x8000;_bf ._da ++;return nil ;};func (_aa *DecoderStats )Reset (){for _bfd :=0;_bfd < len (_aa ._fbf );_bfd ++{_aa ._fbf [_bfd ]=0;_aa ._ddf [_bfd ]=0;};};func (_dcc *DecoderStats )cx ()byte {return _dcc ._fbf [_dcc ._dae ]};
var (_dd =[][4]uint32 {{0x5601,1,1,1},{0x3401,2,6,0},{0x1801,3,9,0},{0x0AC1,4,12,0},{0x0521,5,29,0},{0x0221,38,33,0},{0x5601,7,6,1},{0x5401,8,14,0},{0x4801,9,14,0},{0x3801,10,14,0},{0x3001,11,17,0},{0x2401,12,18,0},{0x1C01,13,20,0},{0x1601,29,21,0},{0x5601,15,14,1},{0x5401,16,14,0},{0x5101,17,15,0},{0x4801,18,16,0},{0x3801,19,17,0},{0x3401,20,18,0},{0x3001,21,19,0},{0x2801,22,19,0},{0x2401,23,20,0},{0x2201,24,21,0},{0x1C01,25,22,0},{0x1801,26,23,0},{0x1601,27,24,0},{0x1401,28,25,0},{0x1201,29,26,0},{0x1101,30,27,0},{0x0AC1,31,28,0},{0x09C1,32,29,0},{0x08A1,33,30,0},{0x0521,34,31,0},{0x0441,35,32,0},{0x02A1,36,33,0},{0x0221,37,34,0},{0x0141,38,35,0},{0x0111,39,36,0},{0x0085,40,37,0},{0x0049,41,38,0},{0x0025,42,39,0},{0x0015,43,40,0},{0x0009,44,41,0},{0x0005,45,42,0},{0x0001,45,43,0},{0x5601,46,46,0}};
);func (_fgc *DecoderStats )Overwrite (dNew *DecoderStats ){for _dfc :=0;_dfc < len (_fgc ._fbf );_dfc ++{_fgc ._fbf [_dfc ]=dNew ._fbf [_dfc ];_fgc ._ddf [_dfc ]=dNew ._ddf [_dfc ];};};func (_ecg *Decoder )mpsExchange (_cab *DecoderStats ,_cbf int32 )int {_ag :=_cab ._ddf [_cab ._dae ];
if _ecg ._ad < _dd [_cbf ][0]{if _dd [_cbf ][3]==1{_cab .toggleMps ();};_cab .setEntry (int (_dd [_cbf ][2]));return int (1-_ag );};_cab .setEntry (int (_dd [_cbf ][1]));return int (_ag );};func (_fbg *DecoderStats )Copy ()*DecoderStats {_gbc :=&DecoderStats {_cgea :_fbg ._cgea ,_fbf :make ([]byte ,_fbg ._cgea )};
copy (_gbc ._fbf ,_fbg ._fbf );return _gbc ;};func (_ddd *Decoder )lpsExchange (_cbg *DecoderStats ,_cfe int32 ,_fgg uint32 )int {_abc :=_cbg .getMps ();if _ddd ._ad < _fgg {_cbg .setEntry (int (_dd [_cfe ][1]));_ddd ._ad =_fgg ;return int (_abc );};if _dd [_cfe ][3]==1{_cbg .toggleMps ();
};_cbg .setEntry (int (_dd [_cfe ][2]));_ddd ._ad =_fgg ;return int (1-_abc );};type DecoderStats struct{_dae int32 ;_cgea int32 ;_fbf []byte ;_ddf []byte ;};func (_ecc *DecoderStats )String ()string {_dcb :=&_d .Builder {};_dcb .WriteString (_e .Sprintf ("S\u0074\u0061\u0074\u0073\u003a\u0020\u0020\u0025\u0064\u000a",len (_ecc ._fbf )));
for _bc ,_ead :=range _ecc ._fbf {if _ead !=0{_dcb .WriteString (_e .Sprintf ("N\u006f\u0074\u0020\u007aer\u006f \u0061\u0074\u003a\u0020\u0025d\u0020\u002d\u0020\u0025\u0064\u000a",_bc ,_ead ));};};return _dcb .String ();};func (_cb *Decoder )decodeIntBit (_dc *DecoderStats )(int ,error ){_dc .SetIndex (int32 (_cb ._df ));
_ac ,_fe :=_cb .DecodeBit (_dc );if _fe !=nil {_cg .Log .Debug ("\u0041\u0072\u0069\u0074\u0068\u006d\u0065t\u0069\u0063\u0044e\u0063\u006f\u0064e\u0072\u0020'\u0064\u0065\u0063\u006f\u0064\u0065I\u006etB\u0069\u0074\u0027\u002d\u003e\u0020\u0044\u0065\u0063\u006f\u0064\u0065\u0042\u0069\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_fe );
return _ac ,_fe ;};if _cb ._df < 256{_cb ._df =((_cb ._df <<uint64 (1))|int64 (_ac ))&0x1ff;}else {_cb ._df =(((_cb ._df <<uint64 (1)|int64 (_ac ))&511)|256)&0x1ff;};return _ac ,nil ;};func New (r *_cge .Reader )(*Decoder ,error ){_eb :=&Decoder {_g :r ,ContextSize :[]uint32 {16,13,10,10},ReferedToContextSize :[]uint32 {13,10}};
if _ed :=_eb .init ();_ed !=nil {return nil ,_ed ;};return _eb ,nil ;};func (_abcc *DecoderStats )setEntry (_cfeg int ){_ebg :=byte (_cfeg &0x7f);_abcc ._fbf [_abcc ._dae ]=_ebg ;};func (_fbc *DecoderStats )toggleMps (){_fbc ._ddf [_fbc ._dae ]^=1};type Decoder struct{ContextSize []uint32 ;
ReferedToContextSize []uint32 ;_g *_cge .Reader ;_f uint8 ;_b uint64 ;_ad uint32 ;_df int64 ;_cga int32 ;_da int32 ;_gf int64 ;};func (_gb *Decoder )readByte ()error {if _gb ._g .AbsolutePosition ()> _gb ._gf {if _ ,_efd :=_gb ._g .Seek (-1,_a .SeekCurrent );
_efd !=nil {return _efd ;};};_ab ,_db :=_gb ._g .ReadByte ();if _db !=nil {return _db ;};_gb ._f =_ab ;if _gb ._f ==0xFF{_gc ,_bg :=_gb ._g .ReadByte ();if _bg !=nil {return _bg ;};if _gc > 0x8F{_gb ._b +=0xFF00;_gb ._cga =8;if _ ,_eg :=_gb ._g .Seek (-2,_a .SeekCurrent );
_eg !=nil {return _eg ;};}else {_gb ._b +=uint64 (_gc )<<9;_gb ._cga =7;};}else {_ab ,_db =_gb ._g .ReadByte ();if _db !=nil {return _db ;};_gb ._f =_ab ;_gb ._b +=uint64 (_gb ._f )<<8;_gb ._cga =8;};_gb ._b &=0xFFFFFFFFFF;return nil ;};func (_ecgd *DecoderStats )getMps ()byte {return _ecgd ._ddf [_ecgd ._dae ]};
