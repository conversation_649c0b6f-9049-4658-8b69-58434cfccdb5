//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package document ;import (_b "encoding/binary";_f "fmt";_e "github.com/unidoc/unipdf/v4/common";_fb "github.com/unidoc/unipdf/v4/internal/bitwise";_d "github.com/unidoc/unipdf/v4/internal/jbig2/basic";_ad "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";
_gd "github.com/unidoc/unipdf/v4/internal/jbig2/document/segments";_gb "github.com/unidoc/unipdf/v4/internal/jbig2/encoder/classer";_ff "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_af "io";_ba "math";_a "runtime/debug";);func (_afgb *Document )isFileHeaderPresent ()(bool ,error ){_afgb .InputStream .Mark ();
for _ ,_cgc :=range _df {_ada ,_dcd :=_afgb .InputStream .ReadByte ();if _dcd !=nil {return false ,_dcd ;};if _cgc !=_ada {_afgb .InputStream .Reset ();return false ,nil ;};};_afgb .InputStream .Reset ();return true ,nil ;};func (_abd *Document )encodeFileHeader (_eecb _fb .BinaryWriter )(_acge int ,_fgd error ){const _bed ="\u0065\u006ec\u006f\u0064\u0065F\u0069\u006c\u0065\u0048\u0065\u0061\u0064\u0065\u0072";
_acge ,_fgd =_eecb .Write (_df );if _fgd !=nil {return _acge ,_ff .Wrap (_fgd ,_bed ,"\u0069\u0064");};if _fgd =_eecb .WriteByte (0x01);_fgd !=nil {return _acge ,_ff .Wrap (_fgd ,_bed ,"\u0066\u006c\u0061g\u0073");};_acge ++;_gdga :=make ([]byte ,4);_b .BigEndian .PutUint32 (_gdga ,_abd .NumberOfPages );
_bec ,_fgd :=_eecb .Write (_gdga );if _fgd !=nil {return _bec ,_ff .Wrap (_fgd ,_bed ,"p\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072");};_acge +=_bec ;return _acge ,nil ;};func (_eec *Document )GetGlobalSegment (i int )(*_gd .Header ,error ){_bgf ,_eda :=_eec .GlobalSegments .GetSegment (i );
if _eda !=nil {return nil ,_ff .Wrap (_eda ,"\u0047\u0065t\u0047\u006c\u006fb\u0061\u006c\u0053\u0065\u0067\u006d\u0065\u006e\u0074","");};return _bgf ,nil ;};func (_ee *Document )AddClassifiedPage (bm *_ad .Bitmap ,method _gb .Method )(_db error ){const _gbf ="\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0041\u0064d\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064P\u0061\u0067\u0065";
if !_ee .FullHeaders &&_ee .NumberOfPages !=0{return _ff .Error (_gbf ,"\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0070\u0061\u0067\u0065\u002e\u0020\u0046\u0069\u006c\u0065\u004d\u006f\u0064\u0065\u0020\u0064\u0069\u0073\u0061\u006c\u006c\u006f\u0077\u0073\u0020\u0061\u0064\u0064i\u006e\u0067\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e \u006f\u006e\u0065\u0020\u0070\u0061g\u0065");
};if _ee .Classer ==nil {if _ee .Classer ,_db =_gb .Init (_gb .DefaultSettings ());_db !=nil {return _ff .Wrap (_db ,_gbf ,"");};};_ggd :=int (_ee .nextPageNumber ());_ebd :=&Page {Segments :[]*_gd .Header {},Bitmap :bm ,Document :_ee ,FinalHeight :bm .Height ,FinalWidth :bm .Width ,PageNumber :_ggd };
_ee .Pages [_ggd ]=_ebd ;switch method {case _gb .RankHaus :_ebd .EncodingMethod =RankHausEM ;case _gb .Correlation :_ebd .EncodingMethod =CorrelationEM ;};_ebd .AddPageInformationSegment ();if _db =_ee .Classer .AddPage (bm ,_ggd ,method );_db !=nil {return _ff .Wrap (_db ,_gbf ,"");
};if _ee .FullHeaders {_ebd .AddEndOfPageSegment ();};return nil ;};func _ga (_fag int )int {_ggde :=0;_afg :=(_fag &(_fag -1))==0;_fag >>=1;for ;_fag !=0;_fag >>=1{_ggde ++;};if _afg {return _ggde ;};return _ggde +1;};func (_ag *Document )nextPageNumber ()uint32 {_ag .NumberOfPages ++;
return _ag .NumberOfPages };func (_ddfc *Page )clearSegmentData (){for _bfd :=range _ddfc .Segments {_ddfc .Segments [_bfd ].CleanSegmentData ();};};func (_abdd *Page )getWidth ()(int ,error ){const _afe ="\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068";
if _abdd .FinalWidth !=0{return _abdd .FinalWidth ,nil ;};_baf :=_abdd .getPageInformationSegment ();if _baf ==nil {return 0,_ff .Error (_afe ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");};_bfc ,_gdeg :=_baf .GetSegmentData ();
if _gdeg !=nil {return 0,_ff .Wrap (_gdeg ,_afe ,"");};_dfd ,_bedd :=_bfc .(*_gd .PageInformationSegment );if !_bedd {return 0,_ff .Errorf (_afe ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_bfc );
};_abdd .FinalWidth =_dfd .PageBMWidth ;return _abdd .FinalWidth ,nil ;};func (_cag *Page )GetWidth ()(int ,error ){return _cag .getWidth ()};func (_acda *Page )countRegions ()int {var _cab int ;for _ ,_fff :=range _acda .Segments {switch _fff .Type {case 6,7,22,23,38,39,42,43:_cab ++;
};};return _cab ;};func (_ebg *Page )AddEndOfPageSegment (){_ccg :=&_gd .Header {Type :_gd .TEndOfPage ,PageAssociation :_ebg .PageNumber };_ebg .Segments =append (_ebg .Segments ,_ccg );};func (_efce *Page )createStripedPage (_adfg *_gd .PageInformationSegment )error {const _ced ="\u0063\u0072\u0065\u0061\u0074\u0065\u0053\u0074\u0072\u0069\u0070\u0065d\u0050\u0061\u0067\u0065";
_bdcd ,_afb :=_efce .collectPageStripes ();if _afb !=nil {return _ff .Wrap (_afb ,_ced ,"");};var _bggg int ;for _ ,_cage :=range _bdcd {if _feba ,_afdc :=_cage .(*_gd .EndOfStripe );_afdc {_bggg =_feba .LineNumber ()+1;}else {_cf :=_cage .(_gd .Regioner );
_ecf :=_cf .GetRegionInfo ();_feg :=_efce .getCombinationOperator (_adfg ,_ecf .CombinaionOperator );_ecd ,_fdff :=_cf .GetRegionBitmap ();if _fdff !=nil {return _ff .Wrap (_fdff ,_ced ,"");};_fdff =_ad .Blit (_ecd ,_efce .Bitmap ,int (_ecf .XLocation ),_bggg ,_feg );
if _fdff !=nil {return _ff .Wrap (_fdff ,_ced ,"");};};};return nil ;};func (_bg *Document )AddGenericPage (bm *_ad .Bitmap ,duplicateLineRemoval bool )(_ggc error ){const _gbg ="\u0044\u006f\u0063um\u0065\u006e\u0074\u002e\u0041\u0064\u0064\u0047\u0065\u006e\u0065\u0072\u0069\u0063\u0050\u0061\u0067\u0065";
if !_bg .FullHeaders &&_bg .NumberOfPages !=0{return _ff .Error (_gbg ,"\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0070\u0061\u0067\u0065\u002e\u0020\u0046\u0069\u006c\u0065\u004d\u006f\u0064\u0065\u0020\u0064\u0069\u0073\u0061\u006c\u006c\u006f\u0077\u0073\u0020\u0061\u0064\u0064i\u006e\u0067\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e \u006f\u006e\u0065\u0020\u0070\u0061g\u0065");
};_afa :=&Page {Segments :[]*_gd .Header {},Bitmap :bm ,Document :_bg ,FinalHeight :bm .Height ,FinalWidth :bm .Width ,IsLossless :true ,BlackIsOne :bm .Color ==_ad .Chocolate };_afa .PageNumber =int (_bg .nextPageNumber ());_bg .Pages [_afa .PageNumber ]=_afa ;
bm .InverseData ();_afa .AddPageInformationSegment ();if _ggc =_afa .AddGenericRegion (bm ,0,0,0,_gd .TImmediateGenericRegion ,duplicateLineRemoval );_ggc !=nil {return _ff .Wrap (_ggc ,_gbg ,"");};if _bg .FullHeaders {_afa .AddEndOfPageSegment ();};return nil ;
};func (_faff *Page )getResolutionY ()(int ,error ){const _aca ="\u0067\u0065\u0074\u0052\u0065\u0073\u006f\u006c\u0075t\u0069\u006f\u006e\u0059";if _faff .ResolutionY !=0{return _faff .ResolutionY ,nil ;};_abaa :=_faff .getPageInformationSegment ();if _abaa ==nil {return 0,_ff .Error (_aca ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");
};_ddc ,_cfb :=_abaa .GetSegmentData ();if _cfb !=nil {return 0,_ff .Wrap (_cfb ,_aca ,"");};_aeg ,_fcdd :=_ddc .(*_gd .PageInformationSegment );if !_fcdd {return 0,_ff .Errorf (_aca ,"\u0070\u0061\u0067\u0065\u0020\u0069\u006e\u0066o\u0072\u006d\u0061ti\u006f\u006e\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0074\u0079\u0070\u0065\u003a\u0027%\u0054\u0027",_ddc );
};_faff .ResolutionY =_aeg .ResolutionY ;return _faff .ResolutionY ,nil ;};type Page struct{Segments []*_gd .Header ;PageNumber int ;Bitmap *_ad .Bitmap ;FinalHeight int ;FinalWidth int ;ResolutionX int ;ResolutionY int ;IsLossless bool ;Document *Document ;
FirstSegmentNumber int ;EncodingMethod EncodingMethod ;BlackIsOne bool ;};func (_de *Page )GetResolutionY ()(int ,error ){return _de .getResolutionY ()};func (_dbbf *Document )mapData ()error {const _caa ="\u006da\u0070\u0044\u0061\u0074\u0061";var (_dfaf []*_gd .Header ;
_gcf int64 ;_ae _gd .Type ;);_cbc ,_ade :=_dbbf .isFileHeaderPresent ();if _ade !=nil {return _ff .Wrap (_ade ,_caa ,"");};if _cbc {if _ade =_dbbf .parseFileHeader ();_ade !=nil {return _ff .Wrap (_ade ,_caa ,"");};_gcf +=int64 (_dbbf ._eb );_dbbf .FullHeaders =true ;
};var (_ecb *Page ;_edaa bool ;);for _ae !=51&&!_edaa {_eg ,_acf :=_gd .NewHeader (_dbbf ,_dbbf .InputStream ,_gcf ,_dbbf .OrganizationType );if _acf !=nil {return _ff .Wrap (_acf ,_caa ,"");};_e .Log .Trace ("\u0044\u0065c\u006f\u0064\u0069\u006eg\u0020\u0073e\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u0075m\u0062\u0065\u0072\u003a\u0020\u0025\u0064\u002c\u0020\u0054\u0079\u0070e\u003a\u0020\u0025\u0073",_eg .SegmentNumber ,_eg .Type );
_ae =_eg .Type ;if _ae !=_gd .TEndOfFile {if _eg .PageAssociation !=0{_ecb =_dbbf .Pages [_eg .PageAssociation ];if _ecb ==nil {_ecb =_dbbe (_dbbf ,_eg .PageAssociation );_dbbf .Pages [_eg .PageAssociation ]=_ecb ;if _dbbf .NumberOfPagesUnknown {_dbbf .NumberOfPages ++;
};};_ecb .Segments =append (_ecb .Segments ,_eg );}else {_dbbf .GlobalSegments .AddSegment (_eg );};};_dfaf =append (_dfaf ,_eg );_gcf =_dbbf .InputStream .AbsolutePosition ();if _dbbf .OrganizationType ==_gd .OSequential {_gcf +=int64 (_eg .SegmentDataLength );
};_edaa ,_acf =_dbbf .reachedEOF (_gcf );if _acf !=nil {_e .Log .Debug ("\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0072\u0065\u0061\u0063h\u0065\u0064\u0020\u0045\u004f\u0046\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_acf );
return _ff .Wrap (_acf ,_caa ,"");};};_dbbf .determineRandomDataOffsets (_dfaf ,uint64 (_gcf ));return nil ;};func (_acd *Document )encodeEOFHeader (_bea _fb .BinaryWriter )(_gcb int ,_adf error ){_bgg :=&_gd .Header {SegmentNumber :_acd .nextSegmentNumber (),Type :_gd .TEndOfFile };
if _gcb ,_adf =_bgg .Encode (_bea );_adf !=nil {return 0,_ff .Wrap (_adf ,"\u0065n\u0063o\u0064\u0065\u0045\u004f\u0046\u0048\u0065\u0061\u0064\u0065\u0072","");};return _gcb ,nil ;};type Document struct{Pages map[int ]*Page ;NumberOfPagesUnknown bool ;
NumberOfPages uint32 ;GBUseExtTemplate bool ;InputStream *_fb .Reader ;GlobalSegments *Globals ;OrganizationType _gd .OrganizationType ;Classer *_gb .Classer ;XRes ,YRes int ;FullHeaders bool ;CurrentSegmentNumber uint32 ;AverageTemplates *_ad .Bitmaps ;
BaseIndexes []int ;Refinement bool ;RefineLevel int ;_eb uint8 ;_ef *_fb .BufferedWriter ;EncodeGlobals bool ;_gg int ;_ac map[int ][]int ;_fc map[int ][]int ;_gdg []int ;_fa map[int ]int ;};func (_aeb *Document )nextSegmentNumber ()uint32 {_ggcc :=_aeb .CurrentSegmentNumber ;
_aeb .CurrentSegmentNumber ++;return _ggcc ;};func (_cbe *Document )Encode ()(_fab []byte ,_bfb error ){const _dbb ="\u0044o\u0063u\u006d\u0065\u006e\u0074\u002e\u0045\u006e\u0063\u006f\u0064\u0065";var _abb ,_dgg int ;if _cbe .FullHeaders {if _abb ,_bfb =_cbe .encodeFileHeader (_cbe ._ef );
_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");};};var (_gdd bool ;_afde *_gd .Header ;_bega *Page ;);if _bfb =_cbe .completeClassifiedPages ();_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");};if _bfb =_cbe .produceClassifiedPages ();_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");
};if _cbe .GlobalSegments !=nil {for _ ,_afde =range _cbe .GlobalSegments .Segments {if _bfb =_cbe .encodeSegment (_afde ,&_abb );_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");};};};for _gdb :=1;_gdb <=int (_cbe .NumberOfPages );_gdb ++{if _bega ,_gdd =_cbe .Pages [_gdb ];
!_gdd {return nil ,_ff .Errorf (_dbb ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_gdb );};for _ ,_afde =range _bega .Segments {if _bfb =_cbe .encodeSegment (_afde ,&_abb );_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");
};};};if _cbe .FullHeaders {if _dgg ,_bfb =_cbe .encodeEOFHeader (_cbe ._ef );_bfb !=nil {return nil ,_ff .Wrap (_bfb ,_dbb ,"");};_abb +=_dgg ;};_fab =_cbe ._ef .Data ();if len (_fab )!=_abb {_e .Log .Debug ("\u0042\u0079\u0074\u0065\u0073 \u0077\u0072\u0069\u0074\u0074\u0065\u006e \u0028\u006e\u0029\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0064\u0061\u0074\u0061\u0020\u0065\u006e\u0063\u006fd\u0065\u0064\u003a\u0020\u0027\u0025d\u0027",_abb ,len (_fab ));
};return _fab ,nil ;};func (_fdgf *Page )collectPageStripes ()(_ccda []_gd .Segmenter ,_dcfd error ){const _gbbb ="\u0063o\u006cl\u0065\u0063\u0074\u0050\u0061g\u0065\u0053t\u0072\u0069\u0070\u0065\u0073";var _aba _gd .Segmenter ;for _ ,_dedg :=range _fdgf .Segments {switch _dedg .Type {case 6,7,22,23,38,39,42,43:_aba ,_dcfd =_dedg .GetSegmentData ();
if _dcfd !=nil {return nil ,_ff .Wrap (_dcfd ,_gbbb ,"");};_ccda =append (_ccda ,_aba );case 50:_aba ,_dcfd =_dedg .GetSegmentData ();if _dcfd !=nil {return nil ,_dcfd ;};_fdc ,_ecbb :=_aba .(*_gd .EndOfStripe );if !_ecbb {return nil ,_ff .Errorf (_gbbb ,"\u0045\u006e\u0064\u004f\u0066\u0053\u0074\u0072\u0069\u0070\u0065\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u006f\u0066\u0020\u0076\u0061l\u0069\u0064\u0020\u0074\u0079p\u0065\u003a \u0027\u0025\u0054\u0027",_aba );
};_ccda =append (_ccda ,_fdc );_fdgf .FinalHeight =_fdc .LineNumber ();};};return _ccda ,nil ;};func (_cbea *Globals )AddSegment (segment *_gd .Header ){_cbea .Segments =append (_cbea .Segments ,segment );};func (_cae *Document )determineRandomDataOffsets (_bcb []*_gd .Header ,_ddf uint64 ){if _cae .OrganizationType !=_gd .ORandom {return ;
};for _ ,_abc :=range _bcb {_abc .SegmentDataStartOffset =_ddf ;_ddf +=_abc .SegmentDataLength ;};};func _dbg (_age *_fb .Reader ,_fdf *Globals )(*Document ,error ){_bgb :=&Document {Pages :make (map[int ]*Page ),InputStream :_age ,OrganizationType :_gd .OSequential ,NumberOfPagesUnknown :true ,GlobalSegments :_fdf ,_eb :9};
if _bgb .GlobalSegments ==nil {_bgb .GlobalSegments =&Globals {};};if _dbf :=_bgb .mapData ();_dbf !=nil {return nil ,_dbf ;};return _bgb ,nil ;};func (_adc *Page )fitsPage (_bae *_gd .PageInformationSegment ,_eeg *_ad .Bitmap )bool {return _adc .countRegions ()==1&&_bae .DefaultPixelValue ==0&&_bae .PageBMWidth ==_eeg .Width &&_bae .PageBMHeight ==_eeg .Height ;
};func (_ffgd *Page )GetHeight ()(int ,error ){return _ffgd .getHeight ()};func DecodeDocument (input *_fb .Reader ,globals *Globals )(*Document ,error ){return _dbg (input ,globals );};func (_ceb *Page )Encode (w _fb .BinaryWriter )(_fda int ,_beb error ){const _edge ="P\u0061\u0067\u0065\u002e\u0045\u006e\u0063\u006f\u0064\u0065";
var _gaa int ;for _ ,_cef :=range _ceb .Segments {if _gaa ,_beb =_cef .Encode (w );_beb !=nil {return _fda ,_ff .Wrap (_beb ,_edge ,"");};_fda +=_gaa ;};return _fda ,nil ;};func (_fcg *Page )GetSegment (number int )(*_gd .Header ,error ){const _cgb ="\u0050a\u0067e\u002e\u0047\u0065\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074";
for _ ,_cecb :=range _fcg .Segments {if _cecb .SegmentNumber ==uint32 (number ){return _cecb ,nil ;};};_dce :=make ([]uint32 ,len (_fcg .Segments ));for _egd ,_gdbf :=range _fcg .Segments {_dce [_egd ]=_gdbf .SegmentNumber ;};return nil ,_ff .Errorf (_cgb ,"\u0073e\u0067\u006d\u0065n\u0074\u0020\u0077i\u0074h \u006e\u0075\u006d\u0062\u0065\u0072\u003a \u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0067\u0065\u003a\u0020'%\u0064'\u002e\u0020\u004b\u006e\u006f\u0077n\u0020\u0073\u0065\u0067\u006de\u006e\u0074\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0073\u003a \u0025\u0076",number ,_fcg .PageNumber ,_dce );
};type EncodingMethod int ;func (_bfg *Page )getHeight ()(int ,error ){const _bagf ="\u0067e\u0074\u0048\u0065\u0069\u0067\u0068t";if _bfg .FinalHeight !=0{return _bfg .FinalHeight ,nil ;};_aga :=_bfg .getPageInformationSegment ();if _aga ==nil {return 0,_ff .Error (_bagf ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");
};_fcb ,_dbeg :=_aga .GetSegmentData ();if _dbeg !=nil {return 0,_ff .Wrap (_dbeg ,_bagf ,"");};_dbc ,_aee :=_fcb .(*_gd .PageInformationSegment );if !_aee {return 0,_ff .Errorf (_bagf ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_fcb );
};if _dbc .PageBMHeight ==_ba .MaxInt32 {_ ,_dbeg =_bfg .GetBitmap ();if _dbeg !=nil {return 0,_ff .Wrap (_dbeg ,_bagf ,"");};}else {_bfg .FinalHeight =_dbc .PageBMHeight ;};return _bfg .FinalHeight ,nil ;};func (_dcg *Page )nextSegmentNumber ()uint32 {return _dcg .Document .nextSegmentNumber ()};
func (_dff *Document )GetPage (pageNumber int )(_gd .Pager ,error ){const _ebbc ="\u0044\u006fc\u0075\u006d\u0065n\u0074\u002e\u0047\u0065\u0074\u0050\u0061\u0067\u0065";if pageNumber < 0{_e .Log .Debug ("\u004a\u0042\u0049\u00472\u0020\u0050\u0061\u0067\u0065\u0020\u002d\u0020\u0047e\u0074\u0050\u0061\u0067\u0065\u003a\u0020\u0025\u0064\u002e\u0020\u0050\u0061\u0067\u0065\u0020\u0063\u0061n\u006e\u006f\u0074\u0020\u0062e\u0020\u006c\u006f\u0077\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0030\u002e\u0020\u0025\u0073",pageNumber ,_a .Stack ());
return nil ,_ff .Errorf (_ebbc ,"\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032\u0020d\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u002d\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064 \u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u003a\u0020\u0025\u0064",pageNumber );
};if pageNumber > len (_dff .Pages ){_e .Log .Debug ("\u0050\u0061\u0067\u0065 n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0064\u002e\u0020%\u0073",pageNumber ,_a .Stack ());return nil ,_ff .Error (_ebbc ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065n\u0074\u0020\u002d\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_bad ,_cg :=_dff .Pages [pageNumber ];if !_cg {_e .Log .Debug ("\u0050\u0061\u0067\u0065 n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0064\u002e\u0020%\u0073",pageNumber ,_a .Stack ());return nil ,_ff .Errorf (_ebbc ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065n\u0074\u0020\u002d\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};return _bad ,nil ;};func (_gbff *Page )getPageInformationSegment ()*_gd .Header {for _ ,_abfb :=range _gbff .Segments {if _abfb .Type ==_gd .TPageInformation {return _abfb ;};};_e .Log .Debug ("\u0050\u0061\u0067\u0065\u0020\u0069\u006e\u0066o\u0072\u006d\u0061ti\u006f\u006e\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0066o\u0072\u0020\u0070\u0061\u0067\u0065\u003a\u0020%\u0073\u002e",_gbff );
return nil ;};func (_ddb *Document )GetNumberOfPages ()(uint32 ,error ){if _ddb .NumberOfPagesUnknown ||_ddb .NumberOfPages ==0{if len (_ddb .Pages )==0{if _ggb :=_ddb .mapData ();_ggb !=nil {return 0,_ff .Wrap (_ggb ,"\u0044o\u0063\u0075\u006d\u0065n\u0074\u002e\u0047\u0065\u0074N\u0075m\u0062e\u0072\u004f\u0066\u0050\u0061\u0067\u0065s","");
};};return uint32 (len (_ddb .Pages )),nil ;};return _ddb .NumberOfPages ,nil ;};func (_edbe *Document )parseFileHeader ()error {const _fcd ="\u0070a\u0072s\u0065\u0046\u0069\u006c\u0065\u0048\u0065\u0061\u0064\u0065\u0072";_ ,_cc :=_edbe .InputStream .Seek (8,_af .SeekStart );
if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u0069\u0064");};_ ,_cc =_edbe .InputStream .ReadBits (5);if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u0072\u0065\u0073\u0065\u0072\u0076\u0065\u0064\u0020\u0062\u0069\u0074\u0073");};_cd ,_cc :=_edbe .InputStream .ReadBit ();
if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u0065x\u0074e\u006e\u0064\u0065\u0064\u0020t\u0065\u006dp\u006c\u0061\u0074\u0065\u0073");};if _cd ==1{_edbe .GBUseExtTemplate =true ;};_cd ,_cc =_edbe .InputStream .ReadBit ();if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u0075\u006e\u006b\u006eow\u006e\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072");
};if _cd !=1{_edbe .NumberOfPagesUnknown =false ;};_cd ,_cc =_edbe .InputStream .ReadBit ();if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u006f\u0072\u0067\u0061\u006e\u0069\u007a\u0061\u0074\u0069\u006f\u006e \u0074\u0079\u0070\u0065");};_edbe .OrganizationType =_gd .OrganizationType (_cd );
if !_edbe .NumberOfPagesUnknown {_edbe .NumberOfPages ,_cc =_edbe .InputStream .ReadUint32 ();if _cc !=nil {return _ff .Wrap (_cc ,_fcd ,"\u006eu\u006db\u0065\u0072\u0020\u006f\u0066\u0020\u0070\u0061\u0067\u0065\u0073");};_edbe ._eb =13;};return nil ;
};func (_ab *Document )produceClassifiedPages ()(_gga error ){const _dfa ="\u0070\u0072\u006f\u0064uc\u0065\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0050\u0061\u0067e\u0073";if _ab .Classer ==nil {return nil ;};var (_aa *Page ;_dfab bool ;
_fed *_gd .Header ;);for _gcc :=1;_gcc <=int (_ab .NumberOfPages );_gcc ++{if _aa ,_dfab =_ab .Pages [_gcc ];!_dfab {return _ff .Errorf (_dfa ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_gcc );};if _aa .EncodingMethod ==GenericEM {continue ;
};if _fed ==nil {if _fed ,_gga =_ab .GlobalSegments .GetSymbolDictionary ();_gga !=nil {return _ff .Wrap (_gga ,_dfa ,"");};};if _gga =_ab .produceClassifiedPage (_aa ,_fed );_gga !=nil {return _ff .Wrapf (_gga ,_dfa ,"\u0070\u0061\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_gcc );
};};return nil ;};func _dbbe (_fga *Document ,_fef int )*Page {return &Page {Document :_fga ,PageNumber :_fef ,Segments :[]*_gd .Header {}};};const (GenericEM EncodingMethod =iota ;CorrelationEM ;RankHausEM ;);func (_ffcg *Document )encodeSegment (_bee *_gd .Header ,_edb *int )error {const _dd ="\u0065\u006e\u0063\u006f\u0064\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074";
_bee .SegmentNumber =_ffcg .nextSegmentNumber ();_faf ,_afga :=_bee .Encode (_ffcg ._ef );if _afga !=nil {return _ff .Wrapf (_afga ,_dd ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",_bee .SegmentNumber );};*_edb +=_faf ;
return nil ;};func (_aaf *Page )createNormalPage (_bcf *_gd .PageInformationSegment )error {const _afc ="\u0063\u0072e\u0061\u0074\u0065N\u006f\u0072\u006d\u0061\u006c\u0050\u0061\u0067\u0065";_aaf .Bitmap =_ad .New (_bcf .PageBMWidth ,_bcf .PageBMHeight );
if _bcf .DefaultPixelValue !=0{_aaf .Bitmap .SetDefaultPixel ();};for _ ,_afgac :=range _aaf .Segments {switch _afgac .Type {case 6,7,22,23,38,39,42,43:_e .Log .Trace ("\u0047\u0065\u0074\u0074in\u0067\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0064",_afgac .SegmentNumber );
_ebf ,_deb :=_afgac .GetSegmentData ();if _deb !=nil {return _deb ;};_eccd ,_fgac :=_ebf .(_gd .Regioner );if !_fgac {_e .Log .Debug ("\u0053\u0065g\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0054\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0052\u0065\u0067\u0069on\u0065\u0072",_ebf );
return _ff .Errorf (_afc ,"i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062i\u0067\u0032\u0020\u0073\u0065\u0067\u006den\u0074\u0020\u0074\u0079p\u0065\u0020\u002d\u0020\u006e\u006f\u0074\u0020\u0061 R\u0065\u0067i\u006f\u006e\u0065\u0072\u003a\u0020\u0025\u0054",_ebf );
};_bcfe ,_deb :=_eccd .GetRegionBitmap ();if _deb !=nil {return _ff .Wrap (_deb ,_afc ,"");};if _aaf .fitsPage (_bcf ,_bcfe ){_aaf .Bitmap =_bcfe ;}else {_ggf :=_eccd .GetRegionInfo ();_cbafd :=_aaf .getCombinationOperator (_bcf ,_ggf .CombinaionOperator );
_deb =_ad .Blit (_bcfe ,_aaf .Bitmap ,int (_ggf .XLocation ),int (_ggf .YLocation ),_cbafd );if _deb !=nil {return _ff .Wrap (_deb ,_afc ,"");};};};};return nil ;};func (_bcca *Page )GetResolutionX ()(int ,error ){return _bcca .getResolutionX ()};func (_gag *Globals )GetSegment (segmentNumber int )(*_gd .Header ,error ){const _gf ="\u0047l\u006fb\u0061\u006c\u0073\u002e\u0047e\u0074\u0053e\u0067\u006d\u0065\u006e\u0074";
if _gag ==nil {return nil ,_ff .Error (_gf ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if len (_gag .Segments )==0{return nil ,_ff .Error (_gf ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");
};var _fgdd *_gd .Header ;for _ ,_fgdd =range _gag .Segments {if _fgdd .SegmentNumber ==uint32 (segmentNumber ){break ;};};if _fgdd ==nil {return nil ,_ff .Error (_gf ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};return _fgdd ,nil ;};func (_fe *Document )completeClassifiedPages ()(_fbd error ){const _ea ="\u0063\u006f\u006dpl\u0065\u0074\u0065\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0050\u0061\u0067\u0065\u0073";if _fe .Classer ==nil {return nil ;
};_fe ._gdg =make ([]int ,_fe .Classer .UndilatedTemplates .Size ());for _bd :=0;_bd < _fe .Classer .ClassIDs .Size ();_bd ++{_da ,_c :=_fe .Classer .ClassIDs .Get (_bd );if _c !=nil {return _ff .Wrapf (_c ,_ea ,"\u0063\u006c\u0061\u0073s \u0077\u0069\u0074\u0068\u0020\u0069\u0064\u003a\u0020\u0027\u0025\u0064\u0027",_bd );
};_fe ._gdg [_da ]++;};var _ge []int ;for _fbdd :=0;_fbdd < _fe .Classer .UndilatedTemplates .Size ();_fbdd ++{if _fe .NumberOfPages ==1||_fe ._gdg [_fbdd ]> 1{_ge =append (_ge ,_fbdd );};};var (_add *Page ;_bb bool ;);for _gc ,_fd :=range *_fe .Classer .ComponentPageNumbers {if _add ,_bb =_fe .Pages [_fd ];
!_bb {return _ff .Errorf (_ea ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_gc );};if _add .EncodingMethod ==GenericEM {_e .Log .Error ("\u0047\u0065\u006e\u0065\u0072\u0069c\u0020\u0070\u0061g\u0065\u0020\u0077i\u0074\u0068\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u003a \u0027\u0025\u0064\u0027\u0020ma\u0070\u0070\u0065\u0064\u0020\u0061\u0073\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0020\u0070\u0061\u0067\u0065",_gc );
continue ;};_fe ._fc [_fd ]=append (_fe ._fc [_fd ],_gc );_eae ,_acg :=_fe .Classer .ClassIDs .Get (_gc );if _acg !=nil {return _ff .Wrapf (_acg ,_ea ,"\u006e\u006f\u0020\u0073uc\u0068\u0020\u0063\u006c\u0061\u0073\u0073\u0049\u0044\u003a\u0020\u0025\u0064",_gc );
};if _fe ._gdg [_eae ]==1&&_fe .NumberOfPages !=1{_fg :=append (_fe ._ac [_fd ],_eae );_fe ._ac [_fd ]=_fg ;};};if _fbd =_fe .Classer .ComputeLLCorners ();_fbd !=nil {return _ff .Wrap (_fbd ,_ea ,"");};if _ ,_fbd =_fe .addSymbolDictionary (0,_fe .Classer .UndilatedTemplates ,_ge ,_fe ._fa ,false );
_fbd !=nil {return _ff .Wrap (_fbd ,_ea ,"");};return nil ;};func (_dged *Globals )GetSegmentByIndex (index int )(*_gd .Header ,error ){const _edg ="\u0047l\u006f\u0062\u0061\u006cs\u002e\u0047\u0065\u0074\u0053e\u0067m\u0065n\u0074\u0042\u0079\u0049\u006e\u0064\u0065x";
if _dged ==nil {return nil ,_ff .Error (_edg ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if len (_dged .Segments )==0{return nil ,_ff .Error (_edg ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");
};if index > len (_dged .Segments )-1{return nil ,_ff .Error (_edg ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};return _dged .Segments [index ],nil ;};func (_agb *Page )addTextRegionSegment (_dgb []*_gd .Header ,_baae ,_cge map[int ]int ,_cdc []int ,_cee *_ad .Points ,_fcc *_ad .Bitmaps ,_eca *_d .IntSlice ,_agd *_ad .Boxes ,_ccd ,_gccd int ){_bde :=&_gd .TextRegion {NumberOfSymbols :uint32 (_gccd )};
_bde .InitEncode (_baae ,_cge ,_cdc ,_cee ,_fcc ,_eca ,_agd ,_agb .FinalWidth ,_agb .FinalHeight ,_ccd );_becg :=&_gd .Header {RTSegments :_dgb ,SegmentData :_bde ,PageAssociation :_agb .PageNumber ,Type :_gd .TImmediateTextRegion };_fdd :=_gd .TPageInformation ;
if _cge !=nil {_fdd =_gd .TSymbolDictionary ;};var _gde int ;for ;_gde < len (_agb .Segments );_gde ++{if _agb .Segments [_gde ].Type ==_fdd {_gde ++;break ;};};_agb .Segments =append (_agb .Segments ,nil );copy (_agb .Segments [_gde +1:],_agb .Segments [_gde :]);
_agb .Segments [_gde ]=_becg ;};func (_afd *Document )completeSymbols ()(_dad error ){const _ecc ="\u0063o\u006dp\u006c\u0065\u0074\u0065\u0053\u0079\u006d\u0062\u006f\u006c\u0073";if _afd .Classer ==nil {return nil ;};if _afd .Classer .UndilatedTemplates ==nil {return _ff .Error (_ecc ,"\u006e\u006f t\u0065\u006d\u0070l\u0061\u0074\u0065\u0073 de\u0066in\u0065\u0064\u0020\u0066\u006f\u0072\u0020th\u0065\u0020\u0063\u006c\u0061\u0073\u0073e\u0072");
};_efe :=len (_afd .Pages )==1;_baa :=make ([]int ,_afd .Classer .UndilatedTemplates .Size ());var _gad int ;for _bbc :=0;_bbc < _afd .Classer .ClassIDs .Size ();_bbc ++{_gad ,_dad =_afd .Classer .ClassIDs .Get (_bbc );if _dad !=nil {return _ff .Wrap (_dad ,_ecc ,"\u0063\u006c\u0061\u0073\u0073\u0020\u0049\u0044\u0027\u0073");
};_baa [_gad ]++;};var _gac []int ;for _bf :=0;_bf < _afd .Classer .UndilatedTemplates .Size ();_bf ++{if _baa [_bf ]==0{return _ff .Error (_ecc ,"\u006eo\u0020\u0073y\u006d\u0062\u006f\u006cs\u0020\u0069\u006es\u0074\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006fun\u0064\u0020\u0066o\u0072\u0020g\u0069\u0076\u0065\u006e\u0020\u0063l\u0061\u0073s\u003f\u0020");
};if _baa [_bf ]> 1||_efe {_gac =append (_gac ,_bf );};};_afd ._gg =len (_gac );var _eag ,_dab int ;for _ebb :=0;_ebb < _afd .Classer .ComponentPageNumbers .Size ();_ebb ++{_eag ,_dad =_afd .Classer .ComponentPageNumbers .Get (_ebb );if _dad !=nil {return _ff .Wrapf (_dad ,_ecc ,"p\u0061\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027 \u006e\u006f\u0074\u0020\u0066\u006f\u0075nd\u0020\u0069\u006e\u0020t\u0068\u0065\u0020\u0063\u006c\u0061\u0073\u0073\u0065r \u0070\u0061g\u0065\u006e\u0075\u006d\u0062\u0065\u0072\u0073",_ebb );
};_dab ,_dad =_afd .Classer .ClassIDs .Get (_ebb );if _dad !=nil {return _ff .Wrapf (_dad ,_ecc ,"\u0063\u0061\u006e\u0027\u0074\u0020\u0067e\u0074\u0020\u0073y\u006d\u0062\u006f\u006c \u0066\u006f\u0072\u0020\u0070\u0061\u0067\u0065\u0020\u0027\u0025\u0064\u0027\u0020\u0066\u0072\u006f\u006d\u0020\u0063\u006c\u0061\u0073\u0073\u0065\u0072",_eag );
};if _baa [_dab ]==1&&!_efe {_afd ._ac [_eag ]=append (_afd ._ac [_eag ],_dab );};};if _dad =_afd .Classer .ComputeLLCorners ();_dad !=nil {return _ff .Wrap (_dad ,_ecc ,"");};return nil ;};func (_dga *Page )createPage (_gged *_gd .PageInformationSegment )error {var _bba error ;
if !_gged .IsStripe ||_gged .PageBMHeight !=-1{_bba =_dga .createNormalPage (_gged );}else {_bba =_dga .createStripedPage (_gged );};return _bba ;};func (_dba *Page )AddPageInformationSegment (){_dfbg :=&_gd .PageInformationSegment {PageBMWidth :_dba .FinalWidth ,PageBMHeight :_dba .FinalHeight ,ResolutionX :_dba .ResolutionX ,ResolutionY :_dba .ResolutionY ,IsLossless :_dba .IsLossless };
if _dba .BlackIsOne {_dfbg .DefaultPixelValue =uint8 (0x1);};_cec :=&_gd .Header {PageAssociation :_dba .PageNumber ,SegmentDataLength :uint64 (_dfbg .Size ()),SegmentData :_dfbg ,Type :_gd .TPageInformation };_dba .Segments =append (_dba .Segments ,_cec );
};func (_cbf *Page )String ()string {return _f .Sprintf ("\u0050\u0061\u0067\u0065\u0020\u0023\u0025\u0064",_cbf .PageNumber );};func InitEncodeDocument (fullHeaders bool )*Document {return &Document {FullHeaders :fullHeaders ,_ef :_fb .BufferedMSB (),Pages :map[int ]*Page {},_ac :map[int ][]int {},_fa :map[int ]int {},_fc :map[int ][]int {}};
};func (_abcg *Page )lastSegmentNumber ()(_fde uint32 ,_cbg error ){const _cde ="\u006c\u0061\u0073\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u004eu\u006d\u0062\u0065\u0072";if len (_abcg .Segments )==0{return _fde ,_ff .Errorf (_cde ,"\u006e\u006f\u0020se\u0067\u006d\u0065\u006e\u0074\u0073\u0020\u0066\u006fu\u006ed\u0020i\u006e \u0074\u0068\u0065\u0020\u0070\u0061\u0067\u0065\u0020\u0027\u0025\u0064\u0027",_abcg .PageNumber );
};return _abcg .Segments [len (_abcg .Segments )-1].SegmentNumber ,nil ;};type Globals struct{Segments []*_gd .Header ;};func (_fcde *Page )AddGenericRegion (bm *_ad .Bitmap ,xloc ,yloc ,template int ,tp _gd .Type ,duplicateLineRemoval bool )error {const _dbd ="P\u0061\u0067\u0065\u002eAd\u0064G\u0065\u006e\u0065\u0072\u0069c\u0052\u0065\u0067\u0069\u006f\u006e";
_fea :=&_gd .GenericRegion {};if _edc :=_fea .InitEncode (bm ,xloc ,yloc ,template ,duplicateLineRemoval );_edc !=nil {return _ff .Wrap (_edc ,_dbd ,"");};_bda :=&_gd .Header {Type :_gd .TImmediateGenericRegion ,PageAssociation :_fcde .PageNumber ,SegmentData :_fea };
_fcde .Segments =append (_fcde .Segments ,_bda );return nil ;};func (_cb *Document )produceClassifiedPage (_dfb *Page ,_fdg *_gd .Header )(_dg error ){const _bdc ="p\u0072\u006f\u0064\u0075ce\u0043l\u0061\u0073\u0073\u0069\u0066i\u0065\u0064\u0050\u0061\u0067\u0065";
var _dge map[int ]int ;_ec :=_cb ._gg ;_fcf :=[]*_gd .Header {_fdg };if len (_cb ._ac [_dfb .PageNumber ])> 0{_dge =map[int ]int {};_bc ,_ffg :=_cb .addSymbolDictionary (_dfb .PageNumber ,_cb .Classer .UndilatedTemplates ,_cb ._ac [_dfb .PageNumber ],_dge ,false );
if _ffg !=nil {return _ff .Wrap (_ffg ,_bdc ,"");};_fcf =append (_fcf ,_bc );_ec +=len (_cb ._ac [_dfb .PageNumber ]);};_ffc :=_cb ._fc [_dfb .PageNumber ];_e .Log .Debug ("P\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020c\u006f\u006d\u0070\u0073: \u0025\u0076",_dfb .PageNumber ,_ffc );
_dfb .addTextRegionSegment (_fcf ,_cb ._fa ,_dge ,_cb ._fc [_dfb .PageNumber ],_cb .Classer .PtaLL ,_cb .Classer .UndilatedTemplates ,_cb .Classer .ClassIDs ,nil ,_ga (_ec ),len (_cb ._fc [_dfb .PageNumber ]));return nil ;};func (_bef *Globals )GetSymbolDictionary ()(*_gd .Header ,error ){const _afad ="G\u006c\u006f\u0062\u0061\u006c\u0073.\u0047\u0065\u0074\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079";
if _bef ==nil {return nil ,_ff .Error (_afad ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if len (_bef .Segments )==0{return nil ,_ff .Error (_afad ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");
};for _ ,_ffgc :=range _bef .Segments {if _ffgc .Type ==_gd .TSymbolDictionary {return _ffgc ,nil ;};};return nil ,_ff .Error (_afad ,"\u0067\u006c\u006fba\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0020d\u0069c\u0074i\u006fn\u0061\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};func (_cbaf *Page )GetBitmap ()(_bggf *_ad .Bitmap ,_ddfg error ){_e .Log .Trace (_f .Sprintf ("\u005b\u0050\u0041G\u0045\u005d\u005b\u0023%\u0064\u005d\u0020\u0047\u0065\u0074\u0042i\u0074\u006d\u0061\u0070\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e",_cbaf .PageNumber ));
defer func (){if _ddfg !=nil {_e .Log .Trace (_f .Sprintf ("\u005b\u0050\u0041\u0047\u0045\u005d\u005b\u0023\u0025\u0064\u005d\u0020\u0047\u0065\u0074B\u0069t\u006d\u0061\u0070\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_cbaf .PageNumber ,_ddfg ));
}else {_e .Log .Trace (_f .Sprintf ("\u005b\u0050\u0041\u0047\u0045\u005d\u005b\u0023\u0025\u0064]\u0020\u0047\u0065\u0074\u0042\u0069\u0074m\u0061\u0070\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064",_cbaf .PageNumber ));};}();if _cbaf .Bitmap !=nil {return _cbaf .Bitmap ,nil ;
};_ddfg =_cbaf .composePageBitmap ();if _ddfg !=nil {return nil ,_ddfg ;};return _cbaf .Bitmap ,nil ;};func (_dgec *Document )addSymbolDictionary (_gbb int ,_eea *_ad .Bitmaps ,_be []int ,_ce map[int ]int ,_gdc bool )(*_gd .Header ,error ){const _beg ="\u0061\u0064\u0064\u0053ym\u0062\u006f\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079";
_gge :=&_gd .SymbolDictionary {};if _gbgf :=_gge .InitEncode (_eea ,_be ,_ce ,_gdc );_gbgf !=nil {return nil ,_gbgf ;};_feb :=&_gd .Header {Type :_gd .TSymbolDictionary ,PageAssociation :_gbb ,SegmentData :_gge };if _gbb ==0{if _dgec .GlobalSegments ==nil {_dgec .GlobalSegments =&Globals {};
};_dgec .GlobalSegments .AddSegment (_feb );return _feb ,nil ;};_bcc ,_dgef :=_dgec .Pages [_gbb ];if !_dgef {return nil ,_ff .Errorf (_beg ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_gbb );};var (_dc int ;
_ed *_gd .Header ;);for _dc ,_ed =range _bcc .Segments {if _ed .Type ==_gd .TPageInformation {break ;};};_dc ++;_bcc .Segments =append (_bcc .Segments ,nil );copy (_bcc .Segments [_dc +1:],_bcc .Segments [_dc :]);_bcc .Segments [_dc ]=_feb ;return _feb ,nil ;
};func (_gcg *Page )getResolutionX ()(int ,error ){const _eee ="\u0067\u0065\u0074\u0052\u0065\u0073\u006f\u006c\u0075t\u0069\u006f\u006e\u0058";if _gcg .ResolutionX !=0{return _gcg .ResolutionX ,nil ;};_adcb :=_gcg .getPageInformationSegment ();if _adcb ==nil {return 0,_ff .Error (_eee ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");
};_ddfgf ,_afcc :=_adcb .GetSegmentData ();if _afcc !=nil {return 0,_ff .Wrap (_afcc ,_eee ,"");};_fefd ,_ggg :=_ddfgf .(*_gd .PageInformationSegment );if !_ggg {return 0,_ff .Errorf (_eee ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_ddfgf );
};_gcg .ResolutionX =_fefd .ResolutionX ;return _gcg .ResolutionX ,nil ;};var _df =[]byte {0x97,0x4A,0x42,0x32,0x0D,0x0A,0x1A,0x0A};func (_dcf *Document )reachedEOF (_afgc int64 )(bool ,error ){const _bbg ="\u0072\u0065\u0061\u0063\u0068\u0065\u0064\u0045\u004f\u0046";
_ ,_adg :=_dcf .InputStream .Seek (_afgc ,_af .SeekStart );if _adg !=nil {_e .Log .Debug ("\u0072\u0065\u0061c\u0068\u0065\u0064\u0045\u004f\u0046\u0020\u002d\u0020\u0064\u002e\u0049\u006e\u0070\u0075\u0074\u0053\u0074\u0072\u0065\u0061\u006d\u002e\u0053\u0065\u0065\u006b\u0020\u0066a\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_adg );
return false ,_ff .Wrap (_adg ,_bbg ,"\u0069n\u0070\u0075\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020s\u0065\u0065\u006b\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};_ ,_adg =_dcf .InputStream .ReadBits (32);if _adg ==_af .EOF {return true ,nil ;
}else if _adg !=nil {return false ,_ff .Wrap (_adg ,_bbg ,"");};return false ,nil ;};func (_gadf *Page )getCombinationOperator (_fabfa *_gd .PageInformationSegment ,_dcb _ad .CombinationOperator )_ad .CombinationOperator {if _fabfa .CombinationOperatorOverrideAllowed (){return _dcb ;
};return _fabfa .CombinationOperator ();};func (_gce *Page )composePageBitmap ()error {const _gcca ="\u0063\u006f\u006d\u0070\u006f\u0073\u0065\u0050\u0061\u0067\u0065\u0042i\u0074\u006d\u0061\u0070";if _gce .PageNumber ==0{return nil ;};_ffe :=_gce .getPageInformationSegment ();
if _ffe ==nil {return _ff .Error (_gcca ,"\u0070\u0061\u0067e \u0069\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069o\u006e \u0073e\u0067m\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_gdf ,_fabf :=_ffe .GetSegmentData ();
if _fabf !=nil {return _fabf ;};_dbfg ,_ddfd :=_gdf .(*_gd .PageInformationSegment );if !_ddfd {return _ff .Error (_gcca ,"\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006da\u0074\u0069\u006f\u006e \u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065");
};if _fabf =_gce .createPage (_dbfg );_fabf !=nil {return _ff .Wrap (_fabf ,_gcca ,"");};_gce .clearSegmentData ();return nil ;};