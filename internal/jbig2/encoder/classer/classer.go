//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package classer ;import (_e "github.com/unidoc/unipdf/v4/common";_df "github.com/unidoc/unipdf/v4/internal/jbig2/basic";_fb "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";_f "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_d "image";_c "math";
);const (MaxDiffWidth =2;MaxDiffHeight =2;);func (_gde *Settings )SetDefault (){if _gde .MaxCompWidth ==0{switch _gde .Components {case _fb .ComponentConn :_gde .MaxCompWidth =MaxConnCompWidth ;case _fb .ComponentCharacters :_gde .MaxCompWidth =MaxCharCompWidth ;
case _fb .ComponentWords :_gde .MaxCompWidth =MaxWordCompWidth ;};};if _gde .MaxCompHeight ==0{_gde .MaxCompHeight =MaxCompHeight ;};if _gde .Thresh ==0.0{_gde .Thresh =0.9;};if _gde .WeightFactor ==0.0{_gde .WeightFactor =0.75;};if _gde .RankHaus ==0.0{_gde .RankHaus =0.97;
};if _gde .SizeHaus ==0{_gde .SizeHaus =2;};};var _fde bool ;func (_edf *Classer )classifyRankHouseNonOne (_dfb *_fb .Boxes ,_fee ,_ebb ,_cbc *_fb .Bitmaps ,_gegc *_fb .Points ,_abgb *_df .NumSlice ,_bdf int )(_bgg error ){const _baf ="\u0043\u006c\u0061\u0073s\u0065\u0072\u002e\u0063\u006c\u0061\u0073\u0073\u0069\u0066y\u0052a\u006e\u006b\u0048\u006f\u0075\u0073\u0065O\u006e\u0065";
var (_fddc ,_egc ,_da ,_dbfd float32 ;_gdg ,_efdd ,_bbf int ;_bca ,_gbc ,_gbg ,_ffbg ,_acb *_fb .Bitmap ;_ggc ,_gafc bool ;);_fgfd :=_fb .MakePixelSumTab8 ();for _gbce :=0;_gbce < len (_fee .Values );_gbce ++{if _gbc ,_bgg =_ebb .GetBitmap (_gbce );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"b\u006d\u0073\u0031\u002e\u0047\u0065\u0074\u0028\u0069\u0029");
};if _gdg ,_bgg =_abgb .GetInt (_gbce );_bgg !=nil {_e .Log .Trace ("\u0047\u0065t\u0074\u0069\u006e\u0067 \u0046\u0047T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073 \u0061\u0074\u003a\u0020\u0025\u0064\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_gbce ,_bgg );
};if _gbg ,_bgg =_cbc .GetBitmap (_gbce );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"b\u006d\u0073\u0032\u002e\u0047\u0065\u0074\u0028\u0069\u0029");};if _fddc ,_egc ,_bgg =_gegc .GetGeometry (_gbce );_bgg !=nil {return _f .Wrapf (_bgg ,_baf ,"\u0070t\u0061[\u0069\u005d\u002e\u0047\u0065\u006f\u006d\u0065\u0074\u0072\u0079");
};_abb :=len (_edf .UndilatedTemplates .Values );_ggc =false ;_efcg :=_daa (_edf ,_gbc );for _bbf =_efcg .Next ();_bbf > -1;{if _ffbg ,_bgg =_edf .UndilatedTemplates .GetBitmap (_bbf );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"\u0070\u0069\u0078\u0061\u0074\u002e\u005b\u0069\u0043l\u0061\u0073\u0073\u005d");
};if _efdd ,_bgg =_edf .FgTemplates .GetInt (_bbf );_bgg !=nil {_e .Log .Trace ("\u0047\u0065\u0074\u0074\u0069\u006eg\u0020\u0046\u0047\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u005b\u0025d\u005d\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_bbf ,_bgg );
};if _acb ,_bgg =_edf .DilatedTemplates .GetBitmap (_bbf );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"\u0070\u0069\u0078\u0061\u0074\u0064\u005b\u0069\u0043l\u0061\u0073\u0073\u005d");};if _da ,_dbfd ,_bgg =_edf .CentroidPointsTemplates .GetGeometry (_bbf );
_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"\u0043\u0065\u006et\u0072\u006f\u0069\u0064P\u006f\u0069\u006e\u0074\u0073\u0054\u0065m\u0070\u006c\u0061\u0074\u0065\u0073\u005b\u0069\u0043\u006c\u0061\u0073\u0073\u005d");};_gafc ,_bgg =_fb .RankHausTest (_gbc ,_gbg ,_ffbg ,_acb ,_fddc -_da ,_egc -_dbfd ,MaxDiffWidth ,MaxDiffHeight ,_gdg ,_efdd ,float32 (_edf .Settings .RankHaus ),_fgfd );
if _bgg !=nil {return _f .Wrap (_bgg ,_baf ,"");};if _gafc {_ggc =true ;if _bgg =_edf .ClassIDs .Add (_bbf );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"");};if _bgg =_edf .ComponentPageNumbers .Add (_bdf );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"");};
if _edf .Settings .KeepClassInstances {_ecbc ,_aag :=_edf .ClassInstances .GetBitmaps (_bbf );if _aag !=nil {return _f .Wrap (_aag ,_baf ,"\u0063\u002e\u0050\u0069\u0078\u0061\u0061\u002e\u0047\u0065\u0074B\u0069\u0074\u006d\u0061\u0070\u0073\u0028\u0069\u0043\u006ca\u0073\u0073\u0029");
};if _bca ,_aag =_fee .GetBitmap (_gbce );_aag !=nil {return _f .Wrap (_aag ,_baf ,"\u0070i\u0078\u0061\u005b\u0069\u005d");};_ecbc .Values =append (_ecbc .Values ,_bca );_gce ,_aag :=_dfb .Get (_gbce );if _aag !=nil {return _f .Wrap (_aag ,_baf ,"b\u006f\u0078\u0061\u002e\u0047\u0065\u0074\u0028\u0069\u0029");
};_ecbc .Boxes =append (_ecbc .Boxes ,_gce );};break ;};};if !_ggc {if _bgg =_edf .ClassIDs .Add (_abb );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"\u0021\u0066\u006f\u0075\u006e\u0064");};if _bgg =_edf .ComponentPageNumbers .Add (_bdf );_bgg !=nil {return _f .Wrap (_bgg ,_baf ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_cegd :=&_fb .Bitmaps {};_bca =_fee .Values [_gbce ];_cegd .AddBitmap (_bca );_gag ,_feeg :=_bca .Width ,_bca .Height ;_edf .TemplatesSize .Add (uint64 (_gag )*uint64 (_feeg ),_abb );_gfd ,_gdae :=_dfb .Get (_gbce );if _gdae !=nil {return _f .Wrap (_gdae ,_baf ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_cegd .AddBox (_gfd );_edf .ClassInstances .AddBitmaps (_cegd );_edf .CentroidPointsTemplates .AddPoint (_fddc ,_egc );_edf .UndilatedTemplates .AddBitmap (_gbc );_edf .DilatedTemplates .AddBitmap (_gbg );_edf .FgTemplates .AddInt (_gdg );};};_edf .NumberOfClasses =len (_edf .UndilatedTemplates .Values );
return nil ;};const (MaxConnCompWidth =350;MaxCharCompWidth =350;MaxWordCompWidth =1000;MaxCompHeight =120;);func (_fdef Settings )Validate ()error {const _dgag ="\u0053\u0065\u0074\u0074\u0069\u006e\u0067\u0073\u002e\u0056\u0061\u006ci\u0064\u0061\u0074\u0065";
if _fdef .Thresh < 0.4||_fdef .Thresh > 0.98{return _f .Error (_dgag ,"\u006a\u0062i\u0067\u0032\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0074\u0068\u0072\u0065\u0073\u0068\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0030\u002e\u0034\u0020\u002d\u0020\u0030\u002e\u0039\u0038\u005d");
};if _fdef .WeightFactor < 0.0||_fdef .WeightFactor > 1.0{return _f .Error (_dgag ,"\u006a\u0062i\u0067\u0032\u0020\u0065\u006ec\u006f\u0064\u0065\u0072\u0020w\u0065\u0069\u0067\u0068\u0074\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0030\u002e\u0030\u0020\u002d\u0020\u0031\u002e\u0030\u005d");
};if _fdef .RankHaus < 0.5||_fdef .RankHaus > 1.0{return _f .Error (_dgag ,"\u006a\u0062\u0069\u0067\u0032\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0072a\u006e\u006b\u0020\u0068\u0061\u0075\u0073\u0020\u0076\u0061\u006c\u0075\u0065 \u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065 [\u0030\u002e\u0035\u0020\u002d\u0020\u0031\u002e\u0030\u005d");
};if _fdef .SizeHaus < 1||_fdef .SizeHaus > 10{return _f .Error (_dgag ,"\u006a\u0062\u0069\u0067\u0032 \u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0073\u0069\u007a\u0065\u0020h\u0061\u0075\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0031\u0020\u002d\u0020\u0031\u0030]");
};switch _fdef .Components {case _fb .ComponentConn ,_fb .ComponentCharacters ,_fb .ComponentWords :default:return _f .Error (_dgag ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0063\u006c\u0061\u0073s\u0065r\u0020c\u006f\u006d\u0070\u006f\u006e\u0065\u006et");
};return nil ;};func (_ag *Classer )AddPage (inputPage *_fb .Bitmap ,pageNumber int ,method Method )(_dff error ){const _ae ="\u0043l\u0061s\u0073\u0065\u0072\u002e\u0041\u0064\u0064\u0050\u0061\u0067\u0065";_ag .Widths [pageNumber ]=inputPage .Width ;
_ag .Heights [pageNumber ]=inputPage .Height ;if _dff =_ag .verifyMethod (method );_dff !=nil {return _f .Wrap (_dff ,_ae ,"");};_aga ,_g ,_dff :=inputPage .GetComponents (_ag .Settings .Components ,_ag .Settings .MaxCompWidth ,_ag .Settings .MaxCompHeight );
if _dff !=nil {return _f .Wrap (_dff ,_ae ,"");};_e .Log .Debug ("\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074s\u003a\u0020\u0025\u0076",_aga );if _dff =_ag .addPageComponents (inputPage ,_g ,_aga ,pageNumber ,method );_dff !=nil {return _f .Wrap (_dff ,_ae ,"");
};return nil ;};func DefaultSettings ()Settings {_ffa :=&Settings {};_ffa .SetDefault ();return *_ffa };func (_bff *Classer )classifyCorrelation (_afc *_fb .Boxes ,_gc *_fb .Bitmaps ,_feg int )error {const _ced ="\u0063\u006c\u0061\u0073si\u0066\u0079\u0043\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e";
if _afc ==nil {return _f .Error (_ced ,"\u006e\u0065\u0077\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062o\u0078\u0065\u0073\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064");
};if _gc ==nil {return _f .Error (_ced ,"\u006e\u0065wC\u006f\u006d\u0070o\u006e\u0065\u006e\u0074s b\u0069tm\u0061\u0070\u0020\u0061\u0072\u0072\u0061y \u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064");};_agd :=len (_gc .Values );if _agd ==0{_e .Log .Debug ("\u0063l\u0061\u0073s\u0069\u0066\u0079C\u006f\u0072\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0020\u002d\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0070\u0069\u0078\u0061s\u0020\u0069\u0073\u0020\u0065\u006d\u0070\u0074\u0079");
return nil ;};var (_gaf ,_ed *_fb .Bitmap ;_fgfb error ;);_gb :=&_fb .Bitmaps {Values :make ([]*_fb .Bitmap ,_agd )};for _gcc ,_fce :=range _gc .Values {_ed ,_fgfb =_fce .AddBorderGeneral (JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,0);
if _fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"");};_gb .Values [_gcc ]=_ed ;};_dfc :=_bff .FgTemplates ;_gdb :=_fb .MakePixelSumTab8 ();_bcc :=_fb .MakePixelCentroidTab8 ();_faf :=make ([]int ,_agd );_acaa :=make ([][]int ,_agd );_def :=_fb .Points (make ([]_fb .Point ,_agd ));
_aac :=&_def ;var (_aacg ,_geg int ;_egf ,_feb ,_gcg int ;_adab ,_ab int ;_cad byte ;);for _efa ,_bffc :=range _gb .Values {_acaa [_efa ]=make ([]int ,_bffc .Height );_aacg =0;_geg =0;_feb =(_bffc .Height -1)*_bffc .RowStride ;_egf =0;for _ab =_bffc .Height -1;
_ab >=0;_ab ,_feb =_ab -1,_feb -_bffc .RowStride {_acaa [_efa ][_ab ]=_egf ;_gcg =0;for _adab =0;_adab < _bffc .RowStride ;_adab ++{_cad =_bffc .Data [_feb +_adab ];_gcg +=_gdb [_cad ];_aacg +=_bcc [_cad ]+_adab *8*_gdb [_cad ];};_egf +=_gcg ;_geg +=_gcg *_ab ;
};_faf [_efa ]=_egf ;if _egf > 0{(*_aac )[_efa ]=_fb .Point {X :float32 (_aacg )/float32 (_egf ),Y :float32 (_geg )/float32 (_egf )};}else {(*_aac )[_efa ]=_fb .Point {X :float32 (_bffc .Width )/float32 (2),Y :float32 (_bffc .Height )/float32 (2)};};};
if _fgfb =_bff .CentroidPoints .Add (_aac );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0063\u0065\u006et\u0072\u006f\u0069\u0064\u0020\u0061\u0064\u0064");};var (_agb ,_ffb ,_ded int ;_ege float64 ;_bd ,_gdba ,_cbd ,_gee float32 ;_cfc ,_gdc _fb .Point ;
_fdg bool ;_efdg *similarTemplatesFinder ;_bg int ;_cca *_fb .Bitmap ;_ecg *_d .Rectangle ;_cacb *_fb .Bitmaps ;);for _bg ,_ed =range _gb .Values {_ffb =_faf [_bg ];if _bd ,_gdba ,_fgfb =_aac .GetGeometry (_bg );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0070t\u0061\u0020\u002d\u0020\u0069");
};_fdg =false ;_cbe :=len (_bff .UndilatedTemplates .Values );_efdg =_daa (_bff ,_ed );for _dg :=_efdg .Next ();_dg > -1;{if _cca ,_fgfb =_bff .UndilatedTemplates .GetBitmap (_dg );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0075\u006e\u0069dl\u0061\u0074\u0065\u0064\u005b\u0069\u0063\u006c\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u0062\u006d\u0032");
};if _ded ,_fgfb =_dfc .GetInt (_dg );_fgfb !=nil {_e .Log .Trace ("\u0046\u0047\u0020T\u0065\u006d\u0070\u006ca\u0074\u0065\u0020\u005b\u0069\u0063\u006ca\u0073\u0073\u005d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_fgfb );};if _cbd ,_gee ,_fgfb =_bff .CentroidPointsTemplates .GetGeometry (_dg );
_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0050\u006f\u0069\u006e\u0074T\u0065\u006d\u0070\u006c\u0061\u0074e\u0073\u005b\u0069\u0063\u006c\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u00782\u002c\u0079\u0032\u0020");
};if _bff .Settings .WeightFactor > 0.0{if _agb ,_fgfb =_bff .TemplateAreas .Get (_dg );_fgfb !=nil {_e .Log .Trace ("\u0054\u0065\u006dp\u006c\u0061\u0074\u0065A\u0072\u0065\u0061\u0073\u005b\u0069\u0063l\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u0061\u0072\u0065\u0061\u0020\u0025\u0076",_fgfb );
};_ege =_bff .Settings .Thresh +(1.0-_bff .Settings .Thresh )*_bff .Settings .WeightFactor *float64 (_ded )/float64 (_agb );}else {_ege =_bff .Settings .Thresh ;};_aef ,_fda :=_fb .CorrelationScoreThresholded (_ed ,_cca ,_ffb ,_ded ,_cfc .X -_gdc .X ,_cfc .Y -_gdc .Y ,MaxDiffWidth ,MaxDiffHeight ,_gdb ,_acaa [_bg ],float32 (_ege ));
if _fda !=nil {return _f .Wrap (_fda ,_ced ,"");};if _fde {var (_aff ,_abf float64 ;_cegb ,_cbgc int ;);_aff ,_fda =_fb .CorrelationScore (_ed ,_cca ,_ffb ,_ded ,_bd -_cbd ,_gdba -_gee ,MaxDiffWidth ,MaxDiffHeight ,_gdb );if _fda !=nil {return _f .Wrap (_fda ,_ced ,"d\u0065\u0062\u0075\u0067Co\u0072r\u0065\u006c\u0061\u0074\u0069o\u006e\u0053\u0063\u006f\u0072\u0065");
};_abf ,_fda =_fb .CorrelationScoreSimple (_ed ,_cca ,_ffb ,_ded ,_bd -_cbd ,_gdba -_gee ,MaxDiffWidth ,MaxDiffHeight ,_gdb );if _fda !=nil {return _f .Wrap (_fda ,_ced ,"d\u0065\u0062\u0075\u0067Co\u0072r\u0065\u006c\u0061\u0074\u0069o\u006e\u0053\u0063\u006f\u0072\u0065");
};_cegb =int (_c .Sqrt (_aff *float64 (_ffb )*float64 (_ded )));_cbgc =int (_c .Sqrt (_abf *float64 (_ffb )*float64 (_ded )));if (_aff >=_ege )!=(_abf >=_ege ){return _f .Errorf (_ced ,"\u0064\u0065\u0062\u0075\u0067\u0020\u0043\u006f\u0072r\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0063\u006f\u0072\u0065\u0020\u006d\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0020-\u0020\u0025d\u0028\u00250\u002e\u0034\u0066\u002c\u0020\u0025\u0076\u0029\u0020\u0076\u0073\u0020\u0025d(\u0025\u0030\u002e\u0034\u0066\u002c\u0020\u0025\u0076)\u0020\u0025\u0030\u002e\u0034\u0066",_cegb ,_aff ,_aff >=_ege ,_cbgc ,_abf ,_abf >=_ege ,_aff -_abf );
};if _aff >=_ege !=_aef {return _f .Errorf (_ced ,"\u0064\u0065\u0062\u0075\u0067\u0020\u0043o\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e \u0073\u0063\u006f\u0072\u0065 \u004d\u0069\u0073\u006d\u0061t\u0063\u0068 \u0062\u0065\u0074w\u0065\u0065\u006e\u0020\u0063\u006frr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020/\u0020\u0074\u0068\u0072\u0065s\u0068\u006f\u006c\u0064\u002e\u0020\u0043\u006f\u006dpa\u0072\u0069\u0073\u006f\u006e:\u0020\u0025\u0030\u002e\u0034\u0066\u0028\u0025\u0030\u002e\u0034\u0066\u002c\u0020\u0025\u0064\u0029\u0020\u003e\u003d\u0020\u00250\u002e\u0034\u0066\u0028\u0025\u0030\u002e\u0034\u0066\u0029\u0020\u0076\u0073\u0020\u0025\u0076",_aff ,_aff *float64 (_ffb )*float64 (_ded ),_cegb ,_ege ,float32 (_ege )*float32 (_ffb )*float32 (_ded ),_aef );
};};if _aef {_fdg =true ;if _fda =_bff .ClassIDs .Add (_dg );_fda !=nil {return _f .Wrap (_fda ,_ced ,"\u006f\u0076\u0065\u0072\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006c\u0064");};if _fda =_bff .ComponentPageNumbers .Add (_feg );_fda !=nil {return _f .Wrap (_fda ,_ced ,"\u006f\u0076\u0065\u0072\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006c\u0064");
};if _bff .Settings .KeepClassInstances {if _gaf ,_fda =_gc .GetBitmap (_bg );_fda !=nil {return _f .Wrap (_fda ,_ced ,"\u004b\u0065\u0065\u0070Cl\u0061\u0073\u0073\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073\u0020\u002d \u0069");};if _cacb ,_fda =_bff .ClassInstances .GetBitmaps (_dg );
_fda !=nil {return _f .Wrap (_fda ,_ced ,"K\u0065\u0065\u0070\u0043\u006c\u0061s\u0073\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065s\u0020\u002d\u0020i\u0043l\u0061\u0073\u0073");};_cacb .AddBitmap (_gaf );if _ecg ,_fda =_afc .Get (_bg );_fda !=nil {return _f .Wrap (_fda ,_ced ,"\u004be\u0065p\u0043\u006c\u0061\u0073\u0073I\u006e\u0073t\u0061\u006e\u0063\u0065\u0073");
};_cacb .AddBox (_ecg );};break ;};};if !_fdg {if _fgfb =_bff .ClassIDs .Add (_cbe );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0021\u0066\u006f\u0075\u006e\u0064");};if _fgfb =_bff .ComponentPageNumbers .Add (_feg );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_cacb =&_fb .Bitmaps {};if _gaf ,_fgfb =_gc .GetBitmap (_bg );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_cacb .AddBitmap (_gaf );_gea ,_bge :=_gaf .Width ,_gaf .Height ;_bcf :=uint64 (_bge )*uint64 (_gea );_bff .TemplatesSize .Add (_bcf ,_cbe );
if _ecg ,_fgfb =_afc .Get (_bg );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_cacb .AddBox (_ecg );_bff .ClassInstances .AddBitmaps (_cacb );_bff .CentroidPointsTemplates .AddPoint (_bd ,_gdba );_bff .FgTemplates .AddInt (_ffb );
_bff .UndilatedTemplates .AddBitmap (_gaf );_agb =(_ed .Width -2*JbAddedPixels )*(_ed .Height -2*JbAddedPixels );if _fgfb =_bff .TemplateAreas .Add (_agb );_fgfb !=nil {return _f .Wrap (_fgfb ,_ced ,"\u0021\u0066\u006f\u0075\u006e\u0064");};};};_bff .NumberOfClasses =len (_bff .UndilatedTemplates .Values );
return nil ;};type Settings struct{MaxCompWidth int ;MaxCompHeight int ;SizeHaus int ;RankHaus float64 ;Thresh float64 ;WeightFactor float64 ;KeepClassInstances bool ;Components _fb .Component ;Method Method ;};func _daa (_ffc *Classer ,_ddb *_fb .Bitmap )*similarTemplatesFinder {return &similarTemplatesFinder {Width :_ddb .Width ,Height :_ddb .Height ,Classer :_ffc };
};func (_fg *Classer )ComputeLLCorners ()(_dd error ){const _fgg ="\u0043l\u0061\u0073\u0073\u0065\u0072\u002e\u0043\u006f\u006d\u0070\u0075t\u0065\u004c\u004c\u0043\u006f\u0072\u006e\u0065\u0072\u0073";if _fg .PtaUL ==nil {return _f .Error (_fgg ,"\u0055\u004c\u0020\u0043or\u006e\u0065\u0072\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");
};_dfd :=len (*_fg .PtaUL );_fg .PtaLL =&_fb .Points {};var (_b ,_fe float32 ;_gf ,_fd int ;_bf *_fb .Bitmap ;);for _fa :=0;_fa < _dfd ;_fa ++{_b ,_fe ,_dd =_fg .PtaUL .GetGeometry (_fa );if _dd !=nil {_e .Log .Debug ("\u0047e\u0074\u0074\u0069\u006e\u0067\u0020\u0050\u0074\u0061\u0055\u004c \u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_dd );
return _f .Wrap (_dd ,_fgg ,"\u0050\u0074\u0061\u0055\u004c\u0020\u0047\u0065\u006fm\u0065\u0074\u0072\u0079");};_gf ,_dd =_fg .ClassIDs .Get (_fa );if _dd !=nil {_e .Log .Debug ("\u0047\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0043\u006c\u0061s\u0073\u0049\u0044\u0020\u0066\u0061\u0069\u006c\u0065\u0064:\u0020\u0025\u0076",_dd );
return _f .Wrap (_dd ,_fgg ,"\u0043l\u0061\u0073\u0073\u0049\u0044");};_bf ,_dd =_fg .UndilatedTemplates .GetBitmap (_gf );if _dd !=nil {_e .Log .Debug ("\u0047\u0065t\u0074\u0069\u006e\u0067 \u0055\u006ed\u0069\u006c\u0061\u0074\u0065\u0064\u0054\u0065m\u0070\u006c\u0061\u0074\u0065\u0073\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_dd );
return _f .Wrap (_dd ,_fgg ,"\u0055\u006e\u0064\u0069la\u0074\u0065\u0064\u0020\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073");};_fd =_bf .Height ;_fg .PtaLL .AddPoint (_b ,_fe +float32 (_fd ));};return nil ;};func (_cac *Classer )verifyMethod (_cbg Method )error {if _cbg !=RankHaus &&_cbg !=Correlation {return _f .Error ("\u0076\u0065\u0072i\u0066\u0079\u004d\u0065\u0074\u0068\u006f\u0064","\u0069\u006e\u0076\u0061li\u0064\u0020\u0063\u006c\u0061\u0073\u0073\u0065\u0072\u0020\u006d\u0065\u0074\u0068o\u0064");
};return nil ;};func (_cb *Classer )getULCorners (_ff *_fb .Bitmap ,_cc *_fb .Boxes )error {const _cf ="\u0067\u0065\u0074U\u004c\u0043\u006f\u0072\u006e\u0065\u0072\u0073";if _ff ==nil {return _f .Error (_cf ,"\u006e\u0069l\u0020\u0069\u006da\u0067\u0065\u0020\u0062\u0069\u0074\u006d\u0061\u0070");
};if _cc ==nil {return _f .Error (_cf ,"\u006e\u0069\u006c\u0020\u0062\u006f\u0075\u006e\u0064\u0073");};if _cb .PtaUL ==nil {_cb .PtaUL =&_fb .Points {};};_ce :=len (*_cc );var (_ecb ,_bc ,_dbc ,_fgf int ;_aa ,_aee ,_eb ,_cae float32 ;_gfg error ;_af *_d .Rectangle ;
_cg *_fb .Bitmap ;_ad _d .Point ;);for _gad :=0;_gad < _ce ;_gad ++{_ecb =_cb .BaseIndex +_gad ;if _aa ,_aee ,_gfg =_cb .CentroidPoints .GetGeometry (_ecb );_gfg !=nil {return _f .Wrap (_gfg ,_cf ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0050o\u0069\u006e\u0074\u0073");
};if _bc ,_gfg =_cb .ClassIDs .Get (_ecb );_gfg !=nil {return _f .Wrap (_gfg ,_cf ,"\u0043\u006c\u0061s\u0073\u0049\u0044\u0073\u002e\u0047\u0065\u0074");};if _eb ,_cae ,_gfg =_cb .CentroidPointsTemplates .GetGeometry (_bc );_gfg !=nil {return _f .Wrap (_gfg ,_cf ,"\u0043\u0065\u006etr\u006f\u0069\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073");
};_eg :=_eb -_aa ;_be :=_cae -_aee ;if _eg >=0{_dbc =int (_eg +0.5);}else {_dbc =int (_eg -0.5);};if _be >=0{_fgf =int (_be +0.5);}else {_fgf =int (_be -0.5);};if _af ,_gfg =_cc .Get (_gad );_gfg !=nil {return _f .Wrap (_gfg ,_cf ,"");};_ac ,_ceg :=_af .Min .X ,_af .Min .Y ;
_cg ,_gfg =_cb .UndilatedTemplates .GetBitmap (_bc );if _gfg !=nil {return _f .Wrap (_gfg ,_cf ,"\u0055\u006e\u0064\u0069\u006c\u0061\u0074\u0065\u0064\u0054e\u006d\u0070\u006c\u0061\u0074\u0065\u0073.\u0047\u0065\u0074\u0028\u0069\u0043\u006c\u0061\u0073\u0073\u0029");
};_ad ,_gfg =_gac (_ff ,_ac ,_ceg ,_dbc ,_fgf ,_cg );if _gfg !=nil {return _f .Wrap (_gfg ,_cf ,"");};_cb .PtaUL .AddPoint (float32 (_ac -_dbc +_ad .X ),float32 (_ceg -_fgf +_ad .Y ));};return nil ;};type Classer struct{BaseIndex int ;Settings Settings ;
ComponentsNumber *_df .IntSlice ;TemplateAreas *_df .IntSlice ;Widths map[int ]int ;Heights map[int ]int ;NumberOfClasses int ;ClassInstances *_fb .BitmapsArray ;UndilatedTemplates *_fb .Bitmaps ;DilatedTemplates *_fb .Bitmaps ;TemplatesSize _df .IntsMap ;
FgTemplates *_df .NumSlice ;CentroidPoints *_fb .Points ;CentroidPointsTemplates *_fb .Points ;ClassIDs *_df .IntSlice ;ComponentPageNumbers *_df .IntSlice ;PtaUL *_fb .Points ;PtaLL *_fb .Points ;};func (_cadf *Classer )classifyRankHaus (_abg *_fb .Boxes ,_eca *_fb .Bitmaps ,_fbd int )error {const _gff ="\u0063\u006ca\u0073\u0073\u0069f\u0079\u0052\u0061\u006e\u006b\u0048\u0061\u0075\u0073";
if _abg ==nil {return _f .Error (_gff ,"\u0062\u006fx\u0061\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if _eca ==nil {return _f .Error (_gff ,"\u0070\u0069x\u0061\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};_gda :=len (_eca .Values );if _gda ==0{return _f .Error (_gff ,"e\u006dp\u0074\u0079\u0020\u006e\u0065\u0077\u0020\u0063o\u006d\u0070\u006f\u006een\u0074\u0073");};_fdc :=_eca .CountPixels ();_cdc :=_cadf .Settings .SizeHaus ;_cce :=_fb .SelCreateBrick (_cdc ,_cdc ,_cdc /2,_cdc /2,_fb .SelHit );
_dbg :=&_fb .Bitmaps {Values :make ([]*_fb .Bitmap ,_gda )};_dda :=&_fb .Bitmaps {Values :make ([]*_fb .Bitmap ,_gda )};var (_eba ,_caa ,_agaf *_fb .Bitmap ;_agbf error ;);for _affg :=0;_affg < _gda ;_affg ++{_eba ,_agbf =_eca .GetBitmap (_affg );if _agbf !=nil {return _f .Wrap (_agbf ,_gff ,"");
};_caa ,_agbf =_eba .AddBorderGeneral (JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,0);if _agbf !=nil {return _f .Wrap (_agbf ,_gff ,"");};_agaf ,_agbf =_fb .Dilate (nil ,_caa ,_cce );if _agbf !=nil {return _f .Wrap (_agbf ,_gff ,"");};_dbg .Values [_gda ]=_caa ;
_dda .Values [_gda ]=_agaf ;};_cbb ,_agbf :=_fb .Centroids (_dbg .Values );if _agbf !=nil {return _f .Wrap (_agbf ,_gff ,"");};if _agbf =_cbb .Add (_cadf .CentroidPoints );_agbf !=nil {_e .Log .Trace ("\u004e\u006f\u0020\u0063en\u0074\u0072\u006f\u0069\u0064\u0073\u0020\u0074\u006f\u0020\u0061\u0064\u0064");
};if _cadf .Settings .RankHaus ==1.0{_agbf =_cadf .classifyRankHouseOne (_abg ,_eca ,_dbg ,_dda ,_cbb ,_fbd );}else {_agbf =_cadf .classifyRankHouseNonOne (_abg ,_eca ,_dbg ,_dda ,_cbb ,_fdc ,_fbd );};if _agbf !=nil {return _f .Wrap (_agbf ,_gff ,"");};
return nil ;};var TwoByTwoWalk =[]int {0,0,0,1,-1,0,0,-1,1,0,-1,1,1,1,-1,-1,1,-1,0,-2,2,0,0,2,-2,0,-1,-2,1,-2,2,-1,2,1,1,2,-1,2,-2,1,-2,-1,-2,-2,2,-2,2,2,-2,2};func (_afd *similarTemplatesFinder )Next ()int {var (_gfc ,_bcd ,_ddd ,_bba int ;_dag bool ;
_bgf *_fb .Bitmap ;_fbb error ;);for {if _afd .Index >=25{return -1;};_bcd =_afd .Width +TwoByTwoWalk [2*_afd .Index ];_gfc =_afd .Height +TwoByTwoWalk [2*_afd .Index +1];if _gfc < 1||_bcd < 1{_afd .Index ++;continue ;};if len (_afd .CurrentNumbers )==0{_afd .CurrentNumbers ,_dag =_afd .Classer .TemplatesSize .GetSlice (uint64 (_bcd )*uint64 (_gfc ));
if !_dag {_afd .Index ++;continue ;};_afd .N =0;};_ddd =len (_afd .CurrentNumbers );for ;_afd .N < _ddd ;_afd .N ++{_bba =_afd .CurrentNumbers [_afd .N ];_bgf ,_fbb =_afd .Classer .DilatedTemplates .GetBitmap (_bba );if _fbb !=nil {_e .Log .Debug ("\u0046\u0069\u006e\u0064\u004e\u0065\u0078\u0074\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u003a\u0020\u0074\u0065\u006d\u0070\u006c\u0061t\u0065\u0020\u006e\u006f\u0074 \u0066\u006fu\u006e\u0064\u003a\u0020");
return 0;};if _bgf .Width -2*JbAddedPixels ==_bcd &&_bgf .Height -2*JbAddedPixels ==_gfc {return _bba ;};};_afd .Index ++;_afd .CurrentNumbers =nil ;};};func (_gg *Classer )addPageComponents (_age *_fb .Bitmap ,_ca *_fb .Boxes ,_ea *_fb .Bitmaps ,_fc int ,_efc Method )error {const _ba ="\u0043l\u0061\u0073\u0073\u0065r\u002e\u0041\u0064\u0064\u0050a\u0067e\u0043o\u006d\u0070\u006f\u006e\u0065\u006e\u0074s";
if _age ==nil {return _f .Error (_ba ,"\u006e\u0069\u006c\u0020\u0069\u006e\u0070\u0075\u0074 \u0070\u0061\u0067\u0065");};if _ca ==nil ||_ea ==nil ||len (*_ca )==0{_e .Log .Trace ("\u0041\u0064\u0064P\u0061\u0067\u0065\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u003a\u0020\u0025\u0073\u002e\u0020\u004e\u006f\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074\u0073\u0020\u0066\u006f\u0075\u006e\u0064",_age );
return nil ;};var _ga error ;switch _efc {case RankHaus :_ga =_gg .classifyRankHaus (_ca ,_ea ,_fc );case Correlation :_ga =_gg .classifyCorrelation (_ca ,_ea ,_fc );default:_e .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077\u006e\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0079\u0020\u006d\u0065\u0074\u0068\u006f\u0064\u003a\u0020'%\u0076\u0027",_efc );
return _f .Error (_ba ,"\u0075\u006e\u006bno\u0077\u006e\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0079\u0020\u006d\u0065\u0074\u0068\u006f\u0064");};if _ga !=nil {return _f .Wrap (_ga ,_ba ,"");};if _ga =_gg .getULCorners (_age ,_ca );_ga !=nil {return _f .Wrap (_ga ,_ba ,"");
};_ec :=len (*_ca );_gg .BaseIndex +=_ec ;if _ga =_gg .ComponentsNumber .Add (_ec );_ga !=nil {return _f .Wrap (_ga ,_ba ,"");};return nil ;};const (RankHaus Method =iota ;Correlation ;);type similarTemplatesFinder struct{Classer *Classer ;Width int ;Height int ;
Index int ;CurrentNumbers []int ;N int ;};const JbAddedPixels =6;type Method int ;func _gac (_cgg *_fb .Bitmap ,_fdd ,_add ,_bb ,_ada int ,_aca *_fb .Bitmap )(_fdf _d .Point ,_dbf error ){const _bfg ="\u0066i\u006e\u0061\u006c\u0041l\u0069\u0067\u006e\u006d\u0065n\u0074P\u006fs\u0069\u0074\u0069\u006f\u006e\u0069\u006eg";
if _cgg ==nil {return _fdf ,_f .Error (_bfg ,"\u0073\u006f\u0075\u0072ce\u0020\u006e\u006f\u0074\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064");};if _aca ==nil {return _fdf ,_f .Error (_bfg ,"t\u0065\u006d\u0070\u006cat\u0065 \u006e\u006f\u0074\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064");
};_ge ,_ecd :=_aca .Width ,_aca .Height ;_adg ,_dc :=_fdd -_bb -JbAddedPixels ,_add -_ada -JbAddedPixels ;_e .Log .Trace ("\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0079\u003a\u0020\u0027\u0025\u0064'\u002c\u0020\u0077\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0068\u003a \u0027\u0025\u0064\u0027\u002c\u0020\u0062\u0078\u003a\u0020\u0027\u0025d'\u002c\u0020\u0062\u0079\u003a\u0020\u0027\u0025\u0064\u0027",_fdd ,_add ,_ge ,_ecd ,_adg ,_dc );
_cd ,_dbf :=_fb .Rect (_adg ,_dc ,_ge ,_ecd );if _dbf !=nil {return _fdf ,_f .Wrap (_dbf ,_bfg ,"");};_cge ,_ ,_dbf :=_cgg .ClipRectangle (_cd );if _dbf !=nil {_e .Log .Error ("\u0043a\u006e\u0027\u0074\u0020\u0063\u006c\u0069\u0070\u0020\u0072\u0065c\u0074\u0061\u006e\u0067\u006c\u0065\u003a\u0020\u0025\u0076",_cd );
return _fdf ,_f .Wrap (_dbf ,_bfg ,"");};_bec :=_fb .New (_cge .Width ,_cge .Height );_ddc :=_c .MaxInt32 ;var _aad ,_efd ,_cff ,_de ,_ee int ;for _aad =-1;_aad <=1;_aad ++{for _efd =-1;_efd <=1;_efd ++{if _ ,_dbf =_fb .Copy (_bec ,_cge );_dbf !=nil {return _fdf ,_f .Wrap (_dbf ,_bfg ,"");
};if _dbf =_bec .RasterOperation (_efd ,_aad ,_ge ,_ecd ,_fb .PixSrcXorDst ,_aca ,0,0);_dbf !=nil {return _fdf ,_f .Wrap (_dbf ,_bfg ,"");};_cff =_bec .CountPixels ();if _cff < _ddc {_de =_efd ;_ee =_aad ;_ddc =_cff ;};};};_fdf .X =_de ;_fdf .Y =_ee ;return _fdf ,nil ;
};func Init (settings Settings )(*Classer ,error ){const _db ="\u0063\u006c\u0061s\u0073\u0065\u0072\u002e\u0049\u006e\u0069\u0074";_dfg :=&Classer {Settings :settings ,Widths :map[int ]int {},Heights :map[int ]int {},TemplatesSize :_df .IntsMap {},TemplateAreas :&_df .IntSlice {},ComponentPageNumbers :&_df .IntSlice {},ClassIDs :&_df .IntSlice {},ComponentsNumber :&_df .IntSlice {},CentroidPoints :&_fb .Points {},CentroidPointsTemplates :&_fb .Points {},UndilatedTemplates :&_fb .Bitmaps {},DilatedTemplates :&_fb .Bitmaps {},ClassInstances :&_fb .BitmapsArray {},FgTemplates :&_df .NumSlice {}};
if _ef :=_dfg .Settings .Validate ();_ef !=nil {return nil ,_f .Wrap (_ef ,_db ,"");};return _dfg ,nil ;};func (_dbce *Classer )classifyRankHouseOne (_aeb *_fb .Boxes ,_egg ,_agag ,_afb *_fb .Bitmaps ,_dga *_fb .Points ,_cee int )(_cdd error ){const _aea ="\u0043\u006c\u0061\u0073s\u0065\u0072\u002e\u0063\u006c\u0061\u0073\u0073\u0069\u0066y\u0052a\u006e\u006b\u0048\u006f\u0075\u0073\u0065O\u006e\u0065";
var (_fga ,_fdgg ,_gab ,_edb float32 ;_bfc int ;_adaf ,_ecac ,_dcf ,_fad ,_cggd *_fb .Bitmap ;_ecf ,_cea bool ;);for _dfff :=0;_dfff < len (_egg .Values );_dfff ++{_ecac =_agag .Values [_dfff ];_dcf =_afb .Values [_dfff ];_fga ,_fdgg ,_cdd =_dga .GetGeometry (_dfff );
if _cdd !=nil {return _f .Wrapf (_cdd ,_aea ,"\u0066\u0069\u0072\u0073\u0074\u0020\u0067\u0065\u006fm\u0065\u0074\u0072\u0079");};_dbgb :=len (_dbce .UndilatedTemplates .Values );_ecf =false ;_efae :=_daa (_dbce ,_ecac );for _bfc =_efae .Next ();_bfc > -1;
{_fad ,_cdd =_dbce .UndilatedTemplates .GetBitmap (_bfc );if _cdd !=nil {return _f .Wrap (_cdd ,_aea ,"\u0062\u006d\u0033");};_cggd ,_cdd =_dbce .DilatedTemplates .GetBitmap (_bfc );if _cdd !=nil {return _f .Wrap (_cdd ,_aea ,"\u0062\u006d\u0034");};_gab ,_edb ,_cdd =_dbce .CentroidPointsTemplates .GetGeometry (_bfc );
if _cdd !=nil {return _f .Wrap (_cdd ,_aea ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0054\u0065\u006d\u0070l\u0061\u0074\u0065\u0073");};_cea ,_cdd =_fb .HausTest (_ecac ,_dcf ,_fad ,_cggd ,_fga -_gab ,_fdgg -_edb ,MaxDiffWidth ,MaxDiffHeight );
if _cdd !=nil {return _f .Wrap (_cdd ,_aea ,"");};if _cea {_ecf =true ;if _cdd =_dbce .ClassIDs .Add (_bfc );_cdd !=nil {return _f .Wrap (_cdd ,_aea ,"");};if _cdd =_dbce .ComponentPageNumbers .Add (_cee );_cdd !=nil {return _f .Wrap (_cdd ,_aea ,"");};
if _dbce .Settings .KeepClassInstances {_ggb ,_efaf :=_dbce .ClassInstances .GetBitmaps (_bfc );if _efaf !=nil {return _f .Wrap (_efaf ,_aea ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");};_adaf ,_efaf =_egg .GetBitmap (_dfff );if _efaf !=nil {return _f .Wrap (_efaf ,_aea ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");
};_ggb .AddBitmap (_adaf );_afbd ,_efaf :=_aeb .Get (_dfff );if _efaf !=nil {return _f .Wrap (_efaf ,_aea ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");};_ggb .AddBox (_afbd );};break ;};};if !_ecf {if _cdd =_dbce .ClassIDs .Add (_dbgb );_cdd !=nil {return _f .Wrap (_cdd ,_aea ,"");
};if _cdd =_dbce .ComponentPageNumbers .Add (_cee );_cdd !=nil {return _f .Wrap (_cdd ,_aea ,"");};_cfd :=&_fb .Bitmaps {};_adaf ,_cdd =_egg .GetBitmap (_dfff );if _cdd !=nil {return _f .Wrap (_cdd ,_aea ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_cfd .Values =append (_cfd .Values ,_adaf );
_ede ,_abc :=_adaf .Width ,_adaf .Height ;_dbce .TemplatesSize .Add (uint64 (_abc )*uint64 (_ede ),_dbgb );_fgc ,_dfe :=_aeb .Get (_dfff );if _dfe !=nil {return _f .Wrap (_dfe ,_aea ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_cfd .AddBox (_fgc );_dbce .ClassInstances .AddBitmaps (_cfd );
_dbce .CentroidPointsTemplates .AddPoint (_fga ,_fdgg );_dbce .UndilatedTemplates .AddBitmap (_ecac );_dbce .DilatedTemplates .AddBitmap (_dcf );};};return nil ;};