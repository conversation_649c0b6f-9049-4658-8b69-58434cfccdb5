//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package bitmap ;import (_ba "encoding/binary";_c "github.com/stretchr/testify/require";_bg "github.com/unidoc/unipdf/v4/common";_a "github.com/unidoc/unipdf/v4/internal/bitwise";_dg "github.com/unidoc/unipdf/v4/internal/imageutil";_ec "github.com/unidoc/unipdf/v4/internal/jbig2/basic";
_d "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_ce "image";_b "math";_ee "sort";_f "strings";_ed "testing";);func (_efdg *Boxes )selectWithIndicator (_cafc *_ec .NumSlice )(_geca *Boxes ,_dgdg error ){const _dggfa ="\u0042o\u0078\u0065\u0073\u002es\u0065\u006c\u0065\u0063\u0074W\u0069t\u0068I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _efdg ==nil {return nil ,_d .Error (_dggfa ,"b\u006f\u0078\u0065\u0073 '\u0062'\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064");};if _cafc ==nil {return nil ,_d .Error (_dggfa ,"\u0027\u006ea\u0027\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if len (*_cafc )!=len (*_efdg ){return nil ,_d .Error (_dggfa ,"\u0062\u006f\u0078\u0065\u0073\u0020\u0027\u0062\u0027\u0020\u0068\u0061\u0073\u0020\u0064\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0074\u0020s\u0069\u007a\u0065\u0020\u0074h\u0061\u006e \u0027\u006e\u0061\u0027");
};var _ffd ,_ceae int ;for _cdgb :=0;_cdgb < len (*_cafc );_cdgb ++{if _ffd ,_dgdg =_cafc .GetInt (_cdgb );_dgdg !=nil {return nil ,_d .Wrap (_dgdg ,_dggfa ,"\u0063\u0068\u0065\u0063\u006b\u0069\u006e\u0067\u0020c\u006f\u0075\u006e\u0074");};if _ffd ==1{_ceae ++;
};};if _ceae ==len (*_efdg ){return _efdg ,nil ;};_aeeg :=Boxes {};for _daa :=0;_daa < len (*_cafc );_daa ++{_ffd =int ((*_cafc )[_daa ]);if _ffd ==0{continue ;};_aeeg =append (_aeeg ,(*_efdg )[_daa ]);};_geca =&_aeeg ;return _geca ,nil ;};func _gbbf (_bdec *Bitmap ,_eeab ,_decca int ,_acdaff ,_fef int ,_cdda RasterOperator ){var (_ddfac bool ;
_fefa bool ;_debd int ;_fcdf int ;_eeeb int ;_fecab int ;_dcag bool ;_gag byte ;);_fffc :=8-(_eeab &7);_ebeg :=_ffgb [_fffc ];_dfbc :=_bdec .RowStride *_decca +(_eeab >>3);if _acdaff < _fffc {_ddfac =true ;_ebeg &=_bcbg [8-_fffc +_acdaff ];};if !_ddfac {_debd =(_acdaff -_fffc )>>3;
if _debd !=0{_fefa =true ;_fcdf =_dfbc +1;};};_eeeb =(_eeab +_acdaff )&7;if !(_ddfac ||_eeeb ==0){_dcag =true ;_gag =_bcbg [_eeeb ];_fecab =_dfbc +1+_debd ;};var _cfeea ,_gaad int ;switch _cdda {case PixClr :for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_dfbc ]=_eead (_bdec .Data [_dfbc ],0x0,_ebeg );
_dfbc +=_bdec .RowStride ;};if _fefa {for _cfeea =0;_cfeea < _fef ;_cfeea ++{for _gaad =0;_gaad < _debd ;_gaad ++{_bdec .Data [_fcdf +_gaad ]=0x0;};_fcdf +=_bdec .RowStride ;};};if _dcag {for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_fecab ]=_eead (_bdec .Data [_fecab ],0x0,_gag );
_fecab +=_bdec .RowStride ;};};case PixSet :for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_dfbc ]=_eead (_bdec .Data [_dfbc ],0xff,_ebeg );_dfbc +=_bdec .RowStride ;};if _fefa {for _cfeea =0;_cfeea < _fef ;_cfeea ++{for _gaad =0;_gaad < _debd ;_gaad ++{_bdec .Data [_fcdf +_gaad ]=0xff;
};_fcdf +=_bdec .RowStride ;};};if _dcag {for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_fecab ]=_eead (_bdec .Data [_fecab ],0xff,_gag );_fecab +=_bdec .RowStride ;};};case PixNotDst :for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_dfbc ]=_eead (_bdec .Data [_dfbc ],^_bdec .Data [_dfbc ],_ebeg );
_dfbc +=_bdec .RowStride ;};if _fefa {for _cfeea =0;_cfeea < _fef ;_cfeea ++{for _gaad =0;_gaad < _debd ;_gaad ++{_bdec .Data [_fcdf +_gaad ]=^(_bdec .Data [_fcdf +_gaad ]);};_fcdf +=_bdec .RowStride ;};};if _dcag {for _cfeea =0;_cfeea < _fef ;_cfeea ++{_bdec .Data [_fecab ]=_eead (_bdec .Data [_fecab ],^_bdec .Data [_fecab ],_gag );
_fecab +=_bdec .RowStride ;};};};};func (_fcfg MorphProcess )verify (_bfbg int ,_abac ,_gabgf *int )error {const _cgbg ="\u004d\u006f\u0072\u0070hP\u0072\u006f\u0063\u0065\u0073\u0073\u002e\u0076\u0065\u0072\u0069\u0066\u0079";switch _fcfg .Operation {case MopDilation ,MopErosion ,MopOpening ,MopClosing :if len (_fcfg .Arguments )!=2{return _d .Error (_cgbg ,"\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0064\u0027\u002c\u0020\u0027\u0065\u0027\u002c \u0027\u006f\u0027\u002c\u0020\u0027\u0063\u0027\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0032\u0020\u0061r\u0067\u0075\u006d\u0065\u006et\u0073");
};_dfga ,_fdcd :=_fcfg .getWidthHeight ();if _dfga <=0||_fdcd <=0{return _d .Error (_cgbg ,"O\u0070er\u0061t\u0069o\u006e\u003a\u0020\u0027\u0064'\u002c\u0020\u0027e\u0027\u002c\u0020\u0027\u006f'\u002c\u0020\u0027c\u0027\u0020\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073 \u0062\u006f\u0074h w\u0069\u0064\u0074\u0068\u0020\u0061n\u0064\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020\u0074\u006f\u0020b\u0065 \u003e\u003d\u0020\u0030");
};case MopRankBinaryReduction :_ece :=len (_fcfg .Arguments );*_abac +=_ece ;if _ece < 1||_ece > 4{return _d .Error (_cgbg ,"\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0072\u0027\u0020\u0072\u0065\u0071\u0075\u0069r\u0065\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061s\u0074\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0061\u0074\u0020\u006d\u006fs\u0074\u0020\u0034\u0020\u0061\u0072g\u0075\u006d\u0065n\u0074\u0073");
};for _ebca :=0;_ebca < _ece ;_ebca ++{if _fcfg .Arguments [_ebca ]< 1||_fcfg .Arguments [_ebca ]> 4{return _d .Error (_cgbg ,"\u0052\u0061\u006e\u006b\u0042\u0069n\u0061\u0072\u0079\u0052\u0065\u0064\u0075\u0063\u0074\u0069\u006f\u006e\u0020\u006c\u0065\u0076\u0065\u006c\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065 \u00280\u002c\u0020\u0034\u003e");
};};case MopReplicativeBinaryExpansion :if len (_fcfg .Arguments )==0{return _d .Error (_cgbg ,"\u0052\u0065\u0070\u006c\u0069\u0063\u0061\u0074i\u0076\u0065\u0042in\u0061\u0072\u0079\u0045\u0078\u0070a\u006e\u0073\u0069\u006f\u006e\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020o\u006e\u0065\u0020\u0061\u0072\u0067\u0075\u006de\u006e\u0074");
};_acef :=_fcfg .Arguments [0];if _acef !=2&&_acef !=4&&_acef !=8{return _d .Error (_cgbg ,"R\u0065\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u0076\u0065\u0042\u0069\u006e\u0061\u0072\u0079\u0045\u0078\u0070\u0061\u006e\u0073\u0069\u006f\u006e\u0020m\u0075s\u0074\u0020\u0062\u0065 \u006f\u0066 \u0066\u0061\u0063\u0074\u006f\u0072\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d");
};*_abac -=_ccge [_acef /4];case MopAddBorder :if len (_fcfg .Arguments )==0{return _d .Error (_cgbg ,"\u0041\u0064\u0064B\u006f\u0072\u0064\u0065r\u0020\u0072\u0065\u0071\u0075\u0069\u0072e\u0073\u0020\u006f\u006e\u0065\u0020\u0061\u0072\u0067\u0075\u006d\u0065\u006e\u0074");
};_aafdg :=_fcfg .Arguments [0];if _bfbg > 0{return _d .Error (_cgbg ,"\u0041\u0064\u0064\u0042\u006f\u0072\u0064\u0065\u0072\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0061\u0020f\u0069\u0072\u0073\u0074\u0020\u006d\u006f\u0072\u0070\u0068\u0020\u0070\u0072o\u0063\u0065\u0073\u0073");
};if _aafdg < 1{return _d .Error (_cgbg ,"\u0041\u0064\u0064\u0042o\u0072\u0064\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065 \u006co\u0077\u0065\u0072\u0020\u0074\u0068\u0061n\u0020\u0030");};*_gabgf =_aafdg ;};return nil ;};func Centroid (bm *Bitmap ,centTab ,sumTab []int )(Point ,error ){return bm .centroid (centTab ,sumTab )};
var (_bcbg =[]byte {0x00,0x80,0xC0,0xE0,0xF0,0xF8,0xFC,0xFE,0xFF};_ffgb =[]byte {0x00,0x01,0x03,0x07,0x0F,0x1F,0x3F,0x7F,0xFF};);func (_afc *Bitmap )SetPixel (x ,y int ,pixel byte )error {_cfca :=_afc .GetByteIndex (x ,y );if _cfca > len (_afc .Data )-1{return _d .Errorf ("\u0053\u0065\u0074\u0050\u0069\u0078\u0065\u006c","\u0069\u006e\u0064\u0065x \u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u003a\u0020%\u0064",_cfca );
};_egfaa :=_afc .GetBitOffset (x );_geb :=uint (7-_egfaa );_gdfd :=_afc .Data [_cfca ];var _edag byte ;if pixel ==1{_edag =_gdfd |(pixel &0x01<<_geb );}else {_edag =_gdfd &^(1<<_geb );};_afc .Data [_cfca ]=_edag ;return nil ;};func (_agd *Bitmap )setBit (_defd int ){_agd .Data [(_defd >>3)]|=0x80>>uint (_defd &7)};
func (_debb *Bitmaps )SortByWidth (){_bagab :=(*byWidth )(_debb );_ee .Sort (_bagab )};func TstRSymbol (t *_ed .T ,scale ...int )*Bitmap {_bfgb ,_gddg :=NewWithData (4,5,[]byte {0xF0,0x90,0xF0,0xA0,0x90});_c .NoError (t ,_gddg );return TstGetScaledSymbol (t ,_bfgb ,scale ...);
};type MorphOperation int ;type byHeight Bitmaps ;func (_fcba *Selection )findMaxTranslations ()(_cacdd ,_ebce ,_caec ,_fdfe int ){for _agegde :=0;_agegde < _fcba .Height ;_agegde ++{for _ffgbf :=0;_ffgbf < _fcba .Width ;_ffgbf ++{if _fcba .Data [_agegde ][_ffgbf ]==SelHit {_cacdd =_gffac (_cacdd ,_fcba .Cx -_ffgbf );
_ebce =_gffac (_ebce ,_fcba .Cy -_agegde );_caec =_gffac (_caec ,_ffgbf -_fcba .Cx );_fdfe =_gffac (_fdfe ,_agegde -_fcba .Cy );};};};return _cacdd ,_ebce ,_caec ,_fdfe ;};func NewWithData (width ,height int ,data []byte )(*Bitmap ,error ){const _dfg ="N\u0065\u0077\u0057\u0069\u0074\u0068\u0044\u0061\u0074\u0061";
_gab :=_cca (width ,height );_gab .Data =data ;if len (data )< height *_gab .RowStride {return nil ,_d .Errorf (_dfg ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061\u0020l\u0065\u006e\u0067\u0074\u0068\u003a \u0025\u0064\u0020\u002d\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062e\u003a\u0020\u0025\u0064",len (data ),height *_gab .RowStride );
};return _gab ,nil ;};func (_dacd *Bitmap )resizeImageData (_bddb *Bitmap )error {if _bddb ==nil {return _d .Error ("\u0072e\u0073i\u007a\u0065\u0049\u006d\u0061\u0067\u0065\u0044\u0061\u0074\u0061","\u0073r\u0063 \u0069\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _dacd .SizesEqual (_bddb ){return nil ;};_dacd .Data =make ([]byte ,len (_bddb .Data ));_dacd .Width =_bddb .Width ;_dacd .Height =_bddb .Height ;_dacd .RowStride =_bddb .RowStride ;return nil ;};func _cacd (_cegd *Bitmap ,_adb ,_fccf ,_gdeg ,_bbga int ,_eddf RasterOperator ,_bcbe *Bitmap ,_dfef ,_efged int )error {var (_ffdf bool ;
_gcgg bool ;_ebdb byte ;_bea int ;_dfb int ;_gfbg int ;_aebg int ;_aggfg bool ;_cedeg int ;_ddae int ;_gegf int ;_aefe bool ;_bbge byte ;_cbfbe int ;_eccf int ;_bbda int ;_bdeb byte ;_ebdf int ;_bcgbf int ;_edefe uint ;_gdba uint ;_fdag byte ;_ecce shift ;
_bcdfe bool ;_decb bool ;_cafgc ,_fdfag int ;);if _dfef &7!=0{_bcgbf =8-(_dfef &7);};if _adb &7!=0{_dfb =8-(_adb &7);};if _bcgbf ==0&&_dfb ==0{_fdag =_ffgb [0];}else {if _dfb > _bcgbf {_edefe =uint (_dfb -_bcgbf );}else {_edefe =uint (8-(_bcgbf -_dfb ));
};_gdba =8-_edefe ;_fdag =_ffgb [_edefe ];};if (_adb &7)!=0{_ffdf =true ;_bea =8-(_adb &7);_ebdb =_ffgb [_bea ];_gfbg =_cegd .RowStride *_fccf +(_adb >>3);_aebg =_bcbe .RowStride *_efged +(_dfef >>3);_ebdf =8-(_dfef &7);if _bea > _ebdf {_ecce =_gbgc ;if _gdeg >=_bcgbf {_bcdfe =true ;
};}else {_ecce =_dada ;};};if _gdeg < _bea {_gcgg =true ;_ebdb &=_bcbg [8-_bea +_gdeg ];};if !_gcgg {_cedeg =(_gdeg -_bea )>>3;if _cedeg !=0{_aggfg =true ;_ddae =_cegd .RowStride *_fccf +((_adb +_dfb )>>3);_gegf =_bcbe .RowStride *_efged +((_dfef +_dfb )>>3);
};};_cbfbe =(_adb +_gdeg )&7;if !(_gcgg ||_cbfbe ==0){_aefe =true ;_bbge =_bcbg [_cbfbe ];_eccf =_cegd .RowStride *_fccf +((_adb +_dfb )>>3)+_cedeg ;_bbda =_bcbe .RowStride *_efged +((_dfef +_dfb )>>3)+_cedeg ;if _cbfbe > int (_gdba ){_decb =true ;};};
switch _eddf {case PixSrc :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb ,_ebdb );
_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]=_bdeb ;
};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb ,_bbge );
_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixNotSrc :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;
};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^_bdeb ,_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );
_cegd .Data [_ddae +_fdfag ]=^_bdeb ;};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );
};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^_bdeb ,_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixSrcOrDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );
};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb |_cegd .Data [_gfbg ],_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;
_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]|=_bdeb ;};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;
_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb |_cegd .Data [_eccf ],_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;
};};case PixSrcAndDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb &_cegd .Data [_gfbg ],_ebdb );
_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]&=_bdeb ;
};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb &_cegd .Data [_eccf ],_bbge );
_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixSrcXorDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};
}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb ^_cegd .Data [_gfbg ],_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;
_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]^=_bdeb ;};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;
if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb ^_cegd .Data [_eccf ],_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixNotSrcOrDst :if _ffdf {for _cafgc =0;
_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^_bdeb |_cegd .Data [_gfbg ],_ebdb );
_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]|=^_bdeb ;
};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^_bdeb |_cegd .Data [_eccf ],_bbge );
_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixNotSrcAndDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );
};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^_bdeb &_cegd .Data [_gfbg ],_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;
_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]&=^_bdeb ;};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;
_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^_bdeb &_cegd .Data [_eccf ],_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;
};};case PixSrcOrNotDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb |^_cegd .Data [_gfbg ],_ebdb );
_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]=_bdeb |^_cegd .Data [_ddae +_fdfag ];
};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb |^_cegd .Data [_eccf ],_bbge );
_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixSrcAndNotDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );
};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],_bdeb &^_cegd .Data [_gfbg ],_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;
_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]=_bdeb &^_cegd .Data [_ddae +_fdfag ];};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;
_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],_bdeb &^_cegd .Data [_eccf ],_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;
};};case PixNotPixSrcOrDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^(_bdeb |_cegd .Data [_gfbg ]),_ebdb );
_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]=^(_bdeb |_cegd .Data [_ddae +_fdfag ]);
};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^(_bdeb |_cegd .Data [_eccf ]),_bbge );
_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};case PixNotPixSrcAndDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );
};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^(_bdeb &_cegd .Data [_gfbg ]),_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;
_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );_cegd .Data [_ddae +_fdfag ]=^(_bdeb &_cegd .Data [_ddae +_fdfag ]);};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};
if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^(_bdeb &_cegd .Data [_eccf ]),_bbge );_eccf +=_cegd .RowStride ;
_bbda +=_bcbe .RowStride ;};};case PixNotPixSrcXorDst :if _ffdf {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{if _ecce ==_gbgc {_bdeb =_bcbe .Data [_aebg ]<<_edefe ;if _bcdfe {_bdeb =_eead (_bdeb ,_bcbe .Data [_aebg +1]>>_gdba ,_fdag );};}else {_bdeb =_bcbe .Data [_aebg ]>>_gdba ;
};_cegd .Data [_gfbg ]=_eead (_cegd .Data [_gfbg ],^(_bdeb ^_cegd .Data [_gfbg ]),_ebdb );_gfbg +=_cegd .RowStride ;_aebg +=_bcbe .RowStride ;};};if _aggfg {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{for _fdfag =0;_fdfag < _cedeg ;_fdfag ++{_bdeb =_eead (_bcbe .Data [_gegf +_fdfag ]<<_edefe ,_bcbe .Data [_gegf +_fdfag +1]>>_gdba ,_fdag );
_cegd .Data [_ddae +_fdfag ]=^(_bdeb ^_cegd .Data [_ddae +_fdfag ]);};_ddae +=_cegd .RowStride ;_gegf +=_bcbe .RowStride ;};};if _aefe {for _cafgc =0;_cafgc < _bbga ;_cafgc ++{_bdeb =_bcbe .Data [_bbda ]<<_edefe ;if _decb {_bdeb =_eead (_bdeb ,_bcbe .Data [_bbda +1]>>_gdba ,_fdag );
};_cegd .Data [_eccf ]=_eead (_cegd .Data [_eccf ],^(_bdeb ^_cegd .Data [_eccf ]),_bbge );_eccf +=_cegd .RowStride ;_bbda +=_bcbe .RowStride ;};};default:_bg .Log .Debug ("\u004f\u0070e\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069tt\u0065\u0064",_eddf );
return _d .Error ("\u0072a\u0073t\u0065\u0072\u004f\u0070\u0047e\u006e\u0065r\u0061\u006c\u004c\u006f\u0077","\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065r\u0061\u0074\u0069\u006f\u006e\u0020\u006eo\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064");
};return nil ;};func _aecg (_agfc ,_edbea ,_deec *Bitmap )(*Bitmap ,error ){const _gcef ="\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0078\u006f\u0072";if _edbea ==nil {return nil ,_d .Error (_gcef ,"'\u0062\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");
};if _deec ==nil {return nil ,_d .Error (_gcef ,"'\u0062\u0032\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};if _agfc ==_deec {return nil ,_d .Error (_gcef ,"'\u0064\u0027\u0020\u003d\u003d\u0020\u0027\u0062\u0032\u0027");};if !_edbea .SizesEqual (_deec ){_bg .Log .Debug ("\u0025s\u0020\u002d \u0042\u0069\u0074\u006da\u0070\u0020\u0027b\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074 e\u0071\u0075\u0061l\u0020\u0073i\u007a\u0065\u0020\u0077\u0069\u0074h\u0020\u0027b\u0032\u0027",_gcef );
};var _fee error ;if _agfc ,_fee =_daed (_agfc ,_edbea );_fee !=nil {return nil ,_d .Wrap (_fee ,_gcef ,"\u0063\u0061n\u0027\u0074\u0020c\u0072\u0065\u0061\u0074\u0065\u0020\u0027\u0064\u0027");};if _fee =_agfc .RasterOperation (0,0,_agfc .Width ,_agfc .Height ,PixSrcXorDst ,_deec ,0,0);
_fee !=nil {return nil ,_d .Wrap (_fee ,_gcef ,"");};return _agfc ,nil ;};func _edga (_ffeg ,_afeb *Bitmap ,_dcdg CombinationOperator )*Bitmap {_ffg :=New (_ffeg .Width ,_ffeg .Height );for _ggae :=0;_ggae < len (_ffg .Data );_ggae ++{_ffg .Data [_ggae ]=_cdfe (_ffeg .Data [_ggae ],_afeb .Data [_ggae ],_dcdg );
};return _ffg ;};func init (){for _dda :=0;_dda < 256;_dda ++{_ebf [_dda ]=uint8 (_dda &0x1)+(uint8 (_dda >>1)&0x1)+(uint8 (_dda >>2)&0x1)+(uint8 (_dda >>3)&0x1)+(uint8 (_dda >>4)&0x1)+(uint8 (_dda >>5)&0x1)+(uint8 (_dda >>6)&0x1)+(uint8 (_dda >>7)&0x1);
};};func TstDSymbol (t *_ed .T ,scale ...int )*Bitmap {_baec ,_ffffb :=NewWithData (4,5,[]byte {0xf0,0x90,0x90,0x90,0xE0});_c .NoError (t ,_ffffb );return TstGetScaledSymbol (t ,_baec ,scale ...);};func _gffac (_acdd ,_ebdaa int )int {if _acdd > _ebdaa {return _acdd ;
};return _ebdaa ;};var _ _ee .Interface =&ClassedPoints {};func (_cedc *Bitmap )GetByteIndex (x ,y int )int {return y *_cedc .RowStride +(x >>3)};func (_ddcg *Bitmap )setEightFullBytes (_ddf int ,_aegd uint64 )error {if _ddf +7> len (_ddcg .Data )-1{return _d .Error ("\u0073\u0065\u0074\u0045\u0069\u0067\u0068\u0074\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_ddcg .Data [_ddf ]=byte ((_aegd &0xff00000000000000)>>56);_ddcg .Data [_ddf +1]=byte ((_aegd &0xff000000000000)>>48);_ddcg .Data [_ddf +2]=byte ((_aegd &0xff0000000000)>>40);_ddcg .Data [_ddf +3]=byte ((_aegd &0xff00000000)>>32);_ddcg .Data [_ddf +4]=byte ((_aegd &0xff000000)>>24);
_ddcg .Data [_ddf +5]=byte ((_aegd &0xff0000)>>16);_ddcg .Data [_ddf +6]=byte ((_aegd &0xff00)>>8);_ddcg .Data [_ddf +7]=byte (_aegd &0xff);return nil ;};func (_gfgc *Bitmap )createTemplate ()*Bitmap {return &Bitmap {Width :_gfgc .Width ,Height :_gfgc .Height ,RowStride :_gfgc .RowStride ,Color :_gfgc .Color ,Text :_gfgc .Text ,BitmapNumber :_gfgc .BitmapNumber ,Special :_gfgc .Special ,Data :make ([]byte ,len (_gfgc .Data ))};
};func _ggeg (_ddgd *Bitmap ,_defa ,_egecc ,_aaff ,_eff int ,_bbcgg RasterOperator ,_fdaf *Bitmap ,_fcfcf ,_ccfc int )error {var (_ebcfe byte ;_daad int ;_fcb int ;_abaa ,_bba int ;_fgaa ,_dagc int ;);_fbaa :=_aaff >>3;_bebg :=_aaff &7;if _bebg > 0{_ebcfe =_bcbg [_bebg ];
};_daad =_fdaf .RowStride *_ccfc +(_fcfcf >>3);_fcb =_ddgd .RowStride *_egecc +(_defa >>3);switch _bbcgg {case PixSrc :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;
_dagc ++{_ddgd .Data [_bba ]=_fdaf .Data [_abaa ];_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ],_ebcfe );};};case PixNotSrc :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;
_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=^(_fdaf .Data [_abaa ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^_fdaf .Data [_abaa ],_ebcfe );};};case PixSrcOrDst :for _fgaa =0;
_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]|=_fdaf .Data [_abaa ];_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ]|_ddgd .Data [_bba ],_ebcfe );
};};case PixSrcAndDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]&=_fdaf .Data [_abaa ];_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ]&_ddgd .Data [_bba ],_ebcfe );
};};case PixSrcXorDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]^=_fdaf .Data [_abaa ];_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ]^_ddgd .Data [_bba ],_ebcfe );
};};case PixNotSrcOrDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]|=^(_fdaf .Data [_abaa ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^(_fdaf .Data [_abaa ])|_ddgd .Data [_bba ],_ebcfe );
};};case PixNotSrcAndDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]&=^(_fdaf .Data [_abaa ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^(_fdaf .Data [_abaa ])&_ddgd .Data [_bba ],_ebcfe );
};};case PixSrcOrNotDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=_fdaf .Data [_abaa ]|^(_ddgd .Data [_bba ]);_bba ++;_abaa ++;
};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ]|^(_ddgd .Data [_bba ]),_ebcfe );};};case PixSrcAndNotDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;
for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=_fdaf .Data [_abaa ]&^(_ddgd .Data [_bba ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],_fdaf .Data [_abaa ]&^(_ddgd .Data [_bba ]),_ebcfe );};};case PixNotPixSrcOrDst :for _fgaa =0;
_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=^(_fdaf .Data [_abaa ]|_ddgd .Data [_bba ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^(_fdaf .Data [_abaa ]|_ddgd .Data [_bba ]),_ebcfe );
};};case PixNotPixSrcAndDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=^(_fdaf .Data [_abaa ]&_ddgd .Data [_bba ]);_bba ++;_abaa ++;
};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^(_fdaf .Data [_abaa ]&_ddgd .Data [_bba ]),_ebcfe );};};case PixNotPixSrcXorDst :for _fgaa =0;_fgaa < _eff ;_fgaa ++{_abaa =_daad +_fgaa *_fdaf .RowStride ;_bba =_fcb +_fgaa *_ddgd .RowStride ;
for _dagc =0;_dagc < _fbaa ;_dagc ++{_ddgd .Data [_bba ]=^(_fdaf .Data [_abaa ]^_ddgd .Data [_bba ]);_bba ++;_abaa ++;};if _bebg > 0{_ddgd .Data [_bba ]=_eead (_ddgd .Data [_bba ],^(_fdaf .Data [_abaa ]^_ddgd .Data [_bba ]),_ebcfe );};};default:_bg .Log .Debug ("\u0050\u0072ov\u0069\u0064\u0065d\u0020\u0069\u006e\u0076ali\u0064 r\u0061\u0073\u0074\u0065\u0072\u0020\u006fpe\u0072\u0061\u0074\u006f\u0072\u003a\u0020%\u0076",_bbcgg );
return _d .Error ("\u0072\u0061\u0073\u0074er\u004f\u0070\u0042\u0079\u0074\u0065\u0041\u006c\u0069\u0067\u006e\u0065\u0064\u004co\u0077","\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");
};return nil ;};func _dcgad ()[]int {_ffcf :=make ([]int ,256);for _ddfb :=0;_ddfb <=0xff;_ddfb ++{_bbcb :=byte (_ddfb );_ffcf [_bbcb ]=int (_bbcb &0x1)+(int (_bbcb >>1)&0x1)+(int (_bbcb >>2)&0x1)+(int (_bbcb >>3)&0x1)+(int (_bbcb >>4)&0x1)+(int (_bbcb >>5)&0x1)+(int (_bbcb >>6)&0x1)+(int (_bbcb >>7)&0x1);
};return _ffcf ;};func _bcefe (_eefa ,_egaa *Bitmap ,_adfgc *Selection )(*Bitmap ,error ){const _fdda ="\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u004d\u006f\u0072\u0070\u0068A\u0072\u0067\u0073\u0032";var _ccda ,_ebgae int ;if _egaa ==nil {return nil ,_d .Error (_fdda ,"s\u006fu\u0072\u0063\u0065\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");
};if _adfgc ==nil {return nil ,_d .Error (_fdda ,"\u0073e\u006c \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};_ccda =_adfgc .Width ;_ebgae =_adfgc .Height ;if _ccda ==0||_ebgae ==0{return nil ,_d .Error (_fdda ,"\u0073\u0065\u006c\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u0020\u0030");
};if _eefa ==nil {return _egaa .createTemplate (),nil ;};if _dffa :=_eefa .resizeImageData (_egaa );_dffa !=nil {return nil ,_dffa ;};return _eefa ,nil ;};type Getter interface{GetBitmap ()*Bitmap ;};func _dgd (_bbe ,_aee *Bitmap ,_aaf int ,_bcd []byte ,_gbc int )(_bcf error ){const _cga ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0032";
var (_fgb ,_bbcc ,_ede ,_gdb ,_bbce ,_cfc ,_egb ,_df int ;_dd ,_ebb ,_egf ,_gacc uint32 ;_aga ,_gaa byte ;_eeg uint16 ;);_faa :=make ([]byte ,4);_ceg :=make ([]byte ,4);for _ede =0;_ede < _bbe .Height -1;_ede ,_gdb =_ede +2,_gdb +1{_fgb =_ede *_bbe .RowStride ;
_bbcc =_gdb *_aee .RowStride ;for _bbce ,_cfc =0,0;_bbce < _gbc ;_bbce ,_cfc =_bbce +4,_cfc +1{for _egb =0;_egb < 4;_egb ++{_df =_fgb +_bbce +_egb ;if _df <=len (_bbe .Data )-1&&_df < _fgb +_bbe .RowStride {_faa [_egb ]=_bbe .Data [_df ];}else {_faa [_egb ]=0x00;
};_df =_fgb +_bbe .RowStride +_bbce +_egb ;if _df <=len (_bbe .Data )-1&&_df < _fgb +(2*_bbe .RowStride ){_ceg [_egb ]=_bbe .Data [_df ];}else {_ceg [_egb ]=0x00;};};_dd =_ba .BigEndian .Uint32 (_faa );_ebb =_ba .BigEndian .Uint32 (_ceg );_egf =_dd &_ebb ;
_egf |=_egf <<1;_gacc =_dd |_ebb ;_gacc &=_gacc <<1;_ebb =_egf |_gacc ;_ebb &=0xaaaaaaaa;_dd =_ebb |(_ebb <<7);_aga =byte (_dd >>24);_gaa =byte ((_dd >>8)&0xff);_df =_bbcc +_cfc ;if _df +1==len (_aee .Data )-1||_df +1>=_bbcc +_aee .RowStride {if _bcf =_aee .SetByte (_df ,_bcd [_aga ]);
_bcf !=nil {return _d .Wrapf (_bcf ,_cga ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_df );};}else {_eeg =(uint16 (_bcd [_aga ])<<8)|uint16 (_bcd [_gaa ]);if _bcf =_aee .setTwoBytes (_df ,_eeg );_bcf !=nil {return _d .Wrapf (_bcf ,_cga ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_df );
};_cfc ++;};};};return nil ;};func _cfb (_afef ,_gggf *Bitmap ,_cbab ,_facb ,_dbcb uint ,_cacf ,_eca int ,_fgff bool ,_bgbb ,_eaag int )error {for _gge :=_cacf ;_gge < _eca ;_gge ++{if _bgbb +1< len (_afef .Data ){_dga :=_gge +1==_eca ;_aed ,_beb :=_afef .GetByte (_bgbb );
if _beb !=nil {return _beb ;};_bgbb ++;_aed <<=_cbab ;_bffg ,_beb :=_afef .GetByte (_bgbb );if _beb !=nil {return _beb ;};_bffg >>=_facb ;_bfb :=_aed |_bffg ;if _dga &&!_fgff {_bfb =_gbag (_dbcb ,_bfb );};_beb =_gggf .SetByte (_eaag ,_bfb );if _beb !=nil {return _beb ;
};_eaag ++;if _dga &&_fgff {_bcdfd ,_ddfd :=_afef .GetByte (_bgbb );if _ddfd !=nil {return _ddfd ;};_bcdfd <<=_cbab ;_bfb =_gbag (_dbcb ,_bcdfd );if _ddfd =_gggf .SetByte (_eaag ,_bfb );_ddfd !=nil {return _ddfd ;};};continue ;};_acfe ,_fggb :=_afef .GetByte (_bgbb );
if _fggb !=nil {_bg .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0068\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0061\u0074\u003a\u0020%\u0064\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0073",_bgbb ,_fggb );return _fggb ;};
_acfe <<=_cbab ;_bgbb ++;_fggb =_gggf .SetByte (_eaag ,_acfe );if _fggb !=nil {return _fggb ;};_eaag ++;};return nil ;};const (AsymmetricMorphBC BoundaryCondition =iota ;SymmetricMorphBC ;);func (_dcgf *Bitmap )connComponentsBB (_cegg int )(_cgda *Boxes ,_ffcb error ){const _ebcbe ="\u0042\u0069\u0074ma\u0070\u002e\u0063\u006f\u006e\u006e\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0042\u0042";
if _cegg !=4&&_cegg !=8{return nil ,_d .Error (_ebcbe ,"\u0063\u006f\u006e\u006e\u0065\u0063t\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0027\u0034\u0027\u0020\u006fr\u0020\u0027\u0038\u0027");};if _dcgf .Zero (){return &Boxes {},nil ;
};_dcgf .setPadBits (0);_dddg ,_ffcb :=_daed (nil ,_dcgf );if _ffcb !=nil {return nil ,_d .Wrap (_ffcb ,_ebcbe ,"\u0062\u006d\u0031");};_aafe :=&_ec .Stack {};_aafe .Aux =&_ec .Stack {};_cgda =&Boxes {};var (_dbdd ,_bcce int ;_dabb _ce .Point ;_fcg bool ;
_dgbd *_ce .Rectangle ;);for {if _dabb ,_fcg ,_ffcb =_dddg .nextOnPixel (_bcce ,_dbdd );_ffcb !=nil {return nil ,_d .Wrap (_ffcb ,_ebcbe ,"");};if !_fcg {break ;};if _dgbd ,_ffcb =_aaca (_dddg ,_aafe ,_dabb .X ,_dabb .Y ,_cegg );_ffcb !=nil {return nil ,_d .Wrap (_ffcb ,_ebcbe ,"");
};if _ffcb =_cgda .Add (_dgbd );_ffcb !=nil {return nil ,_d .Wrap (_ffcb ,_ebcbe ,"");};_bcce =_dabb .X ;_dbdd =_dabb .Y ;};return _cgda ,nil ;};func (_fgcc *BitmapsArray )AddBitmaps (bm *Bitmaps ){_fgcc .Values =append (_fgcc .Values ,bm )};func _bfef (_ccee *Bitmap ,_dfdf *_ec .Stack ,_faffd ,_eccc int )(_bfca *_ce .Rectangle ,_aged error ){const _ecef ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";
if _ccee ==nil {return nil ,_d .Error (_ecef ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");};if _dfdf ==nil {return nil ,_d .Error (_ecef ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");
};_bcbf ,_ccdb :=_ccee .Width ,_ccee .Height ;_agbac :=_bcbf -1;_bdgb :=_ccdb -1;if _faffd < 0||_faffd > _agbac ||_eccc < 0||_eccc > _bdgb ||!_ccee .GetPixel (_faffd ,_eccc ){return nil ,nil ;};var _bbgb *_ce .Rectangle ;_bbgb ,_aged =Rect (100000,100000,0,0);
if _aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"");};if _aged =_agcgg (_dfdf ,_faffd ,_faffd ,_eccc ,1,_bdgb ,_bbgb );_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"\u0069\u006e\u0069t\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _aged =_agcgg (_dfdf ,_faffd ,_faffd ,_eccc +1,-1,_bdgb ,_bbgb );
_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"\u0032\u006ed\u0020\u0069\u006ei\u0074\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};_bbgb .Min .X ,_bbgb .Max .X =_faffd ,_faffd ;_bbgb .Min .Y ,_bbgb .Max .Y =_eccc ,_eccc ;var (_cece *fillSegment ;
_afdb int ;);for _dfdf .Len ()> 0{if _cece ,_aged =_bggga (_dfdf );_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"");};_eccc =_cece ._dfdd ;for _faffd =_cece ._bffd ;_faffd >=0&&_ccee .GetPixel (_faffd ,_eccc );_faffd --{if _aged =_ccee .SetPixel (_faffd ,_eccc ,0);
_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"");};};if _faffd >=_cece ._bffd {for _faffd ++;_faffd <=_cece ._eagf &&_faffd <=_agbac &&!_ccee .GetPixel (_faffd ,_eccc );_faffd ++{};_afdb =_faffd ;if !(_faffd <=_cece ._eagf &&_faffd <=_agbac ){continue ;
};}else {_afdb =_faffd +1;if _afdb < _cece ._bffd -1{if _aged =_agcgg (_dfdf ,_afdb ,_cece ._bffd -1,_cece ._dfdd ,-_cece ._cddd ,_bdgb ,_bbgb );_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"\u006c\u0065\u0061\u006b\u0020\u006f\u006e\u0020\u006c\u0065\u0066\u0074 \u0073\u0069\u0064\u0065");
};};_faffd =_cece ._bffd +1;};for {for ;_faffd <=_agbac &&_ccee .GetPixel (_faffd ,_eccc );_faffd ++{if _aged =_ccee .SetPixel (_faffd ,_eccc ,0);_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _aged =_agcgg (_dfdf ,_afdb ,_faffd -1,_cece ._dfdd ,_cece ._cddd ,_bdgb ,_bbgb );
_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _faffd > _cece ._eagf +1{if _aged =_agcgg (_dfdf ,_cece ._eagf +1,_faffd -1,_cece ._dfdd ,-_cece ._cddd ,_bdgb ,_bbgb );_aged !=nil {return nil ,_d .Wrap (_aged ,_ecef ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};for _faffd ++;_faffd <=_cece ._eagf &&_faffd <=_agbac &&!_ccee .GetPixel (_faffd ,_eccc );_faffd ++{};_afdb =_faffd ;if !(_faffd <=_cece ._eagf &&_faffd <=_agbac ){break ;};};};_bbgb .Max .X ++;_bbgb .Max .Y ++;return _bbgb ,nil ;};func TstWordBitmapWithSpaces (t *_ed .T ,scale ...int )*Bitmap {_bdefa :=1;
if len (scale )> 0{_bdefa =scale [0];};_cgce :=3;_eaedg :=9+7+15+2*_cgce +2*_cgce ;_cagf :=5+_cgce +5+2*_cgce ;_aabb :=New (_eaedg *_bdefa ,_cagf *_bdefa );_gbgg :=&Bitmaps {};var _cdbcg *int ;_cgce *=_bdefa ;_ebfag :=_cgce ;_cdbcg =&_ebfag ;_bdag :=_cgce ;
_dbfcc :=TstDSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,_cgce );_dbfcc =TstISymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );
_dbfcc =TstTSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,_cgce );_dbfcc =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );
_dbfcc =TstWSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,0);*_cdbcg =_cgce ;_bdag =5*_bdefa +_cgce ;_dbfcc =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,_cgce );
_dbfcc =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstESymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstVSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );
_dbfcc =TstESymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,1*_bdefa );_dbfcc =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_gbgg ,_dbfcc ,_cdbcg ,_bdag ,0);TstWriteSymbols (t ,_gbgg ,_aabb );return _aabb ;};func _cce (_fdc ,_deg *Bitmap ,_dee int ,_beg []byte ,_cdd int )(_caa error ){const _agb ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0031";
var (_defb ,_edbc ,_bfg ,_bgd ,_eac ,_ceaa ,_ccfg ,_bee int ;_gfg ,_dccb uint32 ;_aec ,_dgc byte ;_dcg uint16 ;);_feca :=make ([]byte ,4);_beef :=make ([]byte ,4);for _bfg =0;_bfg < _fdc .Height -1;_bfg ,_bgd =_bfg +2,_bgd +1{_defb =_bfg *_fdc .RowStride ;
_edbc =_bgd *_deg .RowStride ;for _eac ,_ceaa =0,0;_eac < _cdd ;_eac ,_ceaa =_eac +4,_ceaa +1{for _ccfg =0;_ccfg < 4;_ccfg ++{_bee =_defb +_eac +_ccfg ;if _bee <=len (_fdc .Data )-1&&_bee < _defb +_fdc .RowStride {_feca [_ccfg ]=_fdc .Data [_bee ];}else {_feca [_ccfg ]=0x00;
};_bee =_defb +_fdc .RowStride +_eac +_ccfg ;if _bee <=len (_fdc .Data )-1&&_bee < _defb +(2*_fdc .RowStride ){_beef [_ccfg ]=_fdc .Data [_bee ];}else {_beef [_ccfg ]=0x00;};};_gfg =_ba .BigEndian .Uint32 (_feca );_dccb =_ba .BigEndian .Uint32 (_beef );
_dccb |=_gfg ;_dccb |=_dccb <<1;_dccb &=0xaaaaaaaa;_gfg =_dccb |(_dccb <<7);_aec =byte (_gfg >>24);_dgc =byte ((_gfg >>8)&0xff);_bee =_edbc +_ceaa ;if _bee +1==len (_deg .Data )-1||_bee +1>=_edbc +_deg .RowStride {_deg .Data [_bee ]=_beg [_aec ];}else {_dcg =(uint16 (_beg [_aec ])<<8)|uint16 (_beg [_dgc ]);
if _caa =_deg .setTwoBytes (_bee ,_dcg );_caa !=nil {return _d .Wrapf (_caa ,_agb ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_bee );
};_ceaa ++;};};};return nil ;};func New (width ,height int )*Bitmap {_gcd :=_cca (width ,height );_gcd .Data =make ([]byte ,height *_gcd .RowStride );return _gcd ;};func (_eddb *Bitmaps )ClipToBitmap (s *Bitmap )(*Bitmaps ,error ){const _egef ="B\u0069t\u006d\u0061\u0070\u0073\u002e\u0043\u006c\u0069p\u0054\u006f\u0042\u0069tm\u0061\u0070";
if _eddb ==nil {return nil ,_d .Error (_egef ,"\u0042\u0069\u0074\u006dap\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if s ==nil {return nil ,_d .Error (_egef ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};_cgag :=len (_eddb .Values );_feee :=&Bitmaps {Values :make ([]*Bitmap ,_cgag ),Boxes :make ([]*_ce .Rectangle ,_cgag )};var (_egdec ,_fcbe *Bitmap ;_fcdb *_ce .Rectangle ;_cacga error ;);for _ecfd :=0;_ecfd < _cgag ;_ecfd ++{if _egdec ,_cacga =_eddb .GetBitmap (_ecfd );
_cacga !=nil {return nil ,_d .Wrap (_cacga ,_egef ,"");};if _fcdb ,_cacga =_eddb .GetBox (_ecfd );_cacga !=nil {return nil ,_d .Wrap (_cacga ,_egef ,"");};if _fcbe ,_cacga =s .clipRectangle (_fcdb ,nil );_cacga !=nil {return nil ,_d .Wrap (_cacga ,_egef ,"");
};if _fcbe ,_cacga =_fcbe .And (_egdec );_cacga !=nil {return nil ,_d .Wrap (_cacga ,_egef ,"");};_feee .Values [_ecfd ]=_fcbe ;_feee .Boxes [_ecfd ]=_fcdb ;};return _feee ,nil ;};func (_baaa *Bitmap )setEightBytes (_fgga int ,_gbcf uint64 )error {_dgcf :=_baaa .RowStride -(_fgga %_baaa .RowStride );
if _baaa .RowStride !=_baaa .Width >>3{_dgcf --;};if _dgcf >=8{return _baaa .setEightFullBytes (_fgga ,_gbcf );};return _baaa .setEightPartlyBytes (_fgga ,_dgcf ,_gbcf );};func (_acac *Bitmaps )SelectBySize (width ,height int ,tp LocationFilter ,relation SizeComparison )(_baga *Bitmaps ,_afgd error ){const _edda ="B\u0069t\u006d\u0061\u0070\u0073\u002e\u0053\u0065\u006ce\u0063\u0074\u0042\u0079Si\u007a\u0065";
if _acac ==nil {return nil ,_d .Error (_edda ,"\u0027\u0062\u0027 B\u0069\u0074\u006d\u0061\u0070\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};switch tp {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_d .Errorf (_edda ,"\u0070\u0072\u006f\u0076\u0069d\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u006fc\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",tp );
};switch relation {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE ,SizeSelectIfEQ :default:return nil ,_d .Errorf (_edda ,"\u0069\u006e\u0076\u0061li\u0064\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025d\u0027",relation );
};_cgcc ,_afgd :=_acac .makeSizeIndicator (width ,height ,tp ,relation );if _afgd !=nil {return nil ,_d .Wrap (_afgd ,_edda ,"");};_baga ,_afgd =_acac .selectByIndicator (_cgcc );if _afgd !=nil {return nil ,_d .Wrap (_afgd ,_edda ,"");};return _baga ,nil ;
};func (_fbgc Points )Get (i int )(Point ,error ){if i > len (_fbgc )-1{return Point {},_d .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _fbgc [i ],nil ;};func (_faab *ClassedPoints )Less (i ,j int )bool {return _faab ._ebee (i ,j )};type shift int ;func (_eaed *Bitmap )nextOnPixel (_dbd ,_cbfc int )(_gafb _ce .Point ,_gdde bool ,_dcdd error ){const _adgea ="n\u0065\u0078\u0074\u004f\u006e\u0050\u0069\u0078\u0065\u006c";
_gafb ,_gdde ,_dcdd =_eaed .nextOnPixelLow (_eaed .Width ,_eaed .Height ,_eaed .RowStride ,_dbd ,_cbfc );if _dcdd !=nil {return _gafb ,false ,_d .Wrap (_dcdd ,_adgea ,"");};return _gafb ,_gdde ,nil ;};func (_dfdc *Bitmap )inverseData (){if _fde :=_dfdc .RasterOperation (0,0,_dfdc .Width ,_dfdc .Height ,PixNotDst ,nil ,0,0);
_fde !=nil {_bg .Log .Debug ("\u0049n\u0076\u0065\u0072\u0073e\u0020\u0064\u0061\u0074\u0061 \u0066a\u0069l\u0065\u0064\u003a\u0020\u0027\u0025\u0076'",_fde );};if _dfdc .Color ==Chocolate {_dfdc .Color =Vanilla ;}else {_dfdc .Color =Chocolate ;};};func (_gff *Bitmap )RemoveBorder (borderSize int )(*Bitmap ,error ){if borderSize ==0{return _gff .Copy (),nil ;
};_dgb ,_bfgc :=_gff .removeBorderGeneral (borderSize ,borderSize ,borderSize ,borderSize );if _bfgc !=nil {return nil ,_d .Wrap (_bfgc ,"\u0052\u0065\u006do\u0076\u0065\u0042\u006f\u0072\u0064\u0065\u0072","");};return _dgb ,nil ;};func (_ddgag *Bitmaps )GroupByHeight ()(*BitmapsArray ,error ){const _gagb ="\u0047\u0072\u006f\u0075\u0070\u0042\u0079\u0048\u0065\u0069\u0067\u0068\u0074";
if len (_ddgag .Values )==0{return nil ,_d .Error (_gagb ,"\u006eo\u0020v\u0061\u006c\u0075\u0065\u0073 \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");};_eegcc :=&BitmapsArray {};_ddgag .SortByHeight ();_egbf :=-1;_abe :=-1;for _dbcgd :=0;_dbcgd < len (_ddgag .Values );
_dbcgd ++{_ebgea :=_ddgag .Values [_dbcgd ].Height ;if _ebgea > _egbf {_egbf =_ebgea ;_abe ++;_eegcc .Values =append (_eegcc .Values ,&Bitmaps {});};_eegcc .Values [_abe ].AddBitmap (_ddgag .Values [_dbcgd ]);};return _eegcc ,nil ;};func (_cffa *Bitmap )Zero ()bool {_cbc :=_cffa .Width /8;
_faff :=_cffa .Width &7;var _ffea byte ;if _faff !=0{_ffea =byte (0xff<<uint (8-_faff ));};var _ggb ,_eae ,_cac int ;for _eae =0;_eae < _cffa .Height ;_eae ++{_ggb =_cffa .RowStride *_eae ;for _cac =0;_cac < _cbc ;_cac ,_ggb =_cac +1,_ggb +1{if _cffa .Data [_ggb ]!=0{return false ;
};};if _faff > 0{if _cffa .Data [_ggb ]&_ffea !=0{return false ;};};};return true ;};func (_aaa *Bitmap )String ()string {var _cef ="\u000a";for _cfee :=0;_cfee < _aaa .Height ;_cfee ++{var _ecgf string ;for _dgbc :=0;_dgbc < _aaa .Width ;_dgbc ++{_cbd :=_aaa .GetPixel (_dgbc ,_cfee );
if _cbd {_ecgf +="\u0031";}else {_ecgf +="\u0030";};};_cef +=_ecgf +"\u000a";};return _cef ;};var MorphBC BoundaryCondition ;type BitmapsArray struct{Values []*Bitmaps ;Boxes []*_ce .Rectangle ;};func _adf (_fdbc int )int {if _fdbc < 0{return -_fdbc ;};
return _fdbc ;};func _gaf ()(_edd [256]uint64 ){for _cedb :=0;_cedb < 256;_cedb ++{if _cedb &0x01!=0{_edd [_cedb ]|=0xff;};if _cedb &0x02!=0{_edd [_cedb ]|=0xff00;};if _cedb &0x04!=0{_edd [_cedb ]|=0xff0000;};if _cedb &0x08!=0{_edd [_cedb ]|=0xff000000;
};if _cedb &0x10!=0{_edd [_cedb ]|=0xff00000000;};if _cedb &0x20!=0{_edd [_cedb ]|=0xff0000000000;};if _cedb &0x40!=0{_edd [_cedb ]|=0xff000000000000;};if _cedb &0x80!=0{_edd [_cedb ]|=0xff00000000000000;};};return _edd ;};func (_bgbc *Bitmap )Equals (s *Bitmap )bool {if len (_bgbc .Data )!=len (s .Data )||_bgbc .Width !=s .Width ||_bgbc .Height !=s .Height {return false ;
};for _bbbd :=0;_bbbd < _bgbc .Height ;_bbbd ++{_dcd :=_bbbd *_bgbc .RowStride ;for _ged :=0;_ged < _bgbc .RowStride ;_ged ++{if _bgbc .Data [_dcd +_ged ]!=s .Data [_dcd +_ged ]{return false ;};};};return true ;};type Bitmaps struct{Values []*Bitmap ;Boxes []*_ce .Rectangle ;
};func (_ageg *Bitmap )AddBorderGeneral (left ,right ,top ,bot int ,val int )(*Bitmap ,error ){return _ageg .addBorderGeneral (left ,right ,top ,bot ,val );};func (_egd *Bitmap )SetByte (index int ,v byte )error {if index > len (_egd .Data )-1||index < 0{return _d .Errorf ("\u0053e\u0074\u0042\u0079\u0074\u0065","\u0069\u006e\u0064\u0065x \u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u003a\u0020%\u0064",index );
};_egd .Data [index ]=v ;return nil ;};func (_gacca *Bitmap )GetBitOffset (x int )int {return x &0x07};func (_aaeg Points )GetIntX (i int )(int ,error ){if i >=len (_aaeg ){return 0,_d .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065t\u0049\u006e\u0074\u0058","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return int (_aaeg [i ].X ),nil ;};func ClipBoxToRectangle (box *_ce .Rectangle ,wi ,hi int )(_bfe *_ce .Rectangle ,_bdgf error ){const _ebdcg ="\u0043l\u0069p\u0042\u006f\u0078\u0054\u006fR\u0065\u0063t\u0061\u006e\u0067\u006c\u0065";if box ==nil {return nil ,_d .Error (_ebdcg ,"\u0027\u0062\u006f\u0078\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};if box .Min .X >=wi ||box .Min .Y >=hi ||box .Max .X <=0||box .Max .Y <=0{return nil ,_d .Error (_ebdcg ,"\u0027\u0062\u006fx'\u0020\u006f\u0075\u0074\u0073\u0069\u0064\u0065\u0020\u0072\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065");};_afcc :=*box ;
_bfe =&_afcc ;if _bfe .Min .X < 0{_bfe .Max .X +=_bfe .Min .X ;_bfe .Min .X =0;};if _bfe .Min .Y < 0{_bfe .Max .Y +=_bfe .Min .Y ;_bfe .Min .Y =0;};if _bfe .Max .X > wi {_bfe .Max .X =wi ;};if _bfe .Max .Y > hi {_bfe .Max .Y =hi ;};return _bfe ,nil ;};
func (_bbgee *Bitmaps )GroupByWidth ()(*BitmapsArray ,error ){const _dgcd ="\u0047\u0072\u006fu\u0070\u0042\u0079\u0057\u0069\u0064\u0074\u0068";if len (_bbgee .Values )==0{return nil ,_d .Error (_dgcd ,"\u006eo\u0020v\u0061\u006c\u0075\u0065\u0073 \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");
};_ebbc :=&BitmapsArray {};_bbgee .SortByWidth ();_bgag :=-1;_eacdab :=-1;for _egaaf :=0;_egaaf < len (_bbgee .Values );_egaaf ++{_ddfe :=_bbgee .Values [_egaaf ].Width ;if _ddfe > _bgag {_bgag =_ddfe ;_eacdab ++;_ebbc .Values =append (_ebbc .Values ,&Bitmaps {});
};_ebbc .Values [_eacdab ].AddBitmap (_bbgee .Values [_egaaf ]);};return _ebbc ,nil ;};func (_fdd *Bitmap )ConnComponents (bms *Bitmaps ,connectivity int )(_gaeg *Boxes ,_agdc error ){const _adgdf ="B\u0069\u0074\u006d\u0061p.\u0043o\u006e\u006e\u0043\u006f\u006dp\u006f\u006e\u0065\u006e\u0074\u0073";
if _fdd ==nil {return nil ,_d .Error (_adgdf ,"\u0070r\u006f\u0076\u0069\u0064e\u0064\u0020\u0065\u006d\u0070t\u0079 \u0027b\u0027\u0020\u0062\u0069\u0074\u006d\u0061p");};if connectivity !=4&&connectivity !=8{return nil ,_d .Error (_adgdf ,"\u0063\u006f\u006ene\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u006e\u006f\u0074\u0020\u0034\u0020\u006f\u0072\u0020\u0038");
};if bms ==nil {if _gaeg ,_agdc =_fdd .connComponentsBB (connectivity );_agdc !=nil {return nil ,_d .Wrap (_agdc ,_adgdf ,"");};}else {if _gaeg ,_agdc =_fdd .connComponentsBitmapsBB (bms ,connectivity );_agdc !=nil {return nil ,_d .Wrap (_agdc ,_adgdf ,"");
};};return _gaeg ,nil ;};func _gcb (_egcbg *Bitmap ,_aaac *Bitmap ,_fecf int )(_edgcf error ){const _cddgc ="\u0073\u0065\u0065\u0064\u0066\u0069\u006c\u006c\u0042\u0069\u006e\u0061r\u0079\u004c\u006f\u0077";_dcgdg :=_cfa (_egcbg .Height ,_aaac .Height );
_ebec :=_cfa (_egcbg .RowStride ,_aaac .RowStride );switch _fecf {case 4:_edgcf =_fefc (_egcbg ,_aaac ,_dcgdg ,_ebec );case 8:_edgcf =_ddge (_egcbg ,_aaac ,_dcgdg ,_ebec );default:return _d .Errorf (_cddgc ,"\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u0034\u0020\u006fr\u0020\u0038\u0020\u002d\u0020i\u0073\u003a \u0027\u0025\u0064\u0027",_fecf );
};if _edgcf !=nil {return _d .Wrap (_edgcf ,_cddgc ,"");};return nil ;};func (_aafd *Bitmap )clearAll ()error {return _aafd .RasterOperation (0,0,_aafd .Width ,_aafd .Height ,PixClr ,nil ,0,0);};func (_acfg *Bitmap )setAll ()error {_abcb :=_bgdbc (_acfg ,0,0,_acfg .Width ,_acfg .Height ,PixSet ,nil ,0,0);
if _abcb !=nil {return _d .Wrap (_abcb ,"\u0073\u0065\u0074\u0041\u006c\u006c","");};return nil ;};func TstGetScaledSymbol (t *_ed .T ,sm *Bitmap ,scale ...int )*Bitmap {if len (scale )==0{return sm ;};if scale [0]==1{return sm ;};_dbgca ,_fecd :=MorphSequence (sm ,MorphProcess {Operation :MopReplicativeBinaryExpansion ,Arguments :scale });
_c .NoError (t ,_fecd );return _dbgca ;};func (_dacc *Bitmap )thresholdPixelSum (_acca int )bool {var (_cbg int ;_gffa uint8 ;_bcdb byte ;_dfgc int ;);_ccbbc :=_dacc .RowStride ;_bgfb :=uint (_dacc .Width &0x07);if _bgfb !=0{_gffa =uint8 ((0xff<<(8-_bgfb ))&0xff);
_ccbbc --;};for _ceee :=0;_ceee < _dacc .Height ;_ceee ++{for _dfgc =0;_dfgc < _ccbbc ;_dfgc ++{_bcdb =_dacc .Data [_ceee *_dacc .RowStride +_dfgc ];_cbg +=int (_ebf [_bcdb ]);};if _bgfb !=0{_bcdb =_dacc .Data [_ceee *_dacc .RowStride +_dfgc ]&_gffa ;_cbg +=int (_ebf [_bcdb ]);
};if _cbg > _acca {return true ;};};return false ;};func _abce (_faaf ,_agc ,_dfdcb *Bitmap )(*Bitmap ,error ){const _daccg ="\u0073\u0075\u0062\u0074\u0072\u0061\u0063\u0074";if _agc ==nil {return nil ,_d .Error (_daccg ,"'\u0073\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");
};if _dfdcb ==nil {return nil ,_d .Error (_daccg ,"'\u0073\u0032\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};var _cbaa error ;switch {case _faaf ==_agc :if _cbaa =_faaf .RasterOperation (0,0,_agc .Width ,_agc .Height ,PixNotSrcAndDst ,_dfdcb ,0,0);
_cbaa !=nil {return nil ,_d .Wrap (_cbaa ,_daccg ,"\u0064 \u003d\u003d\u0020\u0073\u0031");};case _faaf ==_dfdcb :if _cbaa =_faaf .RasterOperation (0,0,_agc .Width ,_agc .Height ,PixNotSrcAndDst ,_agc ,0,0);_cbaa !=nil {return nil ,_d .Wrap (_cbaa ,_daccg ,"\u0064 \u003d\u003d\u0020\u0073\u0032");
};default:_faaf ,_cbaa =_daed (_faaf ,_agc );if _cbaa !=nil {return nil ,_d .Wrap (_cbaa ,_daccg ,"");};if _cbaa =_faaf .RasterOperation (0,0,_agc .Width ,_agc .Height ,PixNotSrcAndDst ,_dfdcb ,0,0);_cbaa !=nil {return nil ,_d .Wrap (_cbaa ,_daccg ,"\u0064e\u0066\u0061\u0075\u006c\u0074");
};};return _faaf ,nil ;};func (_fdde *Bitmaps )GetBitmap (i int )(*Bitmap ,error ){const _abdab ="\u0047e\u0074\u0042\u0069\u0074\u006d\u0061p";if _fdde ==nil {return nil ,_d .Error (_abdab ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0042\u0069\u0074ma\u0070\u0073");
};if i > len (_fdde .Values )-1{return nil ,_d .Errorf (_abdab ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );};return _fdde .Values [i ],nil ;};type fillSegment struct{_bffd int ;
_eagf int ;_dfdd int ;_cddd int ;};func (_ffec *Bitmap )clipRectangle (_gegb ,_daeba *_ce .Rectangle )(_decg *Bitmap ,_edbe error ){const _gce ="\u0063\u006c\u0069\u0070\u0052\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065";if _gegb ==nil {return nil ,_d .Error (_gce ,"\u0070r\u006fv\u0069\u0064\u0065\u0064\u0020n\u0069\u006c \u0027\u0062\u006f\u0078\u0027");
};_bcg ,_fgf :=_ffec .Width ,_ffec .Height ;_dbf ,_edbe :=ClipBoxToRectangle (_gegb ,_bcg ,_fgf );if _edbe !=nil {_bg .Log .Warning ("\u0027\u0062ox\u0027\u0020\u0064o\u0065\u0073\u006e\u0027t o\u0076er\u006c\u0061\u0070\u0020\u0062\u0069\u0074ma\u0070\u0020\u0027\u0062\u0027\u003a\u0020%\u0076",_edbe );
return nil ,nil ;};_gbd ,_bdf :=_dbf .Min .X ,_dbf .Min .Y ;_egec ,_gdgd :=_dbf .Max .X -_dbf .Min .X ,_dbf .Max .Y -_dbf .Min .Y ;_decg =New (_egec ,_gdgd );_decg .Text =_ffec .Text ;if _edbe =_decg .RasterOperation (0,0,_egec ,_gdgd ,PixSrc ,_ffec ,_gbd ,_bdf );
_edbe !=nil {return nil ,_d .Wrap (_edbe ,_gce ,"");};if _daeba !=nil {*_daeba =*_dbf ;};return _decg ,nil ;};func (_bfge *ClassedPoints )xSortFunction ()func (_dcde int ,_cfcgc int )bool {return func (_dfag ,_cbca int )bool {return _bfge .XAtIndex (_dfag )< _bfge .XAtIndex (_cbca )};
};type SizeSelection int ;type LocationFilter int ;func _ddce (_aacca *Bitmap ,_bbgd *Bitmap ,_ddbf *Selection ,_dcda **Bitmap )(*Bitmap ,error ){const _deaa ="\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u004d\u006f\u0072\u0070\u0068A\u0072\u0067\u0073\u0031";
if _bbgd ==nil {return nil ,_d .Error (_deaa ,"\u004d\u006f\u0072\u0070\u0068\u0041\u0072\u0067\u0073\u0031\u0020'\u0073\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066i\u006e\u0065\u0064");};if _ddbf ==nil {return nil ,_d .Error (_deaa ,"\u004d\u006f\u0072\u0068p\u0041\u0072\u0067\u0073\u0031\u0020\u0027\u0073\u0065\u006c'\u0020n\u006f\u0074\u0020\u0064\u0065\u0066\u0069n\u0065\u0064");
};_gccgd ,_fbbec :=_ddbf .Height ,_ddbf .Width ;if _gccgd ==0||_fbbec ==0{return nil ,_d .Error (_deaa ,"\u0073\u0065\u006c\u0065ct\u0069\u006f\u006e\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u0020\u0030");};if _aacca ==nil {_aacca =_bbgd .createTemplate ();
*_dcda =_bbgd ;return _aacca ,nil ;};_aacca .Width =_bbgd .Width ;_aacca .Height =_bbgd .Height ;_aacca .RowStride =_bbgd .RowStride ;_aacca .Color =_bbgd .Color ;_aacca .Data =make ([]byte ,_bbgd .RowStride *_bbgd .Height );if _aacca ==_bbgd {*_dcda =_bbgd .Copy ();
}else {*_dcda =_bbgd ;};return _aacca ,nil ;};func (_cffg *Boxes )Add (box *_ce .Rectangle )error {if _cffg ==nil {return _d .Error ("\u0042o\u0078\u0065\u0073\u002e\u0041\u0064d","\u0027\u0042\u006f\u0078es\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};*_cffg =append (*_cffg ,box );return nil ;};func (_cfcff *Bitmap )SizesEqual (s *Bitmap )bool {if _cfcff ==s {return true ;};if _cfcff .Width !=s .Width ||_cfcff .Height !=s .Height {return false ;};return true ;};func (_defdg *Bitmap )centroid (_efge ,_cdcba []int )(Point ,error ){_cbbf :=Point {};
_defdg .setPadBits (0);if len (_efge )==0{_efge =_fffd ();};if len (_cdcba )==0{_cdcba =_dcgad ();};var _bdfe ,_bfbge ,_ggab ,_ddad ,_cgegc ,_fbac int ;var _dccd byte ;for _cgegc =0;_cgegc < _defdg .Height ;_cgegc ++{_bdfee :=_defdg .RowStride *_cgegc ;
_ddad =0;for _fbac =0;_fbac < _defdg .RowStride ;_fbac ++{_dccd =_defdg .Data [_bdfee +_fbac ];if _dccd !=0{_ddad +=_cdcba [_dccd ];_bdfe +=_efge [_dccd ]+_fbac *8*_cdcba [_dccd ];};};_ggab +=_ddad ;_bfbge +=_ddad *_cgegc ;};if _ggab !=0{_cbbf .X =float32 (_bdfe )/float32 (_ggab );
_cbbf .Y =float32 (_bfbge )/float32 (_ggab );};return _cbbf ,nil ;};func (_eee *Bitmap )ToImage ()_ce .Image {_dec ,_abc :=_dg .NewImage (_eee .Width ,_eee .Height ,1,1,_eee .Data ,nil ,nil );if _abc !=nil {_bg .Log .Error ("\u0043\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020j\u0062\u0069\u0067\u0032\u002e\u0042\u0069\u0074m\u0061p\u0020\u0074\u006f\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002e\u0049\u006d\u0061\u0067e\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_abc );
};return _dec ;};func _cbbc (_bggc ,_addd *Bitmap ,_ffdc ,_ccgb int )(*Bitmap ,error ){const _geeb ="\u006fp\u0065\u006e\u0042\u0072\u0069\u0063k";if _addd ==nil {return nil ,_d .Error (_geeb ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};if _ffdc < 1&&_ccgb < 1{return nil ,_d .Error (_geeb ,"\u0068\u0053\u0069\u007ae \u003c\u0020\u0031\u0020\u0026\u0026\u0020\u0076\u0053\u0069\u007a\u0065\u0020\u003c \u0031");};if _ffdc ==1&&_ccgb ==1{return _addd .Copy (),nil ;};if _ffdc ==1||_ccgb ==1{var _fdgd error ;
_bbgg :=SelCreateBrick (_ccgb ,_ffdc ,_ccgb /2,_ffdc /2,SelHit );_bggc ,_fdgd =_efbg (_bggc ,_addd ,_bbgg );if _fdgd !=nil {return nil ,_d .Wrap (_fdgd ,_geeb ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _bggc ,nil ;};_adfg :=SelCreateBrick (1,_ffdc ,0,_ffdc /2,SelHit );_adgef :=SelCreateBrick (_ccgb ,1,_ccgb /2,0,SelHit );_eaecb ,_fgfg :=_ecc (nil ,_addd ,_adfg );if _fgfg !=nil {return nil ,_d .Wrap (_fgfg ,_geeb ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};_bggc ,_fgfg =_ecc (_bggc ,_eaecb ,_adgef );if _fgfg !=nil {return nil ,_d .Wrap (_fgfg ,_geeb ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};_ ,_fgfg =_cdaa (_eaecb ,_bggc ,_adfg );if _fgfg !=nil {return nil ,_d .Wrap (_fgfg ,_geeb ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};_ ,_fgfg =_cdaa (_bggc ,_eaecb ,_adgef );if _fgfg !=nil {return nil ,_d .Wrap (_fgfg ,_geeb ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};return _bggc ,nil ;};func _cfa (_fbbe ,_bcgf int )int {if _fbbe < _bcgf {return _fbbe ;};return _bcgf ;
};func _gcde (_ccab ,_agde *Bitmap ,_efee ,_bgcb int )(*Bitmap ,error ){const _dcea ="\u0065\u0072\u006f\u0064\u0065\u0042\u0072\u0069\u0063\u006b";if _agde ==nil {return nil ,_d .Error (_dcea ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _efee < 1||_bgcb < 1{return nil ,_d .Error (_dcea ,"\u0068\u0073\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0073\u0069\u007a\u0065\u0020\u0061\u0072e\u0020\u006e\u006f\u0074\u0020\u0067\u0072e\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006fr\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0031");
};if _efee ==1&&_bgcb ==1{_agdb ,_cedeb :=_daed (_ccab ,_agde );if _cedeb !=nil {return nil ,_d .Wrap (_cedeb ,_dcea ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u0026\u0026 \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _agdb ,nil ;};if _efee ==1||_bgcb ==1{_ggbb :=SelCreateBrick (_bgcb ,_efee ,_bgcb /2,_efee /2,SelHit );_cecfb ,_bdeg :=_ecc (_ccab ,_agde ,_ggbb );if _bdeg !=nil {return nil ,_d .Wrap (_bdeg ,_dcea ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _cecfb ,nil ;};_cebc :=SelCreateBrick (1,_efee ,0,_efee /2,SelHit );_afd :=SelCreateBrick (_bgcb ,1,_bgcb /2,0,SelHit );_cdfea ,_bcfd :=_ecc (nil ,_agde ,_cebc );if _bcfd !=nil {return nil ,_d .Wrap (_bcfd ,_dcea ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};_ccab ,_bcfd =_ecc (_ccab ,_cdfea ,_afd );if _bcfd !=nil {return nil ,_d .Wrap (_bcfd ,_dcea ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};return _ccab ,nil ;};func (_dccg *Bitmap )GetChocolateData ()[]byte {if _dccg .Color ==Vanilla {_dccg .inverseData ();
};return _dccg .Data ;};func (_aaga *ClassedPoints )Swap (i ,j int ){_aaga .IntSlice [i ],_aaga .IntSlice [j ]=_aaga .IntSlice [j ],_aaga .IntSlice [i ];};func CorrelationScore (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab []int )(_dace float64 ,_cfgb error ){const _gbba ="\u0063\u006fr\u0072\u0065\u006ca\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065";
if bm1 ==nil ||bm2 ==nil {return 0,_d .Error (_gbba ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0062\u0069\u0074ma\u0070\u0073");};if tab ==nil {return 0,_d .Error (_gbba ,"\u0027\u0074\u0061\u0062\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};if area1 <=0||area2 <=0{return 0,_d .Error (_gbba ,"\u0061\u0072\u0065\u0061s\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u0067r\u0065a\u0074\u0065\u0072\u0020\u0074\u0068\u0061n\u0020\u0030");};_cgdd ,_aaef :=bm1 .Width ,bm1 .Height ;_aggf ,_eefd :=bm2 .Width ,bm2 .Height ;
_dgga :=_adf (_cgdd -_aggf );if _dgga > maxDiffW {return 0,nil ;};_bggd :=_adf (_aaef -_eefd );if _bggd > maxDiffH {return 0,nil ;};var _gcae ,_cebb int ;if delX >=0{_gcae =int (delX +0.5);}else {_gcae =int (delX -0.5);};if delY >=0{_cebb =int (delY +0.5);
}else {_cebb =int (delY -0.5);};_cbad :=_gffac (_cebb ,0);_gfd :=_cfa (_eefd +_cebb ,_aaef );_dfad :=bm1 .RowStride *_cbad ;_bggab :=bm2 .RowStride *(_cbad -_cebb );_fbdf :=_gffac (_gcae ,0);_gdcb :=_cfa (_aggf +_gcae ,_cgdd );_ecbaf :=bm2 .RowStride ;
var _bcdbf ,_cbb int ;if _gcae >=8{_bcdbf =_gcae >>3;_dfad +=_bcdbf ;_fbdf -=_bcdbf <<3;_gdcb -=_bcdbf <<3;_gcae &=7;}else if _gcae <=-8{_cbb =-((_gcae +7)>>3);_bggab +=_cbb ;_ecbaf -=_cbb ;_gcae +=_cbb <<3;};if _fbdf >=_gdcb ||_cbad >=_gfd {return 0,nil ;
};_bfec :=(_gdcb +7)>>3;var (_aaccg ,_dgcb ,_abdg byte ;_bggdg ,_cfbc ,_dbba int ;);switch {case _gcae ==0:for _dbba =_cbad ;_dbba < _gfd ;_dbba ,_dfad ,_bggab =_dbba +1,_dfad +bm1 .RowStride ,_bggab +bm2 .RowStride {for _cfbc =0;_cfbc < _bfec ;_cfbc ++{_abdg =bm1 .Data [_dfad +_cfbc ]&bm2 .Data [_bggab +_cfbc ];
_bggdg +=tab [_abdg ];};};case _gcae > 0:if _ecbaf < _bfec {for _dbba =_cbad ;_dbba < _gfd ;_dbba ,_dfad ,_bggab =_dbba +1,_dfad +bm1 .RowStride ,_bggab +bm2 .RowStride {_aaccg ,_dgcb =bm1 .Data [_dfad ],bm2 .Data [_bggab ]>>uint (_gcae );_abdg =_aaccg &_dgcb ;
_bggdg +=tab [_abdg ];for _cfbc =1;_cfbc < _ecbaf ;_cfbc ++{_aaccg ,_dgcb =bm1 .Data [_dfad +_cfbc ],(bm2 .Data [_bggab +_cfbc ]>>uint (_gcae ))|(bm2 .Data [_bggab +_cfbc -1]<<uint (8-_gcae ));_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];};_aaccg =bm1 .Data [_dfad +_cfbc ];
_dgcb =bm2 .Data [_bggab +_cfbc -1]<<uint (8-_gcae );_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];};}else {for _dbba =_cbad ;_dbba < _gfd ;_dbba ,_dfad ,_bggab =_dbba +1,_dfad +bm1 .RowStride ,_bggab +bm2 .RowStride {_aaccg ,_dgcb =bm1 .Data [_dfad ],bm2 .Data [_bggab ]>>uint (_gcae );
_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];for _cfbc =1;_cfbc < _bfec ;_cfbc ++{_aaccg =bm1 .Data [_dfad +_cfbc ];_dgcb =(bm2 .Data [_bggab +_cfbc ]>>uint (_gcae ))|(bm2 .Data [_bggab +_cfbc -1]<<uint (8-_gcae ));_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];
};};};default:if _bfec < _ecbaf {for _dbba =_cbad ;_dbba < _gfd ;_dbba ,_dfad ,_bggab =_dbba +1,_dfad +bm1 .RowStride ,_bggab +bm2 .RowStride {for _cfbc =0;_cfbc < _bfec ;_cfbc ++{_aaccg =bm1 .Data [_dfad +_cfbc ];_dgcb =bm2 .Data [_bggab +_cfbc ]<<uint (-_gcae );
_dgcb |=bm2 .Data [_bggab +_cfbc +1]>>uint (8+_gcae );_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];};};}else {for _dbba =_cbad ;_dbba < _gfd ;_dbba ,_dfad ,_bggab =_dbba +1,_dfad +bm1 .RowStride ,_bggab +bm2 .RowStride {for _cfbc =0;_cfbc < _bfec -1;_cfbc ++{_aaccg =bm1 .Data [_dfad +_cfbc ];
_dgcb =bm2 .Data [_bggab +_cfbc ]<<uint (-_gcae );_dgcb |=bm2 .Data [_bggab +_cfbc +1]>>uint (8+_gcae );_abdg =_aaccg &_dgcb ;_bggdg +=tab [_abdg ];};_aaccg =bm1 .Data [_dfad +_cfbc ];_dgcb =bm2 .Data [_bggab +_cfbc ]<<uint (-_gcae );_abdg =_aaccg &_dgcb ;
_bggdg +=tab [_abdg ];};};};_dace =float64 (_bggdg )*float64 (_bggdg )/(float64 (area1 )*float64 (area2 ));return _dace ,nil ;};func (_gecee *Bitmaps )selectByIndicator (_ffga *_ec .NumSlice )(_ggbf *Bitmaps ,_bfgfa error ){const _adab ="\u0042i\u0074\u006d\u0061\u0070s\u002e\u0073\u0065\u006c\u0065c\u0074B\u0079I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _gecee ==nil {return nil ,_d .Error (_adab ,"\u0027\u0062\u0027 b\u0069\u0074\u006d\u0061\u0070\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if _ffga ==nil {return nil ,_d .Error (_adab ,"'\u006e\u0061\u0027\u0020\u0069\u006ed\u0069\u0063\u0061\u0074\u006f\u0072\u0073\u0020\u006eo\u0074\u0020\u0064e\u0066i\u006e\u0065\u0064");
};if len (_gecee .Values )==0{return _gecee ,nil ;};if len (*_ffga )!=len (_gecee .Values ){return nil ,_d .Errorf (_adab ,"\u006ea\u0020\u006ce\u006e\u0067\u0074\u0068:\u0020\u0025\u0064,\u0020\u0069\u0073\u0020\u0064\u0069\u0066\u0066\u0065re\u006e\u0074\u0020t\u0068\u0061n\u0020\u0062\u0069\u0074\u006d\u0061p\u0073\u003a \u0025\u0064",len (*_ffga ),len (_gecee .Values ));
};var _bcgbd ,_gaeca ,_abbf int ;for _gaeca =0;_gaeca < len (*_ffga );_gaeca ++{if _bcgbd ,_bfgfa =_ffga .GetInt (_gaeca );_bfgfa !=nil {return nil ,_d .Wrap (_bfgfa ,_adab ,"f\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0065\u0063\u006b");};if _bcgbd ==1{_abbf ++;
};};if _abbf ==len (_gecee .Values ){return _gecee ,nil ;};_ggbf =&Bitmaps {};_gabcg :=len (_gecee .Values )==len (_gecee .Boxes );for _gaeca =0;_gaeca < len (*_ffga );_gaeca ++{if _bcgbd =int ((*_ffga )[_gaeca ]);_bcgbd ==0{continue ;};_ggbf .Values =append (_ggbf .Values ,_gecee .Values [_gaeca ]);
if _gabcg {_ggbf .Boxes =append (_ggbf .Boxes ,_gecee .Boxes [_gaeca ]);};};return _ggbf ,nil ;};func (_gaaf *Bitmaps )SelectByIndexes (idx []int )(*Bitmaps ,error ){const _gebf ="B\u0069\u0074\u006d\u0061\u0070\u0073.\u0053\u006f\u0072\u0074\u0049\u006e\u0064\u0065\u0078e\u0073\u0042\u0079H\u0065i\u0067\u0068\u0074";
_bggfg ,_fcec :=_gaaf .selectByIndexes (idx );if _fcec !=nil {return nil ,_d .Wrap (_fcec ,_gebf ,"");};return _bggfg ,nil ;};func (_begf MorphProcess )getWidthHeight ()(_agce ,_bbd int ){return _begf .Arguments [0],_begf .Arguments [1];};func _gdf ()(_caf [256]uint16 ){for _cea :=0;
_cea < 256;_cea ++{if _cea &0x01!=0{_caf [_cea ]|=0x3;};if _cea &0x02!=0{_caf [_cea ]|=0xc;};if _cea &0x04!=0{_caf [_cea ]|=0x30;};if _cea &0x08!=0{_caf [_cea ]|=0xc0;};if _cea &0x10!=0{_caf [_cea ]|=0x300;};if _cea &0x20!=0{_caf [_cea ]|=0xc00;};if _cea &0x40!=0{_caf [_cea ]|=0x3000;
};if _cea &0x80!=0{_caf [_cea ]|=0xc000;};};return _caf ;};func TstOSymbol (t *_ed .T ,scale ...int )*Bitmap {_gfee ,_bcab :=NewWithData (4,5,[]byte {0xF0,0x90,0x90,0x90,0xF0});_c .NoError (t ,_bcab );return TstGetScaledSymbol (t ,_gfee ,scale ...);};func _fbeb (_agcg ,_acfgg *Bitmap ,_edgc ,_fgfc ,_bdc ,_bgbae ,_eaaa ,_efec ,_agdd ,_gdgc int ,_gabc CombinationOperator ,_deae int )error {var _dafa int ;
_ebdc :=func (){_dafa ++;_bdc +=_acfgg .RowStride ;_bgbae +=_agcg .RowStride ;_eaaa +=_agcg .RowStride };for _dafa =_edgc ;_dafa < _fgfc ;_ebdc (){var _bcb uint16 ;_ggc :=_bdc ;for _dccgc :=_bgbae ;_dccgc <=_eaaa ;_dccgc ++{_dggb ,_ade :=_acfgg .GetByte (_ggc );
if _ade !=nil {return _ade ;};_fgcd ,_ade :=_agcg .GetByte (_dccgc );if _ade !=nil {return _ade ;};_bcb =(_bcb |(uint16 (_fgcd )&0xff))<<uint (_gdgc );_fgcd =byte (_bcb >>8);if _ade =_acfgg .SetByte (_ggc ,_cdfe (_dggb ,_fgcd ,_gabc ));_ade !=nil {return _ade ;
};_ggc ++;_bcb <<=uint (_agdd );if _dccgc ==_eaaa {_fgcd =byte (_bcb >>(8-uint8 (_gdgc )));if _deae !=0{_fgcd =_gbag (uint (8+_efec ),_fgcd );};_dggb ,_ade =_acfgg .GetByte (_ggc );if _ade !=nil {return _ade ;};if _ade =_acfgg .SetByte (_ggc ,_cdfe (_dggb ,_fgcd ,_gabc ));
_ade !=nil {return _ade ;};};};};return nil ;};func TstWordBitmap (t *_ed .T ,scale ...int )*Bitmap {_cgefc :=1;if len (scale )> 0{_cgefc =scale [0];};_eced :=3;_affbf :=9+7+15+2*_eced ;_faeb :=5+_eced +5;_defdd :=New (_affbf *_cgefc ,_faeb *_cgefc );_aeaba :=&Bitmaps {};
var _eddae *int ;_eced *=_cgefc ;_efgd :=0;_eddae =&_efgd ;_bfbgg :=0;_bddgf :=TstDSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,_eced );
_bddgf =TstISymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstTSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,_eced );_bddgf =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );
_bddgf =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstWSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,0);*_eddae =0;_bfbgg =5*_cgefc +_eced ;_bddgf =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );
_bddgf =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,_eced );_bddgf =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstESymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );
_bddgf =TstVSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstESymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,1*_cgefc );_bddgf =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_aeaba ,_bddgf ,_eddae ,_bfbgg ,0);
TstWriteSymbols (t ,_aeaba ,_defdd );return _defdd ;};func CorrelationScoreThresholded (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab ,downcount []int ,scoreThreshold float32 )(bool ,error ){const _ccc ="C\u006f\u0072\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054h\u0072\u0065\u0073h\u006fl\u0064\u0065\u0064";
if bm1 ==nil {return false ,_d .Error (_ccc ,"\u0063\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u0062\u006d1\u0020\u0069s\u0020\u006e\u0069\u006c");
};if bm2 ==nil {return false ,_d .Error (_ccc ,"\u0063\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u0062\u006d2\u0020\u0069s\u0020\u006e\u0069\u006c");
};if area1 <=0||area2 <=0{return false ,_d .Error (_ccc ,"c\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0053\u0063\u006f\u0072\u0065\u0054\u0068re\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u002d\u0020\u0061\u0072\u0065\u0061s \u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u003e\u0020\u0030");
};if downcount ==nil {return false ,_d .Error (_ccc ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u006f\u0020\u0027\u0064\u006f\u0077\u006e\u0063\u006f\u0075\u006e\u0074\u0027");};if tab ==nil {return false ,_d .Error (_ccc ,"p\u0072\u006f\u0076\u0069de\u0064 \u006e\u0069\u006c\u0020\u0027s\u0075\u006d\u0074\u0061\u0062\u0027");
};_fegf ,_egfd :=bm1 .Width ,bm1 .Height ;_bgdb ,_dgda :=bm2 .Width ,bm2 .Height ;if _ec .Abs (_fegf -_bgdb )> maxDiffW {return false ,nil ;};if _ec .Abs (_egfd -_dgda )> maxDiffH {return false ,nil ;};_fdec :=int (delX +_ec .Sign (delX )*0.5);_geed :=int (delY +_ec .Sign (delY )*0.5);
_caebd :=int (_b .Ceil (_b .Sqrt (float64 (scoreThreshold )*float64 (area1 )*float64 (area2 ))));_cceg :=bm2 .RowStride ;_fggd :=_gffac (_geed ,0);_ecfb :=_cfa (_dgda +_geed ,_egfd );_aebc :=bm1 .RowStride *_fggd ;_gcdb :=bm2 .RowStride *(_fggd -_geed );
var _bged int ;if _ecfb <=_egfd {_bged =downcount [_ecfb -1];};_bgdg :=_gffac (_fdec ,0);_gabca :=_cfa (_bgdb +_fdec ,_fegf );var _cdge ,_dbddf int ;if _fdec >=8{_cdge =_fdec >>3;_aebc +=_cdge ;_bgdg -=_cdge <<3;_gabca -=_cdge <<3;_fdec &=7;}else if _fdec <=-8{_dbddf =-((_fdec +7)>>3);
_gcdb +=_dbddf ;_cceg -=_dbddf ;_fdec +=_dbddf <<3;};var (_eaaae ,_bfed ,_cege int ;_ffbc ,_cdag ,_bbf byte ;);if _bgdg >=_gabca ||_fggd >=_ecfb {return false ,nil ;};_dgfe :=(_gabca +7)>>3;switch {case _fdec ==0:for _bfed =_fggd ;_bfed < _ecfb ;_bfed ,_aebc ,_gcdb =_bfed +1,_aebc +bm1 .RowStride ,_gcdb +bm2 .RowStride {for _cege =0;
_cege < _dgfe ;_cege ++{_ffbc =bm1 .Data [_aebc +_cege ]&bm2 .Data [_gcdb +_cege ];_eaaae +=tab [_ffbc ];};if _eaaae >=_caebd {return true ,nil ;};if _cdfa :=_eaaae +downcount [_bfed ]-_bged ;_cdfa < _caebd {return false ,nil ;};};case _fdec > 0&&_cceg < _dgfe :for _bfed =_fggd ;
_bfed < _ecfb ;_bfed ,_aebc ,_gcdb =_bfed +1,_aebc +bm1 .RowStride ,_gcdb +bm2 .RowStride {_cdag =bm1 .Data [_aebc ];_bbf =bm2 .Data [_gcdb ]>>uint (_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];for _cege =1;_cege < _cceg ;_cege ++{_cdag =bm1 .Data [_aebc +_cege ];
_bbf =bm2 .Data [_gcdb +_cege ]>>uint (_fdec )|bm2 .Data [_gcdb +_cege -1]<<uint (8-_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];};_cdag =bm1 .Data [_aebc +_cege ];_bbf =bm2 .Data [_gcdb +_cege -1]<<uint (8-_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];
if _eaaae >=_caebd {return true ,nil ;}else if _eaaae +downcount [_bfed ]-_bged < _caebd {return false ,nil ;};};case _fdec > 0&&_cceg >=_dgfe :for _bfed =_fggd ;_bfed < _ecfb ;_bfed ,_aebc ,_gcdb =_bfed +1,_aebc +bm1 .RowStride ,_gcdb +bm2 .RowStride {_cdag =bm1 .Data [_aebc ];
_bbf =bm2 .Data [_gcdb ]>>uint (_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];for _cege =1;_cege < _dgfe ;_cege ++{_cdag =bm1 .Data [_aebc +_cege ];_bbf =bm2 .Data [_gcdb +_cege ]>>uint (_fdec );_bbf |=bm2 .Data [_gcdb +_cege -1]<<uint (8-_fdec );_ffbc =_cdag &_bbf ;
_eaaae +=tab [_ffbc ];};if _eaaae >=_caebd {return true ,nil ;}else if _eaaae +downcount [_bfed ]-_bged < _caebd {return false ,nil ;};};case _dgfe < _cceg :for _bfed =_fggd ;_bfed < _ecfb ;_bfed ,_aebc ,_gcdb =_bfed +1,_aebc +bm1 .RowStride ,_gcdb +bm2 .RowStride {for _cege =0;
_cege < _dgfe ;_cege ++{_cdag =bm1 .Data [_aebc +_cege ];_bbf =bm2 .Data [_gcdb +_cege ]<<uint (-_fdec );_bbf |=bm2 .Data [_gcdb +_cege +1]>>uint (8+_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];};if _eaaae >=_caebd {return true ,nil ;}else if _fgbee :=_eaaae +downcount [_bfed ]-_bged ;
_fgbee < _caebd {return false ,nil ;};};case _cceg >=_dgfe :for _bfed =_fggd ;_bfed < _ecfb ;_bfed ,_aebc ,_gcdb =_bfed +1,_aebc +bm1 .RowStride ,_gcdb +bm2 .RowStride {for _cege =0;_cege < _dgfe ;_cege ++{_cdag =bm1 .Data [_aebc +_cege ];_bbf =bm2 .Data [_gcdb +_cege ]<<uint (-_fdec );
_bbf |=bm2 .Data [_gcdb +_cege +1]>>uint (8+_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];};_cdag =bm1 .Data [_aebc +_cege ];_bbf =bm2 .Data [_gcdb +_cege ]<<uint (-_fdec );_ffbc =_cdag &_bbf ;_eaaae +=tab [_ffbc ];if _eaaae >=_caebd {return true ,nil ;
}else if _eaaae +downcount [_bfed ]-_bged < _caebd {return false ,nil ;};};};_aedd :=float32 (_eaaae )*float32 (_eaaae )/(float32 (area1 )*float32 (area2 ));if _aedd >=scoreThreshold {_bg .Log .Trace ("\u0063\u006f\u0075\u006e\u0074\u003a\u0020\u0025\u0064\u0020\u003c\u0020\u0074\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0020\u0025\u0064\u0020\u0062\u0075\u0074\u0020\u0073c\u006f\u0072\u0065\u0020\u0025\u0066\u0020\u003e\u003d\u0020\u0073\u0063\u006fr\u0065\u0054\u0068\u0072\u0065\u0073h\u006f\u006c\u0064 \u0025\u0066",_eaaae ,_caebd ,_aedd ,scoreThreshold );
};return false ,nil ;};func _efbg (_bced ,_geee *Bitmap ,_bdfc *Selection )(*Bitmap ,error ){const _dggba ="\u006f\u0070\u0065\u006e";var _agcea error ;_bced ,_agcea =_bcefe (_bced ,_geee ,_bdfc );if _agcea !=nil {return nil ,_d .Wrap (_agcea ,_dggba ,"");
};_ffbbf ,_agcea :=_ecc (nil ,_geee ,_bdfc );if _agcea !=nil {return nil ,_d .Wrap (_agcea ,_dggba ,"");};_ ,_agcea =_cdaa (_bced ,_ffbbf ,_bdfc );if _agcea !=nil {return nil ,_d .Wrap (_agcea ,_dggba ,"");};return _bced ,nil ;};func (_aadc *Points )Add (pt *Points )error {const _eafg ="\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0041\u0064\u0064";
if _aadc ==nil {return _d .Error (_eafg ,"\u0070o\u0069n\u0074\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");};if pt ==nil {return _d .Error (_eafg ,"a\u0072\u0067\u0075\u006d\u0065\u006et\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u006eo\u0074\u0020\u0064e\u0066i\u006e\u0065\u0064");
};*_aadc =append (*_aadc ,*pt ...);return nil ;};func _gbag (_eedc uint ,_efd byte )byte {return _efd >>_eedc <<_eedc };func MorphSequence (src *Bitmap ,sequence ...MorphProcess )(*Bitmap ,error ){return _cfcgd (src ,sequence ...);};var (_gbdf =_gdf ();
_fcea =_ccb ();_gbbe =_gaf (););func _bbca (_gde ,_bcgd *Bitmap ,_cdfeb ,_edef int )(*Bitmap ,error ){const _fcc ="\u0063\u006c\u006f\u0073\u0065\u0053\u0061\u0066\u0065B\u0072\u0069\u0063\u006b";if _bcgd ==nil {return nil ,_d .Error (_fcc ,"\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0069\u0073\u0020\u006e\u0069\u006c");
};if _cdfeb < 1||_edef < 1{return nil ,_d .Error (_fcc ,"\u0068s\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0073\u0069z\u0065\u0020\u006e\u006f\u0074\u0020\u003e\u003d\u0020\u0031");};if _cdfeb ==1&&_edef ==1{return _daed (_gde ,_bcgd );};if MorphBC ==SymmetricMorphBC {_cage ,_facg :=_aagb (_gde ,_bcgd ,_cdfeb ,_edef );
if _facg !=nil {return nil ,_d .Wrap (_facg ,_fcc ,"\u0053\u0079m\u006d\u0065\u0074r\u0069\u0063\u004d\u006f\u0072\u0070\u0068\u0042\u0043");};return _cage ,nil ;};_deee :=_gffac (_cdfeb /2,_edef /2);_ddgb :=8*((_deee +7)/8);_agba ,_dgef :=_bcgd .AddBorder (_ddgb ,0);
if _dgef !=nil {return nil ,_d .Wrapf (_dgef ,_fcc ,"\u0042\u006f\u0072\u0064\u0065\u0072\u0053\u0069\u007ae\u003a\u0020\u0025\u0064",_ddgb );};var _gdbb ,_cgec *Bitmap ;if _cdfeb ==1||_edef ==1{_abad :=SelCreateBrick (_edef ,_cdfeb ,_edef /2,_cdfeb /2,SelHit );
_gdbb ,_dgef =_cddg (nil ,_agba ,_abad );if _dgef !=nil {return nil ,_d .Wrap (_dgef ,_fcc ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");};}else {_fdca :=SelCreateBrick (1,_cdfeb ,0,_cdfeb /2,SelHit );
_gcdd ,_bdde :=_cdaa (nil ,_agba ,_fdca );if _bdde !=nil {return nil ,_d .Wrap (_bdde ,_fcc ,"\u0072\u0065\u0067\u0075la\u0072\u0020\u002d\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0064\u0069\u006c\u0061t\u0065");};_ffcga :=SelCreateBrick (_edef ,1,_edef /2,0,SelHit );
_gdbb ,_bdde =_cdaa (nil ,_gcdd ,_ffcga );if _bdde !=nil {return nil ,_d .Wrap (_bdde ,_fcc ,"\u0072\u0065\u0067ul\u0061\u0072\u0020\u002d\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};if _ ,_bdde =_ecc (_gcdd ,_gdbb ,_fdca );
_bdde !=nil {return nil ,_d .Wrap (_bdde ,_fcc ,"r\u0065\u0067\u0075\u006car\u0020-\u0020\u0066\u0069\u0072\u0073t\u0020\u0065\u0072\u006f\u0064\u0065");};if _ ,_bdde =_ecc (_gdbb ,_gcdd ,_ffcga );_bdde !=nil {return nil ,_d .Wrap (_bdde ,_fcc ,"\u0072\u0065\u0067\u0075la\u0072\u0020\u002d\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0065\u0072\u006fd\u0065");
};};if _cgec ,_dgef =_gdbb .RemoveBorder (_ddgb );_dgef !=nil {return nil ,_d .Wrap (_dgef ,_fcc ,"\u0072e\u0067\u0075\u006c\u0061\u0072");};if _gde ==nil {return _cgec ,nil ;};if _ ,_dgef =_daed (_gde ,_cgec );_dgef !=nil {return nil ,_dgef ;};return _gde ,nil ;
};func (_aab *Bitmap )CountPixels ()int {return _aab .countPixels ()};func _ea (_bgc *Bitmap ,_dcc int )(*Bitmap ,error ){const _bbc ="\u0065x\u0070a\u006e\u0064\u0042\u0069\u006ea\u0072\u0079P\u006f\u0077\u0065\u0072\u0032";if _bgc ==nil {return nil ,_d .Error (_bbc ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _dcc ==1{return _daed (nil ,_bgc );};if _dcc !=2&&_dcc !=4&&_dcc !=8{return nil ,_d .Error (_bbc ,"\u0066\u0061\u0063t\u006f\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0069n\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d\u0020\u0072\u0061\u006e\u0067\u0065");
};_bae :=_dcc *_bgc .Width ;_eaa :=_dcc *_bgc .Height ;_ccf :=New (_bae ,_eaa );var _dae error ;switch _dcc {case 2:_dae =_g (_ccf ,_bgc );case 4:_dae =_bce (_ccf ,_bgc );case 8:_dae =_abd (_ccf ,_bgc );};if _dae !=nil {return nil ,_d .Wrap (_dae ,_bbc ,"");
};return _ccf ,nil ;};func _ebe (_gecg ,_gdd *Bitmap ,_cegc int ,_adg []byte ,_ebbg int )(_fbd error ){const _fbb ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0034";var (_gee ,_cbf ,_cgd ,_afea ,_eacd ,_bd ,_beee ,_eba int ;
_bcfa ,_gg uint32 ;_fbba ,_cdf byte ;_fbca uint16 ;);_fab :=make ([]byte ,4);_ecbd :=make ([]byte ,4);for _cgd =0;_cgd < _gecg .Height -1;_cgd ,_afea =_cgd +2,_afea +1{_gee =_cgd *_gecg .RowStride ;_cbf =_afea *_gdd .RowStride ;for _eacd ,_bd =0,0;_eacd < _ebbg ;
_eacd ,_bd =_eacd +4,_bd +1{for _beee =0;_beee < 4;_beee ++{_eba =_gee +_eacd +_beee ;if _eba <=len (_gecg .Data )-1&&_eba < _gee +_gecg .RowStride {_fab [_beee ]=_gecg .Data [_eba ];}else {_fab [_beee ]=0x00;};_eba =_gee +_gecg .RowStride +_eacd +_beee ;
if _eba <=len (_gecg .Data )-1&&_eba < _gee +(2*_gecg .RowStride ){_ecbd [_beee ]=_gecg .Data [_eba ];}else {_ecbd [_beee ]=0x00;};};_bcfa =_ba .BigEndian .Uint32 (_fab );_gg =_ba .BigEndian .Uint32 (_ecbd );_gg &=_bcfa ;_gg &=_gg <<1;_gg &=0xaaaaaaaa;
_bcfa =_gg |(_gg <<7);_fbba =byte (_bcfa >>24);_cdf =byte ((_bcfa >>8)&0xff);_eba =_cbf +_bd ;if _eba +1==len (_gdd .Data )-1||_eba +1>=_cbf +_gdd .RowStride {_gdd .Data [_eba ]=_adg [_fbba ];if _fbd =_gdd .SetByte (_eba ,_adg [_fbba ]);_fbd !=nil {return _d .Wrapf (_fbd ,_fbb ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_eba );
};}else {_fbca =(uint16 (_adg [_fbba ])<<8)|uint16 (_adg [_cdf ]);if _fbd =_gdd .setTwoBytes (_eba ,_fbca );_fbd !=nil {return _d .Wrapf (_fbd ,_fbb ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_eba );
};_bd ++;};};};return nil ;};func Extract (roi _ce .Rectangle ,src *Bitmap )(*Bitmap ,error ){_ecbb :=New (roi .Dx (),roi .Dy ());_agaa :=roi .Min .X &0x07;_ebga :=8-_agaa ;_dggd :=uint (8-_ecbb .Width &0x07);_fdfa :=src .GetByteIndex (roi .Min .X ,roi .Min .Y );
_eaee :=src .GetByteIndex (roi .Max .X -1,roi .Min .Y );_feg :=_ecbb .RowStride ==_eaee +1-_fdfa ;var _gddd int ;for _fff :=roi .Min .Y ;_fff < roi .Max .Y ;_fff ++{_acda :=_fdfa ;_cge :=_gddd ;switch {case _fdfa ==_eaee :_fdgc ,_cgac :=src .GetByte (_acda );
if _cgac !=nil {return nil ,_cgac ;};_fdgc <<=uint (_agaa );_cgac =_ecbb .SetByte (_cge ,_gbag (_dggd ,_fdgc ));if _cgac !=nil {return nil ,_cgac ;};case _agaa ==0:for _begc :=_fdfa ;_begc <=_eaee ;_begc ++{_dcaa ,_gacb :=src .GetByte (_acda );if _gacb !=nil {return nil ,_gacb ;
};_acda ++;if _begc ==_eaee &&_feg {_dcaa =_gbag (_dggd ,_dcaa );};_gacb =_ecbb .SetByte (_cge ,_dcaa );if _gacb !=nil {return nil ,_gacb ;};_cge ++;};default:_fcfc :=_cfb (src ,_ecbb ,uint (_agaa ),uint (_ebga ),_dggd ,_fdfa ,_eaee ,_feg ,_acda ,_cge );
if _fcfc !=nil {return nil ,_fcfc ;};};_fdfa +=src .RowStride ;_eaee +=src .RowStride ;_gddd +=_ecbb .RowStride ;};return _ecbb ,nil ;};func (_bfga *Bitmap )setTwoBytes (_dcga int ,_cdcb uint16 )error {if _dcga +1> len (_bfga .Data )-1{return _d .Errorf ("s\u0065\u0074\u0054\u0077\u006f\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_dcga );
};_bfga .Data [_dcga ]=byte ((_cdcb &0xff00)>>8);_bfga .Data [_dcga +1]=byte (_cdcb &0xff);return nil ;};type RasterOperator int ;func _fdecf (_babe *Bitmap ,_eedb *_ec .Stack ,_fdcdd ,_bdb int )(_fdgdg *_ce .Rectangle ,_ffebe error ){const _ebba ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";
if _babe ==nil {return nil ,_d .Error (_ebba ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");};if _eedb ==nil {return nil ,_d .Error (_ebba ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");
};_cedbd ,_gdcfa :=_babe .Width ,_babe .Height ;_ecde :=_cedbd -1;_ceefc :=_gdcfa -1;if _fdcdd < 0||_fdcdd > _ecde ||_bdb < 0||_bdb > _ceefc ||!_babe .GetPixel (_fdcdd ,_bdb ){return nil ,nil ;};_cbaga :=_ce .Rect (100000,100000,0,0);if _ffebe =_agcgg (_eedb ,_fdcdd ,_fdcdd ,_bdb ,1,_ceefc ,&_cbaga );
_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u0069\u006e\u0069t\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _ffebe =_agcgg (_eedb ,_fdcdd ,_fdcdd ,_bdb +1,-1,_ceefc ,&_cbaga );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u0032\u006ed\u0020\u0069\u006ei\u0074\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");
};_cbaga .Min .X ,_cbaga .Max .X =_fdcdd ,_fdcdd ;_cbaga .Min .Y ,_cbaga .Max .Y =_bdb ,_bdb ;var (_egeg *fillSegment ;_aabae int ;);for _eedb .Len ()> 0{if _egeg ,_ffebe =_bggga (_eedb );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"");};_bdb =_egeg ._dfdd ;
for _fdcdd =_egeg ._bffd -1;_fdcdd >=0&&_babe .GetPixel (_fdcdd ,_bdb );_fdcdd --{if _ffebe =_babe .SetPixel (_fdcdd ,_bdb ,0);_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u0031s\u0074\u0020\u0073\u0065\u0074");};};if _fdcdd >=_egeg ._bffd -1{for {for _fdcdd ++;
_fdcdd <=_egeg ._eagf +1&&_fdcdd <=_ecde &&!_babe .GetPixel (_fdcdd ,_bdb );_fdcdd ++{};_aabae =_fdcdd ;if !(_fdcdd <=_egeg ._eagf +1&&_fdcdd <=_ecde ){break ;};for ;_fdcdd <=_ecde &&_babe .GetPixel (_fdcdd ,_bdb );_fdcdd ++{if _ffebe =_babe .SetPixel (_fdcdd ,_bdb ,0);
_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _ffebe =_agcgg (_eedb ,_aabae ,_fdcdd -1,_egeg ._dfdd ,_egeg ._cddd ,_ceefc ,&_cbaga );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");
};if _fdcdd > _egeg ._eagf {if _ffebe =_agcgg (_eedb ,_egeg ._eagf +1,_fdcdd -1,_egeg ._dfdd ,-_egeg ._cddd ,_ceefc ,&_cbaga );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};};continue ;};_aabae =_fdcdd +1;if _aabae < _egeg ._bffd {if _ffebe =_agcgg (_eedb ,_aabae ,_egeg ._bffd -1,_egeg ._dfdd ,-_egeg ._cddd ,_ceefc ,&_cbaga );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u006c\u0065\u0061\u006b\u0020\u006f\u006e\u0020\u006c\u0065\u0066\u0074 \u0073\u0069\u0064\u0065");
};};_fdcdd =_egeg ._bffd ;for {for ;_fdcdd <=_ecde &&_babe .GetPixel (_fdcdd ,_bdb );_fdcdd ++{if _ffebe =_babe .SetPixel (_fdcdd ,_bdb ,0);_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _ffebe =_agcgg (_eedb ,_aabae ,_fdcdd -1,_egeg ._dfdd ,_egeg ._cddd ,_ceefc ,&_cbaga );
_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _fdcdd > _egeg ._eagf {if _ffebe =_agcgg (_eedb ,_egeg ._eagf +1,_fdcdd -1,_egeg ._dfdd ,-_egeg ._cddd ,_ceefc ,&_cbaga );_ffebe !=nil {return nil ,_d .Wrap (_ffebe ,_ebba ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};for _fdcdd ++;_fdcdd <=_egeg ._eagf +1&&_fdcdd <=_ecde &&!_babe .GetPixel (_fdcdd ,_bdb );_fdcdd ++{};_aabae =_fdcdd ;if !(_fdcdd <=_egeg ._eagf +1&&_fdcdd <=_ecde ){break ;};};};_cbaga .Max .X ++;_cbaga .Max .Y ++;return &_cbaga ,nil ;};func _cff ()(_ef []byte ){_ef =make ([]byte ,256);
for _acgd :=0;_acgd < 256;_acgd ++{_ebab :=byte (_acgd );_ef [_ebab ]=(_ebab &0x01)|((_ebab &0x04)>>1)|((_ebab &0x10)>>2)|((_ebab &0x40)>>3)|((_ebab &0x02)<<3)|((_ebab &0x08)<<2)|((_ebab &0x20)<<1)|(_ebab &0x80);};return _ef ;};func (_ggdg *byHeight )Swap (i ,j int ){_ggdg .Values [i ],_ggdg .Values [j ]=_ggdg .Values [j ],_ggdg .Values [i ];
if _ggdg .Boxes !=nil {_ggdg .Boxes [i ],_ggdg .Boxes [j ]=_ggdg .Boxes [j ],_ggdg .Boxes [i ];};};func (_bbgc *Bitmap )RasterOperation (dx ,dy ,dw ,dh int ,op RasterOperator ,src *Bitmap ,sx ,sy int )error {return _bgdbc (_bbgc ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};const (Vanilla Color =iota ;Chocolate ;);func (_gaec *Bitmap )nextOnPixelLow (_dac ,_edad ,_dddd ,_begd ,_abda int )(_dgf _ce .Point ,_gca bool ,_dggf error ){const _fabg ="B\u0069\u0074\u006d\u0061p.\u006ee\u0078\u0074\u004f\u006e\u0050i\u0078\u0065\u006c\u004c\u006f\u0077";
var (_accf int ;_cbag byte ;);_bacf :=_abda *_dddd ;_bff :=_bacf +(_begd /8);if _cbag ,_dggf =_gaec .GetByte (_bff );_dggf !=nil {return _dgf ,false ,_d .Wrap (_dggf ,_fabg ,"\u0078\u0053\u0074\u0061\u0072\u0074\u0020\u0061\u006e\u0064 \u0079\u0053\u0074\u0061\u0072\u0074\u0020o\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065");
};if _cbag !=0{_ebcf :=_begd -(_begd %8)+7;for _accf =_begd ;_accf <=_ebcf &&_accf < _dac ;_accf ++{if _gaec .GetPixel (_accf ,_abda ){_dgf .X =_accf ;_dgf .Y =_abda ;return _dgf ,true ,nil ;};};};_dfa :=(_begd /8)+1;_accf =8*_dfa ;var _eacda int ;for _bff =_bacf +_dfa ;
_accf < _dac ;_bff ,_accf =_bff +1,_accf +8{if _cbag ,_dggf =_gaec .GetByte (_bff );_dggf !=nil {return _dgf ,false ,_d .Wrap (_dggf ,_fabg ,"r\u0065\u0073\u0074\u0020of\u0020t\u0068\u0065\u0020\u006c\u0069n\u0065\u0020\u0062\u0079\u0074\u0065");};if _cbag ==0{continue ;
};for _eacda =0;_eacda < 8&&_accf < _dac ;_eacda ,_accf =_eacda +1,_accf +1{if _gaec .GetPixel (_accf ,_abda ){_dgf .X =_accf ;_dgf .Y =_abda ;return _dgf ,true ,nil ;};};};for _fdcg :=_abda +1;_fdcg < _edad ;_fdcg ++{_bacf =_fdcg *_dddd ;for _bff ,_accf =_bacf ,0;
_accf < _dac ;_bff ,_accf =_bff +1,_accf +8{if _cbag ,_dggf =_gaec .GetByte (_bff );_dggf !=nil {return _dgf ,false ,_d .Wrap (_dggf ,_fabg ,"\u0066o\u006cl\u006f\u0077\u0069\u006e\u0067\u0020\u006c\u0069\u006e\u0065\u0073");};if _cbag ==0{continue ;};
for _eacda =0;_eacda < 8&&_accf < _dac ;_eacda ,_accf =_eacda +1,_accf +1{if _gaec .GetPixel (_accf ,_fdcg ){_dgf .X =_accf ;_dgf .Y =_fdcg ;return _dgf ,true ,nil ;};};};};return _dgf ,false ,nil ;};func RasterOperation (dest *Bitmap ,dx ,dy ,dw ,dh int ,op RasterOperator ,src *Bitmap ,sx ,sy int )error {return _bgdbc (dest ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};func _cdaa (_abbee *Bitmap ,_gfcd *Bitmap ,_gafd *Selection )(*Bitmap ,error ){var (_efab *Bitmap ;_gcg error ;);_abbee ,_gcg =_ddce (_abbee ,_gfcd ,_gafd ,&_efab );if _gcg !=nil {return nil ,_gcg ;};if _gcg =_abbee .clearAll ();_gcg !=nil {return nil ,_gcg ;
};var _dgbe SelectionValue ;for _bgde :=0;_bgde < _gafd .Height ;_bgde ++{for _bacfc :=0;_bacfc < _gafd .Width ;_bacfc ++{_dgbe =_gafd .Data [_bgde ][_bacfc ];if _dgbe ==SelHit {if _gcg =_abbee .RasterOperation (_bacfc -_gafd .Cx ,_bgde -_gafd .Cy ,_gfcd .Width ,_gfcd .Height ,PixSrcOrDst ,_efab ,0,0);
_gcg !=nil {return nil ,_gcg ;};};};};return _abbee ,nil ;};func (_aegfg *ClassedPoints )XAtIndex (i int )float32 {return (*_aegfg .Points )[_aegfg .IntSlice [i ]].X };func (_ebg *Bitmap )And (s *Bitmap )(_bcc *Bitmap ,_gbad error ){const _gda ="\u0042\u0069\u0074\u006d\u0061\u0070\u002e\u0041\u006e\u0064";
if _ebg ==nil {return nil ,_d .Error (_gda ,"\u0027b\u0069t\u006d\u0061\u0070\u0020\u0027b\u0027\u0020i\u0073\u0020\u006e\u0069\u006c");};if s ==nil {return nil ,_d .Error (_gda ,"\u0062\u0069\u0074\u006d\u0061\u0070\u0020\u0027\u0073\u0027\u0020\u0069s\u0020\u006e\u0069\u006c");
};if !_ebg .SizesEqual (s ){_bg .Log .Debug ("\u0025\u0073\u0020-\u0020\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0027\u0073\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0073\u0069\u007a\u0065 \u0077\u0069\u0074\u0068\u0020\u0027\u0062\u0027",_gda );
};if _bcc ,_gbad =_daed (_bcc ,_ebg );_gbad !=nil {return nil ,_d .Wrap (_gbad ,_gda ,"\u0063\u0061\u006e't\u0020\u0063\u0072\u0065\u0061\u0074\u0065\u0020\u0027\u0064\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _gbad =_bcc .RasterOperation (0,0,_bcc .Width ,_bcc .Height ,PixSrcAndDst ,s ,0,0);
_gbad !=nil {return nil ,_d .Wrap (_gbad ,_gda ,"");};return _bcc ,nil ;};func _cced (_cacea *Bitmap ,_egdfb int )(*Bitmap ,error ){const _ffde ="\u0065x\u0070a\u006e\u0064\u0052\u0065\u0070\u006c\u0069\u0063\u0061\u0074\u0065";if _cacea ==nil {return nil ,_d .Error (_ffde ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _egdfb <=0{return nil ,_d .Error (_ffde ,"i\u006e\u0076\u0061\u006cid\u0020f\u0061\u0063\u0074\u006f\u0072 \u002d\u0020\u003c\u003d\u0020\u0030");};if _egdfb ==1{_faga ,_efgef :=_daed (nil ,_cacea );if _efgef !=nil {return nil ,_d .Wrap (_efgef ,_ffde ,"\u0066\u0061\u0063\u0074\u006f\u0072\u0020\u003d\u0020\u0031");
};return _faga ,nil ;};_ddac ,_adgae :=_fc (_cacea ,_egdfb ,_egdfb );if _adgae !=nil {return nil ,_d .Wrap (_adgae ,_ffde ,"");};return _ddac ,nil ;};func (_bgbg *ClassedPoints )validateIntSlice ()error {const _bacga ="\u0076\u0061l\u0069\u0064\u0061t\u0065\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065";
for _ ,_ebcfa :=range _bgbg .IntSlice {if _ebcfa >=(_bgbg .Points .Size ()){return _d .Errorf (_bacga ,"c\u006c\u0061\u0073\u0073\u0020\u0069\u0064\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0076\u0061\u006ci\u0064 \u0069\u006e\u0064\u0065x\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u003a\u0020\u0025\u0064",_ebcfa ,_bgbg .Points .Size ());
};};return nil ;};func TstFrameBitmap ()*Bitmap {return _bbcbd .Copy ()};func _ecc (_abdf ,_egfg *Bitmap ,_ccbac *Selection )(*Bitmap ,error ){const _gcdbd ="\u0065\u0072\u006fd\u0065";var (_deega error ;_dgaf *Bitmap ;);_abdf ,_deega =_ddce (_abdf ,_egfg ,_ccbac ,&_dgaf );
if _deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"");};if _deega =_abdf .setAll ();_deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"");};var _fdeb SelectionValue ;for _egcc :=0;_egcc < _ccbac .Height ;_egcc ++{for _bad :=0;_bad < _ccbac .Width ;
_bad ++{_fdeb =_ccbac .Data [_egcc ][_bad ];if _fdeb ==SelHit {_deega =_bgdbc (_abdf ,_ccbac .Cx -_bad ,_ccbac .Cy -_egcc ,_egfg .Width ,_egfg .Height ,PixSrcAndDst ,_dgaf ,0,0);if _deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"");};};};};if MorphBC ==SymmetricMorphBC {return _abdf ,nil ;
};_ffba ,_agegd ,_gdgda ,_dabd :=_ccbac .findMaxTranslations ();if _ffba > 0{if _deega =_abdf .RasterOperation (0,0,_ffba ,_egfg .Height ,PixClr ,nil ,0,0);_deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"\u0078\u0070\u0020\u003e\u0020\u0030");};};
if _gdgda > 0{if _deega =_abdf .RasterOperation (_egfg .Width -_gdgda ,0,_gdgda ,_egfg .Height ,PixClr ,nil ,0,0);_deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"\u0078\u006e\u0020\u003e\u0020\u0030");};};if _agegd > 0{if _deega =_abdf .RasterOperation (0,0,_egfg .Width ,_agegd ,PixClr ,nil ,0,0);
_deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"\u0079\u0070\u0020\u003e\u0020\u0030");};};if _dabd > 0{if _deega =_abdf .RasterOperation (0,_egfg .Height -_dabd ,_egfg .Width ,_dabd ,PixClr ,nil ,0,0);_deega !=nil {return nil ,_d .Wrap (_deega ,_gcdbd ,"\u0079\u006e\u0020\u003e\u0020\u0030");
};};return _abdf ,nil ;};func _ecf (_ebc *Bitmap ,_cag *Bitmap ,_gdg int )(_bag error ){const _cd ="e\u0078\u0070\u0061\u006edB\u0069n\u0061\u0072\u0079\u0050\u006fw\u0065\u0072\u0032\u004c\u006f\u0077";switch _gdg {case 2:_bag =_g (_ebc ,_cag );case 4:_bag =_bce (_ebc ,_cag );
case 8:_bag =_abd (_ebc ,_cag );default:return _d .Error (_cd ,"\u0065\u0078p\u0061\u006e\u0073\u0069o\u006e\u0020f\u0061\u0063\u0074\u006f\u0072\u0020\u006e\u006ft\u0020\u0069\u006e\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d\u0020r\u0061\u006e\u0067\u0065");
};if _bag !=nil {_bag =_d .Wrap (_bag ,_cd ,"");};return _bag ;};const (SelDontCare SelectionValue =iota ;SelHit ;SelMiss ;);type BoundaryCondition int ;func NewClassedPoints (points *Points ,classes _ec .IntSlice )(*ClassedPoints ,error ){const _geef ="\u004e\u0065w\u0043\u006c\u0061s\u0073\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073";
if points ==nil {return nil ,_d .Error (_geef ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0070\u006f\u0069\u006e\u0074\u0073");};if classes ==nil {return nil ,_d .Error (_geef ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0063\u006c\u0061ss\u0065\u0073");
};_efdb :=&ClassedPoints {Points :points ,IntSlice :classes };if _ceaac :=_efdb .validateIntSlice ();_ceaac !=nil {return nil ,_d .Wrap (_ceaac ,_geef ,"");};return _efdb ,nil ;};func (_eefg *BitmapsArray )GetBox (i int )(*_ce .Rectangle ,error ){const _gdcge ="\u0042\u0069\u0074\u006dap\u0073\u0041\u0072\u0072\u0061\u0079\u002e\u0047\u0065\u0074\u0042\u006f\u0078";
if _eefg ==nil {return nil ,_d .Error (_gdcge ,"p\u0072\u006f\u0076\u0069\u0064\u0065d\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074m\u0061\u0070\u0073A\u0072r\u0061\u0079\u0027");};if i > len (_eefg .Boxes )-1{return nil ,_d .Errorf (_gdcge ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _eefg .Boxes [i ],nil ;};func Blit (src *Bitmap ,dst *Bitmap ,x ,y int ,op CombinationOperator )error {var _feb ,_eegcg int ;_ceec :=src .RowStride -1;if x < 0{_eegcg =-x ;x =0;}else if x +src .Width > dst .Width {_ceec -=src .Width +x -dst .Width ;
};if y < 0{_feb =-y ;y =0;_eegcg +=src .RowStride ;_ceec +=src .RowStride ;}else if y +src .Height > dst .Height {_feb =src .Height +y -dst .Height ;};var (_cbdg int ;_adfd error ;);_fdf :=x &0x07;_eebf :=8-_fdf ;_dgfd :=src .Width &0x07;_dgeb :=_eebf -_dgfd ;
_cdeg :=_eebf &0x07!=0;_gbadg :=src .Width <=((_ceec -_eegcg )<<3)+_eebf ;_gebd :=dst .GetByteIndex (x ,y );_cdea :=_feb +dst .Height ;if src .Height > _cdea {_cbdg =_cdea ;}else {_cbdg =src .Height ;};switch {case !_cdeg :_adfd =_ddbg (src ,dst ,_feb ,_cbdg ,_gebd ,_eegcg ,_ceec ,op );
case _gbadg :_adfd =_eea (src ,dst ,_feb ,_cbdg ,_gebd ,_eegcg ,_ceec ,_dgeb ,_fdf ,_eebf ,op );default:_adfd =_fbeb (src ,dst ,_feb ,_cbdg ,_gebd ,_eegcg ,_ceec ,_dgeb ,_fdf ,_eebf ,op ,_dgfd );};return _adfd ;};func _abg (_cfe *Bitmap ,_dab int ,_bf []byte )(_de *Bitmap ,_ccba error ){const _def ="\u0072\u0065\u0064\u0075\u0063\u0065\u0052\u0061\u006e\u006b\u0042\u0069n\u0061\u0072\u0079\u0032";
if _cfe ==nil {return nil ,_d .Error (_def ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if _dab < 1||_dab > 4{return nil ,_d .Error (_def ,"\u006c\u0065\u0076\u0065\u006c\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020\u0073e\u0074\u0020\u007b\u0031\u002c\u0032\u002c\u0033\u002c\u0034\u007d");
};if _cfe .Height <=1{return nil ,_d .Errorf (_def ,"\u0073o\u0075\u0072c\u0065\u0020\u0068e\u0069\u0067\u0068\u0074\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061t\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0027\u0032\u0027\u0020-\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",_cfe .Height );
};_de =New (_cfe .Width /2,_cfe .Height /2);if _bf ==nil {_bf =_cff ();};_db :=_cfa (_cfe .RowStride ,2*_de .RowStride );switch _dab {case 1:_ccba =_cce (_cfe ,_de ,_dab ,_bf ,_db );case 2:_ccba =_dgd (_cfe ,_de ,_dab ,_bf ,_db );case 3:_ccba =_cae (_cfe ,_de ,_dab ,_bf ,_db );
case 4:_ccba =_ebe (_cfe ,_de ,_dab ,_bf ,_db );};if _ccba !=nil {return nil ,_ccba ;};return _de ,nil ;};func _ccb ()(_cfg [256]uint32 ){for _gba :=0;_gba < 256;_gba ++{if _gba &0x01!=0{_cfg [_gba ]|=0xf;};if _gba &0x02!=0{_cfg [_gba ]|=0xf0;};if _gba &0x04!=0{_cfg [_gba ]|=0xf00;
};if _gba &0x08!=0{_cfg [_gba ]|=0xf000;};if _gba &0x10!=0{_cfg [_gba ]|=0xf0000;};if _gba &0x20!=0{_cfg [_gba ]|=0xf00000;};if _gba &0x40!=0{_cfg [_gba ]|=0xf000000;};if _gba &0x80!=0{_cfg [_gba ]|=0xf0000000;};};return _cfg ;};func (_gafde *ClassedPoints )ySortFunction ()func (_egga int ,_acad int )bool {return func (_dbeg ,_dbeb int )bool {return _gafde .YAtIndex (_dbeg )< _gafde .YAtIndex (_dbeb )};
};func (_ceaaa *byWidth )Len ()int {return len (_ceaaa .Values )};func Centroids (bms []*Bitmap )(*Points ,error ){_efde :=make ([]Point ,len (bms ));_ccag :=_fffd ();_eadc :=_dcgad ();var _bbcd error ;for _cfcg ,_cceb :=range bms {_efde [_cfcg ],_bbcd =_cceb .centroid (_ccag ,_eadc );
if _bbcd !=nil {return nil ,_bbcd ;};};_fdgcf :=Points (_efde );return &_fdgcf ,nil ;};func (_egfe *Bitmaps )Size ()int {return len (_egfe .Values )};func (_aeae *Bitmaps )makeSizeIndicator (_bbeeb ,_afgb int ,_deeb LocationFilter ,_bagaf SizeComparison )(_fdfc *_ec .NumSlice ,_abaef error ){const _eedff ="\u0042i\u0074\u006d\u0061\u0070s\u002e\u006d\u0061\u006b\u0065S\u0069z\u0065I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _aeae ==nil {return nil ,_d .Error (_eedff ,"\u0062\u0069\u0074ma\u0070\u0073\u0020\u0027\u0062\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};switch _deeb {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_d .Errorf (_eedff ,"\u0070\u0072\u006f\u0076\u0069d\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u006fc\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",_deeb );
};switch _bagaf {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE ,SizeSelectIfEQ :default:return nil ,_d .Errorf (_eedff ,"\u0069\u006e\u0076\u0061li\u0064\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025d\u0027",_bagaf );
};_fdfc =&_ec .NumSlice {};var (_cdab ,_fgbca ,_gbge int ;_bgbf *Bitmap ;);for _ ,_bgbf =range _aeae .Values {_cdab =0;_fgbca ,_gbge =_bgbf .Width ,_bgbf .Height ;switch _deeb {case LocSelectWidth :if (_bagaf ==SizeSelectIfLT &&_fgbca < _bbeeb )||(_bagaf ==SizeSelectIfGT &&_fgbca > _bbeeb )||(_bagaf ==SizeSelectIfLTE &&_fgbca <=_bbeeb )||(_bagaf ==SizeSelectIfGTE &&_fgbca >=_bbeeb )||(_bagaf ==SizeSelectIfEQ &&_fgbca ==_bbeeb ){_cdab =1;
};case LocSelectHeight :if (_bagaf ==SizeSelectIfLT &&_gbge < _afgb )||(_bagaf ==SizeSelectIfGT &&_gbge > _afgb )||(_bagaf ==SizeSelectIfLTE &&_gbge <=_afgb )||(_bagaf ==SizeSelectIfGTE &&_gbge >=_afgb )||(_bagaf ==SizeSelectIfEQ &&_gbge ==_afgb ){_cdab =1;
};case LocSelectIfEither :if (_bagaf ==SizeSelectIfLT &&(_fgbca < _bbeeb ||_gbge < _afgb ))||(_bagaf ==SizeSelectIfGT &&(_fgbca > _bbeeb ||_gbge > _afgb ))||(_bagaf ==SizeSelectIfLTE &&(_fgbca <=_bbeeb ||_gbge <=_afgb ))||(_bagaf ==SizeSelectIfGTE &&(_fgbca >=_bbeeb ||_gbge >=_afgb ))||(_bagaf ==SizeSelectIfEQ &&(_fgbca ==_bbeeb ||_gbge ==_afgb )){_cdab =1;
};case LocSelectIfBoth :if (_bagaf ==SizeSelectIfLT &&(_fgbca < _bbeeb &&_gbge < _afgb ))||(_bagaf ==SizeSelectIfGT &&(_fgbca > _bbeeb &&_gbge > _afgb ))||(_bagaf ==SizeSelectIfLTE &&(_fgbca <=_bbeeb &&_gbge <=_afgb ))||(_bagaf ==SizeSelectIfGTE &&(_fgbca >=_bbeeb &&_gbge >=_afgb ))||(_bagaf ==SizeSelectIfEQ &&(_fgbca ==_bbeeb &&_gbge ==_afgb )){_cdab =1;
};};_fdfc .AddInt (_cdab );};return _fdfc ,nil ;};func TstFrameBitmapData ()[]byte {return _bbcbd .Data };func (_dbdf Points )Size ()int {return len (_dbdf )};func (_fagd *Bitmap )connComponentsBitmapsBB (_bacd *Bitmaps ,_agbg int )(_bgga *Boxes ,_cegcda error ){const _ccdf ="\u0063\u006f\u006enC\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0042\u0069\u0074\u006d\u0061\u0070\u0073\u0042\u0042";
if _agbg !=4&&_agbg !=8{return nil ,_d .Error (_ccdf ,"\u0063\u006f\u006e\u006e\u0065\u0063t\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0027\u0034\u0027\u0020\u006fr\u0020\u0027\u0038\u0027");};if _bacd ==nil {return nil ,_d .Error (_ccdf ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0042\u0069\u0074ma\u0070\u0073");
};if len (_bacd .Values )> 0{return nil ,_d .Error (_ccdf ,"\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u006fn\u002d\u0065\u006d\u0070\u0074\u0079\u0020\u0042\u0069\u0074m\u0061\u0070\u0073");};if _fagd .Zero (){return &Boxes {},nil ;};
var (_acdb ,_bfcbf ,_efeb ,_dgbf *Bitmap ;);_fagd .setPadBits (0);if _acdb ,_cegcda =_daed (nil ,_fagd );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062\u006d\u0031");};if _bfcbf ,_cegcda =_daed (nil ,_fagd );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062\u006d\u0032");
};_bbee :=&_ec .Stack {};_bbee .Aux =&_ec .Stack {};_bgga =&Boxes {};var (_fbag ,_cbeb int ;_egfae _ce .Point ;_dfgca bool ;_cfcd *_ce .Rectangle ;);for {if _egfae ,_dfgca ,_cegcda =_acdb .nextOnPixel (_fbag ,_cbeb );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"");
};if !_dfgca {break ;};if _cfcd ,_cegcda =_aaca (_acdb ,_bbee ,_egfae .X ,_egfae .Y ,_agbg );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"");};if _cegcda =_bgga .Add (_cfcd );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"");};if _efeb ,_cegcda =_acdb .clipRectangle (_cfcd ,nil );
_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062\u006d\u0033");};if _dgbf ,_cegcda =_bfcbf .clipRectangle (_cfcd ,nil );_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062\u006d\u0034");};if _ ,_cegcda =_aecg (_efeb ,_efeb ,_dgbf );
_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062m\u0033\u0020\u005e\u0020\u0062\u006d4");};if _cegcda =_bfcbf .RasterOperation (_cfcd .Min .X ,_cfcd .Min .Y ,_cfcd .Dx (),_cfcd .Dy (),PixSrcXorDst ,_efeb ,0,0);_cegcda !=nil {return nil ,_d .Wrap (_cegcda ,_ccdf ,"\u0062\u006d\u0032\u0020\u002d\u0058\u004f\u0052\u002d>\u0020\u0062\u006d\u0033");
};_bacd .AddBitmap (_efeb );_fbag =_egfae .X ;_cbeb =_egfae .Y ;};_bacd .Boxes =*_bgga ;return _bgga ,nil ;};func RankHausTest (p1 ,p2 ,p3 ,p4 *Bitmap ,delX ,delY float32 ,maxDiffW ,maxDiffH ,area1 ,area3 int ,rank float32 ,tab8 []int )(_aadf bool ,_cebg error ){const _gggg ="\u0052\u0061\u006ek\u0048\u0061\u0075\u0073\u0054\u0065\u0073\u0074";
_bcdg ,_acdaf :=p1 .Width ,p1 .Height ;_gbe ,_ceeaa :=p3 .Width ,p3 .Height ;if _ec .Abs (_bcdg -_gbe )> maxDiffW {return false ,nil ;};if _ec .Abs (_acdaf -_ceeaa )> maxDiffH {return false ,nil ;};_ffed :=int (float32 (area1 )*(1.0-rank )+0.5);_bagd :=int (float32 (area3 )*(1.0-rank )+0.5);
var _gfdc ,_deb int ;if delX >=0{_gfdc =int (delX +0.5);}else {_gfdc =int (delX -0.5);};if delY >=0{_deb =int (delY +0.5);}else {_deb =int (delY -0.5);};_cgeg :=p1 .CreateTemplate ();if _cebg =_cgeg .RasterOperation (0,0,_bcdg ,_acdaf ,PixSrc ,p1 ,0,0);
_cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"p\u0031\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _cebg =_cgeg .RasterOperation (_gfdc ,_deb ,_bcdg ,_acdaf ,PixNotSrcAndDst ,p4 ,0,0);_cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"\u0074 \u0026\u0020\u0021\u0070\u0034");
};_aadf ,_cebg =_cgeg .ThresholdPixelSum (_ffed ,tab8 );if _cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"\u0074\u002d\u003e\u0074\u0068\u0072\u0065\u0073\u0068\u0031");};if _aadf {return false ,nil ;};if _cebg =_cgeg .RasterOperation (_gfdc ,_deb ,_gbe ,_ceeaa ,PixSrc ,p3 ,0,0);
_cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"p\u0033\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _cebg =_cgeg .RasterOperation (0,0,_gbe ,_ceeaa ,PixNotSrcAndDst ,p2 ,0,0);_cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"\u0074 \u0026\u0020\u0021\u0070\u0032");
};_aadf ,_cebg =_cgeg .ThresholdPixelSum (_bagd ,tab8 );if _cebg !=nil {return false ,_d .Wrap (_cebg ,_gggg ,"\u0074\u002d\u003e\u0074\u0068\u0072\u0065\u0073\u0068\u0033");};return !_aadf ,nil ;};func _accac (_agdba ,_afca int ,_gegd string )*Selection {_abced :=&Selection {Height :_agdba ,Width :_afca ,Name :_gegd };
_abced .Data =make ([][]SelectionValue ,_agdba );for _ggfc :=0;_ggfc < _agdba ;_ggfc ++{_abced .Data [_ggfc ]=make ([]SelectionValue ,_afca );};return _abced ;};func (_gedf *byHeight )Len ()int {return len (_gedf .Values )};func (_bbfc *byHeight )Less (i ,j int )bool {return _bbfc .Values [i ].Height < _bbfc .Values [j ].Height };
func (_aeda Points )GetGeometry (i int )(_gacg ,_gbbd float32 ,_eeda error ){if i > len (_aeda )-1{return 0,0,_d .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};_bggg :=_aeda [i ];return _bggg .X ,_bggg .Y ,nil ;};func _cfcgd (_aede *Bitmap ,_ecfe ...MorphProcess )(_dage *Bitmap ,_cgad error ){const _gea ="\u006d\u006f\u0072\u0070\u0068\u0053\u0065\u0071\u0075\u0065\u006e\u0063\u0065";if _aede ==nil {return nil ,_d .Error (_gea ,"\u006d\u006f\u0072\u0070\u0068\u0053\u0065\u0071\u0075\u0065\u006e\u0063\u0065 \u0073\u006f\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};if len (_ecfe )==0{return nil ,_d .Error (_gea ,"m\u006f\u0072\u0070\u0068\u0053\u0065q\u0075\u0065\u006e\u0063\u0065\u002c \u0073\u0065\u0071\u0075\u0065\u006e\u0063e\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");};if _cgad =_bcgb (_ecfe ...);
_cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};var _ffdg ,_afcb ,_gffc int ;_dage =_aede .Copy ();for _ ,_bccf :=range _ecfe {switch _bccf .Operation {case MopDilation :_ffdg ,_afcb =_bccf .getWidthHeight ();_dage ,_cgad =DilateBrick (nil ,_dage ,_ffdg ,_afcb );
if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};case MopErosion :_ffdg ,_afcb =_bccf .getWidthHeight ();_dage ,_cgad =_gcde (nil ,_dage ,_ffdg ,_afcb );if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};case MopOpening :_ffdg ,_afcb =_bccf .getWidthHeight ();
_dage ,_cgad =_cbbc (nil ,_dage ,_ffdg ,_afcb );if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};case MopClosing :_ffdg ,_afcb =_bccf .getWidthHeight ();_dage ,_cgad =_bbca (nil ,_dage ,_ffdg ,_afcb );if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");
};case MopRankBinaryReduction :_dage ,_cgad =_gbb (_dage ,_bccf .Arguments ...);if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};case MopReplicativeBinaryExpansion :_dage ,_cgad =_cced (_dage ,_bccf .Arguments [0]);if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");
};case MopAddBorder :_gffc =_bccf .Arguments [0];_dage ,_cgad =_dage .AddBorder (_gffc ,0);if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"");};default:return nil ,_d .Error (_gea ,"i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006d\u006fr\u0070\u0068\u004f\u0070\u0065\u0072\u0061ti\u006f\u006e\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0074\u006f t\u0068\u0065 \u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065");
};};if _gffc > 0{_dage ,_cgad =_dage .RemoveBorder (_gffc );if _cgad !=nil {return nil ,_d .Wrap (_cgad ,_gea ,"\u0062\u006f\u0072\u0064\u0065\u0072\u0020\u003e\u0020\u0030");};};return _dage ,nil ;};func _eea (_fedf ,_ccae *Bitmap ,_gef ,_ecbf ,_gcdg ,_gdcg ,_bgfg ,_ebde ,_fafc ,_cefc int ,_cfda CombinationOperator )error {var _aacc int ;
_gbab :=func (){_aacc ++;_gcdg +=_ccae .RowStride ;_gdcg +=_fedf .RowStride ;_bgfg +=_fedf .RowStride };for _aacc =_gef ;_aacc < _ecbf ;_gbab (){var _efbc uint16 ;_cacg :=_gcdg ;for _cedd :=_gdcg ;_cedd <=_bgfg ;_cedd ++{_ddgc ,_cebd :=_ccae .GetByte (_cacg );
if _cebd !=nil {return _cebd ;};_adee ,_cebd :=_fedf .GetByte (_cedd );if _cebd !=nil {return _cebd ;};_efbc =(_efbc |uint16 (_adee ))<<uint (_cefc );_adee =byte (_efbc >>8);if _cedd ==_bgfg {_adee =_gbag (uint (_ebde ),_adee );};if _cebd =_ccae .SetByte (_cacg ,_cdfe (_ddgc ,_adee ,_cfda ));
_cebd !=nil {return _cebd ;};_cacg ++;_efbc <<=uint (_fafc );};};return nil ;};const (MopDilation MorphOperation =iota ;MopErosion ;MopOpening ;MopClosing ;MopRankBinaryReduction ;MopReplicativeBinaryExpansion ;MopAddBorder ;);func _bcgb (_dcbf ...MorphProcess )(_babc error ){const _bggdc ="v\u0065r\u0069\u0066\u0079\u004d\u006f\u0072\u0070\u0068P\u0072\u006f\u0063\u0065ss\u0065\u0073";
var _bdcb ,_gcgd int ;for _ggf ,_dbea :=range _dcbf {if _babc =_dbea .verify (_ggf ,&_bdcb ,&_gcgd );_babc !=nil {return _d .Wrap (_babc ,_bggdc ,"");};};if _gcgd !=0&&_bdcb !=0{return _d .Error (_bggdc ,"\u004d\u006f\u0072\u0070\u0068\u0020\u0073\u0065\u0071\u0075\u0065n\u0063\u0065\u0020\u002d\u0020\u0062\u006f\u0072d\u0065r\u0020\u0061\u0064\u0064\u0065\u0064\u0020\u0062\u0075\u0074\u0020\u006e\u0065\u0074\u0020\u0072\u0065\u0064u\u0063\u0074\u0069\u006f\u006e\u0020\u006e\u006f\u0074\u0020\u0030");
};return nil ;};func _bgdbc (_ddfa *Bitmap ,_cegf ,_fcag ,_egac ,_cffae int ,_bddg RasterOperator ,_abfca *Bitmap ,_bgfd ,_gfce int )error {const _accde ="\u0072a\u0073t\u0065\u0072\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e";if _ddfa ==nil {return _d .Error (_accde ,"\u006e\u0069\u006c\u0020\u0027\u0064\u0065\u0073\u0074\u0027\u0020\u0042i\u0074\u006d\u0061\u0070");
};if _bddg ==PixDst {return nil ;};switch _bddg {case PixClr ,PixSet ,PixNotDst :_efc (_ddfa ,_cegf ,_fcag ,_egac ,_cffae ,_bddg );return nil ;};if _abfca ==nil {_bg .Log .Debug ("\u0052a\u0073\u0074e\u0072\u004f\u0070\u0065r\u0061\u0074\u0069o\u006e\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020bi\u0074\u006d\u0061p\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0064e\u0066\u0069n\u0065\u0064");
return _d .Error (_accde ,"\u006e\u0069l\u0020\u0027\u0073r\u0063\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _fece :=_cdfae (_ddfa ,_cegf ,_fcag ,_egac ,_cffae ,_bddg ,_abfca ,_bgfd ,_gfce );_fece !=nil {return _d .Wrap (_fece ,_accde ,"");
};return nil ;};func _fffd ()[]int {_ggd :=make ([]int ,256);_ggd [0]=0;_ggd [1]=7;var _bgad int ;for _bgad =2;_bgad < 4;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -2]+6;};for _bgad =4;_bgad < 8;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -4]+5;};for _bgad =8;_bgad < 16;
_bgad ++{_ggd [_bgad ]=_ggd [_bgad -8]+4;};for _bgad =16;_bgad < 32;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -16]+3;};for _bgad =32;_bgad < 64;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -32]+2;};for _bgad =64;_bgad < 128;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -64]+1;};for _bgad =128;
_bgad < 256;_bgad ++{_ggd [_bgad ]=_ggd [_bgad -128];};return _ggd ;};func (_cgc *Bitmap )Equivalent (s *Bitmap )bool {return _cgc .equivalent (s )};type Component int ;func CorrelationScoreSimple (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab []int )(_adgc float64 ,_ceef error ){const _dbfc ="\u0043\u006f\u0072\u0072el\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0053\u0069\u006d\u0070l\u0065";
if bm1 ==nil ||bm2 ==nil {return _adgc ,_d .Error (_dbfc ,"n\u0069l\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u0073 \u0070\u0072\u006f\u0076id\u0065\u0064");};if tab ==nil {return _adgc ,_d .Error (_dbfc ,"\u0074\u0061\u0062\u0020\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if area1 ==0||area2 ==0{return _adgc ,_d .Error (_dbfc ,"\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0061\u0072e\u0061\u0073\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u0030");};_gfgg ,_gabg :=bm1 .Width ,bm1 .Height ;_aeba ,_gfgd :=bm2 .Width ,bm2 .Height ;
if _adf (_gfgg -_aeba )> maxDiffW {return 0,nil ;};if _adf (_gabg -_gfgd )> maxDiffH {return 0,nil ;};var _bgac ,_dbgc int ;if delX >=0{_bgac =int (delX +0.5);}else {_bgac =int (delX -0.5);};if delY >=0{_dbgc =int (delY +0.5);}else {_dbgc =int (delY -0.5);
};_bgff :=bm1 .createTemplate ();if _ceef =_bgff .RasterOperation (_bgac ,_dbgc ,_aeba ,_gfgd ,PixSrc ,bm2 ,0,0);_ceef !=nil {return _adgc ,_d .Wrap (_ceef ,_dbfc ,"\u0062m\u0032 \u0074\u006f\u0020\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};if _ceef =_bgff .RasterOperation (0,0,_gfgg ,_gabg ,PixSrcAndDst ,bm1 ,0,0);
_ceef !=nil {return _adgc ,_d .Wrap (_ceef ,_dbfc ,"b\u006d\u0031\u0020\u0061\u006e\u0064\u0020\u0062\u006d\u0054");};_gfcc :=_bgff .countPixels ();_adgc =float64 (_gfcc )*float64 (_gfcc )/(float64 (area1 )*float64 (area2 ));return _adgc ,nil ;};func _aacg (_dgae ,_agga ,_gfaa *Bitmap ,_efcb int )(*Bitmap ,error ){const _geac ="\u0073\u0065\u0065\u0064\u0046\u0069\u006c\u006c\u0042i\u006e\u0061\u0072\u0079";
if _agga ==nil {return nil ,_d .Error (_geac ,"s\u006fu\u0072\u0063\u0065\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");};if _gfaa ==nil {return nil ,_d .Error (_geac ,"'\u006da\u0073\u006b\u0027\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");
};if _efcb !=4&&_efcb !=8{return nil ,_d .Error (_geac ,"\u0063\u006f\u006en\u0065\u0063\u0074\u0069v\u0069\u0074\u0079\u0020\u006e\u006f\u0074 \u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0034\u002c\u0038\u007d");};var _gbf error ;_dgae ,_gbf =_daed (_dgae ,_agga );
if _gbf !=nil {return nil ,_d .Wrap (_gbf ,_geac ,"\u0063o\u0070y\u0020\u0073\u006f\u0075\u0072c\u0065\u0020t\u006f\u0020\u0027\u0064\u0027");};_ceegd :=_agga .createTemplate ();_gfaa .setPadBits (0);for _beeg :=0;_beeg < _eeff ;_beeg ++{_ceegd ,_gbf =_daed (_ceegd ,_dgae );
if _gbf !=nil {return nil ,_d .Wrapf (_gbf ,_geac ,"\u0069\u0074\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064",_beeg );};if _gbf =_gcb (_dgae ,_gfaa ,_efcb );_gbf !=nil {return nil ,_d .Wrapf (_gbf ,_geac ,"\u0069\u0074\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064",_beeg );
};if _ceegd .Equals (_dgae ){break ;};};return _dgae ,nil ;};func (_dgdc *BitmapsArray )GetBitmaps (i int )(*Bitmaps ,error ){const _edbcbb ="\u0042\u0069\u0074ma\u0070\u0073\u0041\u0072\u0072\u0061\u0079\u002e\u0047\u0065\u0074\u0042\u0069\u0074\u006d\u0061\u0070\u0073";
if _dgdc ==nil {return nil ,_d .Error (_edbcbb ,"p\u0072\u006f\u0076\u0069\u0064\u0065d\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074m\u0061\u0070\u0073A\u0072r\u0061\u0079\u0027");};if i > len (_dgdc .Values )-1{return nil ,_d .Errorf (_edbcbb ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _dgdc .Values [i ],nil ;};func Copy (d ,s *Bitmap )(*Bitmap ,error ){return _daed (d ,s )};func (_abab *Bitmap )equivalent (_aeg *Bitmap )bool {if _abab ==_aeg {return true ;};if !_abab .SizesEqual (_aeg ){return false ;};_bgcd :=_edga (_abab ,_aeg ,CmbOpXor );
_ebgg :=_abab .countPixels ();_bde :=int (0.25*float32 (_ebgg ));if _bgcd .thresholdPixelSum (_bde ){return false ;};var (_ccfgg [9][9]int ;_bdef [18][9]int ;_bca [9][18]int ;_gecb int ;_fag int ;);_egfad :=9;_gfga :=_abab .Height /_egfad ;_eedg :=_abab .Width /_egfad ;
_bgg ,_bcaf :=_gfga /2,_eedg /2;if _gfga < _eedg {_bgg =_eedg /2;_bcaf =_gfga /2;};_fce :=float64 (_bgg )*float64 (_bcaf )*_b .Pi ;_cggf :=int (float64 (_gfga *_eedg /2)*0.9);_gbda :=int (float64 (_eedg *_gfga /2)*0.9);for _dba :=0;_dba < _egfad ;_dba ++{_cfd :=_eedg *_dba +_gecb ;
var _bafc int ;if _dba ==_egfad -1{_gecb =0;_bafc =_abab .Width ;}else {_bafc =_cfd +_eedg ;if ((_abab .Width -_gecb )%_egfad )> 0{_gecb ++;_bafc ++;};};for _bgf :=0;_bgf < _egfad ;_bgf ++{_gece :=_gfga *_bgf +_fag ;var _cdc int ;if _bgf ==_egfad -1{_fag =0;
_cdc =_abab .Height ;}else {_cdc =_gece +_gfga ;if (_abab .Height -_fag )%_egfad > 0{_fag ++;_cdc ++;};};var _facd ,_fgbe ,_gbg ,_aafde int ;_bda :=(_cfd +_bafc )/2;_bfgf :=(_gece +_cdc )/2;for _dega :=_cfd ;_dega < _bafc ;_dega ++{for _bac :=_gece ;_bac < _cdc ;
_bac ++{if _bgcd .GetPixel (_dega ,_bac ){if _dega < _bda {_facd ++;}else {_fgbe ++;};if _bac < _bfgf {_aafde ++;}else {_gbg ++;};};};};_ccfgg [_dba ][_bgf ]=_facd +_fgbe ;_bdef [_dba *2][_bgf ]=_facd ;_bdef [_dba *2+1][_bgf ]=_fgbe ;_bca [_dba ][_bgf *2]=_aafde ;
_bca [_dba ][_bgf *2+1]=_gbg ;};};for _eedf :=0;_eedf < _egfad *2-1;_eedf ++{for _bbcf :=0;_bbcf < (_egfad -1);_bbcf ++{var _cdfb int ;for _bdefc :=0;_bdefc < 2;_bdefc ++{for _efe :=0;_efe < 2;_efe ++{_cdfb +=_bdef [_eedf +_bdefc ][_bbcf +_efe ];};};if _cdfb > _gbda {return false ;
};};};for _gcf :=0;_gcf < (_egfad -1);_gcf ++{for _efg :=0;_efg < ((_egfad *2)-1);_efg ++{var _ceeg int ;for _eeba :=0;_eeba < 2;_eeba ++{for _aeee :=0;_aeee < 2;_aeee ++{_ceeg +=_bca [_gcf +_eeba ][_efg +_aeee ];};};if _ceeg > _cggf {return false ;};};
};for _fdce :=0;_fdce < (_egfad -2);_fdce ++{for _egdf :=0;_egdf < (_egfad -2);_egdf ++{var _ddc ,_bga int ;for _cbe :=0;_cbe < 3;_cbe ++{for _dbae :=0;_dbae < 3;_dbae ++{if _cbe ==_dbae {_ddc +=_ccfgg [_fdce +_cbe ][_egdf +_dbae ];};if (2-_cbe )==_dbae {_bga +=_ccfgg [_fdce +_cbe ][_egdf +_dbae ];
};};};if _ddc > _gbda ||_bga > _gbda {return false ;};};};for _fbaf :=0;_fbaf < (_egfad -1);_fbaf ++{for _egde :=0;_egde < (_egfad -1);_egde ++{var _edbf int ;for _fbeg :=0;_fbeg < 2;_fbeg ++{for _cdg :=0;_cdg < 2;_cdg ++{_edbf +=_ccfgg [_fbaf +_fbeg ][_egde +_cdg ];
};};if float64 (_edbf )> _fce {return false ;};};};return true ;};func (_adba *Bitmaps )SortByHeight (){_cgcf :=(*byHeight )(_adba );_ee .Sort (_cgcf )};func _cddg (_bgdc ,_cdfd *Bitmap ,_dbaf *Selection )(*Bitmap ,error ){const _eceg ="c\u006c\u006f\u0073\u0065\u0042\u0069\u0074\u006d\u0061\u0070";
var _fca error ;if _bgdc ,_fca =_bcefe (_bgdc ,_cdfd ,_dbaf );_fca !=nil {return nil ,_fca ;};_bcddg ,_fca :=_cdaa (nil ,_cdfd ,_dbaf );if _fca !=nil {return nil ,_d .Wrap (_fca ,_eceg ,"");};if _ ,_fca =_ecc (_bgdc ,_bcddg ,_dbaf );_fca !=nil {return nil ,_d .Wrap (_fca ,_eceg ,"");
};return _bgdc ,nil ;};func TstAddSymbol (t *_ed .T ,bms *Bitmaps ,sym *Bitmap ,x *int ,y int ,space int ){bms .AddBitmap (sym );_ababc :=_ce .Rect (*x ,y ,*x +sym .Width ,y +sym .Height );bms .AddBox (&_ababc );*x +=sym .Width +space ;};func Rect (x ,y ,w ,h int )(*_ce .Rectangle ,error ){const _faba ="b\u0069\u0074\u006d\u0061\u0070\u002e\u0052\u0065\u0063\u0074";
if x < 0{w +=x ;x =0;if w <=0{return nil ,_d .Errorf (_faba ,"x\u003a\u0027\u0025\u0064\u0027\u0020<\u0020\u0030\u0020\u0061\u006e\u0064\u0020\u0077\u003a \u0027\u0025\u0064'\u0020<\u003d\u0020\u0030",x ,w );};};if y < 0{h +=y ;y =0;if h <=0{return nil ,_d .Error (_faba ,"\u0079\u0020\u003c 0\u0020\u0061\u006e\u0064\u0020\u0062\u006f\u0078\u0020\u006f\u0066\u0066\u0020\u002b\u0071\u0075\u0061\u0064");
};};_bfcb :=_ce .Rect (x ,y ,x +w ,y +h );return &_bfcb ,nil ;};func (_cab *Bitmap )InverseData (){_cab .inverseData ()};func (_edcd *Bitmaps )AddBox (box *_ce .Rectangle ){_edcd .Boxes =append (_edcd .Boxes ,box )};type Selection struct{Height ,Width int ;
Cx ,Cy int ;Name string ;Data [][]SelectionValue ;};func (_bbgdd *Bitmaps )AddBitmap (bm *Bitmap ){_bbgdd .Values =append (_bbgdd .Values ,bm )};func CombineBytes (oldByte ,newByte byte ,op CombinationOperator )byte {return _cdfe (oldByte ,newByte ,op );
};func (_fedb *ClassedPoints )GetIntYByClass (i int )(int ,error ){const _ffff ="\u0043\u006c\u0061\u0073s\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047e\u0074I\u006e\u0074\u0059\u0042\u0079\u0043\u006ca\u0073\u0073";if i >=_fedb .IntSlice .Size (){return 0,_d .Errorf (_ffff ,"\u0069\u003a\u0020\u0027\u0025\u0064\u0027 \u0069\u0073\u0020o\u0075\u0074\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065",i );
};return int (_fedb .YAtIndex (i )),nil ;};func (_gdga *byWidth )Less (i ,j int )bool {return _gdga .Values [i ].Width < _gdga .Values [j ].Width };type SelectionValue int ;func (_ccff *BitmapsArray )AddBox (box *_ce .Rectangle ){_ccff .Boxes =append (_ccff .Boxes ,box )};
func DilateBrick (d ,s *Bitmap ,hSize ,vSize int )(*Bitmap ,error ){return _cbfb (d ,s ,hSize ,vSize )};func _aaca (_febbf *Bitmap ,_adbc *_ec .Stack ,_ceege ,_aecf ,_fabf int )(_bbgcb *_ce .Rectangle ,_gaee error ){const _cdbb ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";
if _febbf ==nil {return nil ,_d .Error (_cdbb ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");};if _adbc ==nil {return nil ,_d .Error (_cdbb ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");
};switch _fabf {case 4:if _bbgcb ,_gaee =_bfef (_febbf ,_adbc ,_ceege ,_aecf );_gaee !=nil {return nil ,_d .Wrap (_gaee ,_cdbb ,"");};return _bbgcb ,nil ;case 8:if _bbgcb ,_gaee =_fdecf (_febbf ,_adbc ,_ceege ,_aecf );_gaee !=nil {return nil ,_d .Wrap (_gaee ,_cdbb ,"");
};return _bbgcb ,nil ;default:return nil ,_d .Errorf (_cdbb ,"\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u0069\u0073 \u006eo\u0074\u0020\u0034\u0020\u006f\u0072\u0020\u0038\u003a\u0020\u0027\u0025\u0064\u0027",_fabf );
};};func (_age *Bitmap )AddBorder (borderSize ,val int )(*Bitmap ,error ){if borderSize ==0{return _age .Copy (),nil ;};_ccbb ,_cegcd :=_age .addBorderGeneral (borderSize ,borderSize ,borderSize ,borderSize ,val );if _cegcd !=nil {return nil ,_d .Wrap (_cegcd ,"\u0041d\u0064\u0042\u006f\u0072\u0064\u0065r","");
};return _ccbb ,nil ;};func (_cabg CombinationOperator )String ()string {var _ccgfd string ;switch _cabg {case CmbOpOr :_ccgfd ="\u004f\u0052";case CmbOpAnd :_ccgfd ="\u0041\u004e\u0044";case CmbOpXor :_ccgfd ="\u0058\u004f\u0052";case CmbOpXNor :_ccgfd ="\u0058\u004e\u004f\u0052";
case CmbOpReplace :_ccgfd ="\u0052E\u0050\u004c\u0041\u0043\u0045";case CmbOpNot :_ccgfd ="\u004e\u004f\u0054";};return _ccgfd ;};func _cbfb (_fdeg ,_ebdd *Bitmap ,_dgab ,_gccf int )(*Bitmap ,error ){const _cdbc ="d\u0069\u006c\u0061\u0074\u0065\u0042\u0072\u0069\u0063\u006b";
if _ebdd ==nil {_bg .Log .Debug ("\u0064\u0069\u006c\u0061\u0074\u0065\u0042\u0072\u0069\u0063k\u0020\u0073\u006f\u0075\u0072\u0063\u0065 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");return nil ,_d .Error (_cdbc ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};if _dgab < 1||_gccf < 1{return nil ,_d .Error (_cdbc ,"\u0068\u0053\u007a\u0069\u0065 \u0061\u006e\u0064\u0020\u0076\u0053\u0069\u007a\u0065\u0020\u0061\u0072\u0065 \u006e\u006f\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0031");
};if _dgab ==1&&_gccf ==1{_bdga ,_fdac :=_daed (_fdeg ,_ebdd );if _fdac !=nil {return nil ,_d .Wrap (_fdac ,_cdbc ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u0026\u0026 \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _bdga ,nil ;};if _dgab ==1||_gccf ==1{_bcef :=SelCreateBrick (_gccf ,_dgab ,_gccf /2,_dgab /2,SelHit );_gecf ,_dgca :=_cdaa (_fdeg ,_ebdd ,_bcef );if _dgca !=nil {return nil ,_d .Wrap (_dgca ,_cdbc ,"\u0068s\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _gecf ,nil ;};_cgbc :=SelCreateBrick (1,_dgab ,0,_dgab /2,SelHit );_cfcda :=SelCreateBrick (_gccf ,1,_gccf /2,0,SelHit );_aef ,_bfcc :=_cdaa (nil ,_ebdd ,_cgbc );if _bfcc !=nil {return nil ,_d .Wrap (_bfcc ,_cdbc ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};_fdeg ,_bfcc =_cdaa (_fdeg ,_aef ,_cfcda );if _bfcc !=nil {return nil ,_d .Wrap (_bfcc ,_cdbc ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};return _fdeg ,nil ;};func _fddaf (_dcfe *Bitmap ,_gcda ,_agcb int ,_bgadb ,_aeded int ,_fcga RasterOperator ){var (_ebge int ;
_fddf byte ;_ebcd ,_aade int ;_cfef int ;);_afdc :=_bgadb >>3;_eecf :=_bgadb &7;if _eecf > 0{_fddf =_bcbg [_eecf ];};_ebge =_dcfe .RowStride *_agcb +(_gcda >>3);switch _fcga {case PixClr :for _ebcd =0;_ebcd < _aeded ;_ebcd ++{_cfef =_ebge +_ebcd *_dcfe .RowStride ;
for _aade =0;_aade < _afdc ;_aade ++{_dcfe .Data [_cfef ]=0x0;_cfef ++;};if _eecf > 0{_dcfe .Data [_cfef ]=_eead (_dcfe .Data [_cfef ],0x0,_fddf );};};case PixSet :for _ebcd =0;_ebcd < _aeded ;_ebcd ++{_cfef =_ebge +_ebcd *_dcfe .RowStride ;for _aade =0;
_aade < _afdc ;_aade ++{_dcfe .Data [_cfef ]=0xff;_cfef ++;};if _eecf > 0{_dcfe .Data [_cfef ]=_eead (_dcfe .Data [_cfef ],0xff,_fddf );};};case PixNotDst :for _ebcd =0;_ebcd < _aeded ;_ebcd ++{_cfef =_ebge +_ebcd *_dcfe .RowStride ;for _aade =0;_aade < _afdc ;
_aade ++{_dcfe .Data [_cfef ]=^_dcfe .Data [_cfef ];_cfef ++;};if _eecf > 0{_dcfe .Data [_cfef ]=_eead (_dcfe .Data [_cfef ],^_dcfe .Data [_cfef ],_fddf );};};};};func HausTest (p1 ,p2 ,p3 ,p4 *Bitmap ,delX ,delY float32 ,maxDiffW ,maxDiffH int )(bool ,error ){const _eag ="\u0048\u0061\u0075\u0073\u0054\u0065\u0073\u0074";
_bed ,_defbg :=p1 .Width ,p1 .Height ;_gfb ,_ffcd :=p3 .Width ,p3 .Height ;if _ec .Abs (_bed -_gfb )> maxDiffW {return false ,nil ;};if _ec .Abs (_defbg -_ffcd )> maxDiffH {return false ,nil ;};_dbcbg :=int (delX +_ec .Sign (delX )*0.5);_cbbd :=int (delY +_ec .Sign (delY )*0.5);
var _dbac error ;_fgbc :=p1 .CreateTemplate ();if _dbac =_fgbc .RasterOperation (0,0,_bed ,_defbg ,PixSrc ,p1 ,0,0);_dbac !=nil {return false ,_d .Wrap (_dbac ,_eag ,"p\u0031\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _dbac =_fgbc .RasterOperation (_dbcbg ,_cbbd ,_bed ,_defbg ,PixNotSrcAndDst ,p4 ,0,0);
_dbac !=nil {return false ,_d .Wrap (_dbac ,_eag ,"\u0021p\u0034\u0020\u0026\u0020\u0074");};if _fgbc .Zero (){return false ,nil ;};if _dbac =_fgbc .RasterOperation (_dbcbg ,_cbbd ,_gfb ,_ffcd ,PixSrc ,p3 ,0,0);_dbac !=nil {return false ,_d .Wrap (_dbac ,_eag ,"p\u0033\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");
};if _dbac =_fgbc .RasterOperation (0,0,_gfb ,_ffcd ,PixNotSrcAndDst ,p2 ,0,0);_dbac !=nil {return false ,_d .Wrap (_dbac ,_eag ,"\u0021p\u0032\u0020\u0026\u0020\u0074");};return _fgbc .Zero (),nil ;};func (_bec *Bitmap )CreateTemplate ()*Bitmap {return _bec .createTemplate ()};
func (_aad *Bitmap )countPixels ()int {var (_egdd int ;_fbbf uint8 ;_gbdd byte ;_ega int ;);_deeg :=_aad .RowStride ;_dffc :=uint (_aad .Width &0x07);if _dffc !=0{_fbbf =uint8 ((0xff<<(8-_dffc ))&0xff);_deeg --;};for _cabd :=0;_cabd < _aad .Height ;_cabd ++{for _ega =0;
_ega < _deeg ;_ega ++{_gbdd =_aad .Data [_cabd *_aad .RowStride +_ega ];_egdd +=int (_ebf [_gbdd ]);};if _dffc !=0{_egdd +=int (_ebf [_aad .Data [_cabd *_aad .RowStride +_ega ]&_fbbf ]);};};return _egdd ;};func _ddbg (_baea ,_eedd *Bitmap ,_ddca ,_aaba ,_bdae ,_fdfb ,_bccab int ,_cad CombinationOperator )error {var _ddg int ;
_gad :=func (){_ddg ++;_bdae +=_eedd .RowStride ;_fdfb +=_baea .RowStride ;_bccab +=_baea .RowStride };for _ddg =_ddca ;_ddg < _aaba ;_gad (){_bcdd :=_bdae ;for _dbcg :=_fdfb ;_dbcg <=_bccab ;_dbcg ++{_ceff ,_cecf :=_eedd .GetByte (_bcdd );if _cecf !=nil {return _cecf ;
};_cabb ,_cecf :=_baea .GetByte (_dbcg );if _cecf !=nil {return _cecf ;};if _cecf =_eedd .SetByte (_bcdd ,_cdfe (_ceff ,_cabb ,_cad ));_cecf !=nil {return _cecf ;};_bcdd ++;};};return nil ;};var _ebf [256]uint8 ;var (_bbcbd *Bitmap ;_febe *Bitmap ;);func (_bdd *Bitmap )removeBorderGeneral (_geceg ,_aacb ,_eaec ,_cdb int )(*Bitmap ,error ){const _daf ="\u0072\u0065\u006d\u006fve\u0042\u006f\u0072\u0064\u0065\u0072\u0047\u0065\u006e\u0065\u0072\u0061\u006c";
if _geceg < 0||_aacb < 0||_eaec < 0||_cdb < 0{return nil ,_d .Error (_daf ,"\u006e\u0065g\u0061\u0074\u0069\u0076\u0065\u0020\u0062\u0072\u006f\u0064\u0065\u0072\u0020\u0072\u0065\u006d\u006f\u0076\u0065\u0020\u0076\u0061lu\u0065\u0073");};_bcca ,_adgd :=_bdd .Width ,_bdd .Height ;
_fdg :=_bcca -_geceg -_aacb ;_cace :=_adgd -_eaec -_cdb ;if _fdg <=0{return nil ,_d .Errorf (_daf ,"w\u0069\u0064\u0074\u0068: \u0025d\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u003e\u0020\u0030",_fdg );};if _cace <=0{return nil ,_d .Errorf (_daf ,"\u0068\u0065\u0069\u0067ht\u003a\u0020\u0025\u0064\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e \u0030",_cace );
};_dbg :=New (_fdg ,_cace );_dbg .Color =_bdd .Color ;_fbdb :=_dbg .RasterOperation (0,0,_fdg ,_cace ,PixSrc ,_bdd ,_geceg ,_eaec );if _fbdb !=nil {return nil ,_d .Wrap (_fbdb ,_daf ,"");};return _dbg ,nil ;};func TstESymbol (t *_ed .T ,scale ...int )*Bitmap {_babg ,_eacgf :=NewWithData (4,5,[]byte {0xF0,0x80,0xE0,0x80,0xF0});
_c .NoError (t ,_eacgf );return TstGetScaledSymbol (t ,_babg ,scale ...);};func _bggga (_bbfb *_ec .Stack )(_ded *fillSegment ,_affb error ){const _dbddg ="\u0070\u006f\u0070\u0046\u0069\u006c\u006c\u0053\u0065g\u006d\u0065\u006e\u0074";if _bbfb ==nil {return nil ,_d .Error (_dbddg ,"\u006ei\u006c \u0073\u0074\u0061\u0063\u006b \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");
};if _bbfb .Aux ==nil {return nil ,_d .Error (_dbddg ,"a\u0075x\u0053\u0074\u0061\u0063\u006b\u0020\u006e\u006ft\u0020\u0064\u0065\u0066in\u0065\u0064");};_gebb ,_cfbe :=_bbfb .Pop ();if !_cfbe {return nil ,nil ;};_gaegf ,_cfbe :=_gebb .(*fillSegment );
if !_cfbe {return nil ,_d .Error (_dbddg ,"\u0073\u0074\u0061ck\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074\u0020c\u006fn\u0074a\u0069n\u0020\u002a\u0066\u0069\u006c\u006c\u0053\u0065\u0067\u006d\u0065\u006e\u0074");};_ded =&fillSegment {_gaegf ._bffd ,_gaegf ._eagf ,_gaegf ._dfdd +_gaegf ._cddd ,_gaegf ._cddd };
_bbfb .Aux .Push (_gaegf );return _ded ,nil ;};const (_ SizeComparison =iota ;SizeSelectIfLT ;SizeSelectIfGT ;SizeSelectIfLTE ;SizeSelectIfGTE ;SizeSelectIfEQ ;);func MakePixelSumTab8 ()[]int {return _dcgad ()};func (_ceda Points )XSorter ()func (_eagb ,_ffbac int )bool {return func (_agge ,_bafcf int )bool {return _ceda [_agge ].X < _ceda [_bafcf ].X };
};func (_dbc *Bitmap )GetPixel (x ,y int )bool {_eafa :=_dbc .GetByteIndex (x ,y );_ccd :=_dbc .GetBitOffset (x );_efb :=uint (7-_ccd );if _eafa > len (_dbc .Data )-1{_bg .Log .Debug ("\u0054\u0072\u0079\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0067\u0065\u0074\u0020\u0070\u0069\u0078\u0065\u006c\u0020o\u0075\u0074\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0064\u0061\u0074\u0061\u0020\u0072\u0061\u006e\u0067\u0065\u002e \u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0079\u003a\u0027\u0025\u0064'\u002c\u0020\u0062m\u003a\u0020\u0027\u0025\u0073\u0027",x ,y ,_dbc );
return false ;};if (_dbc .Data [_eafa ]>>_efb )&0x01>=1{return true ;};return false ;};func (_gccde *Bitmap )addPadBits ()(_dcab error ){const _bageb ="\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0061\u0064\u0064\u0050\u0061d\u0042\u0069\u0074\u0073";_cede :=_gccde .Width %8;
if _cede ==0{return nil ;};_ace :=_gccde .Width /8;_dgg :=_a .NewReader (_gccde .Data );_bge :=make ([]byte ,_gccde .Height *_gccde .RowStride );_dcgc :=_a .NewWriterMSB (_bge );_caeb :=make ([]byte ,_ace );var (_fgc int ;_dfd uint64 ;);for _fgc =0;_fgc < _gccde .Height ;
_fgc ++{if _ ,_dcab =_dgg .Read (_caeb );_dcab !=nil {return _d .Wrap (_dcab ,_bageb ,"\u0066u\u006c\u006c\u0020\u0062\u0079\u0074e");};if _ ,_dcab =_dcgc .Write (_caeb );_dcab !=nil {return _d .Wrap (_dcab ,_bageb ,"\u0066\u0075\u006c\u006c\u0020\u0062\u0079\u0074\u0065\u0073");
};if _dfd ,_dcab =_dgg .ReadBits (byte (_cede ));_dcab !=nil {return _d .Wrap (_dcab ,_bageb ,"\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0062\u0069\u0074\u0073");};if _dcab =_dcgc .WriteByte (byte (_dfd )<<uint (8-_cede ));_dcab !=nil {return _d .Wrap (_dcab ,_bageb ,"\u006ca\u0073\u0074\u0020\u0062\u0079\u0074e");
};};_gccde .Data =_dcgc .Data ();return nil ;};func (_bacfd *byWidth )Swap (i ,j int ){_bacfd .Values [i ],_bacfd .Values [j ]=_bacfd .Values [j ],_bacfd .Values [i ];if _bacfd .Boxes !=nil {_bacfd .Boxes [i ],_bacfd .Boxes [j ]=_bacfd .Boxes [j ],_bacfd .Boxes [i ];
};};func _dadc (_gdgcf *Bitmap )(_abcf *Bitmap ,_bdcf int ,_cbeba error ){const _deegg ="\u0042i\u0074\u006d\u0061\u0070.\u0077\u006f\u0072\u0064\u004da\u0073k\u0042y\u0044\u0069\u006c\u0061\u0074\u0069\u006fn";if _gdgcf ==nil {return nil ,0,_d .Errorf (_deegg ,"\u0027\u0073\u0027\u0020bi\u0074\u006d\u0061\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");
};var _efef ,_adea *Bitmap ;if _efef ,_cbeba =_daed (nil ,_gdgcf );_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0063\u006f\u0070\u0079\u0020\u0027\u0073\u0027");};var (_egc [13]int ;_ddga ,_cfab int ;);_bggf :=12;_dbbd :=_ec .NewNumSlice (_bggf +1);
_cfeb :=_ec .NewNumSlice (_bggf +1);var _bbbdd *Boxes ;for _eeaa :=0;_eeaa <=_bggf ;_eeaa ++{if _eeaa ==0{if _adea ,_cbeba =_daed (nil ,_efef );_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0066i\u0072\u0073\u0074\u0020\u0062\u006d2");};}else {if _adea ,_cbeba =_cfcgd (_efef ,MorphProcess {Operation :MopDilation ,Arguments :[]int {2,1}});
_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0064\u0069\u006ca\u0074\u0069\u006f\u006e\u0020\u0062\u006d\u0032");};};if _bbbdd ,_cbeba =_adea .connComponentsBB (4);_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"");};_egc [_eeaa ]=len (*_bbbdd );
_dbbd .AddInt (_egc [_eeaa ]);switch _eeaa {case 0:_ddga =_egc [0];default:_cfab =_egc [_eeaa -1]-_egc [_eeaa ];_cfeb .AddInt (_cfab );};_efef =_adea ;};_adfdd :=true ;_bfa :=2;var _ecd ,_eega int ;for _deab :=1;_deab < len (*_cfeb );_deab ++{if _ecd ,_cbeba =_dbbd .GetInt (_deab );
_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0043\u0068\u0065\u0063ki\u006e\u0067\u0020\u0062\u0065\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0069o\u006e");};if _adfdd &&_ecd < int (0.3*float32 (_ddga )){_bfa =_deab +1;_adfdd =false ;
};if _cfab ,_cbeba =_cfeb .GetInt (_deab );_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006ea\u0044\u0069\u0066\u0066");};if _cfab > _eega {_eega =_cfab ;};};_adgeg :=_gdgcf .XResolution ;if _adgeg ==0{_adgeg =150;
};if _adgeg > 110{_bfa ++;};if _bfa < 2{_bg .Log .Trace ("J\u0042\u0049\u0047\u0032\u0020\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u0042\u0065\u0073\u0074 \u0074\u006f\u0020\u006d\u0069\u006e\u0069\u006d\u0075\u006d a\u006c\u006c\u006fw\u0061b\u006c\u0065");
_bfa =2;};_bdcf =_bfa +1;if _abcf ,_cbeba =_aagb (nil ,_gdgcf ,_bfa +1,1);_cbeba !=nil {return nil ,0,_d .Wrap (_cbeba ,_deegg ,"\u0067\u0065\u0074\u0074in\u0067\u0020\u006d\u0061\u0073\u006b\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};return _abcf ,_bdcf ,nil ;
};func (_fcf *Bitmap )setFourBytes (_facde int ,_cecg uint32 )error {if _facde +3> len (_fcf .Data )-1{return _d .Errorf ("\u0073\u0065\u0074F\u006f\u0075\u0072\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_facde );
};_fcf .Data [_facde ]=byte ((_cecg &0xff000000)>>24);_fcf .Data [_facde +1]=byte ((_cecg &0xff0000)>>16);_fcf .Data [_facde +2]=byte ((_cecg &0xff00)>>8);_fcf .Data [_facde +3]=byte (_cecg &0xff);return nil ;};func (_edg *Bitmap )GetVanillaData ()[]byte {if _edg .Color ==Chocolate {_edg .inverseData ();
};return _edg .Data ;};func _cdfe (_eacdg ,_eaeeg byte ,_afg CombinationOperator )byte {switch _afg {case CmbOpOr :return _eaeeg |_eacdg ;case CmbOpAnd :return _eaeeg &_eacdg ;case CmbOpXor :return _eaeeg ^_eacdg ;case CmbOpXNor :return ^(_eaeeg ^_eacdg );
case CmbOpNot :return ^(_eaeeg );default:return _eaeeg ;};};func _cdfae (_ddaa *Bitmap ,_cfde ,_gfdb int ,_gcfa ,_eabd int ,_bbdc RasterOperator ,_eebd *Bitmap ,_abfd ,_gbdc int )error {var _fdbba ,_bbgf ,_bbbc ,_bcec int ;if _cfde < 0{_abfd -=_cfde ;_gcfa +=_cfde ;
_cfde =0;};if _abfd < 0{_cfde -=_abfd ;_gcfa +=_abfd ;_abfd =0;};_fdbba =_cfde +_gcfa -_ddaa .Width ;if _fdbba > 0{_gcfa -=_fdbba ;};_bbgf =_abfd +_gcfa -_eebd .Width ;if _bbgf > 0{_gcfa -=_bbgf ;};if _gfdb < 0{_gbdc -=_gfdb ;_eabd +=_gfdb ;_gfdb =0;};
if _gbdc < 0{_gfdb -=_gbdc ;_eabd +=_gbdc ;_gbdc =0;};_bbbc =_gfdb +_eabd -_ddaa .Height ;if _bbbc > 0{_eabd -=_bbbc ;};_bcec =_gbdc +_eabd -_eebd .Height ;if _bcec > 0{_eabd -=_bcec ;};if _gcfa <=0||_eabd <=0{return nil ;};var _eedcb error ;switch {case _cfde &7==0&&_abfd &7==0:_eedcb =_ggeg (_ddaa ,_cfde ,_gfdb ,_gcfa ,_eabd ,_bbdc ,_eebd ,_abfd ,_gbdc );
case _cfde &7==_abfd &7:_eedcb =_edage (_ddaa ,_cfde ,_gfdb ,_gcfa ,_eabd ,_bbdc ,_eebd ,_abfd ,_gbdc );default:_eedcb =_cacd (_ddaa ,_cfde ,_gfdb ,_gcfa ,_eabd ,_bbdc ,_eebd ,_abfd ,_gbdc );};if _eedcb !=nil {return _d .Wrap (_eedcb ,"r\u0061\u0073\u0074\u0065\u0072\u004f\u0070\u004c\u006f\u0077","");
};return nil ;};type MorphProcess struct{Operation MorphOperation ;Arguments []int ;};func (_eaba *Boxes )SelectBySize (width ,height int ,tp LocationFilter ,relation SizeComparison )(_dfc *Boxes ,_bdg error ){const _egg ="\u0042o\u0078e\u0073\u002e\u0053\u0065\u006ce\u0063\u0074B\u0079\u0053\u0069\u007a\u0065";
if _eaba ==nil {return nil ,_d .Error (_egg ,"b\u006f\u0078\u0065\u0073 '\u0062'\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064");};if len (*_eaba )==0{return _eaba ,nil ;};switch tp {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_d .Errorf (_egg ,"\u0069\u006e\u0076al\u0069\u0064\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",tp );
};switch relation {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE :default:return nil ,_d .Errorf (_egg ,"i\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020t\u0079\u0070\u0065:\u0020'\u0025\u0064\u0027",tp );
};_bacg :=_eaba .makeSizeIndicator (width ,height ,tp ,relation );_cbcc ,_bdg :=_eaba .selectWithIndicator (_bacg );if _bdg !=nil {return nil ,_d .Wrap (_bdg ,_egg ,"");};return _cbcc ,nil ;};func (_agee *ClassedPoints )SortByX (){_agee ._ebee =_agee .xSortFunction ();
_ee .Sort (_agee )};const _eeff =5000;type Color int ;func _edage (_dabbc *Bitmap ,_gdcf ,_dagb ,_fagab ,_fgad int ,_eebe RasterOperator ,_abcd *Bitmap ,_ggaf ,_cdcf int )error {var (_cabbe bool ;_bgab bool ;_gdae int ;_bcea int ;_gdfe int ;_ddcd bool ;
_aedaa byte ;_fcce int ;_dfe int ;_baeag int ;_adgca ,_edcb int ;);_gccc :=8-(_gdcf &7);_febb :=_ffgb [_gccc ];_dcgdc :=_dabbc .RowStride *_dagb +(_gdcf >>3);_cdgec :=_abcd .RowStride *_cdcf +(_ggaf >>3);if _fagab < _gccc {_cabbe =true ;_febb &=_bcbg [8-_gccc +_fagab ];
};if !_cabbe {_gdae =(_fagab -_gccc )>>3;if _gdae > 0{_bgab =true ;_bcea =_dcgdc +1;_gdfe =_cdgec +1;};};_fcce =(_gdcf +_fagab )&7;if !(_cabbe ||_fcce ==0){_ddcd =true ;_aedaa =_bcbg [_fcce ];_dfe =_dcgdc +1+_gdae ;_baeag =_cdgec +1+_gdae ;};switch _eebe {case PixSrc :for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ],_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=_abcd .Data [_gdfe +_edcb ];
};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ],_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotSrc :for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^_abcd .Data [_cdgec ],_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=^_abcd .Data [_gdfe +_edcb ];
};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^_abcd .Data [_baeag ],_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixSrcOrDst :for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ]|_dabbc .Data [_dcgdc ],_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;
_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]|=_abcd .Data [_gdfe +_edcb ];};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ]|_dabbc .Data [_dfe ],_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixSrcAndDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ]&_dabbc .Data [_dcgdc ],_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;
};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]&=_abcd .Data [_gdfe +_edcb ];};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ]&_dabbc .Data [_dfe ],_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixSrcXorDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ]^_dabbc .Data [_dcgdc ],_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;
};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]^=_abcd .Data [_gdfe +_edcb ];};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ]^_dabbc .Data [_dfe ],_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotSrcOrDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^(_abcd .Data [_cdgec ])|_dabbc .Data [_dcgdc ],_febb );_dcgdc +=_dabbc .RowStride ;
_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]|=^(_abcd .Data [_gdfe +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^(_abcd .Data [_baeag ])|_dabbc .Data [_dfe ],_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotSrcAndDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^(_abcd .Data [_cdgec ])&_dabbc .Data [_dcgdc ],_febb );
_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]&=^_abcd .Data [_gdfe +_edcb ];};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;
};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^(_abcd .Data [_baeag ])&_dabbc .Data [_dfe ],_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixSrcOrNotDst :for _adgca =0;_adgca < _fgad ;
_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ]|^(_dabbc .Data [_dcgdc ]),_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;
_edcb ++{_dabbc .Data [_bcea +_edcb ]=_abcd .Data [_gdfe +_edcb ]|^(_dabbc .Data [_bcea +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ]|^(_dabbc .Data [_dfe ]),_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixSrcAndNotDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],_abcd .Data [_cdgec ]&^(_dabbc .Data [_dcgdc ]),_febb );_dcgdc +=_dabbc .RowStride ;
_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=_abcd .Data [_gdfe +_edcb ]&^(_dabbc .Data [_bcea +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;
};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],_abcd .Data [_baeag ]&^(_dabbc .Data [_dfe ]),_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotPixSrcOrDst :for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^(_abcd .Data [_cdgec ]|_dabbc .Data [_dcgdc ]),_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;
_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=^(_abcd .Data [_gdfe +_edcb ]|_dabbc .Data [_bcea +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^(_abcd .Data [_baeag ]|_dabbc .Data [_dfe ]),_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotPixSrcAndDst :for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^(_abcd .Data [_cdgec ]&_dabbc .Data [_dcgdc ]),_febb );_dcgdc +=_dabbc .RowStride ;
_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=^(_abcd .Data [_gdfe +_edcb ]&_dabbc .Data [_bcea +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;
};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^(_abcd .Data [_baeag ]&_dabbc .Data [_dfe ]),_aedaa );_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};case PixNotPixSrcXorDst :for _adgca =0;
_adgca < _fgad ;_adgca ++{_dabbc .Data [_dcgdc ]=_eead (_dabbc .Data [_dcgdc ],^(_abcd .Data [_cdgec ]^_dabbc .Data [_dcgdc ]),_febb );_dcgdc +=_dabbc .RowStride ;_cdgec +=_abcd .RowStride ;};if _bgab {for _adgca =0;_adgca < _fgad ;_adgca ++{for _edcb =0;
_edcb < _gdae ;_edcb ++{_dabbc .Data [_bcea +_edcb ]=^(_abcd .Data [_gdfe +_edcb ]^_dabbc .Data [_bcea +_edcb ]);};_bcea +=_dabbc .RowStride ;_gdfe +=_abcd .RowStride ;};};if _ddcd {for _adgca =0;_adgca < _fgad ;_adgca ++{_dabbc .Data [_dfe ]=_eead (_dabbc .Data [_dfe ],^(_abcd .Data [_baeag ]^_dabbc .Data [_dfe ]),_aedaa );
_dfe +=_dabbc .RowStride ;_baeag +=_abcd .RowStride ;};};default:_bg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0064",_eebe );return _d .Error ("\u0072\u0061\u0073\u0074er\u004f\u0070\u0056\u0041\u006c\u0069\u0067\u006e\u0065\u0064\u004c\u006f\u0077","\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");
};return nil ;};func TstCSymbol (t *_ed .T )*Bitmap {t .Helper ();_acea :=New (6,6);_c .NoError (t ,_acea .SetPixel (1,0,1));_c .NoError (t ,_acea .SetPixel (2,0,1));_c .NoError (t ,_acea .SetPixel (3,0,1));_c .NoError (t ,_acea .SetPixel (4,0,1));_c .NoError (t ,_acea .SetPixel (0,1,1));
_c .NoError (t ,_acea .SetPixel (5,1,1));_c .NoError (t ,_acea .SetPixel (0,2,1));_c .NoError (t ,_acea .SetPixel (0,3,1));_c .NoError (t ,_acea .SetPixel (0,4,1));_c .NoError (t ,_acea .SetPixel (5,4,1));_c .NoError (t ,_acea .SetPixel (1,5,1));_c .NoError (t ,_acea .SetPixel (2,5,1));
_c .NoError (t ,_acea .SetPixel (3,5,1));_c .NoError (t ,_acea .SetPixel (4,5,1));return _acea ;};func TstASymbol (t *_ed .T )*Bitmap {t .Helper ();_affc :=New (6,6);_c .NoError (t ,_affc .SetPixel (1,0,1));_c .NoError (t ,_affc .SetPixel (2,0,1));_c .NoError (t ,_affc .SetPixel (3,0,1));
_c .NoError (t ,_affc .SetPixel (4,0,1));_c .NoError (t ,_affc .SetPixel (5,1,1));_c .NoError (t ,_affc .SetPixel (1,2,1));_c .NoError (t ,_affc .SetPixel (2,2,1));_c .NoError (t ,_affc .SetPixel (3,2,1));_c .NoError (t ,_affc .SetPixel (4,2,1));_c .NoError (t ,_affc .SetPixel (5,2,1));
_c .NoError (t ,_affc .SetPixel (0,3,1));_c .NoError (t ,_affc .SetPixel (5,3,1));_c .NoError (t ,_affc .SetPixel (0,4,1));_c .NoError (t ,_affc .SetPixel (5,4,1));_c .NoError (t ,_affc .SetPixel (1,5,1));_c .NoError (t ,_affc .SetPixel (2,5,1));_c .NoError (t ,_affc .SetPixel (3,5,1));
_c .NoError (t ,_affc .SetPixel (4,5,1));_c .NoError (t ,_affc .SetPixel (5,5,1));return _affc ;};func _efc (_acff *Bitmap ,_dbfe ,_abae ,_bebf ,_fdcc int ,_ffeb RasterOperator ){if _dbfe < 0{_bebf +=_dbfe ;_dbfe =0;};_aceb :=_dbfe +_bebf -_acff .Width ;
if _aceb > 0{_bebf -=_aceb ;};if _abae < 0{_fdcc +=_abae ;_abae =0;};_dcf :=_abae +_fdcc -_acff .Height ;if _dcf > 0{_fdcc -=_dcf ;};if _bebf <=0||_fdcc <=0{return ;};if (_dbfe &7)==0{_fddaf (_acff ,_dbfe ,_abae ,_bebf ,_fdcc ,_ffeb );}else {_gbbf (_acff ,_dbfe ,_abae ,_bebf ,_fdcc ,_ffeb );
};};type byWidth Bitmaps ;func (_cgef *Bitmap )GetComponents (components Component ,maxWidth ,maxHeight int )(_afgf *Bitmaps ,_ecgc *Boxes ,_cgdb error ){const _dacda ="B\u0069t\u006d\u0061\u0070\u002e\u0047\u0065\u0074\u0043o\u006d\u0070\u006f\u006een\u0074\u0073";
if _cgef ==nil {return nil ,nil ,_d .Error (_dacda ,"\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0042\u0069\u0074\u006da\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069n\u0065\u0064\u002e");};switch components {case ComponentConn ,ComponentCharacters ,ComponentWords :default:return nil ,nil ,_d .Error (_dacda ,"\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074s\u0020\u0070\u0061\u0072\u0061\u006d\u0065t\u0065\u0072");
};if _cgef .Zero (){_ecgc =&Boxes {};_afgf =&Bitmaps {};return _afgf ,_ecgc ,nil ;};switch components {case ComponentConn :_afgf =&Bitmaps {};if _ecgc ,_cgdb =_cgef .ConnComponents (_afgf ,8);_cgdb !=nil {return nil ,nil ,_d .Wrap (_cgdb ,_dacda ,"\u006e\u006f \u0070\u0072\u0065p\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};case ComponentCharacters :_afeaf ,_dde :=MorphSequence (_cgef ,MorphProcess {Operation :MopClosing ,Arguments :[]int {1,6}});if _dde !=nil {return nil ,nil ,_d .Wrap (_dde ,_dacda ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};if _bg .Log .IsLogLevel (_bg .LogLevelTrace ){_bg .Log .Trace ("\u0043o\u006d\u0070o\u006e\u0065\u006e\u0074C\u0068\u0061\u0072a\u0063\u0074\u0065\u0072\u0073\u0020\u0062\u0069\u0074ma\u0070\u0020\u0061f\u0074\u0065r\u0020\u0063\u006c\u006f\u0073\u0069n\u0067\u003a \u0025\u0073",_afeaf .String ());
};_gcab :=&Bitmaps {};_ecgc ,_dde =_afeaf .ConnComponents (_gcab ,8);if _dde !=nil {return nil ,nil ,_d .Wrap (_dde ,_dacda ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};if _bg .Log .IsLogLevel (_bg .LogLevelTrace ){_bg .Log .Trace ("\u0043\u006f\u006d\u0070\u006f\u006ee\u006e\u0074\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u0020a\u0066\u0074\u0065\u0072\u0020\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076i\u0074y\u003a\u0020\u0025\u0073",_gcab .String ());
};if _afgf ,_dde =_gcab .ClipToBitmap (_cgef );_dde !=nil {return nil ,nil ,_d .Wrap (_dde ,_dacda ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");};case ComponentWords :_dbe :=1;
var _gaed *Bitmap ;switch {case _cgef .XResolution <=200:_gaed =_cgef ;case _cgef .XResolution <=400:_dbe =2;_gaed ,_cgdb =_gbb (_cgef ,1,0,0,0);if _cgdb !=nil {return nil ,nil ,_d .Wrap (_cgdb ,_dacda ,"w\u006f\u0072\u0064\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0020\u002d \u0078\u0072\u0065s\u003c=\u0034\u0030\u0030");
};default:_dbe =4;_gaed ,_cgdb =_gbb (_cgef ,1,1,0,0);if _cgdb !=nil {return nil ,nil ,_d .Wrap (_cgdb ,_dacda ,"\u0077\u006f\u0072\u0064 \u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073 \u002d \u0078\u0072\u0065\u0073\u0020\u003e\u00204\u0030\u0030");
};};_bebc ,_ ,_dbb :=_dadc (_gaed );if _dbb !=nil {return nil ,nil ,_d .Wrap (_dbb ,_dacda ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");};_fdbb ,_dbb :=_cced (_bebc ,_dbe );if _dbb !=nil {return nil ,nil ,_d .Wrap (_dbb ,_dacda ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");
};_gcdgc :=&Bitmaps {};if _ecgc ,_dbb =_fdbb .ConnComponents (_gcdgc ,4);_dbb !=nil {return nil ,nil ,_d .Wrap (_dbb ,_dacda ,"\u0077\u006f\u0072\u0064\u0020\u0070r\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u002c\u0020\u0063\u006f\u006en\u0065\u0063\u0074\u0020\u0065\u0078\u0070a\u006e\u0064\u0065\u0064");
};if _afgf ,_dbb =_gcdgc .ClipToBitmap (_cgef );_dbb !=nil {return nil ,nil ,_d .Wrap (_dbb ,_dacda ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");};};_afgf ,_cgdb =_afgf .SelectBySize (maxWidth ,maxHeight ,LocSelectIfBoth ,SizeSelectIfLTE );
if _cgdb !=nil {return nil ,nil ,_d .Wrap (_cgdb ,_dacda ,"");};_ecgc ,_cgdb =_ecgc .SelectBySize (maxWidth ,maxHeight ,LocSelectIfBoth ,SizeSelectIfLTE );if _cgdb !=nil {return nil ,nil ,_d .Wrap (_cgdb ,_dacda ,"");};return _afgf ,_ecgc ,nil ;};func (_gegc *Bitmap )ClipRectangle (box *_ce .Rectangle )(_aag *Bitmap ,_ccg *_ce .Rectangle ,_ggg error ){const _bbg ="\u0043\u006c\u0069\u0070\u0052\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065";
if box ==nil {return nil ,nil ,_d .Error (_bbg ,"\u0062o\u0078 \u0069\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");};_bage ,_caea :=_gegc .Width ,_gegc .Height ;_beefc :=_ce .Rect (0,0,_bage ,_caea );if !box .Overlaps (_beefc ){return nil ,nil ,_d .Error (_bbg ,"b\u006f\u0078\u0020\u0064oe\u0073n\u0027\u0074\u0020\u006f\u0076e\u0072\u006c\u0061\u0070\u0020\u0062");
};_cbac :=box .Intersect (_beefc );_ege ,_ebda :=_cbac .Min .X ,_cbac .Min .Y ;_baa ,_ada :=_cbac .Dx (),_cbac .Dy ();_aag =New (_baa ,_ada );_aag .Text =_gegc .Text ;if _ggg =_aag .RasterOperation (0,0,_baa ,_ada ,PixSrc ,_gegc ,_ege ,_ebda );_ggg !=nil {return nil ,nil ,_d .Wrap (_ggg ,_bbg ,"\u0050\u0069\u0078\u0053\u0072\u0063\u0020\u0074\u006f\u0020\u0063\u006ci\u0070\u0070\u0065\u0064");
};_ccg =&_cbac ;return _aag ,_ccg ,nil ;};type SizeComparison int ;func (_ebcab *Bitmaps )HeightSorter ()func (_eece ,_baagda int )bool {return func (_ddfeb ,_ecgb int )bool {_bcga :=_ebcab .Values [_ddfeb ].Height < _ebcab .Values [_ecgb ].Height ;_bg .Log .Debug ("H\u0065i\u0067\u0068\u0074\u003a\u0020\u0025\u0076\u0020<\u0020\u0025\u0076\u0020= \u0025\u0076",_ebcab .Values [_ddfeb ].Height ,_ebcab .Values [_ecgb ].Height ,_bcga );
return _bcga ;};};const (_ LocationFilter =iota ;LocSelectWidth ;LocSelectHeight ;LocSelectXVal ;LocSelectYVal ;LocSelectIfEither ;LocSelectIfBoth ;);func TstWriteSymbols (t *_ed .T ,bms *Bitmaps ,src *Bitmap ){for _egaag :=0;_egaag < bms .Size ();_egaag ++{_efcd :=bms .Values [_egaag ];
_daaa :=bms .Boxes [_egaag ];_cgcd :=src .RasterOperation (_daaa .Min .X ,_daaa .Min .Y ,_efcd .Width ,_efcd .Height ,PixSrc ,_efcd ,0,0);_c .NoError (t ,_cgcd );};};func (_bfeb *Points )AddPoint (x ,y float32 ){*_bfeb =append (*_bfeb ,Point {x ,y })};
func (_ebdg *Bitmaps )selectByIndexes (_bfgfb []int )(*Bitmaps ,error ){_gcgdf :=&Bitmaps {};for _ ,_aaaa :=range _bfgfb {_cecc ,_dfaf :=_ebdg .GetBitmap (_aaaa );if _dfaf !=nil {return nil ,_d .Wrap (_dfaf ,"\u0073e\u006ce\u0063\u0074\u0042\u0079\u0049\u006e\u0064\u0065\u0078\u0065\u0073","");
};_gcgdf .AddBitmap (_cecc );};return _gcgdf ,nil ;};func TstNSymbol (t *_ed .T ,scale ...int )*Bitmap {_ebgb ,_afcg :=NewWithData (4,5,[]byte {0x90,0xD0,0xB0,0x90,0x90});_c .NoError (t ,_afcg );return TstGetScaledSymbol (t ,_ebgb ,scale ...);};func (_abbc Points )YSorter ()func (_gfa ,_fffe int )bool {return func (_aeab ,_abf int )bool {return _abbc [_aeab ].Y < _abbc [_abf ].Y };
};func (_cbcf *Bitmap )setPadBits (_ebggd int ){_aeb :=8-_cbcf .Width %8;if _aeb ==8{return ;};_aff :=_cbcf .Width /8;_fga :=_ffgb [_aeb ];if _ebggd ==0{_fga ^=_fga ;};var _eede int ;for _ceab :=0;_ceab < _cbcf .Height ;_ceab ++{_eede =_ceab *_cbcf .RowStride +_aff ;
if _ebggd ==0{_cbcf .Data [_eede ]&=_fga ;}else {_cbcf .Data [_eede ]|=_fga ;};};};type Point struct{X ,Y float32 ;};func _fc (_fgg *Bitmap ,_ecg ,_cgg int )(*Bitmap ,error ){const _cb ="e\u0078\u0070\u0061\u006edB\u0069n\u0061\u0072\u0079\u0052\u0065p\u006c\u0069\u0063\u0061\u0074\u0065";
if _fgg ==nil {return nil ,_d .Error (_cb ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");};if _ecg <=0||_cgg <=0{return nil ,_d .Error (_cb ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0063\u0061l\u0065\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020<\u003d\u0020\u0030");
};if _ecg ==_cgg {if _ecg ==1{_be ,_bbcg :=_daed (nil ,_fgg );if _bbcg !=nil {return nil ,_d .Wrap (_bbcg ,_cb ,"\u0078\u0046\u0061\u0063\u0074\u0020\u003d\u003d\u0020y\u0046\u0061\u0063\u0074");};return _be ,nil ;};if _ecg ==2||_ecg ==4||_ecg ==8{_eda ,_bef :=_ea (_fgg ,_ecg );
if _bef !=nil {return nil ,_d .Wrap (_bef ,_cb ,"\u0078\u0046a\u0063\u0074\u0020i\u006e\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d");};return _eda ,nil ;};};_bgba :=_ecg *_fgg .Width ;_fbg :=_cgg *_fgg .Height ;_af :=New (_bgba ,_fbg );_ffc :=_af .RowStride ;
var (_dca ,_gc ,_bbb ,_gac ,_eaf int ;_gb byte ;_ge error ;);for _gc =0;_gc < _fgg .Height ;_gc ++{_dca =_cgg *_gc *_ffc ;for _bbb =0;_bbb < _fgg .Width ;_bbb ++{if _cba :=_fgg .GetPixel (_bbb ,_gc );_cba {_eaf =_ecg *_bbb ;for _gac =0;_gac < _ecg ;_gac ++{_af .setBit (_dca *8+_eaf +_gac );
};};};for _gac =1;_gac < _cgg ;_gac ++{_ced :=_dca +_gac *_ffc ;for _cde :=0;_cde < _ffc ;_cde ++{if _gb ,_ge =_af .GetByte (_dca +_cde );_ge !=nil {return nil ,_d .Wrapf (_ge ,_cb ,"\u0072\u0065\u0070\u006cic\u0061\u0074\u0069\u006e\u0067\u0020\u006c\u0069\u006e\u0065\u003a\u0020\u0027\u0025d\u0027",_gac );
};if _ge =_af .SetByte (_ced +_cde ,_gb );_ge !=nil {return nil ,_d .Wrap (_ge ,_cb ,"\u0053\u0065\u0074\u0074in\u0067\u0020\u0062\u0079\u0074\u0065\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};};};};return _af ,nil ;};func MakePixelCentroidTab8 ()[]int {return _fffd ()};
func _gbb (_gcc *Bitmap ,_ad ...int )(_eeb *Bitmap ,_gdc error ){const _ae ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0043\u0061\u0073\u0063\u0061\u0064\u0065";if _gcc ==nil {return nil ,_d .Error (_ae ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};if len (_ad )==0||len (_ad )> 4{return nil ,_d .Error (_ae ,"t\u0068\u0065\u0072\u0065\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0061\u0074\u0020\u006cea\u0073\u0074\u0020\u006fn\u0065\u0020\u0061\u006e\u0064\u0020\u0061\u0074\u0020mo\u0073\u0074 \u0034\u0020\u006c\u0065\u0076\u0065\u006c\u0073");
};if _ad [0]<=0{_bg .Log .Debug ("\u006c\u0065\u0076\u0065\u006c\u0031\u0020\u003c\u003d\u0020\u0030 \u002d\u0020\u006e\u006f\u0020\u0072\u0065\u0064\u0075\u0063t\u0069\u006f\u006e");_eeb ,_gdc =_daed (nil ,_gcc );if _gdc !=nil {return nil ,_d .Wrap (_gdc ,_ae ,"l\u0065\u0076\u0065\u006c\u0031\u0020\u003c\u003d\u0020\u0030");
};return _eeb ,nil ;};_geg :=_cff ();_eeb =_gcc ;for _ebcb ,_cda :=range _ad {if _cda <=0{break ;};_eeb ,_gdc =_abg (_eeb ,_cda ,_geg );if _gdc !=nil {return nil ,_d .Wrapf (_gdc ,_ae ,"\u006c\u0065\u0076\u0065\u006c\u0025\u0064\u0020\u0072\u0065\u0064\u0075c\u0074\u0069\u006f\u006e",_ebcb );
};};return _eeb ,nil ;};func (_faee *Bitmaps )WidthSorter ()func (_cbde ,_beff int )bool {return func (_ggge ,_aabe int )bool {return _faee .Values [_ggge ].Width < _faee .Values [_aabe ].Width };};func Dilate (d *Bitmap ,s *Bitmap ,sel *Selection )(*Bitmap ,error ){return _cdaa (d ,s ,sel )};
func (_abfc *ClassedPoints )SortByY (){_abfc ._ebee =_abfc .ySortFunction ();_ee .Sort (_abfc )};func _cae (_aba ,_ecb *Bitmap ,_gdgg int ,_baf []byte ,_aca int )(_agf error ){const _dff ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0033";
var (_dad ,_caae ,_acd ,_gec ,_fed ,_ffbf ,_cfcf ,_fad int ;_afe ,_dge ,_eaff ,_abbe uint32 ;_ddb ,_ebd byte ;_ddd uint16 ;);_gcce :=make ([]byte ,4);_aecd :=make ([]byte ,4);for _acd =0;_acd < _aba .Height -1;_acd ,_gec =_acd +2,_gec +1{_dad =_acd *_aba .RowStride ;
_caae =_gec *_ecb .RowStride ;for _fed ,_ffbf =0,0;_fed < _aca ;_fed ,_ffbf =_fed +4,_ffbf +1{for _cfcf =0;_cfcf < 4;_cfcf ++{_fad =_dad +_fed +_cfcf ;if _fad <=len (_aba .Data )-1&&_fad < _dad +_aba .RowStride {_gcce [_cfcf ]=_aba .Data [_fad ];}else {_gcce [_cfcf ]=0x00;
};_fad =_dad +_aba .RowStride +_fed +_cfcf ;if _fad <=len (_aba .Data )-1&&_fad < _dad +(2*_aba .RowStride ){_aecd [_cfcf ]=_aba .Data [_fad ];}else {_aecd [_cfcf ]=0x00;};};_afe =_ba .BigEndian .Uint32 (_gcce );_dge =_ba .BigEndian .Uint32 (_aecd );_eaff =_afe &_dge ;
_eaff |=_eaff <<1;_abbe =_afe |_dge ;_abbe &=_abbe <<1;_dge =_eaff &_abbe ;_dge &=0xaaaaaaaa;_afe =_dge |(_dge <<7);_ddb =byte (_afe >>24);_ebd =byte ((_afe >>8)&0xff);_fad =_caae +_ffbf ;if _fad +1==len (_ecb .Data )-1||_fad +1>=_caae +_ecb .RowStride {if _agf =_ecb .SetByte (_fad ,_baf [_ddb ]);
_agf !=nil {return _d .Wrapf (_agf ,_dff ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_fad );};}else {_ddd =(uint16 (_baf [_ddb ])<<8)|uint16 (_baf [_ebd ]);if _agf =_ecb .setTwoBytes (_fad ,_ddd );_agf !=nil {return _d .Wrapf (_agf ,_dff ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_fad );
};_ffbf ++;};};};return nil ;};func TstTSymbol (t *_ed .T ,scale ...int )*Bitmap {_cgbf ,_fbfe :=NewWithData (5,5,[]byte {0xF8,0x20,0x20,0x20,0x20});_c .NoError (t ,_fbfe );return TstGetScaledSymbol (t ,_cgbf ,scale ...);};var _ccge =[5]int {1,2,3,0,4};
func (_gggab *Bitmaps )GetBox (i int )(*_ce .Rectangle ,error ){const _acgg ="\u0047\u0065\u0074\u0042\u006f\u0078";if _gggab ==nil {return nil ,_d .Error (_acgg ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074\u006d\u0061\u0070s\u0027");
};if i > len (_gggab .Boxes )-1{return nil ,_d .Errorf (_acgg ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );};return _gggab .Boxes [i ],nil ;};type ClassedPoints struct{*Points ;
_ec .IntSlice ;_ebee func (_adcc ,_dbcf int )bool ;};func TstImageBitmapData ()[]byte {return _febe .Data };type Bitmap struct{Width ,Height int ;BitmapNumber int ;RowStride int ;Data []byte ;Color Color ;Special int ;Text string ;XResolution ,YResolution int ;
};func _g (_gf ,_ac *Bitmap )(_ff error ){const _da ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0032";_aa :=_ac .RowStride ;_fe :=_gf .RowStride ;var (_eb byte ;_gd uint16 ;_edb ,_acg ,_bgb ,_bc ,_fa int ;
);for _bgb =0;_bgb < _ac .Height ;_bgb ++{_edb =_bgb *_aa ;_acg =2*_bgb *_fe ;for _bc =0;_bc < _aa ;_bc ++{_eb =_ac .Data [_edb +_bc ];_gd =_gbdf [_eb ];_fa =_acg +_bc *2;if _gf .RowStride !=_ac .RowStride *2&&(_bc +1)*2> _gf .RowStride {_ff =_gf .SetByte (_fa ,byte (_gd >>8));
}else {_ff =_gf .setTwoBytes (_fa ,_gd );};if _ff !=nil {return _d .Wrap (_ff ,_da ,"");};};for _bc =0;_bc < _fe ;_bc ++{_fa =_acg +_fe +_bc ;_eb =_gf .Data [_acg +_bc ];if _ff =_gf .SetByte (_fa ,_eb );_ff !=nil {return _d .Wrapf (_ff ,_da ,"c\u006f\u0070\u0079\u0020\u0064\u006fu\u0062\u006c\u0065\u0064\u0020\u006ci\u006e\u0065\u003a\u0020\u0027\u0025\u0064'\u002c\u0020\u0042\u0079\u0074\u0065\u003a\u0020\u0027\u0025d\u0027",_acg +_bc ,_acg +_fe +_bc );
};};};return nil ;};func (_gae *Bitmap )GetUnpaddedData ()([]byte ,error ){_fac :=uint (_gae .Width &0x07);if _fac ==0{return _gae .Data ,nil ;};_adga :=_gae .Width *_gae .Height ;if _adga %8!=0{_adga >>=3;_adga ++;}else {_adga >>=3;};_edbcb :=make ([]byte ,_adga );
_baee :=_a .NewWriterMSB (_edbcb );const _fbf ="\u0047e\u0074U\u006e\u0070\u0061\u0064\u0064\u0065\u0064\u0044\u0061\u0074\u0061";for _agg :=0;_agg < _gae .Height ;_agg ++{for _dccf :=0;_dccf < _gae .RowStride ;_dccf ++{_dag :=_gae .Data [_agg *_gae .RowStride +_dccf ];
if _dccf !=_gae .RowStride -1{_dgce :=_baee .WriteByte (_dag );if _dgce !=nil {return nil ,_d .Wrap (_dgce ,_fbf ,"");};continue ;};for _fba :=uint (0);_fba < _fac ;_fba ++{_fadg :=_baee .WriteBit (int (_dag >>(7-_fba )&0x01));if _fadg !=nil {return nil ,_d .Wrap (_fadg ,_fbf ,"");
};};};};return _edbcb ,nil ;};type Boxes []*_ce .Rectangle ;func _eead (_cfbd ,_abff ,_gbbc byte )byte {return (_cfbd &^(_gbbc ))|(_abff &_gbbc )};func (_cabe *ClassedPoints )GroupByY ()([]*ClassedPoints ,error ){const _cccd ="\u0043\u006c\u0061\u0073se\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0072\u006f\u0075\u0070\u0042y\u0059";
if _efbe :=_cabe .validateIntSlice ();_efbe !=nil {return nil ,_d .Wrap (_efbe ,_cccd ,"");};if _cabe .IntSlice .Size ()==0{return nil ,_d .Error (_cccd ,"\u004e\u006f\u0020\u0063la\u0073\u0073\u0065\u0073\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064");
};_cabe .SortByY ();var (_bfbc []*ClassedPoints ;_cbfd int ;);_fbed :=-1;var _gdff *ClassedPoints ;for _bbeg :=0;_bbeg < len (_cabe .IntSlice );_bbeg ++{_cbfd =int (_cabe .YAtIndex (_bbeg ));if _cbfd !=_fbed {_gdff =&ClassedPoints {Points :_cabe .Points };
_fbed =_cbfd ;_bfbc =append (_bfbc ,_gdff );};_gdff .IntSlice =append (_gdff .IntSlice ,_cabe .IntSlice [_bbeg ]);};for _ ,_ggga :=range _bfbc {_ggga .SortByX ();};return _bfbc ,nil ;};func TstImageBitmap ()*Bitmap {return _febe .Copy ()};func TstImageBitmapInverseData ()[]byte {_gebe :=_febe .Copy ();
_gebe .InverseData ();return _gebe .Data ;};const (PixSrc RasterOperator =0xc;PixDst RasterOperator =0xa;PixNotSrc RasterOperator =0x3;PixNotDst RasterOperator =0x5;PixClr RasterOperator =0x0;PixSet RasterOperator =0xf;PixSrcOrDst RasterOperator =0xe;PixSrcAndDst RasterOperator =0x8;
PixSrcXorDst RasterOperator =0x6;PixNotSrcOrDst RasterOperator =0xb;PixNotSrcAndDst RasterOperator =0x2;PixSrcOrNotDst RasterOperator =0xd;PixSrcAndNotDst RasterOperator =0x4;PixNotPixSrcOrDst RasterOperator =0x1;PixNotPixSrcAndDst RasterOperator =0x7;
PixNotPixSrcXorDst RasterOperator =0x9;PixPaint =PixSrcOrDst ;PixSubtract =PixNotSrcAndDst ;PixMask =PixSrcAndDst ;);func _fefc (_bgbce ,_bfac *Bitmap ,_daeda ,_acb int )(_cbeg error ){const _begb ="\u0073e\u0065d\u0066\u0069\u006c\u006c\u0042i\u006e\u0061r\u0079\u004c\u006f\u0077\u0034";
var (_ccbbb ,_dbgg ,_bbbe ,_acce int ;_fcgg ,_ebfa ,_acdc ,_abdd ,_facf ,_aefea ,_bcfb byte ;);for _ccbbb =0;_ccbbb < _daeda ;_ccbbb ++{_bbbe =_ccbbb *_bgbce .RowStride ;_acce =_ccbbb *_bfac .RowStride ;for _dbgg =0;_dbgg < _acb ;_dbgg ++{_fcgg ,_cbeg =_bgbce .GetByte (_bbbe +_dbgg );
if _cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0066i\u0072\u0073\u0074\u0020\u0067\u0065t");};_ebfa ,_cbeg =_bfac .GetByte (_acce +_dbgg );if _cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0067\u0065\u0074");
};if _ccbbb > 0{_acdc ,_cbeg =_bgbce .GetByte (_bbbe -_bgbce .RowStride +_dbgg );if _cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0069\u0020\u003e \u0030");};_fcgg |=_acdc ;};if _dbgg > 0{_abdd ,_cbeg =_bgbce .GetByte (_bbbe +_dbgg -1);if _cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u006a\u0020\u003e \u0030");
};_fcgg |=_abdd <<7;};_fcgg &=_ebfa ;if _fcgg ==0||(^_fcgg )==0{if _cbeg =_bgbce .SetByte (_bbbe +_dbgg ,_fcgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"b\u0074\u0020\u003d\u003d 0\u0020|\u007c\u0020\u0028\u005e\u0062t\u0029\u0020\u003d\u003d\u0020\u0030");
};continue ;};for {_bcfb =_fcgg ;_fcgg =(_fcgg |(_fcgg >>1)|(_fcgg <<1))&_ebfa ;if (_fcgg ^_bcfb )==0{if _cbeg =_bgbce .SetByte (_bbbe +_dbgg ,_fcgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0070\u0072\u0065\u0076 \u0062\u0079\u0074\u0065");
};break ;};};};};for _ccbbb =_daeda -1;_ccbbb >=0;_ccbbb --{_bbbe =_ccbbb *_bgbce .RowStride ;_acce =_ccbbb *_bfac .RowStride ;for _dbgg =_acb -1;_dbgg >=0;_dbgg --{if _fcgg ,_cbeg =_bgbce .GetByte (_bbbe +_dbgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0072\u0065\u0076\u0065\u0072\u0073\u0065\u0020\u0066\u0069\u0072\u0073t\u0020\u0067\u0065\u0074");
};if _ebfa ,_cbeg =_bfac .GetByte (_acce +_dbgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"r\u0065\u0076\u0065\u0072se\u0020g\u0065\u0074\u0020\u006d\u0061s\u006b\u0020\u0062\u0079\u0074\u0065");};if _ccbbb < _daeda -1{if _facf ,_cbeg =_bgbce .GetByte (_bbbe +_bgbce .RowStride +_dbgg );
_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0072\u0065v\u0065\u0072\u0073e\u0020\u0069\u0020\u003c\u0020\u0068\u0020\u002d\u0031");};_fcgg |=_facf ;};if _dbgg < _acb -1{if _aefea ,_cbeg =_bgbce .GetByte (_bbbe +_dbgg +1);_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0072\u0065\u0076\u0065rs\u0065\u0020\u006a\u0020\u003c\u0020\u0077\u0070\u006c\u0020\u002d\u0020\u0031");
};_fcgg |=_aefea >>7;};_fcgg &=_ebfa ;if _fcgg ==0||(^_fcgg )==0{if _cbeg =_bgbce .SetByte (_bbbe +_dbgg ,_fcgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006d\u0061\u0073k\u0065\u0064\u0020\u0062\u0079\u0074\u0065\u0020\u0066\u0061i\u006c\u0065\u0064");
};continue ;};for {_bcfb =_fcgg ;_fcgg =(_fcgg |(_fcgg >>1)|(_fcgg <<1))&_ebfa ;if (_fcgg ^_bcfb )==0{if _cbeg =_bgbce .SetByte (_bbbe +_dbgg ,_fcgg );_cbeg !=nil {return _d .Wrap (_cbeg ,_begb ,"\u0072e\u0076\u0065\u0072\u0073e\u0020\u0073\u0065\u0074\u0074i\u006eg\u0020p\u0072\u0065\u0076\u0020\u0062\u0079\u0074e");
};break ;};};};};return nil ;};func (_gabe *Bitmaps )String ()string {_eacg :=_f .Builder {};for _ ,_ffege :=range _gabe .Values {_eacg .WriteString (_ffege .String ());_eacg .WriteRune ('\n');};return _eacg .String ();};func TstISymbol (t *_ed .T ,scale ...int )*Bitmap {_cfed ,_aaab :=NewWithData (1,5,[]byte {0x80,0x80,0x80,0x80,0x80});
_c .NoError (t ,_aaab );return TstGetScaledSymbol (t ,_cfed ,scale ...);};func (_baagd *Boxes )makeSizeIndicator (_cbda ,_bfce int ,_gada LocationFilter ,_efdc SizeComparison )*_ec .NumSlice {_dce :=&_ec .NumSlice {};var _cbae ,_eef ,_faac int ;for _ ,_aegf :=range *_baagd {_cbae =0;
_eef ,_faac =_aegf .Dx (),_aegf .Dy ();switch _gada {case LocSelectWidth :if (_efdc ==SizeSelectIfLT &&_eef < _cbda )||(_efdc ==SizeSelectIfGT &&_eef > _cbda )||(_efdc ==SizeSelectIfLTE &&_eef <=_cbda )||(_efdc ==SizeSelectIfGTE &&_eef >=_cbda ){_cbae =1;
};case LocSelectHeight :if (_efdc ==SizeSelectIfLT &&_faac < _bfce )||(_efdc ==SizeSelectIfGT &&_faac > _bfce )||(_efdc ==SizeSelectIfLTE &&_faac <=_bfce )||(_efdc ==SizeSelectIfGTE &&_faac >=_bfce ){_cbae =1;};case LocSelectIfEither :if (_efdc ==SizeSelectIfLT &&(_faac < _bfce ||_eef < _cbda ))||(_efdc ==SizeSelectIfGT &&(_faac > _bfce ||_eef > _cbda ))||(_efdc ==SizeSelectIfLTE &&(_faac <=_bfce ||_eef <=_cbda ))||(_efdc ==SizeSelectIfGTE &&(_faac >=_bfce ||_eef >=_cbda )){_cbae =1;
};case LocSelectIfBoth :if (_efdc ==SizeSelectIfLT &&(_faac < _bfce &&_eef < _cbda ))||(_efdc ==SizeSelectIfGT &&(_faac > _bfce &&_eef > _cbda ))||(_efdc ==SizeSelectIfLTE &&(_faac <=_bfce &&_eef <=_cbda ))||(_efdc ==SizeSelectIfGTE &&(_faac >=_bfce &&_eef >=_cbda )){_cbae =1;
};};_dce .AddInt (_cbae );};return _dce ;};func (_eab *Bitmap )addBorderGeneral (_aac ,_adge ,_eafd ,_dea int ,_ccaf int )(*Bitmap ,error ){const _eabf ="\u0061\u0064d\u0042\u006f\u0072d\u0065\u0072\u0047\u0065\u006e\u0065\u0072\u0061\u006c";if _aac < 0||_adge < 0||_eafd < 0||_dea < 0{return nil ,_d .Error (_eabf ,"n\u0065\u0067\u0061\u0074iv\u0065 \u0062\u006f\u0072\u0064\u0065r\u0020\u0061\u0064\u0064\u0065\u0064");
};_gdce ,_efa :=_eab .Width ,_eab .Height ;_afb :=_gdce +_aac +_adge ;_ecba :=_efa +_eafd +_dea ;_efbb :=New (_afb ,_ecba );_efbb .Color =_eab .Color ;_gccd :=PixClr ;if _ccaf > 0{_gccd =PixSet ;};_cafd :=_efbb .RasterOperation (0,0,_aac ,_ecba ,_gccd ,nil ,0,0);
if _cafd !=nil {return nil ,_d .Wrap (_cafd ,_eabf ,"\u006c\u0065\u0066\u0074");};_cafd =_efbb .RasterOperation (_afb -_adge ,0,_adge ,_ecba ,_gccd ,nil ,0,0);if _cafd !=nil {return nil ,_d .Wrap (_cafd ,_eabf ,"\u0072\u0069\u0067h\u0074");};_cafd =_efbb .RasterOperation (0,0,_afb ,_eafd ,_gccd ,nil ,0,0);
if _cafd !=nil {return nil ,_d .Wrap (_cafd ,_eabf ,"\u0074\u006f\u0070");};_cafd =_efbb .RasterOperation (0,_ecba -_dea ,_afb ,_dea ,_gccd ,nil ,0,0);if _cafd !=nil {return nil ,_d .Wrap (_cafd ,_eabf ,"\u0062\u006f\u0074\u0074\u006f\u006d");};_cafd =_efbb .RasterOperation (_aac ,_eafd ,_gdce ,_efa ,PixSrc ,_eab ,0,0);
if _cafd !=nil {return nil ,_d .Wrap (_cafd ,_eabf ,"\u0063\u006f\u0070\u0079");};return _efbb ,nil ;};const (_ SizeSelection =iota ;SizeSelectByWidth ;SizeSelectByHeight ;SizeSelectByMaxDimension ;SizeSelectByArea ;SizeSelectByPerimeter ;);func _cca (_cgb ,_caac int )*Bitmap {return &Bitmap {Width :_cgb ,Height :_caac ,RowStride :(_cgb +7)>>3};
};func TstWSymbol (t *_ed .T ,scale ...int )*Bitmap {_cfaf ,_gfaag :=NewWithData (5,5,[]byte {0x88,0x88,0xA8,0xD8,0x88});_c .NoError (t ,_gfaag );return TstGetScaledSymbol (t ,_cfaf ,scale ...);};func (_ddfc *ClassedPoints )Len ()int {return _ddfc .IntSlice .Size ()};
func (_bfc *Bitmap )setEightPartlyBytes (_fagc ,_fbgf int ,_aae uint64 )(_gfe error ){var (_fcee byte ;_decc int ;);const _bacc ="\u0073\u0065\u0074\u0045ig\u0068\u0074\u0050\u0061\u0072\u0074\u006c\u0079\u0042\u0079\u0074\u0065\u0073";for _ceb :=1;_ceb <=_fbgf ;
_ceb ++{_decc =64-_ceb *8;_fcee =byte (_aae >>uint (_decc )&0xff);_bg .Log .Trace ("\u0074\u0065\u006d\u0070\u003a\u0020\u0025\u0030\u0038\u0062\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a %\u0064,\u0020\u0069\u0064\u0078\u003a\u0020\u0025\u0064\u002c\u0020\u0066\u0075l\u006c\u0042\u0079\u0074\u0065\u0073\u004e\u0075\u006d\u0062\u0065\u0072\u003a\u0020\u0025\u0064\u002c \u0073\u0068\u0069\u0066\u0074\u003a\u0020\u0025\u0064",_fcee ,_fagc ,_fagc +_ceb -1,_fbgf ,_decc );
if _gfe =_bfc .SetByte (_fagc +_ceb -1,_fcee );_gfe !=nil {return _d .Wrap (_gfe ,_bacc ,"\u0066\u0075\u006c\u006c\u0042\u0079\u0074\u0065");};};_fdcgb :=_bfc .RowStride *8-_bfc .Width ;if _fdcgb ==0{return nil ;};_decc -=8;_fcee =byte (_aae >>uint (_decc )&0xff)<<uint (_fdcgb );
if _gfe =_bfc .SetByte (_fagc +_fbgf ,_fcee );_gfe !=nil {return _d .Wrap (_gfe ,_bacc ,"\u0070\u0061\u0064\u0064\u0065\u0064");};return nil ;};func (_bab *Bitmap )Copy ()*Bitmap {_eed :=make ([]byte ,len (_bab .Data ));copy (_eed ,_bab .Data );return &Bitmap {Width :_bab .Width ,Height :_bab .Height ,RowStride :_bab .RowStride ,Data :_eed ,Color :_bab .Color ,Text :_bab .Text ,BitmapNumber :_bab .BitmapNumber ,Special :_bab .Special };
};func (_ffcg *Boxes )Get (i int )(*_ce .Rectangle ,error ){const _gaba ="\u0042o\u0078\u0065\u0073\u002e\u0047\u0065t";if _ffcg ==nil {return nil ,_d .Error (_gaba ,"\u0027\u0042\u006f\u0078es\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if i > len (*_ffcg )-1{return nil ,_d .Errorf (_gaba ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );};return (*_ffcg )[i ],nil ;};func SelCreateBrick (h ,w int ,cy ,cx int ,tp SelectionValue )*Selection {_bfbe :=_accac (h ,w ,"");
_bfbe .setOrigin (cy ,cx );var _ecaf ,_gbaf int ;for _ecaf =0;_ecaf < h ;_ecaf ++{for _gbaf =0;_gbaf < w ;_gbaf ++{_bfbe .Data [_ecaf ][_gbaf ]=tp ;};};return _bfbe ;};const (_gbgc shift =iota ;_dada ;);const (ComponentConn Component =iota ;ComponentCharacters ;
ComponentWords ;);func (_ddabe *Bitmaps )CountPixels ()*_ec .NumSlice {_caed :=&_ec .NumSlice {};for _ ,_bdaf :=range _ddabe .Values {_caed .AddInt (_bdaf .CountPixels ());};return _caed ;};func init (){const _eagee ="\u0062\u0069\u0074\u006dap\u0073\u002e\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0069\u007a\u0061\u0074\u0069o\u006e";
_bbcbd =New (50,40);var _bgcc error ;_bbcbd ,_bgcc =_bbcbd .AddBorder (2,1);if _bgcc !=nil {panic (_d .Wrap (_bgcc ,_eagee ,"f\u0072\u0061\u006d\u0065\u0042\u0069\u0074\u006d\u0061\u0070"));};_febe ,_bgcc =NewWithData (50,22,_cfce );if _bgcc !=nil {panic (_d .Wrap (_bgcc ,_eagee ,"i\u006d\u0061\u0067\u0065\u0042\u0069\u0074\u006d\u0061\u0070"));
};};func (_fadd *Bitmap )GetByte (index int )(byte ,error ){if index > len (_fadd .Data )-1||index < 0{return 0,_d .Errorf ("\u0047e\u0074\u0042\u0079\u0074\u0065","\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",index );
};return _fadd .Data [index ],nil ;};func _daed (_gedg ,_fggg *Bitmap )(*Bitmap ,error ){if _fggg ==nil {return nil ,_d .Error ("\u0063\u006f\u0070\u0079\u0042\u0069\u0074\u006d\u0061\u0070","\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _fggg ==_gedg {return _gedg ,nil ;};if _gedg ==nil {_gedg =_fggg .createTemplate ();copy (_gedg .Data ,_fggg .Data );return _gedg ,nil ;};_dbgf :=_gedg .resizeImageData (_fggg );if _dbgf !=nil {return nil ,_d .Wrap (_dbgf ,"\u0063\u006f\u0070\u0079\u0042\u0069\u0074\u006d\u0061\u0070","");
};_gedg .Text =_fggg .Text ;copy (_gedg .Data ,_fggg .Data );return _gedg ,nil ;};func (_acfc *Bitmap )SetPadBits (value int ){_acfc .setPadBits (value )};type Points []Point ;func _abd (_bceg ,_fgd *Bitmap )(_ffb error ){const _ffe ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0038";
_dc :=_fgd .RowStride ;_fec :=_bceg .RowStride ;var _acf ,_cf ,_ga ,_cg ,_abb int ;for _ga =0;_ga < _fgd .Height ;_ga ++{_acf =_ga *_dc ;_cf =8*_ga *_fec ;for _cg =0;_cg < _dc ;_cg ++{if _ffb =_bceg .setEightBytes (_cf +_cg *8,_gbbe [_fgd .Data [_acf +_cg ]]);
_ffb !=nil {return _d .Wrap (_ffb ,_ffe ,"");};};for _abb =1;_abb < 8;_abb ++{for _cg =0;_cg < _fec ;_cg ++{if _ffb =_bceg .SetByte (_cf +_abb *_fec +_cg ,_bceg .Data [_cf +_cg ]);_ffb !=nil {return _d .Wrap (_ffb ,_ffe ,"");};};};};return nil ;};func (_cgf *Bitmap )RemoveBorderGeneral (left ,right ,top ,bot int )(*Bitmap ,error ){return _cgf .removeBorderGeneral (left ,right ,top ,bot );
};func TstVSymbol (t *_ed .T ,scale ...int )*Bitmap {_cfbb ,_ebea :=NewWithData (5,5,[]byte {0x88,0x88,0x88,0x50,0x20});_c .NoError (t ,_ebea );return TstGetScaledSymbol (t ,_cfbb ,scale ...);};func _ddge (_fgffc ,_dcdae *Bitmap ,_dfaa ,_abddf int )(_agfe error ){const _agdec ="\u0073e\u0065d\u0066\u0069\u006c\u006c\u0042i\u006e\u0061r\u0079\u004c\u006f\u0077\u0038";
var (_eedfa ,_fdae ,_ddab ,_cgbcg int ;_gfgge ,_agbd ,_bgfa ,_defda ,_bggdd ,_ffbbc ,_ebaf ,_cffag byte ;);for _eedfa =0;_eedfa < _dfaa ;_eedfa ++{_ddab =_eedfa *_fgffc .RowStride ;_cgbcg =_eedfa *_dcdae .RowStride ;for _fdae =0;_fdae < _abddf ;_fdae ++{if _gfgge ,_agfe =_fgffc .GetByte (_ddab +_fdae );
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0067e\u0074 \u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");};if _agbd ,_agfe =_dcdae .GetByte (_cgbcg +_fdae );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0067\u0065\u0074\u0020\u006d\u0061\u0073\u006b\u0020\u0062\u0079\u0074\u0065");
};if _eedfa > 0{if _bgfa ,_agfe =_fgffc .GetByte (_ddab -_fgffc .RowStride +_fdae );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0069\u0020\u003e\u0020\u0030\u0020\u0062\u0079\u0074\u0065");};_gfgge |=_bgfa |(_bgfa <<1)|(_bgfa >>1);if _fdae > 0{if _cffag ,_agfe =_fgffc .GetByte (_ddab -_fgffc .RowStride +_fdae -1);
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0069\u0020\u003e\u00200 \u0026\u0026\u0020\u006a\u0020\u003e\u0020\u0030\u0020\u0062\u0079\u0074\u0065");};_gfgge |=_cffag <<7;};if _fdae < _abddf -1{if _cffag ,_agfe =_fgffc .GetByte (_ddab -_fgffc .RowStride +_fdae +1);
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u006a\u0020<\u0020\u0077\u0070l\u0020\u002d\u0020\u0031\u0020\u0062\u0079\u0074\u0065");};_gfgge |=_cffag >>7;};};if _fdae > 0{if _defda ,_agfe =_fgffc .GetByte (_ddab +_fdae -1);_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u006a\u0020\u003e \u0030");
};_gfgge |=_defda <<7;};_gfgge &=_agbd ;if _gfgge ==0||^_gfgge ==0{if _agfe =_fgffc .SetByte (_ddab +_fdae ,_gfgge );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0073e\u0074t\u0069\u006e\u0067\u0020\u0065m\u0070\u0074y\u0020\u0062\u0079\u0074\u0065");
};};for {_ebaf =_gfgge ;_gfgge =(_gfgge |(_gfgge >>1)|(_gfgge <<1))&_agbd ;if (_gfgge ^_ebaf )==0{if _agfe =_fgffc .SetByte (_ddab +_fdae ,_gfgge );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0070\u0072\u0065\u0076 \u0062\u0079\u0074\u0065");
};break ;};};};};for _eedfa =_dfaa -1;_eedfa >=0;_eedfa --{_ddab =_eedfa *_fgffc .RowStride ;_cgbcg =_eedfa *_dcdae .RowStride ;for _fdae =_abddf -1;_fdae >=0;_fdae --{if _gfgge ,_agfe =_fgffc .GetByte (_ddab +_fdae );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0072\u0065\u0076er\u0073\u0065\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");
};if _agbd ,_agfe =_dcdae .GetByte (_cgbcg +_fdae );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"r\u0065\u0076\u0065\u0072se\u0020g\u0065\u0074\u0020\u006d\u0061s\u006b\u0020\u0062\u0079\u0074\u0065");};if _eedfa < _dfaa -1{if _bggdd ,_agfe =_fgffc .GetByte (_ddab +_fgffc .RowStride +_fdae );
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0069\u0020\u003c\u0020h\u0020\u002d\u0020\u0031\u0020\u002d\u003e\u0020\u0067\u0065t\u0020s\u006f\u0075\u0072\u0063\u0065\u0020\u0062y\u0074\u0065");};_gfgge |=_bggdd |(_bggdd <<1)|_bggdd >>1;if _fdae > 0{if _cffag ,_agfe =_fgffc .GetByte (_ddab +_fgffc .RowStride +_fdae -1);
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0069\u0020\u003c h\u002d\u0031\u0020\u0026\u0020\u006a\u0020\u003e\u00200\u0020-\u003e \u0067e\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");};_gfgge |=_cffag <<7;};if _fdae < _abddf -1{if _cffag ,_agfe =_fgffc .GetByte (_ddab +_fgffc .RowStride +_fdae +1);
_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0069\u0020\u003c\u0020\u0068\u002d\u0031\u0020\u0026\u0026\u0020\u006a\u0020\u003c\u0077\u0070\u006c\u002d\u0031\u0020\u002d\u003e\u0020\u0067e\u0074\u0020\u0073\u006f\u0075r\u0063\u0065 \u0062\u0079\u0074\u0065");
};_gfgge |=_cffag >>7;};};if _fdae < _abddf -1{if _ffbbc ,_agfe =_fgffc .GetByte (_ddab +_fdae +1);_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u006a\u0020<\u0020\u0077\u0070\u006c\u0020\u002d\u0031\u0020\u002d\u003e\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020by\u0074\u0065");
};_gfgge |=_ffbbc >>7;};_gfgge &=_agbd ;if _gfgge ==0||(^_gfgge )==0{if _agfe =_fgffc .SetByte (_ddab +_fdae ,_gfgge );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"\u0073e\u0074 \u006d\u0061\u0073\u006b\u0065\u0064\u0020\u0062\u0079\u0074\u0065");};};
for {_ebaf =_gfgge ;_gfgge =(_gfgge |(_gfgge >>1)|(_gfgge <<1))&_agbd ;if (_gfgge ^_ebaf )==0{if _agfe =_fgffc .SetByte (_ddab +_fdae ,_gfgge );_agfe !=nil {return _d .Wrap (_agfe ,_agdec ,"r\u0065\u0076\u0065\u0072se\u0020s\u0065\u0074\u0020\u0070\u0072e\u0076\u0020\u0062\u0079\u0074\u0065");
};break ;};};};};return nil ;};func _aagb (_cafg ,_adc *Bitmap ,_cbgf ,_gaae int )(*Bitmap ,error ){const _dfcb ="\u0063\u006c\u006f\u0073\u0065\u0042\u0072\u0069\u0063\u006b";if _adc ==nil {return nil ,_d .Error (_dfcb ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _cbgf < 1||_gaae < 1{return nil ,_d .Error (_dfcb ,"\u0068S\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0053\u0069z\u0065\u0020\u006e\u006f\u0074\u0020\u003e\u003d\u0020\u0031");};if _cbgf ==1&&_gaae ==1{return _adc .Copy (),nil ;};if _cbgf ==1||_gaae ==1{_ebag :=SelCreateBrick (_gaae ,_cbgf ,_gaae /2,_cbgf /2,SelHit );
var _bfad error ;_cafg ,_bfad =_cddg (_cafg ,_adc ,_ebag );if _bfad !=nil {return nil ,_d .Wrap (_bfad ,_dfcb ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");};return _cafg ,nil ;
};_bfd :=SelCreateBrick (1,_cbgf ,0,_cbgf /2,SelHit );_dcb :=SelCreateBrick (_gaae ,1,_gaae /2,0,SelHit );_gccg ,_eaea :=_cdaa (nil ,_adc ,_bfd );if _eaea !=nil {return nil ,_d .Wrap (_eaea ,_dfcb ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};if _cafg ,_eaea =_cdaa (_cafg ,_gccg ,_dcb );_eaea !=nil {return nil ,_d .Wrap (_eaea ,_dfcb ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};if _ ,_eaea =_ecc (_gccg ,_cafg ,_bfd );_eaea !=nil {return nil ,_d .Wrap (_eaea ,_dfcb ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};if _ ,_eaea =_ecc (_cafg ,_gccg ,_dcb );_eaea !=nil {return nil ,_d .Wrap (_eaea ,_dfcb ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};return _cafg ,nil ;};type CombinationOperator int ;func (_bdgg *ClassedPoints )GetIntXByClass (i int )(int ,error ){const _dcgd ="\u0043\u006c\u0061\u0073s\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047e\u0074I\u006e\u0074\u0059\u0042\u0079\u0043\u006ca\u0073\u0073";
if i >=_bdgg .IntSlice .Size (){return 0,_d .Errorf (_dcgd ,"\u0069\u003a\u0020\u0027\u0025\u0064\u0027 \u0069\u0073\u0020o\u0075\u0074\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065",i );
};return int (_bdgg .XAtIndex (i )),nil ;};func (_ffbb *Bitmap )ThresholdPixelSum (thresh int ,tab8 []int )(_fea bool ,_fda error ){const _adag ="\u0042i\u0074\u006d\u0061\u0070\u002e\u0054\u0068\u0072\u0065\u0073\u0068o\u006c\u0064\u0050\u0069\u0078\u0065\u006c\u0053\u0075\u006d";
if tab8 ==nil {tab8 =_dcgad ();};_beefcd :=_ffbb .Width >>3;_daeb :=_ffbb .Width &7;_fae :=byte (0xff<<uint (8-_daeb ));var (_baag ,_gga ,_ead ,_accd int ;_ceea byte ;);for _baag =0;_baag < _ffbb .Height ;_baag ++{_ead =_ffbb .RowStride *_baag ;for _gga =0;
_gga < _beefcd ;_gga ++{_ceea ,_fda =_ffbb .GetByte (_ead +_gga );if _fda !=nil {return false ,_d .Wrap (_fda ,_adag ,"\u0066\u0075\u006c\u006c\u0042\u0079\u0074\u0065");};_accd +=tab8 [_ceea ];};if _daeb !=0{_ceea ,_fda =_ffbb .GetByte (_ead +_gga );if _fda !=nil {return false ,_d .Wrap (_fda ,_adag ,"p\u0061\u0072\u0074\u0069\u0061\u006c\u0042\u0079\u0074\u0065");
};_ceea &=_fae ;_accd +=tab8 [_ceea ];};if _accd > thresh {return true ,nil ;};};return _fea ,nil ;};var _cfce =[]byte {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x78,0x27,0xC2,0x27,0x91,0x00,0x22,0x48,0x21,0x03,0x24,0x91,0x00,0x22,0x48,0x21,0x02,0xA4,0x95,0x00,0x22,0x48,0x21,0x02,0x64,0x9B,0x00,0x3C,0x78,0x21,0x02,0x27,0x91,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x15,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};
func _bce (_bb ,_gfc *Bitmap )(_cee error ){const _eec ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0034";_fb :=_gfc .RowStride ;_fbc :=_bb .RowStride ;_fd :=_gfc .RowStride *4-_bb .RowStride ;var (_cec ,_edc byte ;
_fg uint32 ;_acc ,_eg ,_ab ,_ca ,_cc ,_ag ,_faf int ;);for _ab =0;_ab < _gfc .Height ;_ab ++{_acc =_ab *_fb ;_eg =4*_ab *_fbc ;for _ca =0;_ca < _fb ;_ca ++{_cec =_gfc .Data [_acc +_ca ];_fg =_fcea [_cec ];_ag =_eg +_ca *4;if _fd !=0&&(_ca +1)*4> _bb .RowStride {for _cc =_fd ;
_cc > 0;_cc --{_edc =byte ((_fg >>uint (_cc *8))&0xff);_faf =_ag +(_fd -_cc );if _cee =_bb .SetByte (_faf ,_edc );_cee !=nil {return _d .Wrapf (_cee ,_eec ,"D\u0069\u0066\u0066\u0065\u0072\u0065n\u0074\u0020\u0072\u006f\u0077\u0073\u0074\u0072\u0069d\u0065\u0073\u002e \u004b:\u0020\u0025\u0064",_cc );
};};}else if _cee =_bb .setFourBytes (_ag ,_fg );_cee !=nil {return _d .Wrap (_cee ,_eec ,"");};if _cee =_bb .setFourBytes (_eg +_ca *4,_fcea [_gfc .Data [_acc +_ca ]]);_cee !=nil {return _d .Wrap (_cee ,_eec ,"");};};for _cc =1;_cc < 4;_cc ++{for _ca =0;
_ca < _fbc ;_ca ++{if _cee =_bb .SetByte (_eg +_cc *_fbc +_ca ,_bb .Data [_eg +_ca ]);_cee !=nil {return _d .Wrapf (_cee ,_eec ,"\u0063\u006f\u0070\u0079\u0020\u0027\u0071\u0075\u0061\u0064\u0072\u0061\u0062l\u0065\u0027\u0020\u006c\u0069\u006ee\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0062\u0079\u0074\u0065\u003a \u0027\u0025\u0064\u0027",_cc ,_ca );
};};};};return nil ;};func (_fbec *ClassedPoints )YAtIndex (i int )float32 {return (*_fbec .Points )[_fbec .IntSlice [i ]].Y };func TstPSymbol (t *_ed .T )*Bitmap {t .Helper ();_cdgd :=New (5,8);_c .NoError (t ,_cdgd .SetPixel (0,0,1));_c .NoError (t ,_cdgd .SetPixel (1,0,1));
_c .NoError (t ,_cdgd .SetPixel (2,0,1));_c .NoError (t ,_cdgd .SetPixel (3,0,1));_c .NoError (t ,_cdgd .SetPixel (4,1,1));_c .NoError (t ,_cdgd .SetPixel (0,1,1));_c .NoError (t ,_cdgd .SetPixel (4,2,1));_c .NoError (t ,_cdgd .SetPixel (0,2,1));_c .NoError (t ,_cdgd .SetPixel (4,3,1));
_c .NoError (t ,_cdgd .SetPixel (0,3,1));_c .NoError (t ,_cdgd .SetPixel (0,4,1));_c .NoError (t ,_cdgd .SetPixel (1,4,1));_c .NoError (t ,_cdgd .SetPixel (2,4,1));_c .NoError (t ,_cdgd .SetPixel (3,4,1));_c .NoError (t ,_cdgd .SetPixel (0,5,1));_c .NoError (t ,_cdgd .SetPixel (0,6,1));
_c .NoError (t ,_cdgd .SetPixel (0,7,1));return _cdgd ;};func (_dffcc *Selection )setOrigin (_cbdc ,_gabf int ){_dffcc .Cy ,_dffcc .Cx =_cbdc ,_gabf };func NewWithUnpaddedData (width ,height int ,data []byte )(*Bitmap ,error ){const _egfa ="\u004e\u0065\u0077\u0057it\u0068\u0055\u006e\u0070\u0061\u0064\u0064\u0065\u0064\u0044\u0061\u0074\u0061";
_fdb :=_cca (width ,height );_fdb .Data =data ;if _fdbf :=((width *height )+7)>>3;len (data )< _fdbf {return nil ,_d .Errorf (_egfa ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064a\u0074\u0061\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002e\u0020\u0054\u0068\u0065\u0020\u0064\u0061t\u0061\u0020s\u0068\u006fu\u006c\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0074 l\u0065\u0061\u0073\u0074\u003a\u0020\u0027\u0025\u0064'\u0020\u0062\u0079\u0074\u0065\u0073",len (data ),_fdbf );
};if _fbe :=_fdb .addPadBits ();_fbe !=nil {return nil ,_d .Wrap (_fbe ,_egfa ,"");};return _fdb ,nil ;};func _agcgg (_dgbfb *_ec .Stack ,_bega ,_aaae ,_deeed ,_egcg ,_dgge int ,_ecbg *_ce .Rectangle )(_gcefa error ){const _ggca ="\u0070\u0075\u0073\u0068\u0046\u0069\u006c\u006c\u0053\u0065\u0067m\u0065\u006e\u0074\u0042\u006f\u0075\u006e\u0064\u0069\u006eg\u0042\u006f\u0078";
if _dgbfb ==nil {return _d .Error (_ggca ,"\u006ei\u006c \u0073\u0074\u0061\u0063\u006b \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");};if _ecbg ==nil {return _d .Error (_ggca ,"\u0070\u0072\u006f\u0076i\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0069\u006da\u0067e\u002e\u0052\u0065\u0063\u0074\u0061\u006eg\u006c\u0065");
};_ecbg .Min .X =_ec .Min (_ecbg .Min .X ,_bega );_ecbg .Max .X =_ec .Max (_ecbg .Max .X ,_aaae );_ecbg .Min .Y =_ec .Min (_ecbg .Min .Y ,_deeed );_ecbg .Max .Y =_ec .Max (_ecbg .Max .Y ,_deeed );if !(_deeed +_egcg >=0&&_deeed +_egcg <=_dgge ){return nil ;
};if _dgbfb .Aux ==nil {return _d .Error (_ggca ,"a\u0075x\u0053\u0074\u0061\u0063\u006b\u0020\u006e\u006ft\u0020\u0064\u0065\u0066in\u0065\u0064");};var _dfddf *fillSegment ;_egbb ,_fegd :=_dgbfb .Aux .Pop ();if _fegd {if _dfddf ,_fegd =_egbb .(*fillSegment );
!_fegd {return _d .Error (_ggca ,"a\u0075\u0078\u0053\u0074\u0061\u0063k\u0020\u0064\u0061\u0074\u0061\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061 \u002a\u0066\u0069\u006c\u006c\u0053\u0065\u0067\u006d\u0065n\u0074");};}else {_dfddf =&fillSegment {};
};_dfddf ._bffd =_bega ;_dfddf ._eagf =_aaae ;_dfddf ._dfdd =_deeed ;_dfddf ._cddd =_egcg ;_dgbfb .Push (_dfddf );return nil ;};func (_eegc *Bitmap )SetDefaultPixel (){for _eebg :=range _eegc .Data {_eegc .Data [_eebg ]=byte (0xff);};};const (CmbOpOr CombinationOperator =iota ;
CmbOpAnd ;CmbOpXor ;CmbOpXNor ;CmbOpReplace ;CmbOpNot ;);func (_fcd Points )GetIntY (i int )(int ,error ){if i >=len (_fcd ){return 0,_d .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065t\u0049\u006e\u0074\u0059","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return int (_fcd [i ].Y ),nil ;};