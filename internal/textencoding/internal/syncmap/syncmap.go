//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package syncmap ;import _a "sync";func (_g *RuneByteMap )Write (r rune ,b byte ){_g ._fa .Lock ();defer _g ._fa .Unlock ();_g ._fd [r ]=b };func (_ee *ByteRuneMap )Read (b byte )(rune ,bool ){_ee ._f .RLock ();defer _ee ._f .RUnlock ();_fg ,_ff :=_ee ._ag [b ];
return _fg ,_ff ;};func MakeByteRuneMap (length int )*ByteRuneMap {return &ByteRuneMap {_ag :make (map[byte ]rune ,length )}};func (_dc *StringRuneMap )Write (g string ,r rune ){_dc ._daa .Lock ();defer _dc ._daa .Unlock ();_dc ._ebb [g ]=r ;};func (_aa *RuneSet )Exists (r rune )bool {_aa ._dbf .RLock ();
defer _aa ._dbf .RUnlock ();_ ,_gg :=_aa ._bf [r ];return _gg ;};func (_c *RuneByteMap )Read (r rune )(byte ,bool ){_c ._fa .RLock ();defer _c ._fa .RUnlock ();_cb ,_cf :=_c ._fd [r ];return _cb ,_cf ;};type ByteRuneMap struct{_ag map[byte ]rune ;_f _a .RWMutex ;
};func (_ec *RuneByteMap )Range (f func (_dg rune ,_b byte )(_ad bool )){_ec ._fa .RLock ();defer _ec ._fa .RUnlock ();for _gf ,_aca :=range _ec ._fd {if f (_gf ,_aca ){break ;};};};func (_fb *StringsMap )Read (g string )(string ,bool ){_fb ._afcd .RLock ();
defer _fb ._afcd .RUnlock ();_fef ,_be :=_fb ._eaf [g ];return _fef ,_be ;};func (_cg *RuneUint16Map )RangeDelete (f func (_bd rune ,_aea uint16 )(_aeac bool ,_eb bool )){_cg ._eg .Lock ();defer _cg ._eg .Unlock ();for _fae ,_aac :=range _cg ._bba {_dea ,_cbd :=f (_fae ,_aac );
if _dea {delete (_cg ._bba ,_fae );};if _cbd {break ;};};};func (_aeb *RuneSet )Length ()int {_aeb ._dbf .RLock ();defer _aeb ._dbf .RUnlock ();return len (_aeb ._bf )};func (_dge *StringsMap )Range (f func (_fded ,_bda string )(_ggd bool )){_dge ._afcd .RLock ();
defer _dge ._afcd .RUnlock ();for _ef ,_bgb :=range _dge ._eaf {if f (_ef ,_bgb ){break ;};};};func (_ca *RuneUint16Map )Read (r rune )(uint16 ,bool ){_ca ._eg .RLock ();defer _ca ._eg .RUnlock ();_afa ,_dbg :=_ca ._bba [r ];return _afa ,_dbg ;};type StringsTuple struct{Key ,Value string ;
};func (_cba *RuneStringMap )Read (r rune )(string ,bool ){_cba ._fff .RLock ();defer _cba ._fff .RUnlock ();_gfc ,_ea :=_cba ._ba [r ];return _gfc ,_ea ;};func (_deg *StringRuneMap )Read (g string )(rune ,bool ){_deg ._daa .RLock ();defer _deg ._daa .RUnlock ();
_gfa ,_aeg :=_deg ._ebb [g ];return _gfa ,_aeg ;};type RuneByteMap struct{_fd map[rune ]byte ;_fa _a .RWMutex ;};func (_dd *RuneSet )Write (r rune ){_dd ._dbf .Lock ();defer _dd ._dbf .Unlock ();_dd ._bf [r ]=struct{}{}};func (_cgb *RuneUint16Map )Delete (r rune ){_cgb ._eg .Lock ();
defer _cgb ._eg .Unlock ();delete (_cgb ._bba ,r );};func (_fe *ByteRuneMap )Length ()int {_fe ._f .RLock ();defer _fe ._f .RUnlock ();return len (_fe ._ag )};type RuneStringMap struct{_ba map[rune ]string ;_fff _a .RWMutex ;};type RuneSet struct{_bf map[rune ]struct{};
_dbf _a .RWMutex ;};func (_ecf *RuneStringMap )Range (f func (_afc rune ,_gfb string )(_ecgb bool )){_ecf ._fff .RLock ();defer _ecf ._fff .RUnlock ();for _df ,_bag :=range _ecf ._ba {if f (_df ,_bag ){break ;};};};func MakeRuneByteMap (length int )*RuneByteMap {_ae :=make (map[rune ]byte ,length );
return &RuneByteMap {_fd :_ae };};func NewStringRuneMap (m map[string ]rune )*StringRuneMap {return &StringRuneMap {_ebb :m }};func NewByteRuneMap (m map[byte ]rune )*ByteRuneMap {return &ByteRuneMap {_ag :m }};func NewRuneStringMap (m map[rune ]string )*RuneStringMap {return &RuneStringMap {_ba :m }};
func NewStringsMap (tuples []StringsTuple )*StringsMap {_edea :=map[string ]string {};for _ ,_aab :=range tuples {_edea [_aab .Key ]=_aab .Value ;};return &StringsMap {_eaf :_edea };};func (_afce *StringRuneMap )Length ()int {_afce ._daa .RLock ();defer _afce ._daa .RUnlock ();
return len (_afce ._ebb );};func (_fag *RuneStringMap )Write (r rune ,s string ){_fag ._fff .Lock ();defer _fag ._fff .Unlock ();_fag ._ba [r ]=s ;};func (_af *ByteRuneMap )Write (b byte ,r rune ){_af ._f .Lock ();defer _af ._f .Unlock ();_af ._ag [b ]=r };
func (_ce *RuneSet )Range (f func (_ed rune )(_cbb bool )){_ce ._dbf .RLock ();defer _ce ._dbf .RUnlock ();for _ecg :=range _ce ._bf {if f (_ecg ){break ;};};};type RuneUint16Map struct{_bba map[rune ]uint16 ;_eg _a .RWMutex ;};type StringRuneMap struct{_ebb map[string ]rune ;
_daa _a .RWMutex ;};func (_ede *StringRuneMap )Range (f func (_bc string ,_fed rune )(_fde bool )){_ede ._daa .RLock ();defer _ede ._daa .RUnlock ();for _ggg ,_cbdf :=range _ede ._ebb {if f (_ggg ,_cbdf ){break ;};};};func (_fcc *RuneUint16Map )Range (f func (_de rune ,_gb uint16 )(_feb bool )){_fcc ._eg .RLock ();
defer _fcc ._eg .RUnlock ();for _dbd ,_afeb :=range _fcc ._bba {if f (_dbd ,_afeb ){break ;};};};func (_edf *RuneStringMap )Length ()int {_edf ._fff .RLock ();defer _edf ._fff .RUnlock ();return len (_edf ._ba );};func (_fgc *RuneUint16Map )Write (r rune ,g uint16 ){_fgc ._eg .Lock ();
defer _fgc ._eg .Unlock ();_fgc ._bba [r ]=g ;};func (_da *RuneUint16Map )Length ()int {_da ._eg .RLock ();defer _da ._eg .RUnlock ();return len (_da ._bba );};func (_fea *StringsMap )Copy ()*StringsMap {_fea ._afcd .RLock ();defer _fea ._afcd .RUnlock ();
_eafc :=map[string ]string {};for _ffb ,_aec :=range _fea ._eaf {_eafc [_ffb ]=_aec ;};return &StringsMap {_eaf :_eafc };};type StringsMap struct{_eaf map[string ]string ;_afcd _a .RWMutex ;};func (_afe *ByteRuneMap )Range (f func (_d byte ,_ac rune )(_db bool )){_afe ._f .RLock ();
defer _afe ._f .RUnlock ();for _age ,_fc :=range _afe ._ag {if f (_age ,_fc ){break ;};};};func MakeRuneSet (length int )*RuneSet {return &RuneSet {_bf :make (map[rune ]struct{},length )}};func MakeRuneUint16Map (length int )*RuneUint16Map {return &RuneUint16Map {_bba :make (map[rune ]uint16 ,length )};
};func (_aff *StringsMap )Write (g1 ,g2 string ){_aff ._afcd .Lock ();defer _aff ._afcd .Unlock ();_aff ._eaf [g1 ]=g2 ;};func (_bb *RuneByteMap )Length ()int {_bb ._fa .RLock ();defer _bb ._fa .RUnlock ();return len (_bb ._fd )};