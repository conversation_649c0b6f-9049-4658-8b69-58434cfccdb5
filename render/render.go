//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package render ;import (_g "errors";_bb "fmt";_dd "github.com/adrg/sysfont";_ed "github.com/unidoc/unipdf/v4/annotator";_fg "github.com/unidoc/unipdf/v4/common";_fc "github.com/unidoc/unipdf/v4/contentstream";_cg "github.com/unidoc/unipdf/v4/contentstream/draw";
_bbc "github.com/unidoc/unipdf/v4/core";_e "github.com/unidoc/unipdf/v4/internal/license";_ef "github.com/unidoc/unipdf/v4/internal/transform";_gb "github.com/unidoc/unipdf/v4/model";_ac "github.com/unidoc/unipdf/v4/render/internal/context";_ff "github.com/unidoc/unipdf/v4/render/internal/context/imagerender";
_da "golang.org/x/image/draw";_gc "image";_gg "image/color";_c "image/draw";_a "image/jpeg";_dgg "image/png";_ge "math";_d "os";_dg "path/filepath";_b "strings";);

// PdfShadingType defines PDF shading types.
// Source: PDF32000_2008.pdf. Chapter *******
type PdfShadingType int64 ;func _fdg (_facb *_gb .Image ,_ceeee _gg .Color )_gc .Image {_bcfe ,_bda :=int (_facb .Width ),int (_facb .Height );_caae :=_gc .NewRGBA (_gc .Rect (0,0,_bcfe ,_bda ));for _ccbc :=0;_ccbc < _bda ;_ccbc ++{for _ccbe :=0;_ccbe < _bcfe ;
_ccbe ++{_gcda ,_caf :=_facb .ColorAt (_ccbe ,_ccbc );if _caf !=nil {_fg .Log .Debug ("\u0063o\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069e\u0076\u0065\u0020\u0069m\u0061\u0067\u0065\u0020\u006da\u0073\u006b\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061y\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002e",_ccbe ,_ccbc );
continue ;};_bdff ,_abcb ,_gcff ,_ :=_gcda .RGBA ();var _edag _gg .Color ;if _bdff +_abcb +_gcff ==0{_edag =_ceeee ;}else {_edag =_gg .Transparent ;};_caae .Set (_ccbe ,_ccbc ,_edag );};};return _caae ;};

// Render converts the specified PDF page into an image, flattens annotations by default and returns the result.
func (_ba *ImageDevice )Render (page *_gb .PdfPage )(_gc .Image ,error ){return _ba .RenderWithOpts (page ,false );};func (_gccf renderer )processRadialShading (_eae _ac .Context ,_dfa *_gb .PdfShading )(_ac .Gradient ,*_bbc .PdfObjectArray ,error ){_eebd :=_dfa .GetContext ().(*_gb .PdfShadingType3 );
if len (_eebd .Function )==0{return nil ,nil ,_g .New ("\u006e\u006f\u0020\u0067\u0072\u0061\u0064i\u0065\u006e\u0074 \u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0073\u006b\u0069\u0070\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e");
};_dbf ,_efad :=_eebd .Coords .ToFloat64Array ();if _efad !=nil {return nil ,nil ,_g .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0067e\u0074\u0074\u0069\u006e\u0067\u0020\u0073h\u0061\u0064\u0069\u006e\u0067\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e");
};_eebe :=_dfa .ColorSpace ;_cfff :=_bbc .MakeArrayFromFloats ([]float64 {0,0,1,1});var _ffec ,_deec ,_ffce ,_ead ,_fba ,_aeb float64 ;_ffec ,_deec =_eae .Matrix ().Transform (_dbf [0],_dbf [1]);_ffce ,_ead =_eae .Matrix ().Transform (_dbf [3],_dbf [4]);
_fba ,_ =_eae .Matrix ().Transform (_dbf [2],0);_aeb ,_ =_eae .Matrix ().Transform (_dbf [5],0);_gfea ,_ :=_eae .Matrix ().Translation ();_fba -=_gfea ;_aeb -=_gfea ;for _eebf ,_faa :=range _dbf {if _eebf ==2||_eebf ==5{continue ;};if _faa > 1.0{_fbf :=_ge .Min (_ffec -_fba ,_ffce -_aeb );
_ccb :=_ge .Min (_deec -_fba ,_ead -_aeb );_afb :=_ge .Max (_ffec +_fba ,_ffce +_aeb );_fgaf :=_ge .Max (_deec +_fba ,_ead +_aeb );_aee :=_afb -_fbf ;_ebee :=_ccb -_fgaf ;_cfff =_bbc .MakeArrayFromFloats ([]float64 {_fbf ,_ccb ,_aee ,_ebee });break ;};
};_ggb :=_ff .NewRadialGradient (_ffec ,_deec ,_fba ,_ffce ,_ead ,_aeb );if _ggbd ,_bffc :=_eebd .Function [0].(*_gb .PdfFunctionType2 );_bffc {_ggb ,_efad =_affd (_ggb ,_ggbd ,_eebe ,1.0,true );}else if _dcf ,_gcde :=_eebd .Function [0].(*_gb .PdfFunctionType3 );
_gcde {_bfef :=append ([]float64 {0},_dcf .Bounds ...);_bfef =append (_bfef ,1.0);_ggb ,_efad =_aecf (_ggb ,_dcf ,_eebe ,_bfef );};if _efad !=nil {return nil ,nil ,_efad ;};return _ggb ,_cfff ,nil ;};func _affd (_cecd _ac .Gradient ,_afcgg *_gb .PdfFunctionType2 ,_aabg _gb .PdfColorspace ,_dfac float64 ,_fdd bool )(_ac .Gradient ,error ){switch _aabg .(type ){case *_gb .PdfColorspaceDeviceRGB :if len (_afcgg .C0 )!=3||len (_afcgg .C1 )!=3{return nil ,_g .New ("\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u0020\u0052\u0047\u0042\u0020\u0063o\u006co\u0072\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006c\u0065\u006e\u0067\u0074\u0068");
};_bfg :=_afcgg .C0 ;_gcb :=_afcgg .C1 ;if _fdd {_cecd .AddColorStop (0.0,_gg .RGBA {R :uint8 (_bfg [0]*255),G :uint8 (_bfg [1]*255),B :uint8 (_bfg [2]*255),A :255});};_cecd .AddColorStop (_dfac ,_gg .RGBA {R :uint8 (_gcb [0]*255),G :uint8 (_gcb [1]*255),B :uint8 (_gcb [2]*255),A :255});
case *_gb .PdfColorspaceDeviceCMYK :if len (_afcgg .C0 )!=4||len (_afcgg .C1 )!=4{return nil ,_g .New ("\u0069\u006e\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0043\u004d\u0059\u004b\u0020\u0063\u006f\u006c\u006f\u0072 \u0061\u0072\u0072\u0061\u0079\u0020\u006ce\u006e\u0067\u0074\u0068");
};_ceeb :=_afcgg .C0 ;_dcg :=_afcgg .C1 ;if _fdd {_cecd .AddColorStop (0.0,_gg .CMYK {C :uint8 (_ceeb [0]*255),M :uint8 (_ceeb [1]*255),Y :uint8 (_ceeb [2]*255),K :uint8 (_ceeb [3]*255)});};_cecd .AddColorStop (_dfac ,_gg .CMYK {C :uint8 (_dcg [0]*255),M :uint8 (_dcg [1]*255),Y :uint8 (_dcg [2]*255),K :uint8 (_dcg [3]*255)});
default:return nil ,_bb .Errorf ("u\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0063\u006f\u006c\u006f\u0072 \u0073\u0070\u0061c\u0065:\u0020\u0025\u0073",_aabg .String ());};return _cecd ,nil ;};func _fcga (_cefa _bbc .PdfObject )(_gc .Image ,error ){_dbc ,_cecc :=_bbc .GetStream (_cefa );
if !_cecc {return nil ,nil ;};_eba ,_bfdf :=_gb .NewXObjectImageFromStream (_dbc );if _bfdf !=nil {return nil ,_bfdf ;};_fea ,_bfdf :=_eba .ToImage ();if _bfdf !=nil {return nil ,_bfdf ;};if _fea .Width ==0||_fea .Height ==0{_fg .Log .Debug ("\u0073o\u0066t\u0020\u006d\u0061\u0073\u006b\u0020\u0069\u006d\u0061\u0067\u0065 \u0068\u0061\u0073\u0020i\u006e\u0076\u0061\u006c\u0069d\u0020\u0064\u0069\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061y\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002e",_fea .Width ,_fea .Height );
return nil ,nil ;};return _accg (_fea ),nil ;};

// ImageDevice is used to render PDF pages to image targets.
type ImageDevice struct{renderer ;

// OutputWidth represents the width of the rendered images in pixels.
// The heights of the output images are calculated based on the selected
// width and the original height of each rendered page.
OutputWidth int ;};

// RenderWithOpts converts the specified PDF page into an image, optionally flattens annotations and returns the result.
func (_eg *ImageDevice )RenderWithOpts (page *_gb .PdfPage ,skipFlattening bool )(_gc .Image ,error ){_cge ,_df :=page .GetMediaBox ();if _df !=nil {return nil ,_df ;};_cge .Normalize ();_ca :=page .CropBox ;var _aa ,_ddc float64 ;if _ca !=nil {_ca .Normalize ();
_aa ,_ddc =_ca .Width (),_ca .Height ();};_ace :=page .Rotate ;_ce ,_aad ,_cd ,_cac :=_cge .Llx ,_cge .Lly ,_cge .Width (),_cge .Height ();_bab :=_ef .IdentityMatrix ();if _ace !=nil &&*_ace %360!=0&&*_ace %90==0{_dc :=-float64 (*_ace );_bdb :=_bffaa (_cd ,_cac ,_dc );
_bab =_bab .Translate ((_bdb .Width -_cd )/2+_cd /2,(_bdb .Height -_cac )/2+_cac /2).Rotate (_dc *_ge .Pi /180).Translate (-_cd /2,-_cac /2);_cd ,_cac =_bdb .Width ,_bdb .Height ;if _ca !=nil {_fd :=_bffaa (_aa ,_ddc ,_dc );_aa ,_ddc =_fd .Width ,_fd .Height ;
};};if _ce !=0||_aad !=0{_bab =_bab .Translate (-_ce ,-_aad );};_eg ._adb =1.0;if _eg .OutputWidth !=0{_dcd :=_cd ;if _ca !=nil {_dcd =_aa ;};_eg ._adb =float64 (_eg .OutputWidth )/_dcd ;_cd ,_cac ,_aa ,_ddc =_cd *_eg ._adb ,_cac *_eg ._adb ,_aa *_eg ._adb ,_ddc *_eg ._adb ;
_bab =_ef .ScaleMatrix (_eg ._adb ,_eg ._adb ).Mult (_bab );};_ee :=_ff .NewContext (int (_cd ),int (_cac ));if _ede :=_eg .renderPage (_ee ,page ,_bab ,skipFlattening );_ede !=nil {return nil ,_ede ;};_fgf :=_ee .Image ();if _ca !=nil {_ceb ,_ad :=(_ca .Llx -_ce )*_eg ._adb ,(_ca .Lly -_aad )*_eg ._adb ;
_af :=_gc .Rect (0,0,int (_aa ),int (_ddc ));_acc :=_gc .Pt (int (_ceb ),int (_cac -_ad -_ddc ));_eec :=_gc .NewRGBA (_af );_c .Draw (_eec ,_af ,_fgf ,_acc ,_c .Src );_fgf =_eec ;};return _fgf ,nil ;};func (_bba renderer )processLinearShading (_cad _ac .Context ,_gdc *_gb .PdfShading )(_ac .Gradient ,*_bbc .PdfObjectArray ,error ){_dgf :=_gdc .GetContext ().(*_gb .PdfShadingType2 );
if len (_dgf .Function )==0{return nil ,nil ,_g .New ("\u006e\u006f\u0020\u0067\u0072\u0061\u0064i\u0065\u006e\u0074 \u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0073\u006b\u0069\u0070\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e");
};_fcgf ,_dfb :=_dgf .Coords .ToFloat64Array ();if _dfb !=nil {return nil ,nil ,_g .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0067e\u0074\u0074\u0069\u006e\u0067\u0020\u0073h\u0061\u0064\u0069\u006e\u0067\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e");
};_ggab :=_gdc .ColorSpace ;_gcd ,_bgfe :=_cad .Matrix ().Transform (_fcgf [0],_fcgf [1]);_cab ,_gefe :=_cad .Matrix ().Transform (_fcgf [2],_fcgf [3]);_acg :=_ff .NewLinearGradient (_gcd ,_bgfe ,_cab ,_gefe );_agga :=_bbc .MakeArrayFromFloats ([]float64 {0,0,1,1});
for _ ,_cgdf :=range _fcgf {if _cgdf > 1{_agga =_dgf .Coords ;break ;};};if _bbdc ,_bdg :=_dgf .Function [0].(*_gb .PdfFunctionType2 );_bdg {_acg ,_dfb =_affd (_acg ,_bbdc ,_ggab ,1.0,true );}else if _feeg ,_fedd :=_dgf .Function [0].(*_gb .PdfFunctionType3 );
_fedd {_dag :=append ([]float64 {0},_feeg .Bounds ...);_dag =append (_dag ,1.0);_acg ,_dfb =_aecf (_acg ,_feeg ,_ggab ,_dag );};return _acg ,_agga ,_dfb ;};

// NewImageDevice returns a new image device.
func NewImageDevice ()*ImageDevice {const _bd ="r\u0065\u006e\u0064\u0065r.\u004ee\u0077\u0049\u006d\u0061\u0067e\u0044\u0065\u0076\u0069\u0063\u0065";_e .TrackUse (_bd );return &ImageDevice {};};var (_edeg =_g .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");
_cdg =_g .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072"););func _cce (_dfc string ,_afce _gc .Image ,_dcfd int )error {_cddb ,_ffbb :=_d .Create (_dfc );if _ffbb !=nil {return _ffbb ;};defer _cddb .Close ();
return _a .Encode (_cddb ,_afce ,&_a .Options {Quality :_dcfd });};func (_bca renderer )processGradient (_abdcc _ac .Context ,_faf *_fc .ContentStreamOperation ,_bbb *_gb .PdfPageResources ,_bef *_bbc .PdfObjectName )(_ac .Gradient ,error ){if _ecf ,_ggcb :=_bbb .GetPatternByName (*_bef );
_ggcb &&_ecf .IsShading (){_accf :=_ecf .GetAsShadingPattern ().Shading ;_gecd ,_ ,_baba :=_bca .processShading (_abdcc ,_accf );if _baba !=nil {return nil ,_baba ;};return _gecd ,nil ;};return nil ,nil ;};func _fgdg (_bffa ,_egd _gc .Image )_gc .Image {_begg ,_fcf :=_egd .Bounds ().Size (),_bffa .Bounds ().Size ();
_cbb ,_beb :=_begg .X ,_begg .Y ;if _fcf .X > _cbb {_cbb =_fcf .X ;};if _fcf .Y > _beb {_beb =_fcf .Y ;};_eaf :=_gc .Rect (0,0,_cbb ,_beb );if _begg .X !=_cbb ||_begg .Y !=_beb {_defe :=_gc .NewRGBA (_eaf );_da .BiLinear .Scale (_defe ,_eaf ,_egd ,_egd .Bounds (),_da .Over ,nil );
_egd =_defe ;};if _fcf .X !=_cbb ||_fcf .Y !=_beb {_gfda :=_gc .NewRGBA (_eaf );_da .BiLinear .Scale (_gfda ,_eaf ,_bffa ,_bffa .Bounds (),_da .Over ,nil );_bffa =_gfda ;};_eff :=_gc .NewRGBA (_eaf );_da .DrawMask (_eff ,_eaf ,_bffa ,_gc .Point {},_egd ,_gc .Point {},_da .Over );
return _eff ;};func _aecf (_cgga _ac .Gradient ,_gfd *_gb .PdfFunctionType3 ,_ccae _gb .PdfColorspace ,_dgfa []float64 )(_ac .Gradient ,error ){var _afd error ;for _gggb :=0;_gggb < len (_gfd .Functions );_gggb ++{if _dfgc ,_dacg :=_gfd .Functions [_gggb ].(*_gb .PdfFunctionType2 );
_dacg {_cgga ,_afd =_affd (_cgga ,_dfgc ,_ccae ,_dgfa [_gggb +1],_gggb ==0);if _afd !=nil {return nil ,_afd ;};};};return _cgga ,nil ;};func (_be renderer )renderContentStream (_bed _ac .Context ,_adg string ,_aff *_gb .PdfPageResources )error {_dcc ,_adf :=_fc .NewContentStreamParser (_adg ).Parse ();
if _adf !=nil {return _adf ;};_ga :=_bed .TextState ();_ga .GlobalScale =_be ._adb ;_ffd :=map[string ]*_ac .TextFont {};_ab :=_dd .NewFinder (&_dd .FinderOpts {Extensions :[]string {"\u002e\u0074\u0074\u0066","\u002e\u0074\u0074\u0063"}});var _aeg *_fc .ContentStreamOperation ;
var _bc bool ;var _geg _ac .FillRule ;_cf :=_fc .NewContentStreamProcessor (*_dcc );_cf .AddHandler (_fc .HandlerConditionEnumAllOperands ,"",func (_def *_fc .ContentStreamOperation ,_aca _fc .GraphicsState ,_fgfd *_gb .PdfPageResources )error {_fg .Log .Debug ("\u0050\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0025\u0073",_def .Operand );
switch _def .Operand {case "\u0071":_bed .Push ();case "\u0051":_bed .Pop ();_ga =_bed .TextState ();case "\u0063\u006d":if len (_def .Params )!=6{return _cdg ;};_cga ,_bce :=_bbc .GetNumbersAsFloat (_def .Params );if _bce !=nil {return _bce ;};_cgc :=_ef .NewMatrix (_cga [0],_cga [1],_cga [2],_cga [3],_cga [4],_cga [5]);
_fg .Log .Debug ("\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0073\u0020\u0073\u0074a\u0074\u0065\u0020\u006d\u0061\u0074\u0072\u0069\u0078\u003a \u0025\u002b\u0076",_cgc );_bed .SetMatrix (_bed .Matrix ().Mult (_cgc ));case "\u0077":if len (_def .Params )!=1{return _cdg ;
};_aega ,_cff :=_bbc .GetNumbersAsFloat (_def .Params );if _cff !=nil {return _cff ;};_bed .SetLineWidth (_aega [0]);case "\u004a":if len (_def .Params )!=1{return _cdg ;};_cag ,_cfa :=_bbc .GetIntVal (_def .Params [0]);if !_cfa {return _edeg ;};switch _cag {case 0:_bed .SetLineCap (_ac .LineCapButt );
case 1:_bed .SetLineCap (_ac .LineCapRound );case 2:_bed .SetLineCap (_ac .LineCapSquare );default:_fg .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u006ee\u0020\u0063\u0061\u0070\u0020\u0073\u0074\u0079\u006c\u0065:\u0020\u0025\u0064",_cag );
return _cdg ;};case "\u006a":if len (_def .Params )!=1{return _cdg ;};_cdf ,_afg :=_bbc .GetIntVal (_def .Params [0]);if !_afg {return _edeg ;};switch _cdf {case 0:_bed .SetLineJoin (_ac .LineJoinBevel );case 1:_bed .SetLineJoin (_ac .LineJoinRound );case 2:_bed .SetLineJoin (_ac .LineJoinBevel );
default:_fg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u006c\u0069\u006e\u0065\u0020\u006a\u006f\u0069\u006e \u0073\u0074\u0079l\u0065:\u0020\u0025\u0064",_cdf );return _cdg ;};case "\u004d":if len (_def .Params )!=1{return _cdg ;};_ddf ,_aada :=_bbc .GetNumbersAsFloat (_def .Params );
if _aada !=nil {return _aada ;};_ =_ddf ;_fg .Log .Debug ("\u004di\u0074\u0065\u0072\u0020l\u0069\u006d\u0069\u0074\u0020n\u006ft\u0020s\u0075\u0070\u0070\u006f\u0072\u0074\u0065d");case "\u0064":if len (_def .Params )!=2{return _cdg ;};_ggag ,_cgf :=_bbc .GetArray (_def .Params [0]);
if !_cgf {return _edeg ;};_ffa ,_cgf :=_bbc .GetIntVal (_def .Params [1]);if !_cgf {_ ,_dee :=_bbc .GetFloatVal (_def .Params [1]);if !_dee {return _edeg ;};};_cb ,_cgag :=_bbc .GetNumbersAsFloat (_ggag .Elements ());if _cgag !=nil {return _cgag ;};_bed .SetDash (_cb ...);
_ =_ffa ;_fg .Log .Debug ("\u004c\u0069n\u0065\u0020\u0064\u0061\u0073\u0068\u0020\u0070\u0068\u0061\u0073\u0065\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006frt\u0065\u0064");case "\u0072\u0069":_fg .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020i\u006e\u0074\u0065\u006e\u0074\u0020\u006eo\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064");
case "\u0069":_fg .Log .Debug ("\u0046\u006c\u0061\u0074\u006e\u0065\u0073\u0073\u0020\u0074\u006f\u006c\u0065\u0072\u0061n\u0063e\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064");case "\u0067\u0073":if len (_def .Params )!=1{return _cdg ;
};_bbe ,_cgaf :=_bbc .GetName (_def .Params [0]);if !_cgaf {return _edeg ;};if _bbe ==nil {return _cdg ;};_aab ,_cgaf :=_fgfd .GetExtGState (*_bbe );if !_cgaf {_fg .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006eo\u0074 \u0066i\u006ed\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u003a\u0020\u0025\u0073",*_bbe );
return _g .New ("\u0072e\u0073o\u0075\u0072\u0063\u0065\u0020n\u006f\u0074 \u0066\u006f\u0075\u006e\u0064");};_cgg ,_cgaf :=_bbc .GetDict (_aab );if !_cgaf {_fg .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020c\u006f\u0075\u006c\u0064 ge\u0074 g\u0072\u0061\u0070\u0068\u0069\u0063\u0073 s\u0074\u0061\u0074\u0065\u0020\u0064\u0069c\u0074");
return _edeg ;};_fg .Log .Debug ("G\u0053\u0020\u0064\u0069\u0063\u0074\u003a\u0020\u0025\u0073",_cgg .String ());_bee :=_cgg .Get ("\u0063\u0061");if _bee !=nil {_eea ,_abf :=_bbc .GetNumberAsFloat (_bee );if _abf ==nil {_abe ,_ffab :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );
if _ffab !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ffab );return _ffab ;};_bdf ,_dff :=_abe .(*_gb .PdfColorDeviceRGB );
if !_dff {_fg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _ffab ;};_bed .SetFillRGBA (_bdf .R (),_bdf .G (),_bdf .B (),_eea );};};case "\u006d":if len (_def .Params )!=2{_fg .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006d\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0073\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_cdg );
return nil ;};_gd ,_abc :=_bbc .GetNumbersAsFloat (_def .Params );if _abc !=nil {return _abc ;};_fg .Log .Debug ("M\u006f\u0076\u0065\u0020\u0074\u006f\u003a\u0020\u0025\u0076",_gd );_bed .NewSubPath ();_bed .MoveTo (_gd [0],_gd [1]);case "\u006c":if len (_def .Params )!=2{_fg .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006c\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0073\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_cdg );
return nil ;};_cee ,_bgf :=_bbc .GetNumbersAsFloat (_def .Params );if _bgf !=nil {return _bgf ;};_bed .LineTo (_cee [0],_cee [1]);case "\u0063":if len (_def .Params )!=6{return _cdg ;};_fa ,_acae :=_bbc .GetNumbersAsFloat (_def .Params );if _acae !=nil {return _acae ;
};_fg .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020\u0062\u0065\u007a\u0069\u0065\u0072\u0020p\u0061\u0072\u0061\u006d\u0073\u003a\u0020\u0025\u002b\u0076",_fa );_bed .CubicTo (_fa [0],_fa [1],_fa [2],_fa [3],_fa [4],_fa [5]);case "\u0076","\u0079":if len (_def .Params )!=4{return _cdg ;
};_aadf ,_gbg :=_bbc .GetNumbersAsFloat (_def .Params );if _gbg !=nil {return _gbg ;};_fg .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020\u0062\u0065\u007a\u0069\u0065\u0072\u0020p\u0061\u0072\u0061\u006d\u0073\u003a\u0020\u0025\u002b\u0076",_aadf );_bed .QuadraticTo (_aadf [0],_aadf [1],_aadf [2],_aadf [3]);
case "\u0068":_bed .ClosePath ();_bed .NewSubPath ();case "\u0072\u0065":if len (_def .Params )!=4{return _cdg ;};_gdb ,_gaf :=_bbc .GetNumbersAsFloat (_def .Params );if _gaf !=nil {return _gaf ;};_bed .DrawRectangle (_gdb [0],_gdb [1],_gdb [2],_gdb [3]);
_bed .NewSubPath ();case "\u0053":_ega ,_fgd :=_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _fgd !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_fgd );
return _fgd ;};_bdd ,_abcg :=_ega .(*_gb .PdfColorDeviceRGB );if !_abcg {_fg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _fgd ;};_bed .SetRGBA (_bdd .R (),_bdd .G (),_bdd .B (),1);
_bed .Stroke ();case "\u0073":_db ,_bcc :=_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _bcc !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bcc );
return _bcc ;};_abeb ,_cfb :=_db .(*_gb .PdfColorDeviceRGB );if !_cfb {_fg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _bcc ;};_bed .ClosePath ();_bed .NewSubPath ();
_bed .SetRGBA (_abeb .R (),_abeb .G (),_abeb .B (),1);_bed .Stroke ();case "\u0066","\u0046":_cae ,_ebe :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );if _ebe !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ebe );
return _ebe ;};switch _fb :=_cae .(type ){case *_gb .PdfColorDeviceRGB :_bed .SetRGBA (_fb .R (),_fb .G (),_fb .B (),1);_bed .SetFillRule (_ac .FillRuleWinding );_bed .Fill ();case *_gb .PdfColorPattern :_bed .Fill ();};_fg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");
case "\u0066\u002a":_ec ,_afc :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );if _afc !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_afc );
return _afc ;};_ege ,_bgff :=_ec .(*_gb .PdfColorDeviceRGB );if !_bgff {_fg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _afc ;};_bed .SetRGBA (_ege .R (),_ege .G (),_ege .B (),1);
_bed .SetFillRule (_ac .FillRuleEvenOdd );_bed .Fill ();case "\u0042":_affb ,_ada :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );if _ada !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ada );
return _ada ;};switch _gca :=_affb .(type ){case *_gb .PdfColorDeviceRGB :_bed .SetRGBA (_gca .R (),_gca .G (),_gca .B (),1);_bed .SetFillRule (_ac .FillRuleWinding );_bed .FillPreserve ();_affb ,_ada =_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );
if _ada !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ada );return _ada ;};if _caeb ,_gef :=_affb .(*_gb .PdfColorDeviceRGB );
_gef {_bed .SetRGBA (_caeb .R (),_caeb .G (),_caeb .B (),1);_bed .Stroke ();};case *_gb .PdfColorPattern :_bed .SetFillRule (_ac .FillRuleWinding );_bed .Fill ();_bed .StrokePattern ();};case "\u0042\u002a":_edc ,_gbgf :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );
if _gbgf !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gbgf );return _gbgf ;};switch _cbe :=_edc .(type ){case *_gb .PdfColorDeviceRGB :_bed .SetRGBA (_cbe .R (),_cbe .G (),_cbe .B (),1);
_bed .SetFillRule (_ac .FillRuleEvenOdd );_bed .FillPreserve ();_edc ,_gbgf =_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _gbgf !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gbgf );
return _gbgf ;};if _baa ,_ffc :=_edc .(*_gb .PdfColorDeviceRGB );_ffc {_bed .SetRGBA (_baa .R (),_baa .G (),_baa .B (),1);_bed .Stroke ();};case *_gb .PdfColorPattern :_bed .SetFillRule (_ac .FillRuleEvenOdd );_bed .Fill ();_bed .StrokePattern ();};case "\u0062":_bed .ClosePath ();
_cagg ,_dea :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );if _dea !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_dea );
return _dea ;};switch _egaf :=_cagg .(type ){case *_gb .PdfColorDeviceRGB :_bed .SetRGBA (_egaf .R (),_egaf .G (),_egaf .B (),1);_bed .NewSubPath ();_bed .SetFillRule (_ac .FillRuleWinding );_bed .FillPreserve ();_cagg ,_dea =_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );
if _dea !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_dea );return _dea ;};if _bcee ,_cef :=_cagg .(*_gb .PdfColorDeviceRGB );
_cef {_bed .SetRGBA (_bcee .R (),_bcee .G (),_bcee .B (),1);_bed .Stroke ();};case *_gb .PdfColorPattern :_bed .NewSubPath ();_bed .SetFillRule (_ac .FillRuleWinding );_bed .Fill ();_bed .StrokePattern ();};case "\u0062\u002a":_bed .ClosePath ();_fge ,_edee :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );
if _edee !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_edee );return _edee ;};switch _afcg :=_fge .(type ){case *_gb .PdfColorDeviceRGB :_bed .SetRGBA (_afcg .R (),_afcg .G (),_afcg .B (),1);
_bed .NewSubPath ();_bed .SetFillRule (_ac .FillRuleEvenOdd );_bed .FillPreserve ();_fge ,_edee =_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _edee !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_edee );
return _edee ;};if _bcf ,_gfe :=_fge .(*_gb .PdfColorDeviceRGB );_gfe {_bed .SetRGBA (_bcf .R (),_bcf .G (),_bcf .B (),1);_bed .Stroke ();};case *_gb .PdfColorPattern :_bed .NewSubPath ();_bed .SetFillRule (_ac .FillRuleEvenOdd );_bed .Fill ();_bed .StrokePattern ();
};case "\u006e":if _bc {_bed .SetFillRule (_geg );_bed .ClipPreserve ();_bc =false ;};_bed .ClearPath ();case "\u0057":_bc =true ;_geg =_ac .FillRuleWinding ;case "\u0057\u002a":_bc =true ;_geg =_ac .FillRuleEvenOdd ;case "\u0072\u0067":_ece ,_aga :=_aca .ColorNonStroking .(*_gb .PdfColorDeviceRGB );
if !_aga {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );return nil ;};_bed .SetFillRGBA (_ece .R (),_ece .G (),_ece .B (),1);
case "\u0052\u0047":_dcb ,_efa :=_aca .ColorStroking .(*_gb .PdfColorDeviceRGB );if !_efa {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_bed .SetStrokeRGBA (_dcb .R (),_dcb .G (),_dcb .B (),1);case "\u006b":_eac ,_ddcc :=_aca .ColorNonStroking .(*_gb .PdfColorDeviceCMYK );if !_ddcc {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );
return nil ;};_ffcb ,_ccd :=_aca .ColorspaceNonStroking .ColorToRGB (_eac );if _ccd !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );
return nil ;};_egc ,_ddcc :=_ffcb .(*_gb .PdfColorDeviceRGB );if !_ddcc {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ffcb );return nil ;
};_bed .SetFillRGBA (_egc .R (),_egc .G (),_egc .B (),1);case "\u004b":_eda ,_efg :=_aca .ColorStroking .(*_gb .PdfColorDeviceCMYK );if !_efg {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_aac ,_ebec :=_aca .ColorspaceStroking .ColorToRGB (_eda );if _ebec !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_fde ,_efg :=_aac .(*_gb .PdfColorDeviceRGB );if !_efg {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aac );return nil ;
};_bed .SetStrokeRGBA (_fde .R (),_fde .G (),_fde .B (),1);case "\u0067":_ded ,_bf :=_aca .ColorNonStroking .(*_gb .PdfColorDeviceGray );if !_bf {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );
return nil ;};_cbg ,_dca :=_aca .ColorspaceNonStroking .ColorToRGB (_ded );if _dca !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );
return nil ;};_ggg ,_bf :=_cbg .(*_gb .PdfColorDeviceRGB );if !_bf {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_cbg );return nil ;
};_bed .SetFillRGBA (_ggg .R (),_ggg .G (),_ggg .B (),1);case "\u0047":_bff ,_cgd :=_aca .ColorStroking .(*_gb .PdfColorDeviceGray );if !_cgd {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_aadb ,_bbdg :=_aca .ColorspaceStroking .ColorToRGB (_bff );if _bbdg !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_dac ,_cgd :=_aadb .(*_gb .PdfColorDeviceRGB );if !_cgd {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aadb );return nil ;
};_bed .SetStrokeRGBA (_dac .R (),_dac .G (),_dac .B (),1);case "\u0063\u0073":if len (_def .Params )> 0{if _gce ,_fdb :=_bbc .GetName (_def .Params [0]);_fdb &&_gce .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{break ;};};_cfe ,_dbb :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );
if _dbb !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );return nil ;};_gaa ,_aec :=_cfe .(*_gb .PdfColorDeviceRGB );
if !_aec {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_cfe );return nil ;};_bed .SetFillRGBA (_gaa .R (),_gaa .G (),_gaa .B (),1);
case "\u0073\u0063":_ggc ,_efe :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );if _efe !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );
return nil ;};_dge ,_gaad :=_ggc .(*_gb .PdfColorDeviceRGB );if !_gaad {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ggc );return nil ;
};_bed .SetFillRGBA (_dge .R (),_dge .G (),_dge .B (),1);case "\u0073\u0063\u006e":if len (_def .Params )> 0&&len (_aeg .Params )> 0{if _afe ,_bfe :=_bbc .GetName (_aeg .Params [0]);_bfe &&_afe .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{if _abb ,_bbdga :=_bbc .GetName (_def .Params [0]);
_bbdga {_ecb ,_bcef :=_be .processGradient (_bed ,_def ,_fgfd ,_abb );if _bcef !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072o\u0063\u0065\u0073\u0073\u0069\u006eg\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0064\u0061\u0074a\u003a\u0020\u0025\u0076",_bcef );
break ;};if _ecb ==nil {_fg .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_bed .SetFillStyle (_ecb );_bed .SetStrokeStyle (_ecb );break ;};};};_gbd ,_fe :=_aca .ColorspaceNonStroking .ColorToRGB (_aca .ColorNonStroking );
if _fe !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorNonStroking );return nil ;};_eca ,_bdfc :=_gbd .(*_gb .PdfColorDeviceRGB );
if !_bdfc {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gbd );return nil ;};_bed .SetFillRGBA (_eca .R (),_eca .G (),_eca .B (),1);
case "\u0043\u0053":if len (_def .Params )> 0{if _ddfe ,_bedd :=_bbc .GetName (_def .Params [0]);_bedd &&_ddfe .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{break ;};};_caa ,_ggf :=_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _ggf !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_agg ,_cfba :=_caa .(*_gb .PdfColorDeviceRGB );if !_cfba {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_caa );return nil ;
};_bed .SetStrokeRGBA (_agg .R (),_agg .G (),_agg .B (),1);case "\u0053\u0043":_cgdg ,_eag :=_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );if _eag !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );
return nil ;};_dfd ,_ccdb :=_cgdg .(*_gb .PdfColorDeviceRGB );if !_ccdb {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_cgdg );return nil ;
};_bed .SetStrokeRGBA (_dfd .R (),_dfd .G (),_dfd .B (),1);case "\u0053\u0043\u004e":if len (_def .Params )> 0&&len (_aeg .Params )> 0{if _bcbg ,_bfa :=_bbc .GetName (_aeg .Params [0]);_bfa &&_bcbg .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{if _gbgd ,_ade :=_bbc .GetName (_def .Params [0]);
_ade {_caga ,_eee :=_be .processGradient (_bed ,_def ,_fgfd ,_gbgd );if _eee !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072o\u0063\u0065\u0073\u0073\u0069\u006eg\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0064\u0061\u0074a\u003a\u0020\u0025\u0076",_eee );
break ;};if _caga ==nil {_fg .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_bed .SetFillStyle (_caga );_bed .SetStrokeStyle (_caga );break ;};};};_bdde ,_fbg :=_aca .ColorspaceStroking .ColorToRGB (_aca .ColorStroking );
if _fbg !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aca .ColorStroking );return nil ;};_acd ,_egcb :=_bdde .(*_gb .PdfColorDeviceRGB );
if !_egcb {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bdde );return nil ;};_bed .SetStrokeRGBA (_acd .R (),_acd .G (),_acd .B (),1);
case "\u0073\u0068":if len (_def .Params )!=1{_fg .Log .Debug ("\u0049n\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0068\u0020\u0070\u0061r\u0061\u006d\u0073\u0020\u0066\u006f\u0072\u006d\u0061\u0074");break ;};_baab ,_fffa :=_bbc .GetName (_def .Params [0]);
if !_fffa {_fg .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020g\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006eg\u0020n\u0061\u006d\u0065");break ;};_ggfc ,_fffa :=_fgfd .GetShadingByName (*_baab );if !_fffa {_fg .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020g\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006eg\u0020d\u0061\u0074\u0061");
break ;};_aed ,_bgg ,_bag :=_be .processShading (_bed ,_ggfc );if _bag !=nil {_fg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006e\u0067\u0020d\u0061\u0074a\u003a\u0020\u0025\u0076",_bag );
break ;};if _aed ==nil {_fg .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_abebg ,_bag :=_bgg .ToFloat64Array ();if _bag !=nil {_fg .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073: \u0025\u0076",_bag );
break ;};_bed .DrawRectangle (_abebg [0],_abebg [1],_abebg [2],_abebg [3]);_bed .NewSubPath ();_bed .SetFillStyle (_aed );_bed .SetStrokeStyle (_aed );_bed .Fill ();case "\u0044\u006f":if len (_def .Params )!=1{return _cdg ;};_fdba ,_bcd :=_bbc .GetName (_def .Params [0]);
if !_bcd {return _edeg ;};_ ,_bbg :=_fgfd .GetXObjectByName (*_fdba );switch _bbg {case _gb .XObjectTypeImage :_fg .Log .Debug ("\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0069\u006d\u0061\u0067e\u003a\u0020\u0025\u0073",_fdba .String ());_cfbb ,_fae :=_fgfd .GetXObjectImageByName (*_fdba );
if _fae !=nil {return _fae ;};_cfg ,_fae :=_cfbb .ToImage ();if _fae !=nil {_fg .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_fae );
return nil ;};if _cde :=_cfbb .ColorSpace ;_cde !=nil {var _fac bool ;switch _cde .(type ){case *_gb .PdfColorspaceSpecialIndexed :_fac =true ;};if _fac {if _bcbf ,_bbda :=_cde .ImageToRGB (*_cfg );_bbda !=nil {_fg .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006fnv\u0065r\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0074\u006f\u0020\u0052G\u0042\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020i\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
}else {_cfg =&_bcbf ;};};};_fad :=_bed .FillPattern ().ColorAt (0,0);var _dba _gc .Image ;if _cfbb .Mask !=nil {if _dba ,_fae =_gag (_cfbb .Mask ,_fad );_fae !=nil {_fg .Log .Debug ("\u0057\u0041\u0052\u004e\u003a \u0063\u006f\u0075\u006c\u0064 \u006eo\u0074\u0020\u0067\u0065\u0074\u0020\u0065\u0078\u0070\u006c\u0069\u0063\u0069\u0074\u0020\u0069\u006d\u0061\u0067e\u0020\u006d\u0061\u0073\u006b\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e");
_dba =nil ;};}else if _cfbb .SMask !=nil {if _dba ,_fae =_fcga (_cfbb .SMask );_fae !=nil {_fg .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0066\u0074\u0020\u0069\u006da\u0067e\u0020\u006d\u0061\u0073k\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
_dba =nil ;};};var _gde _gc .Image ;if _egeb ,_ :=_bbc .GetBoolVal (_cfbb .ImageMask );_egeb {_gde =_fdg (_cfg ,_fad );}else {_gde ,_fae =_cfg .ToGoImage ();if _fae !=nil {_fg .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_fae );
return nil ;};};if _dba !=nil {_gde =_fgdg (_gde ,_dba );};_cgcc :=_gde .Bounds ();_bed .Push ();_bed .Scale (1.0/float64 (_cgcc .Dx ()),-1.0/float64 (_cgcc .Dy ()));_bed .DrawImageAnchored (_gde ,0,0,0,1);_bed .Pop ();case _gb .XObjectTypeForm :_fg .Log .Debug ("\u0058\u004fb\u006a\u0065\u0063t\u0020\u0066\u006f\u0072\u006d\u003a\u0020\u0025\u0073",_fdba .String ());
_adgb ,_bac :=_fgfd .GetXObjectFormByName (*_fdba );if _bac !=nil {return _bac ;};_abd ,_bac :=_adgb .GetContentStream ();if _bac !=nil {return _bac ;};_cec :=_adgb .Resources ;if _cec ==nil {_cec =_fgfd ;};_bed .Push ();if _adgb .Matrix !=nil {_dcbe ,_cbd :=_bbc .GetArray (_adgb .Matrix );
if !_cbd {return _edeg ;};_ffb ,_aadaa :=_bbc .GetNumbersAsFloat (_dcbe .Elements ());if _aadaa !=nil {return _aadaa ;};if len (_ffb )!=6{return _cdg ;};_bge :=_ef .NewMatrix (_ffb [0],_ffb [1],_ffb [2],_ffb [3],_ffb [4],_ffb [5]);_bed .SetMatrix (_bed .Matrix ().Mult (_bge ));
};if _adgb .BBox !=nil {_aea ,_aedg :=_bbc .GetArray (_adgb .BBox );if !_aedg {return _edeg ;};_eeca ,_eaa :=_bbc .GetNumbersAsFloat (_aea .Elements ());if _eaa !=nil {return _eaa ;};if len (_eeca )!=4{_fg .Log .Debug ("\u004c\u0065\u006e\u0020\u003d\u0020\u0025\u0064",len (_eeca ));
return _cdg ;};_bed .DrawRectangle (_eeca [0],_eeca [1],_eeca [2]-_eeca [0],_eeca [3]-_eeca [1]);_bed .SetRGBA (1,0,0,1);_bed .Clip ();}else {_fg .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0052\u0065q\u0075\u0069\u0072e\u0064\u0020\u0042\u0042\u006f\u0078\u0020\u006d\u0069ss\u0069\u006e\u0067 \u006f\u006e \u0058\u004f\u0062\u006a\u0065\u0063t\u0020\u0046o\u0072\u006d");
};_bac =_be .renderContentStream (_bed ,string (_abd ),_cec );if _bac !=nil {return _bac ;};_bed .Pop ();};case "\u0042\u0049":if len (_def .Params )!=1{return _cdg ;};_ged ,_aadba :=_def .Params [0].(*_fc .ContentStreamInlineImage );if !_aadba {return nil ;
};_eeb ,_gdec :=_ged .ToImage (_fgfd );if _gdec !=nil {_fg .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_gdec );
return nil ;};_defa ,_gdec :=_eeb .ToGoImage ();if _gdec !=nil {_fg .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_gdec );
return nil ;};_cca :=_defa .Bounds ();_bed .Push ();_bed .Scale (1.0/float64 (_cca .Dx ()),-1.0/float64 (_cca .Dy ()));_bed .DrawImageAnchored (_defa ,0,0,0,1);_bed .Pop ();case "\u0042\u0054":_ga .Reset ();case "\u0045\u0054":_ga .Reset ();case "\u0054\u0072":if len (_def .Params )!=1{return _cdg ;
};_deef ,_efd :=_bbc .GetNumberAsFloat (_def .Params [0]);if _efd !=nil {return _efd ;};_ga .Tr =_ac .TextRenderingMode (_deef );case "\u0054\u004c":if len (_def .Params )!=1{return _cdg ;};_deb ,_gcf :=_bbc .GetNumberAsFloat (_def .Params [0]);if _gcf !=nil {return _gcf ;
};_ga .Tl =_deb ;case "\u0054\u0063":if len (_def .Params )!=1{return _cdg ;};_abdc ,_ggff :=_bbc .GetNumberAsFloat (_def .Params [0]);if _ggff !=nil {return _ggff ;};_fg .Log .Debug ("\u0054\u0063\u003a\u0020\u0025\u0076",_abdc );_ga .Tc =_abdc ;case "\u0054\u0077":if len (_def .Params )!=1{return _cdg ;
};_bagc ,_fgc :=_bbc .GetNumberAsFloat (_def .Params [0]);if _fgc !=nil {return _fgc ;};_fg .Log .Debug ("\u0054\u0077\u003a\u0020\u0025\u0076",_bagc );_ga .Tw =_bagc ;case "\u0054\u007a":if len (_def .Params )!=1{return _cdg ;};_ceee ,_cgafa :=_bbc .GetNumberAsFloat (_def .Params [0]);
if _cgafa !=nil {return _cgafa ;};_ga .Th =_ceee ;case "\u0054\u0073":if len (_def .Params )!=1{return _cdg ;};_abg ,_gbdf :=_bbc .GetNumberAsFloat (_def .Params [0]);if _gbdf !=nil {return _gbdf ;};_ga .Ts =_abg ;case "\u0054\u0064":if len (_def .Params )!=2{return _cdg ;
};_ddcgc ,_gac :=_bbc .GetNumbersAsFloat (_def .Params );if _gac !=nil {return _gac ;};_fg .Log .Debug ("\u0054\u0064\u003a\u0020\u0025\u0076",_ddcgc );_ga .ProcTd (_ddcgc [0],_ddcgc [1]);case "\u0054\u0044":if len (_def .Params )!=2{return _cdg ;};_aegc ,_eeg :=_bbc .GetNumbersAsFloat (_def .Params );
if _eeg !=nil {return _eeg ;};_fg .Log .Debug ("\u0054\u0044\u003a\u0020\u0025\u0076",_aegc );_ga .ProcTD (_aegc [0],_aegc [1]);case "\u0054\u002a":_ga .ProcTStar ();case "\u0054\u006d":if len (_def .Params )!=6{return _cdg ;};_ffe ,_bfaf :=_bbc .GetNumbersAsFloat (_def .Params );
if _bfaf !=nil {return _bfaf ;};_fg .Log .Debug ("\u0054\u0065x\u0074\u0020\u006da\u0074\u0072\u0069\u0078\u003a\u0020\u0025\u002b\u0076",_ffe );_ga .ProcTm (_ffe [0],_ffe [1],_ffe [2],_ffe [3],_ffe [4],_ffe [5]);case "\u0027":if len (_def .Params )!=1{return _cdg ;
};_aaa ,_gbb :=_bbc .GetStringBytes (_def .Params [0]);if !_gbb {return _edeg ;};_fg .Log .Debug ("\u0027\u0020\u0073t\u0072\u0069\u006e\u0067\u003a\u0020\u0025\u0073",string (_aaa ));_ga .ProcQ (_aaa ,_bed );case "\u0022":if len (_def .Params )!=3{return _cdg ;
};_bfd ,_fee :=_bbc .GetNumberAsFloat (_def .Params [0]);if _fee !=nil {return _fee ;};_fce ,_fee :=_bbc .GetNumberAsFloat (_def .Params [1]);if _fee !=nil {return _fee ;};_bgffa ,_gaae :=_bbc .GetStringBytes (_def .Params [2]);if !_gaae {return _edeg ;
};_ga .ProcDQ (_bgffa ,_bfd ,_fce ,_bed );case "\u0054\u006a":if len (_def .Params )!=1{return _cdg ;};_fed ,_bffg :=_bbc .GetStringBytes (_def .Params [0]);if !_bffg {return _edeg ;};_fg .Log .Debug ("\u0054j\u0020s\u0074\u0072\u0069\u006e\u0067\u003a\u0020\u0060\u0025\u0073\u0060",string (_fed ));
_ga .ProcTj (_fed ,_bed );case "\u0054\u004a":if len (_def .Params )!=1{return _cdg ;};_feb ,_cffd :=_bbc .GetArray (_def .Params [0]);if !_cffd {_fg .Log .Debug ("\u0054\u0079\u0070\u0065\u003a\u0020\u0025\u0054",_feb );return _edeg ;};_fg .Log .Debug ("\u0054\u004a\u0020\u0061\u0072\u0072\u0061\u0079\u003a\u0020\u0025\u002b\u0076",_feb );
for _ ,_gee :=range _feb .Elements (){switch _bgd :=_gee .(type ){case *_bbc .PdfObjectString :if _bgd !=nil {_ga .ProcTj (_bgd .Bytes (),_bed );};case *_bbc .PdfObjectFloat ,*_bbc .PdfObjectInteger :_faca ,_dgeb :=_bbc .GetNumberAsFloat (_bgd );if _dgeb ==nil {_ga .Translate (-_faca *0.001*_ga .Tf .Size *_ga .Th /100.0,0);
};};};case "\u0054\u0066":if len (_def .Params )!=2{return _cdg ;};_fg .Log .Debug ("\u0025\u0023\u0076",_def .Params );_bcde ,_gacd :=_bbc .GetName (_def .Params [0]);if !_gacd ||_bcde ==nil {_fg .Log .Debug ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u006e\u0061m\u0065 \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_def .Params [0]);
return _edeg ;};_fg .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u006e\u0061\u006d\u0065\u003a\u0020\u0025\u0073",_bcde .String ());_ggga ,_cgfe :=_bbc .GetNumberAsFloat (_def .Params [1]);if _cgfe !=nil {_fg .Log .Debug ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0069z\u0065 \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_def .Params [1]);
return _edeg ;};_fg .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0073\u0069\u007a\u0065\u003a\u0020\u0025\u0076",_ggga );_bfafc ,_cgge :=_fgfd .GetFontByName (*_bcde );if !_cgge {_fg .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0046\u006f\u006e\u0074\u0020\u0025s\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_bcde .String ());
return _g .New ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_fg .Log .Debug ("\u0046\u006f\u006e\u0074\u003a\u0020\u0025\u0054",_bfafc );_deag ,_gacd :=_bbc .GetDict (_bfafc );if !_gacd {_fg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0067e\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074");
return _edeg ;};_edf ,_cgfe :=_gb .NewPdfFontFromPdfObject (_deag );if _cgfe !=nil {_fg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066\u0072\u006fm\u0020\u006fb\u006a\u0065\u0063\u0074");
return _cgfe ;};_cgec :=_edf .BaseFont ();if _cgec ==""{_cgec =_bcde .String ();};_dddb ,_gacd :=_ffd [_cgec ];if !_gacd {_dddb ,_cgfe =_ac .NewTextFont (_edf ,_ggga );if _cgfe !=nil {_fg .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_cgfe );
};};if _dddb ==nil {if len (_cgec )> 7&&_cgec [6]=='+'{_cgec =_cgec [7:];};_efgg :=[]string {_cgec ,"\u0054i\u006de\u0073\u0020\u004e\u0065\u0077\u0020\u0052\u006f\u006d\u0061\u006e","\u0041\u0072\u0069a\u006c","D\u0065\u006a\u0061\u0056\u0075\u0020\u0053\u0061\u006e\u0073"};
for _ ,_ddg :=range _efgg {_fg .Log .Debug ("\u0044\u0045\u0042\u0055\u0047\u003a \u0073\u0065\u0061\u0072\u0063\u0068\u0069\u006e\u0067\u0020\u0073\u0079\u0073t\u0065\u006d\u0020\u0066\u006f\u006e\u0074 \u0060\u0025\u0073\u0060",_ddg );if _dddb ,_gacd =_ffd [_ddg ];
_gacd {break ;};_ecc :=_ab .Match (_ddg );if _ecc ==nil {_fg .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0066\u0069\u006e\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_ddg );continue ;};_dddb ,_cgfe =_ac .NewTextFontFromPath (_ecc .Filename ,_ggga );
if _cgfe !=nil {_fg .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_ecc .Filename );continue ;};_fg .Log .Debug ("\u0053\u0075\u0062\u0073\u0074\u0069t\u0075\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0025\u0073 \u0077\u0069\u0074\u0068\u0020\u0025\u0073 \u0028\u0025\u0073\u0029",_cgec ,_ecc .Name ,_ecc .Filename );
_ffd [_ddg ]=_dddb ;break ;};};if _dddb ==nil {_fg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020n\u006f\u0074\u0020\u0066\u0069\u006ed\u0020\u0061\u006e\u0079\u0020\u0073\u0075\u0069\u0074\u0061\u0062\u006c\u0065 \u0066\u006f\u006e\u0074");
return _g .New ("\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0066\u0069\u006e\u0064\u0020a\u006ey\u0020\u0073\u0075\u0069\u0074\u0061\u0062\u006c\u0065\u0020\u0066\u006f\u006e\u0074");};_ga .ProcTf (_dddb .WithSize (_ggga ,_edf ));case "\u0042\u004d\u0043","\u0042\u0044\u0043":if len (_def .Params )==2&&_def .Params [0].String ()=="\u0041\u0072\u0074\u0069\u0066\u0061\u0063\u0074"{_efb ,_aag :=_bbc .GetDict (_def .Params [1]);
if _aag {if _efb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065")!=nil &&_efb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065").String ()=="\u0057a\u0074\u0065\u0072\u006d\u0061\u0072k"{_ga .Tr =_ac .TextRenderingModeInvisible ;};};};case "\u0045\u004d\u0043":default:_fg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0073u\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u006f\u0070\u0065\u0072\u0061\u006e\u0064\u003a\u0020\u0025\u0073",_def .Operand );
};_aeg =_def ;return nil ;});_adf =_cf .Process (_aff );if _adf !=nil {return _adf ;};return nil ;};type renderer struct{_adb float64 };const (ShadingTypeFunctionBased PdfShadingType =1;ShadingTypeAxial PdfShadingType =2;ShadingTypeRadial PdfShadingType =3;
ShadingTypeFreeForm PdfShadingType =4;ShadingTypeLatticeForm PdfShadingType =5;ShadingTypeCoons PdfShadingType =6;ShadingTypeTensorProduct PdfShadingType =7;);func (_gga renderer )renderPage (_de _ac .Context ,_ag *_gb .PdfPage ,_fff _ef .Matrix ,_fcg bool )error {if !_fcg {_bbd :=_gb .FieldFlattenOpts {AnnotFilterFunc :func (_ea *_gb .PdfAnnotation )bool {switch _ea .GetContext ().(type ){case *_gb .PdfAnnotationLine :return true ;
case *_gb .PdfAnnotationSquare :return true ;case *_gb .PdfAnnotationCircle :return true ;case *_gb .PdfAnnotationPolygon :return true ;case *_gb .PdfAnnotationPolyLine :return true ;};return false ;}};_ddd :=_ed .FieldAppearance {};_gec :=_ag .FlattenFieldsWithOpts (_ddd ,&_bbd );
if _gec !=nil {_fg .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0064u\u0072\u0069n\u0067\u0020\u0061\u006e\u006e\u006f\u0074\u0061t\u0069\u006f\u006e\u0020\u0066\u006c\u0061\u0074\u0074\u0065\u006e\u0069n\u0067\u0020\u0025\u0076",_gec );};};_ddcg ,_eb :=_ag .GetAllContentStreams ();
if _eb !=nil {return _eb ;};if _fga :=_fff ;!_fga .Identity (){_ddcg =_bb .Sprintf ("%\u002e\u0032\u0066\u0020\u0025\u002e2\u0066\u0020\u0025\u002e\u0032\u0066 \u0025\u002e\u0032\u0066\u0020\u0025\u002e2\u0066\u0020\u0025\u002e\u0032\u0066\u0020\u0063\u006d\u0020%\u0073",_fga [0],_fga [1],_fga [3],_fga [4],_fga [6],_fga [7],_ddcg );
};_de .Translate (0,float64 (_de .Height ()));_de .Scale (1,-1);_de .Push ();_de .SetRGBA (1,1,1,1);_de .DrawRectangle (0,0,float64 (_de .Width ()),float64 (_de .Height ()));_de .Fill ();_de .Pop ();_de .SetLineWidth (1.0);_de .SetRGBA (0,0,0,1);return _gga .renderContentStream (_de ,_ddcg ,_ag .Resources );
};

// RenderToPath converts the specified PDF page into an image and saves the
// result at the specified location.
func (_gf *ImageDevice )RenderToPath (page *_gb .PdfPage ,outputPath string )error {_bg ,_ae :=_gf .Render (page );if _ae !=nil {return _ae ;};_cc :=_b .ToLower (_dg .Ext (outputPath ));if _cc ==""{return _g .New ("\u0063\u006ful\u0064\u0020\u006eo\u0074\u0020\u0072\u0065cog\u006eiz\u0065\u0020\u006f\u0075\u0074\u0070\u0075t \u0066\u0069\u006c\u0065\u0020\u0074\u0079p\u0065");
};switch _cc {case "\u002e\u0070\u006e\u0067":return _cbc (outputPath ,_bg );case "\u002e\u006a\u0070\u0067","\u002e\u006a\u0070e\u0067":return _cce (outputPath ,_bg ,100);};return _bb .Errorf ("\u0075\u006e\u0072\u0065\u0063\u006fg\u006e\u0069\u007a\u0065\u0064\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020f\u0069\u006c\u0065\u0020\u0074\u0079\u0070e\u003a\u0020\u0025\u0073",_cc );
};func _accg (_ddccb *_gb .Image )_gc .Image {_gdba ,_cced :=int (_ddccb .Width ),int (_ddccb .Height );if _gdba <=0||_cced <=0{_fg .Log .Debug ("\u0069\u006e\u0076\u0061\u006c\u0069d\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u006d\u0065\u006e\u0073i\u006f\u006e\u0073\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",_gdba ,_cced );
return nil ;};_ebed :=_gc .NewRGBA (_gc .Rect (0,0,_gdba ,_cced ));for _bcdb :=0;_bcdb < _cced ;_bcdb ++{for _gafg :=0;_gafg < _gdba ;_gafg ++{_eced ,_dfde :=_ddccb .ColorAt (_gafg ,_bcdb );if _dfde !=nil {_fg .Log .Debug ("\u0063o\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069e\u0076\u0065\u0020\u0069m\u0061\u0067\u0065\u0020\u006da\u0073\u006b\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061y\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002e",_gafg ,_bcdb );
continue ;};_fadc ,_baaba ,_gcg ,_ :=_eced .RGBA ();var _adc _gg .Color ;if _fadc +_baaba +_gcg ==0{_adc =_gg .Transparent ;}else {_fbab :=uint8 (_bbc .RGBToGrayscale (int (_fadc >>8),int (_baaba >>8),int (_gcg >>8)));_adc =_gg .RGBA {R :255,G :255,B :255,A :_fbab };
};_ebed .Set (_gafg ,_bcdb ,_adc );};};return _ebed ;};func _cbc (_dbba string ,_ceebb _gc .Image )error {_bfeg ,_faad :=_d .Create (_dbba );if _faad !=nil {return _faad ;};defer _bfeg .Close ();return _dgg .Encode (_bfeg ,_ceebb );};func _gag (_fcef _bbc .PdfObject ,_adfg _gg .Color )(_gc .Image ,error ){_dga ,_dgad :=_bbc .GetStream (_fcef );
if !_dgad {return nil ,nil ;};_acb ,_beg :=_gb .NewXObjectImageFromStream (_dga );if _beg !=nil {return nil ,_beg ;};_bde ,_beg :=_acb .ToImage ();if _beg !=nil {return nil ,_beg ;};return _fdg (_bde ,_adfg ),nil ;};func _bffaa (_efbc ,_adgbb ,_efbg float64 )_cg .BoundingBox {return _cg .Path {Points :[]_cg .Point {_cg .NewPoint (0,0).Rotate (_efbg ),_cg .NewPoint (_efbc ,0).Rotate (_efbg ),_cg .NewPoint (0,_adgbb ).Rotate (_efbg ),_cg .NewPoint (_efbc ,_adgbb ).Rotate (_efbg )}}.GetBoundingBox ();
};func (_bdbc renderer )processShading (_fcge _ac .Context ,_ffdf *_gb .PdfShading )(_ac .Gradient ,*_bbc .PdfObjectArray ,error ){_cdb :=int64 (*_ffdf .ShadingType );if _cdb ==int64 (ShadingTypeAxial ){return _bdbc .processLinearShading (_fcge ,_ffdf );
}else if _cdb ==int64 (ShadingTypeRadial ){return _bdbc .processRadialShading (_fcge ,_ffdf );}else {_fg .Log .Debug (_bb .Sprintf ("\u0050r\u006f\u0063e\u0073\u0073\u0069n\u0067\u0020\u0067\u0072\u0061\u0064\u0069e\u006e\u0074\u0020\u0074\u0079\u0070e\u0020\u0025\u0064\u0020\u006e\u006f\u0074\u0020\u0079\u0065\u0074 \u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064",_cdb ));
};return nil ,nil ,nil ;};