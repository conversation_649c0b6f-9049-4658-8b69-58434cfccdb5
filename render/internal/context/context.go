//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package context ;import (_dg "errors";_ec "github.com/unidoc/freetype/truetype";_ad "github.com/unidoc/unipdf/v4/core";_fe "github.com/unidoc/unipdf/v4/internal/cmap";_g "github.com/unidoc/unipdf/v4/internal/textencoding";_f "github.com/unidoc/unipdf/v4/internal/transform";
_gc "github.com/unidoc/unipdf/v4/model";_b "golang.org/x/image/font";_c "image";_d "image/color";_dd "strconv";_a "strings";);type TextState struct{Tc float64 ;Tw float64 ;Th float64 ;Tl float64 ;Tf *TextFont ;Ts float64 ;Tm _f .Matrix ;Tlm _f .Matrix ;
Tr TextRenderingMode ;GlobalScale float64 ;};type FillRule int ;func (_cb *TextState )ProcTj (data []byte ,ctx Context ){_bdda :=_cb .Tf .Size ;_ecd :=_cb .Th /100.0;_agf :=_cb .GlobalScale ;_feb :=_f .NewMatrix (_bdda *_ecd ,0,0,_bdda ,0,_cb .Ts );_dbc :=ctx .Matrix ();
_bded :=_dbc .Clone ().Mult (_cb .Tm .Clone ().Mult (_feb )).ScalingFactorY ();_edf :=_cb .Tf .NewFace (_bded );_ab :=_cb .Tf .BytesToCharcodes (data );for _ ,_edbg :=range _ab {_gbc ,_aae :=_cb .Tf .CharcodeToRunes (_edbg );_af :=string (_aae );if _af =="\u0000"{continue ;
};_dfe :=_dbc .Clone ().Mult (_cb .Tm .Clone ().Mult (_feb ));_ac :=_dfe .ScalingFactorY ();_dfe =_dfe .Scale (1/_ac ,-1/_ac );if _cb .Tr !=TextRenderingModeInvisible {ctx .SetMatrix (_dfe );ctx .DrawString (_af ,_edf ,0,0);ctx .SetMatrix (_dbc );};_aee :=0.0;
if _af =="\u0020"{_aee =_cb .Tw ;};_efec ,_ ,_dcb :=_cb .Tf .GetCharMetrics (_gbc );if _dcb {_efec =_efec *0.001*_bdda ;}else {_efec ,_ =ctx .MeasureString (_af ,_edf );_efec =_efec /_agf ;};_cae :=(_efec +_cb .Tc +_aee )*_ecd ;_cb .Tm =_cb .Tm .Mult (_f .TranslationMatrix (_cae ,0));
};};type LineCap int ;func (_daa *TextFont )BytesToCharcodes (data []byte )[]_g .CharCode {if _daa ._bff !=nil {return _daa ._bff .BytesToCharcodes (data );};return _daa .Font .BytesToCharcodes (data );};func (_beb *TextFont )CharcodeToRunes (charcode _g .CharCode )(_g .CharCode ,[]rune ){_ecg :=[]_g .CharCode {charcode };
if _beb ._bff ==nil ||_beb ._bff ==_beb .Font {return _beb .charcodeToRunesSimple (charcode );};_cfa :=_beb ._bff .CharcodesToUnicode (_ecg );_fec ,_ :=_beb .Font .RunesToCharcodeBytes (_cfa );_fcd :=_beb .Font .BytesToCharcodes (_fec );_eag :=charcode ;
if len (_fcd )> 0&&_fcd [0]!=0{_eag =_fcd [0];};if string (_cfa )==string (_fe .MissingCodeRune )&&_beb ._bff .BaseFont ()==_beb .Font .BaseFont (){return _beb .charcodeToRunesSimple (charcode );};return _eag ,_cfa ;};func (_egd *TextFont )GetCharMetrics (code _g .CharCode )(float64 ,float64 ,bool ){if _aab ,_gdg :=_egd .Font .GetCharMetrics (code );
_gdg &&_aab .Wx !=0{return _aab .Wx ,_aab .Wy ,_gdg ;};if _egd ._bff ==nil {return 0,0,false ;};_gab ,_gaa :=_egd ._bff .GetCharMetrics (code );return _gab .Wx ,_gab .Wy ,_gaa &&_gab .Wx !=0;};func (_gcg *TextFont )WithSize (size float64 ,originalFont *_gc .PdfFont )*TextFont {return &TextFont {Font :_gcg .Font ,Size :size ,_gg :_gcg ._gg ,_bff :originalFont };
};type LineJoin int ;func (_gfd *TextState )ProcQ (data []byte ,ctx Context ){_gfd .ProcTStar ();_gfd .ProcTj (data ,ctx )};func (_cea *TextState )ProcTStar (){_cea .ProcTd (0,-_cea .Tl )};func (_add *TextState )ProcDQ (data []byte ,aw ,ac float64 ,ctx Context ){_add .Tw =aw ;
_add .Tc =ac ;_add .ProcQ (data ,ctx );};func NewTextFontFromPath (filePath string ,size float64 )(*TextFont ,error ){_dggd ,_gad :=_gc .NewPdfFontFromTTFFile (filePath );if _gad !=nil {return nil ,_gad ;};return NewTextFont (_dggd ,size );};func (_ag *TextState )ProcTd (tx ,ty float64 ){_ag .Tlm .Concat (_f .TranslationMatrix (tx ,ty ));
_ag .Tm =_ag .Tlm .Clone ();};func (_gcga *TextFont )NewFace (size float64 )_b .Face {return _ec .NewFace (_gcga ._gg ,&_ec .Options {Size :size });};const (LineCapRound LineCap =iota ;LineCapButt ;LineCapSquare ;);const (TextRenderingModeFill TextRenderingMode =iota ;
TextRenderingModeStroke ;TextRenderingModeFillStroke ;TextRenderingModeInvisible ;TextRenderingModeFillClip ;TextRenderingModeStrokeClip ;TextRenderingModeFillStrokeClip ;TextRenderingModeClip ;);type Gradient interface{Pattern ;AddColorStop (_ef float64 ,_cc _d .Color );
};type Pattern interface{ColorAt (_ca ,_gb int )_d .Color ;};const (FillRuleWinding FillRule =iota ;FillRuleEvenOdd ;);type TextRenderingMode int ;func (_cgd *TextState )Reset (){_cgd .Tm =_f .IdentityMatrix ();_cgd .Tlm =_f .IdentityMatrix ()};type TextFont struct{Font *_gc .PdfFont ;
Size float64 ;_gg *_ec .Font ;_bff *_gc .PdfFont ;};func (_dcg *TextState )ProcTm (a ,b ,c ,d ,e ,f float64 ){_dcg .Tm =_f .NewMatrix (a ,b ,c ,d ,e ,f );_dcg .Tlm =_dcg .Tm .Clone ();};func NewTextState ()TextState {return TextState {Th :100,Tm :_f .IdentityMatrix (),Tlm :_f .IdentityMatrix ()};
};func (_bde *TextState )ProcTD (tx ,ty float64 ){_bde .Tl =-ty ;_bde .ProcTd (tx ,ty )};func (_ecb *TextFont )charcodeToRunesSimple (_ddc _g .CharCode )(_g .CharCode ,[]rune ){_ccg :=[]_g .CharCode {_ddc };if _ecb .Font .IsSimple ()&&_ecb ._gg !=nil {if _dde :=_ecb ._gg .Index (rune (_ddc ));
_dde > 0{return _ddc ,[]rune {rune (_ddc )};};};if _ecb ._gg !=nil &&!_ecb ._gg .HasCmap ()&&_a .Contains (_ecb .Font .Encoder ().String (),"\u0049d\u0065\u006e\u0074\u0069\u0074\u0079-"){if _daf :=_ecb ._gg .Index (rune (_ddc ));_daf > 0{return _ddc ,[]rune {rune (_ddc )};
};};return _ddc ,_ecb .Font .CharcodesToUnicode (_ccg );};func (_cdg *TextState )Translate (tx ,ty float64 ){_cdg .Tm =_cdg .Tm .Mult (_f .TranslationMatrix (tx ,ty ));};func NewTextFont (font *_gc .PdfFont ,size float64 )(*TextFont ,error ){_da :=font .FontDescriptor ();
if _da ==nil {return nil ,_dg .New ("\u0063\u006fu\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069pt\u006f\u0072");};_ada ,_bc :=_ad .GetStream (_da .FontFile2 );if !_bc {return nil ,_dg .New ("\u006di\u0073\u0073\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020f\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d");
};_cd ,_fdd :=_ad .DecodeStream (_ada );if _fdd !=nil {return nil ,_fdd ;};_bdd ,_fdd :=_ec .Parse (_cd );if _fdd !=nil {return nil ,_fdd ;};_ggb :=font .FontDescriptor ().FontName .String ();_ggg :=len (_ggb )> 7&&_ggb [6]=='+';if _da .Flags !=nil {_feg ,_efgeg :=_dd .Atoi (_da .Flags .String ());
if _efgeg ==nil &&_feg ==32{_ggg =false ;};};if !_bdd .HasCmap ()&&(!_a .Contains (font .Encoder ().String (),"\u0049d\u0065\u006e\u0074\u0069\u0074\u0079-")||!_ggg ){return nil ,_dg .New ("\u006e\u006f c\u006d\u0061\u0070 \u0061\u006e\u0064\u0020enc\u006fdi\u006e\u0067\u0020\u0069\u0073\u0020\u006eot\u0020\u0069\u0064\u0065\u006e\u0074\u0069t\u0079");
};return &TextFont {Font :font ,Size :size ,_gg :_bdd },nil ;};const (LineJoinRound LineJoin =iota ;LineJoinBevel ;);type Context interface{Push ();Pop ();Matrix ()_f .Matrix ;SetMatrix (_be _f .Matrix );Translate (_efc ,_aa float64 );Scale (_df ,_ff float64 );
Rotate (_gcd float64 );MoveTo (_cf ,_cce float64 );LineTo (_fd ,_ed float64 );CubicTo (_gf ,_cab ,_ea ,_caf ,_eb ,_eg float64 );QuadraticTo (_gd ,_bd ,_gcf ,_ce float64 );NewSubPath ();ClosePath ();ClearPath ();Clip ();ClipPreserve ();ResetClip ();LineWidth ()float64 ;
SetLineWidth (_gfb float64 );SetLineCap (_gfc LineCap );SetLineJoin (_aag LineJoin );SetDash (_de ...float64 );SetDashOffset (_bg float64 );Fill ();FillPreserve ();Stroke ();StrokePreserve ();SetRGBA (_bed ,_bef ,_efe ,_fb float64 );SetFillRGBA (_fc ,_dfd ,_cg ,_ge float64 );
SetFillStyle (_bgb Pattern );SetFillRule (_efee FillRule );SetStrokeRGBA (_gfa ,_ced ,_gbb ,_dfdb float64 );SetStrokeStyle (_fdf Pattern );FillPattern ()Pattern ;StrokePattern ()Pattern ;TextState ()*TextState ;DrawString (_edc string ,_bgd _b .Face ,_dgg ,_ee float64 );
MeasureString (_efg string ,_ba _b .Face )(_cec ,_adf float64 );DrawRectangle (_fdb ,_bf ,_ga ,_db float64 );DrawImage (_edb _c .Image ,_efge ,_egg int );DrawImageAnchored (_bfe _c .Image ,_dc ,_fea int ,_ae ,_eff float64 );Height ()int ;Width ()int ;};
func (_fg *TextState )ProcTf (font *TextFont ){_fg .Tf =font };