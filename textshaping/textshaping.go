//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package textshaping ;import (_e "github.com/unidoc/garabic";_a "golang.org/x/text/unicode/bidi";_d "strings";);

// ArabicShape returns shaped arabic glyphs string.
func ArabicShape (text string )(string ,error ){_f :=_a .Paragraph {};_f .SetString (text );_ad ,_ae :=_f .Order ();if _ae !=nil {return "",_ae ;};for _g :=0;_g < _ad .NumRuns ();_g ++{_fd :=_ad .Run (_g );_fda :=_fd .String ();if _fd .Direction ()==_a .RightToLeft {var (_ag =_e .Shape (_fda );
_fdb =[]rune (_ag );_gf =make ([]rune ,len (_fdb )););_ba :=0;for _ac :=len (_fdb )-1;_ac >=0;_ac --{_gf [_ba ]=_fdb [_ac ];_ba ++;};_fda =string (_gf );text =_d .Replace (text ,_d .TrimSpace (_fd .String ()),_fda ,1);};};return text ,nil ;};